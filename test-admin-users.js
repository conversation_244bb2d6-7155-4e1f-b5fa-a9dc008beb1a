const http = require('http');

const testAdminUsers = async () => {
  try {
    console.log('=== Test de la route /api/admin/users ===');

    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************.Wh9sJP3_DVJF1Sd2ovELeLDpRnUVLKOQTJJJEI3DhbU';

    const options = {
      hostname: 'localhost',
      port: 3003,
      path: '/api/admin/users?page=1&limit=10',
      method: 'GET',
      headers: {
        'x-auth-token': token
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log('✅ Status:', res.statusCode);
        if (res.statusCode === 200) {
          console.log('📊 Données reçues:', JSON.parse(data));
        } else {
          console.error('❌ Erreur:', data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Erreur de requête:', error.message);
    });

    req.end();

  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
  }
};

testAdminUsers();
