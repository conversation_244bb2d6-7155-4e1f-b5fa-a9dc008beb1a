import React from 'react';
import {
  <PERSON>,
  Chip,
  Tooltip,
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utton,
  Badge
} from '@mui/material';
import {
  Sync,
  SyncProblem,
  CloudDone,
  CloudOff,
  Refresh,
  Schedule,
  Warning
} from '@mui/icons-material';

/**
 * Composant indicateur de synchronisation pour les dashboards
 */
const SyncIndicator = ({
  lastSync,
  isOnline = true,
  pendingUpdates = 0,
  loading = false,
  error = null,
  onRefresh,
  variant = 'chip', // 'chip' | 'minimal' | 'detailed'
  showLastSync = true,
  showPendingUpdates = true
}) => {
  const formatLastSync = (timestamp) => {
    if (!timestamp) return 'Jamais synchronisé';
    
    const now = new Date();
    const syncTime = new Date(timestamp);
    const diff = now - syncTime;
    
    if (diff < 60000) return 'Synchronisé à l\'instant';
    if (diff < 3600000) return `Synchronisé il y a ${Math.floor(diff / 60000)} min`;
    if (diff < 86400000) return `Synchronisé il y a ${Math.floor(diff / 3600000)} h`;
    return `Synchronisé le ${syncTime.toLocaleDateString()}`;
  };

  const getSyncStatus = () => {
    if (!isOnline) return { icon: CloudOff, color: 'error', label: 'Hors ligne' };
    if (error) return { icon: SyncProblem, color: 'error', label: 'Erreur de sync' };
    if (loading) return { icon: Sync, color: 'info', label: 'Synchronisation...' };
    if (pendingUpdates > 0) return { icon: SyncProblem, color: 'warning', label: `${pendingUpdates} en attente` };
    if (lastSync) return { icon: CloudDone, color: 'success', label: 'Synchronisé' };
    return { icon: Schedule, color: 'default', label: 'En attente' };
  };

  const status = getSyncStatus();
  const StatusIcon = status.icon;

  const getTooltipContent = () => {
    const lines = [];
    
    if (!isOnline) {
      lines.push('🔴 Hors ligne');
    } else {
      lines.push('🟢 En ligne');
    }
    
    if (error) {
      lines.push(`❌ Erreur: ${error.message || 'Erreur de synchronisation'}`);
    }
    
    if (showLastSync && lastSync) {
      lines.push(`🕒 ${formatLastSync(lastSync)}`);
    }
    
    if (showPendingUpdates && pendingUpdates > 0) {
      lines.push(`⏳ ${pendingUpdates} mise(s) à jour en attente`);
    }
    
    if (loading) {
      lines.push('🔄 Synchronisation en cours...');
    }
    
    return lines.join('\n');
  };

  if (variant === 'minimal') {
    return (
      <Tooltip title={getTooltipContent()}>
        <IconButton size="small" color={status.color} onClick={onRefresh}>
          <Badge badgeContent={pendingUpdates || null} color="error">
            <StatusIcon 
              sx={{ 
                animation: loading ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} 
            />
          </Badge>
        </IconButton>
      </Tooltip>
    );
  }

  if (variant === 'detailed') {
    return (
      <Box 
        display="flex" 
        alignItems="center" 
        gap={1}
        p={1}
        bgcolor={status.color === 'error' ? 'error.light' : 'background.paper'}
        borderRadius={1}
        border={1}
        borderColor={status.color === 'error' ? 'error.main' : 'divider'}
      >
        <StatusIcon 
          color={status.color}
          sx={{ 
            animation: loading ? 'spin 1s linear infinite' : 'none',
            '@keyframes spin': {
              '0%': { transform: 'rotate(0deg)' },
              '100%': { transform: 'rotate(360deg)' }
            }
          }} 
        />
        
        <Box flex={1}>
          <Typography variant="body2" fontWeight="medium">
            {status.label}
          </Typography>
          
          {showLastSync && lastSync && !loading && (
            <Typography variant="caption" color="text.secondary">
              {formatLastSync(lastSync)}
            </Typography>
          )}
          
          {error && (
            <Typography variant="caption" color="error.main" display="block">
              {error.message || 'Erreur de synchronisation'}
            </Typography>
          )}
        </Box>

        {pendingUpdates > 0 && (
          <Badge badgeContent={pendingUpdates} color="warning">
            <Warning color="warning" fontSize="small" />
          </Badge>
        )}

        {onRefresh && (
          <IconButton size="small" onClick={onRefresh} disabled={loading}>
            <Refresh fontSize="small" />
          </IconButton>
        )}
      </Box>
    );
  }

  // Variant 'chip' (default)
  return (
    <Tooltip title={getTooltipContent()}>
      <Chip
        icon={
          <Badge badgeContent={pendingUpdates || null} color="error">
            <StatusIcon 
              sx={{ 
                animation: loading ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} 
            />
          </Badge>
        }
        label={
          <Box display="flex" alignItems="center" gap={0.5}>
            <Typography variant="body2">
              {status.label}
            </Typography>
            {showLastSync && lastSync && !loading && (
              <Typography variant="caption" color="text.secondary">
                ({formatLastSync(lastSync).replace('Synchronisé ', '')})
              </Typography>
            )}
          </Box>
        }
        color={status.color}
        variant="outlined"
        size="small"
        onClick={onRefresh}
        clickable={!!onRefresh}
        sx={{
          cursor: onRefresh ? 'pointer' : 'default',
          '&:hover': onRefresh ? {
            backgroundColor: `${status.color}.light`
          } : {}
        }}
      />
    </Tooltip>
  );
};

/**
 * Composant wrapper pour les dashboards avec synchronisation
 */
export const DashboardSyncWrapper = ({ 
  children, 
  syncData, 
  title = "Dashboard",
  showSyncIndicator = true 
}) => {
  const {
    loading,
    error,
    lastSync,
    isOnline,
    pendingUpdates,
    forceSync
  } = syncData;

  return (
    <Box>
      {showSyncIndicator && (
        <Box 
          display="flex" 
          justifyContent="space-between" 
          alignItems="center" 
          mb={2}
        >
          <Typography variant="h4" component="h1">
            {title}
          </Typography>
          
          <SyncIndicator
            lastSync={lastSync}
            isOnline={isOnline}
            pendingUpdates={pendingUpdates}
            loading={loading}
            error={error}
            onRefresh={forceSync}
            variant="detailed"
          />
        </Box>
      )}
      
      {children}
    </Box>
  );
};

export default SyncIndicator;
