import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useVetConsultations } from '../../../hooks/useVetConsultations';

const ActiveTreatments = () => {
  const { t } = useTranslation();
  const {
    consultations,
    loading,
    error,
    updateConsultation,
    addTreatmentFollowUp
  } = useVetConsultations();

  const [activeTreatments, setActiveTreatments] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTreatment, setSelectedTreatment] = useState(null);
  const [followUpNote, setFollowUpNote] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (consultations) {
      const active = consultations.filter(consultation =>
        consultation.treatments?.some(treatment =>
          treatment.status === 'en_cours' || treatment.status === 'a_surveiller'
        )
      );
      setActiveTreatments(active);
    }
  }, [consultations]);

  const handleOpenDialog = (treatment, consultation) => {
    setSelectedTreatment({ ...treatment, consultationId: consultation.id });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedTreatment(null);
    setFollowUpNote('');
  };

  const handleSubmitFollowUp = async () => {
    if (!selectedTreatment || !followUpNote) return;

    setSubmitting(true);
    try {
      await addTreatmentFollowUp(selectedTreatment.consultationId, selectedTreatment.id, {
        note: followUpNote,
        date: new Date().toISOString(),
        status: selectedTreatment.status
      });

      setSuccess(true);
      setTimeout(() => {
        handleCloseDialog();
        setSuccess(false);
      }, 2000);
    } catch (err) {
      console.error('Erreur lors de l\'ajout du suivi:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      en_cours: { color: 'primary', icon: <InfoIcon />, label: 'En cours' },
      a_surveiller: { color: 'warning', icon: <WarningIcon />, label: 'À surveiller' },
      termine: { color: 'success', icon: <CheckCircleIcon />, label: 'Terminé' }
    };

    const config = statusConfig[status] || statusConfig.en_cours;

    return (
      <Chip
        icon={config.icon}
        label={t(config.label)}
        color={config.color}
        size="small"
      />
    );
  };

  const getDaysRemaining = (startDate, duration) => {
    const start = new Date(startDate);
    const durationDays = parseInt(duration);
    const endDate = new Date(start.setDate(start.getDate() + durationDays));
    const today = new Date();
    const remaining = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
    return remaining;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {t('Traitements Actifs')}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {activeTreatments.length === 0 ? (
              <Alert severity="info">
                {t('Aucun traitement actif en ce moment')}
              </Alert>
            ) : (
              <List>
                {activeTreatments.map((consultation) => (
                  consultation.treatments.map((treatment, index) => (
                    treatment.status !== 'termine' && (
                      <ListItem
                        key={`${consultation.id}-${index}`}
                        divider
                        sx={{ flexDirection: 'column', alignItems: 'flex-start' }}
                      >
                        <Box
                          width="100%"
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          mb={1}
                        >
                          <Typography variant="subtitle1" component="div">
                            {treatment.medication}
                          </Typography>
                          {getStatusChip(treatment.status)}
                        </Box>

                        <ListItemText
                          primary={
                            <Typography variant="body2" color="text.secondary">
                              {t('Prescription')}: {treatment.dosage}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" color="text.secondary">
                                {t('Durée')}: {treatment.duration} {t('jours')}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {t('Jours restants')}: {getDaysRemaining(treatment.startDate, treatment.duration)}
                              </Typography>
                              {treatment.notes && (
                                <Typography variant="body2" color="text.secondary">
                                  {t('Notes')}: {treatment.notes}
                                </Typography>
                              )}
                            </>
                          }
                        />

                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleOpenDialog(treatment, consultation)}
                          >
                            <EditIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    )
                  ))
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {t('Ajouter un suivi de traitement')}
        </DialogTitle>
        <DialogContent>
          {success ? (
            <Alert severity="success">
              {t('Suivi ajouté avec succès')}
            </Alert>
          ) : (
            <TextField
              autoFocus
              margin="dense"
              label={t('Note de suivi')}
              type="text"
              fullWidth
              multiline
              rows={4}
              value={followUpNote}
              onChange={(e) => setFollowUpNote(e.target.value)}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('Annuler')}
          </Button>
          <Button
            onClick={handleSubmitFollowUp}
            color="primary"
            disabled={!followUpNote || submitting}
          >
            {submitting ? <CircularProgress size={24} /> : t('Enregistrer')}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default ActiveTreatments;
