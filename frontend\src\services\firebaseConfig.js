// Import des modules Firebase nécessaires
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

// Configuration Firebase
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || 'AIzaSyBZ9KIB7945XD6qJphq7dsaMJSh8DRBuX8',
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || 'poultray-dz.firebaseapp.com',
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || 'poultray-dz',
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || 'poultray-dz.firebasestorage.app',
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '272222587457',
  appId: import.meta.env.VITE_FIREBASE_APP_ID || '1:272222587457:web:0b6775e967c60267e9d4c0',
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || 'G-5GZQC73FJ8'
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Obtenir l'instance d'authentification Firebase
const auth = getAuth(app);

// Exporter les instances Firebase
export { auth, app };
const firebaseExports = { auth, app };
export default firebaseExports;
