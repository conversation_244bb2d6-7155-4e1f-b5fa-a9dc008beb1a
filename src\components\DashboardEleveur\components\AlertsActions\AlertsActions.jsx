import React from 'react';
import { Typography, Box, Divider, List, ListItem, ListItemIcon, ListItemText, Button, Grid, Card, CardContent } from '@mui/material';
import { Warning, Info, Error, Add, Sell, Vaccines, Visibility } from '@mui/icons-material';

const AlertsActions = ({ data }) => {
  // Données fictives pour la démonstration
  const mockData = {
    alertes: [
      { id: 1, type: 'critical', message: 'Taux de mortalité élevé dans le lot #A123', date: '2023-06-07' },
      { id: 2, type: 'warning', message: 'Vaccination prévue pour le lot #B456 demain', date: '2023-06-07' },
      { id: 3, type: 'info', message: 'Nouvelle mise à jour des prix du marché disponible', date: '2023-06-06' },
      { id: 4, type: 'warning', message: 'Stock d'aliments bas pour le lot #C789', date: '2023-06-06' }
    ],
    actions: [
      { id: 1, label: 'Ajouter un lot', icon: <Add />, action: () => console.log('Ajouter un lot') },
      { id: 2, label: 'Enregistrer une vente', icon: <Sell />, action: () => console.log('Enregistrer une vente') },
      { id: 3, label: 'Programmer vaccination', icon: <Vaccines />, action: () => console.log('Programmer vaccination') },
      { id: 4, label: 'Voir les rapports', icon: <Visibility />, action: () => console.log('Voir les rapports') }
    ]
  };

  // Utiliser les données réelles si disponibles, sinon utiliser les données fictives
  const alertesData = data?.alertes || mockData.alertes;
  const actionsData = data?.actions || mockData.actions;

  // Fonction pour obtenir l'icône en fonction du type d'alerte
  const getAlertIcon = (type) => {
    switch (type) {
      case 'critical':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'info':
        return <Info color="info" />;
      default:
        return <Info />;
    }
  };

  // Fonction pour obtenir la classe CSS en fonction du type d'alerte
  const getAlertClass = (type) => {
    switch (type) {
      case 'critical':
        return 'alert-critical';
      case 'warning':
        return 'alert-warning';
      case 'info':
        return 'alert-info';
      default:
        return '';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Alertes et Actions</Typography>
      <Divider sx={{ mb: 2 }} />

      {/* Section Alertes */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>Alertes Importantes</Typography>
        <List disablePadding>
          {alertesData.map((alerte) => (
            <ListItem key={alerte.id} className={`alert-item ${getAlertClass(alerte.type)}`} disableGutters>
              <ListItemIcon sx={{ minWidth: 40 }}>
                {getAlertIcon(alerte.type)}
              </ListItemIcon>
              <ListItemText
                primary={alerte.message}
                secondary={new Date(alerte.date).toLocaleDateString('fr-FR', { day: 'numeric', month: 'long' })}
              />
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Section Actions Rapides */}
      <Box>
        <Typography variant="subtitle1" gutterBottom>Actions Rapides</Typography>
        <Grid container spacing={2}>
          {actionsData.map((action) => (
            <Grid item xs={6} key={action.id}>
              <Card variant="outlined">
                <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                  <Button
                    fullWidth
                    startIcon={action.icon}
                    onClick={action.action}
                    sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                  >
                    {action.label}
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default AlertsActions;