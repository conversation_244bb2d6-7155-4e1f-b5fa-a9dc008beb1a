import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Save as SaveIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

// Simple HTML preview component
const HtmlPreview = ({ content }) => {
  return (
    <Box
      sx={{
        p: 2,
        bgcolor: 'background.default',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        height: '100%',
        overflow: 'auto',
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

const PageContentGenerator = () => {
  const [formData, setFormData] = useState({
    pageType: 'about',
    keywords: '',
    language: 'fr',
  });

  const [generatedContent, setGeneratedContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [viewMode, setViewMode] = useState('text');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axiosInstance.post('/ai/page-content', formData);
      setGeneratedContent(response.data.content);
      setSuccess('Contenu généré avec succès !');
    } catch (err) {
      console.error('Error generating page content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération du contenu');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setSuccess('Contenu copié dans le presse-papier !');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleReset = () => {
    setFormData({
      pageType: 'about',
      keywords: '',
      language: 'fr',
    });
    setGeneratedContent('');
    setError('');
    setSuccess('');
  };

  const handleViewModeChange = (event, newValue) => {
    setViewMode(newValue);
  };

  // Convert text to simple HTML
  const textToHtml = (text) => {
    if (!text) return '';

    // Convert headings
    let html = text.replace(/^# (.+)$/gm, '<h1>$1</h1>');
    html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
    html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');

    // Convert paragraphs
    html = html.split('\n\n').map(paragraph => {
      if (paragraph.startsWith('<h')) return paragraph;
      return `<p>${paragraph}</p>`;
    }).join('');

    // Convert bold and italic
    html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');

    // Convert lists
    html = html.replace(/^- (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.+<\/li>\n)+/g, '<ul>$&</ul>');

    return html;
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DescriptionIcon color="primary" /> Générateur de Contenu de Page
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Utilisez l'IA pour générer rapidement du contenu pour les pages statiques de votre site.
        </Typography>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <form onSubmit={handleSubmit}>
          <FormControl fullWidth margin="normal">
            <InputLabel>Type de page</InputLabel>
            <Select
              name="pageType"
              value={formData.pageType}
              onChange={handleChange}
              label="Type de page"
            >
              <MenuItem value="about">À propos</MenuItem>
              <MenuItem value="faq">FAQ</MenuItem>
              <MenuItem value="help">Aide / Support</MenuItem>
              <MenuItem value="terms">Conditions d'utilisation</MenuItem>
              <MenuItem value="privacy">Politique de confidentialité</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Mots-clés (séparés par des virgules)"
            name="keywords"
            value={formData.keywords}
            onChange={handleChange}
            margin="normal"
            placeholder="Ex: élevage, volailles, Algérie, plateforme"
          />

          <FormControl fullWidth margin="normal">
            <InputLabel>Langue</InputLabel>
            <Select
              name="language"
              value={formData.language}
              onChange={handleChange}
              label="Langue"
            >
              <MenuItem value="fr">Français</MenuItem>
              <MenuItem value="ar">Arabe</MenuItem>
            </Select>
          </FormControl>

          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <DescriptionIcon />}
            >
              {loading ? 'Génération en cours...' : 'Générer le contenu'}
            </Button>

            <Button
              type="button"
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              startIcon={<RefreshIcon />}
              disabled={loading}
            >
              Réinitialiser
            </Button>
          </Box>
        </form>
      </Paper>

      {generatedContent && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Contenu généré</Typography>
            <Box>
              <Button
                variant="outlined"
                size="small"
                startIcon={<CopyIcon />}
                onClick={handleCopyToClipboard}
                sx={{ mr: 1 }}
              >
                Copier
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<SaveIcon />}
                onClick={() => setSuccess('Contenu enregistré !')}
              >
                Enregistrer
              </Button>
            </Box>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={viewMode} onChange={handleViewModeChange} aria-label="view mode tabs">
              <Tab
                icon={<CodeIcon />}
                label="Texte"
                value="text"
                iconPosition="start"
              />
              <Tab
                icon={<VisibilityIcon />}
                label="Aperçu"
                value="preview"
                iconPosition="start"
              />
            </Tabs>
          </Box>

          <Box sx={{ minHeight: 300 }}>
            {viewMode === 'text' ? (
              <TextField
                fullWidth
                multiline
                rows={12}
                value={generatedContent}
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
            ) : (
              <HtmlPreview content={textToHtml(generatedContent)} />
            )}
          </Box>

          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>Informations</Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label={`Type: ${formData.pageType}`}
                size="small"
                color="primary"
                variant="outlined"
              />
              <Chip
                label={`Langue: ${formData.language === 'fr' ? 'Français' : 'Arabe'}`}
                size="small"
                color="primary"
                variant="outlined"
              />
              {formData.keywords.split(',').map((keyword, index) => (
                keyword.trim() && (
                  <Chip
                    key={index}
                    label={keyword.trim()}
                    size="small"
                    color="secondary"
                    variant="outlined"
                  />
                )
              ))}
            </Box>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default PageContentGenerator;
