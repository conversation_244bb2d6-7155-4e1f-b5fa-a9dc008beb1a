import { useState, useEffect, useCallback, useRef } from 'react';
import syncService, { SYNC_EVENTS, DATA_TYPES } from '../services/syncService';

/**
 * Hook pour la synchronisation automatique des données
 */
export const useDataSync = (dataType, fetchFunction, options = {}) => {
  const {
    interval = 30000,
    immediate = true,
    conflictResolution = 'server_wins',
    retryOnError = true,
    maxRetries = 3,
    enabled = true
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(immediate);
  const [error, setError] = useState(null);
  const [lastSync, setLastSync] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingUpdates, setPendingUpdates] = useState(0);

  const fetchFunctionRef = useRef(fetchFunction);
  const dataTypeRef = useRef(dataType);

  // Mettre à jour les refs quand les props changent
  useEffect(() => {
    fetchFunctionRef.current = fetchFunction;
    dataTypeRef.current = dataType;
  }, [fetchFunction, dataType]);

  // Gestionnaire de mise à jour des données
  const handleDataUpdate = useCallback((event) => {
    if (event.dataType === dataTypeRef.current) {
      setData(event.data);
      setLastSync(event.timestamp);
      setLoading(false);
      setError(null);
    }
  }, []);

  // Gestionnaire d'erreur de synchronisation
  const handleSyncError = useCallback((event) => {
    if (event.dataType === dataTypeRef.current) {
      setError(event.error);
      setLoading(false);
    }
  }, []);

  // Gestionnaire de statut de connexion
  const handleConnectionStatus = useCallback((event) => {
    setIsOnline(event.online);
  }, []);

  // Gestionnaire de rafraîchissement requis
  const handleRefreshRequired = useCallback((event) => {
    if (event.dataType === dataTypeRef.current) {
      // Forcer une synchronisation
      syncService.forcSync(dataTypeRef.current, fetchFunctionRef.current);
    }
  }, []);

  // Démarrer/arrêter la synchronisation
  useEffect(() => {
    if (!enabled) return;

    // Écouter les événements de synchronisation
    syncService.on(SYNC_EVENTS.DATA_UPDATED, handleDataUpdate);
    syncService.on(SYNC_EVENTS.SYNC_ERROR, handleSyncError);
    syncService.on(SYNC_EVENTS.CONNECTION_STATUS, handleConnectionStatus);
    syncService.on(SYNC_EVENTS.REFRESH_REQUIRED, handleRefreshRequired);

    // Démarrer la synchronisation automatique
    syncService.startAutoSync(dataType, fetchFunction, interval, {
      immediate,
      conflictResolution,
      retryOnError,
      maxRetries
    });

    return () => {
      // Nettoyer les listeners
      syncService.off(SYNC_EVENTS.DATA_UPDATED, handleDataUpdate);
      syncService.off(SYNC_EVENTS.SYNC_ERROR, handleSyncError);
      syncService.off(SYNC_EVENTS.CONNECTION_STATUS, handleConnectionStatus);
      syncService.off(SYNC_EVENTS.REFRESH_REQUIRED, handleRefreshRequired);

      // Arrêter la synchronisation
      syncService.stopAutoSync(dataType);
    };
  }, [
    dataType,
    fetchFunction,
    interval,
    immediate,
    conflictResolution,
    retryOnError,
    maxRetries,
    enabled,
    handleDataUpdate,
    handleSyncError,
    handleConnectionStatus,
    handleRefreshRequired
  ]);

  // Mettre à jour le statut des mises à jour en attente
  useEffect(() => {
    const updatePendingStatus = () => {
      const status = syncService.getSyncStatus(dataType);
      setPendingUpdates(status.pendingCount);
    };

    updatePendingStatus();
    const interval = setInterval(updatePendingStatus, 5000);

    return () => clearInterval(interval);
  }, [dataType]);

  // Forcer la synchronisation
  const forceSync = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const newData = await syncService.forcSync(dataType, fetchFunction);
      return newData;
    } catch (err) {
      setError(err);
      throw err;
    }
  }, [dataType, fetchFunction]);

  // Ajouter une mise à jour en attente
  const addPendingUpdate = useCallback((updateFunction, updateData) => {
    syncService.addPendingUpdate(dataType, updateFunction, updateData);
    setPendingUpdates(prev => prev + 1);
  }, [dataType]);

  return {
    data,
    loading,
    error,
    lastSync,
    isOnline,
    pendingUpdates,
    forceSync,
    addPendingUpdate
  };
};

/**
 * Hook pour la synchronisation des données du dashboard
 */
export const useDashboardSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.DASHBOARD, fetchFunction, {
    interval: 60000, // 1 minute
    ...options
  });
};

/**
 * Hook pour la synchronisation des données d'éleveurs
 */
export const useEleveursSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.ELEVEURS, fetchFunction, {
    interval: 120000, // 2 minutes
    ...options
  });
};

/**
 * Hook pour la synchronisation des données de volailles
 */
export const useVolaillesSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.VOLAILLES, fetchFunction, {
    interval: 90000, // 1.5 minutes
    ...options
  });
};

/**
 * Hook pour la synchronisation des consultations
 */
export const useConsultationsSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.CONSULTATIONS, fetchFunction, {
    interval: 30000, // 30 secondes
    ...options
  });
};

/**
 * Hook pour la synchronisation des commandes
 */
export const useCommandesSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.COMMANDES, fetchFunction, {
    interval: 45000, // 45 secondes
    ...options
  });
};

/**
 * Hook pour la synchronisation du stock
 */
export const useStockSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.STOCK, fetchFunction, {
    interval: 60000, // 1 minute
    conflictResolution: 'manual', // Les conflits de stock nécessitent une résolution manuelle
    ...options
  });
};

/**
 * Hook pour la synchronisation des alertes
 */
export const useAlertesSync = (fetchFunction, options = {}) => {
  return useDataSync(DATA_TYPES.ALERTES, fetchFunction, {
    interval: 15000, // 15 secondes (plus fréquent pour les alertes)
    immediate: true,
    ...options
  });
};

/**
 * Hook pour gérer plusieurs synchronisations
 */
export const useMultiSync = (syncConfigs) => {
  const [globalLoading, setGlobalLoading] = useState(true);
  const [globalError, setGlobalError] = useState(null);
  const [syncStatuses, setSyncStatuses] = useState({});

  const syncResults = syncConfigs.map(config => 
    useDataSync(config.dataType, config.fetchFunction, config.options)
  );

  // Calculer l'état global
  useEffect(() => {
    const allLoaded = syncResults.every(result => !result.loading);
    const hasError = syncResults.some(result => result.error);
    const firstError = syncResults.find(result => result.error)?.error;

    setGlobalLoading(!allLoaded);
    setGlobalError(hasError ? firstError : null);

    // Mettre à jour les statuts individuels
    const statuses = {};
    syncConfigs.forEach((config, index) => {
      const result = syncResults[index];
      statuses[config.dataType] = {
        loading: result.loading,
        error: result.error,
        lastSync: result.lastSync,
        pendingUpdates: result.pendingUpdates
      };
    });
    setSyncStatuses(statuses);
  }, [syncResults, syncConfigs]);

  // Forcer la synchronisation de tous les types
  const forceAllSync = useCallback(async () => {
    const promises = syncResults.map(result => result.forceSync());
    return Promise.allSettled(promises);
  }, [syncResults]);

  return {
    data: syncResults.reduce((acc, result, index) => {
      acc[syncConfigs[index].dataType] = result.data;
      return acc;
    }, {}),
    loading: globalLoading,
    error: globalError,
    syncStatuses,
    forceAllSync,
    individual: syncResults
  };
};

export default useDataSync;
