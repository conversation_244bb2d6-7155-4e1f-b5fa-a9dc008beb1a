import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Typography,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  CircularProgress,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import axios from 'axios';

const MarchandProduits = () => {
  const [produits, setProduits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduit, setSelectedProduit] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    prix: '',
    quantite: '',
    categorie: '',
    image: null
  });

  useEffect(() => {
    fetchProduits();
  }, []);

  const fetchProduits = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/marchand/produits');
      setProduits(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des produits');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (produit = null) => {
    if (produit) {
      setSelectedProduit(produit);
      setFormData({
        nom: produit.nom,
        description: produit.description,
        prix: produit.prix.toString(),
        quantite: produit.quantite.toString(),
        categorie: produit.categorie,
        image: null
      });
    } else {
      setSelectedProduit(null);
      setFormData({
        nom: '',
        description: '',
        prix: '',
        quantite: '',
        categorie: '',
        image: null
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduit(null);
  };

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'image' && files) {
      setFormData(prev => ({ ...prev, image: files[0] }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const formDataToSend = new FormData();
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null) {
          formDataToSend.append(key, formData[key]);
        }
      });

      if (selectedProduit) {
        await axios.put(`/api/marchand/produits/${selectedProduit.id}`, formDataToSend);
        setSnackbar({ open: true, message: 'Produit mis à jour avec succès', severity: 'success' });
      } else {
        await axios.post('/api/marchand/produits', formDataToSend);
        setSnackbar({ open: true, message: 'Produit ajouté avec succès', severity: 'success' });
      }

      handleCloseDialog();
      fetchProduits();
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'enregistrement du produit',
        severity: 'error'
      });
      console.error('Erreur:', err);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        await axios.delete(`/api/marchand/produits/${id}`);
        setSnackbar({ open: true, message: 'Produit supprimé avec succès', severity: 'success' });
        fetchProduits();
      } catch (err) {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la suppression du produit',
          severity: 'error'
        });
        console.error('Erreur:', err);
      }
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Mes Produits</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Ajouter un produit
        </Button>
      </Box>

      <Grid container spacing={3}>
        {produits.map((produit) => (
          <Grid item xs={12} sm={6} md={4} key={produit.id}>
            <Card>
              <CardMedia
                component="img"
                height="200"
                image={produit.image_url || '/placeholder-product.png'}
                alt={produit.nom}
              />
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {produit.nom}
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  {produit.description}
                </Typography>
                <Typography variant="h6" color="primary">
                  {produit.prix.toLocaleString('fr-FR', { style: 'currency', currency: 'DZD' })}
                </Typography>
                <Typography variant="body2">
                  Stock: {produit.quantite}
                </Typography>
                <Box mt={2} display="flex" justifyContent="flex-end" gap={1}>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handleOpenDialog(produit)}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(produit.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedProduit ? 'Modifier le produit' : 'Ajouter un produit'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" noValidate onSubmit={handleSubmit} sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  name="nom"
                  label="Nom du produit"
                  fullWidth
                  required
                  value={formData.nom}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="description"
                  label="Description"
                  fullWidth
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  name="prix"
                  label="Prix (DZD)"
                  type="number"
                  fullWidth
                  required
                  value={formData.prix}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  name="quantite"
                  label="Quantité en stock"
                  type="number"
                  fullWidth
                  required
                  value={formData.quantite}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="categorie"
                  label="Catégorie"
                  fullWidth
                  value={formData.categorie}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="image-upload"
                  type="file"
                  name="image"
                  onChange={handleInputChange}
                />
                <label htmlFor="image-upload">
                  <Button variant="outlined" component="span" fullWidth>
                    {formData.image ? 'Changer l\'image' : 'Ajouter une image'}
                  </Button>
                </label>
                {formData.image && (
                  <Typography variant="caption" display="block" mt={1}>
                    Image sélectionnée: {formData.image.name}
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {selectedProduit ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MarchandProduits;
