/**
 * Script de test pour vérifier la configuration Axios
 * Ce script peut être exécuté dans la console du navigateur pour vérifier que
 * l'instance Axios est correctement configurée et fonctionne comme prévu.
 */

import axiosInstance from './axiosConfig';

// Fonction pour tester les requêtes API
const testAxiosRequests = async () => {
  console.log('=== Test de configuration Axios ===');
  
  // 1. Vérifier que l'instance est correctement configurée
  console.log('1. Configuration de l\'instance Axios:');
  console.log('- baseURL:', axiosInstance.defaults.baseURL);
  console.log('- timeout:', axiosInstance.defaults.timeout);
  console.log('- headers par défaut:', axiosInstance.defaults.headers);
  
  // 2. Vérifier le token dans localStorage
  const token = localStorage.getItem('token');
  console.log('2. Token dans localStorage:', token ? 'Présent' : 'Absent');
  if (token) {
    try {
      // Décoder le token JWT (sans vérification)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      const decodedToken = JSON.parse(jsonPayload);
      
      console.log('- Token décodé:', decodedToken);
      console.log('- Expiration:', new Date(decodedToken.exp * 1000).toLocaleString());
      console.log('- Rôle utilisateur:', decodedToken.user?.role);
    } catch (error) {
      console.error('Erreur lors du décodage du token:', error);
    }
  }
  
  // 3. Tester une requête API
  console.log('3. Test de requête API:');
  try {
    console.log('- Envoi d\'une requête GET à /admin/stats...');
    const response = await axiosInstance.get('/admin/stats');
    console.log('- Réponse reçue:', response.data);
    console.log('- Statut:', response.status);
    console.log('✅ Requête réussie!');
  } catch (error) {
    console.error('❌ Erreur lors de la requête:', error);
    console.log('- Message:', error.message);
    console.log('- Statut:', error.response?.status);
    console.log('- Données d\'erreur:', error.response?.data);
  }
  
  // 4. Vérifier les en-têtes envoyés
  console.log('4. Test des en-têtes de requête:');
  try {
    // Créer une fonction pour intercepter et afficher les en-têtes des requêtes
    const originalRequest = axiosInstance.request;
    axiosInstance.request = function(config) {
      console.log('- En-têtes de la requête:', config.headers);
      // Restaurer la fonction originale
      axiosInstance.request = originalRequest;
      return originalRequest.call(this, config);
    };
    
    // Faire une requête pour voir les en-têtes
    await axiosInstance.get('/user/profile');
  } catch (error) {
    console.error('Erreur lors du test des en-têtes:', error);
  }
  
  console.log('=== Fin du test ===');
};

// Exporter la fonction de test
export default testAxiosRequests;

// Si ce script est exécuté directement dans la console
if (typeof window !== 'undefined') {
  console.log('Vous pouvez exécuter le test avec: testAxiosRequests()');
}
