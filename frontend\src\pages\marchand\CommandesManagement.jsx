/**
 * Composant de gestion des commandes pour les marchands
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Autocomplete,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Stepper,
  Step,
  StepLabel,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  LocalShipping as ShippingIcon,
  Assignment as OrderIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Person as PersonIcon,
  Print as PrintIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { marchandAPI } from '../../services/marchandService';

const CommandesManagement = () => {
  const { user } = useAuth();
  const [commandes, setCommandes] = useState([]);
  const [clients, setClients] = useState([]);
  const [produits, setProduits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCommande, setSelectedCommande] = useState(null);
  const [filterStatus, setFilterStatus] = useState('');

  const [formData, setFormData] = useState({
    client_id: '',
    date_commande: new Date(),
    date_livraison_prevue: null,
    statut: 'en_attente',
    priorite: 'normale',
    notes: '',
    adresse_livraison: '',
    produits: []
  });

  const [currentProduit, setCurrentProduit] = useState({
    produit_id: '',
    quantite: '',
    prix_unitaire: ''
  });

  const [stats, setStats] = useState({
    totalCommandes: 0,
    commandesEnAttente: 0,
    commandesConfirmees: 0,
    commandesLivrees: 0,
    chiffreAffairesCommandes: 0
  });

  const statusSteps = ['en_attente', 'confirmee', 'preparee', 'expediee', 'livree'];
  const statusLabels = {
    'en_attente': 'En attente',
    'confirmee': 'Confirmée',
    'preparee': 'Préparée',
    'expediee': 'Expédiée',
    'livree': 'Livrée',
    'annulee': 'Annulée'
  };

  useEffect(() => {
    loadCommandes();
    loadClients();
    loadProduits();
    loadStats();
  }, []);

  const loadCommandes = async () => {
    try {
      setLoading(true);
      const response = await marchandAPI.getCommandes();
      setCommandes(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des commandes');
      console.error('Erreur commandes:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadClients = async () => {
    try {
      const response = await marchandAPI.getClients();
      setClients(response.data || []);
    } catch (err) {
      console.error('Erreur clients:', err);
    }
  };

  const loadProduits = async () => {
    try {
      const response = await marchandAPI.getProduits();
      setProduits(response.data || []);
    } catch (err) {
      console.error('Erreur produits:', err);
    }
  };

  const loadStats = async () => {
    try {
      const response = await marchandAPI.getCommandesStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats commandes:', err);
    }
  };

  const handleOpenDialog = (commande = null) => {
    if (commande) {
      setSelectedCommande(commande);
      setFormData({
        client_id: commande.client_id || '',
        date_commande: new Date(commande.date_commande) || new Date(),
        date_livraison_prevue: commande.date_livraison_prevue ? new Date(commande.date_livraison_prevue) : null,
        statut: commande.statut || 'en_attente',
        priorite: commande.priorite || 'normale',
        notes: commande.notes || '',
        adresse_livraison: commande.adresse_livraison || '',
        produits: commande.produits || []
      });
    } else {
      setSelectedCommande(null);
      setFormData({
        client_id: '',
        date_commande: new Date(),
        date_livraison_prevue: null,
        statut: 'en_attente',
        priorite: 'normale',
        notes: '',
        adresse_livraison: '',
        produits: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCommande(null);
    setCurrentProduit({
      produit_id: '',
      quantite: '',
      prix_unitaire: ''
    });
  };

  const handleAddProduit = () => {
    if (currentProduit.produit_id && currentProduit.quantite) {
      const produit = produits.find(p => p.id === currentProduit.produit_id);
      const newProduit = {
        ...currentProduit,
        id: Date.now(),
        nom_produit: produit?.nom || '',
        prix_unitaire: currentProduit.prix_unitaire || produit?.prix_unitaire || 0,
        sous_total: (currentProduit.quantite * (currentProduit.prix_unitaire || produit?.prix_unitaire || 0))
      };

      setFormData({
        ...formData,
        produits: [...formData.produits, newProduit]
      });

      setCurrentProduit({
        produit_id: '',
        quantite: '',
        prix_unitaire: ''
      });
    }
  };

  const handleRemoveProduit = (produitId) => {
    setFormData({
      ...formData,
      produits: formData.produits.filter(p => p.id !== produitId)
    });
  };

  const handleSubmit = async () => {
    try {
      const commandeData = {
        ...formData,
        marchand_id: user.profile_id,
        montant_total: formData.produits.reduce((total, p) => total + p.sous_total, 0)
      };

      if (selectedCommande) {
        await marchandAPI.updateCommande(selectedCommande.id, commandeData);
      } else {
        await marchandAPI.createCommande(commandeData);
      }

      handleCloseDialog();
      loadCommandes();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la commande');
      console.error('Erreur sauvegarde commande:', err);
    }
  };

  const handleUpdateStatus = async (commandeId, newStatus) => {
    try {
      await marchandAPI.updateCommandeStatus(commandeId, newStatus);
      loadCommandes();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la mise à jour du statut');
      console.error('Erreur update status:', err);
    }
  };

  const getStatutColor = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'en_attente': return 'warning';
      case 'confirmee': return 'info';
      case 'preparee': return 'primary';
      case 'expediee': return 'secondary';
      case 'livree': return 'success';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const getPrioriteColor = (priorite) => {
    switch (priorite?.toLowerCase()) {
      case 'haute': return 'error';
      case 'normale': return 'default';
      case 'basse': return 'info';
      default: return 'default';
    }
  };

  const getStatusStepIndex = (statut) => {
    return statusSteps.indexOf(statut);
  };

  const filteredCommandes = commandes.filter(commande => {
    return !filterStatus || commande.statut === filterStatus;
  });

  const StatCard = ({ title, value, icon, color = 'primary', subtitle }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des commandes...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Commandes
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Commande
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Total Commandes"
              value={stats.totalCommandes}
              icon={<OrderIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="En Attente"
              value={stats.commandesEnAttente}
              icon={<ScheduleIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Confirmées"
              value={stats.commandesConfirmees}
              icon={<CheckIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Livrées"
              value={stats.commandesLivrees}
              icon={<ShippingIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Chiffre d'Affaires"
              value={`${stats.chiffreAffairesCommandes.toLocaleString()} DA`}
              icon={<MoneyIcon />}
              color="secondary"
            />
          </Grid>
        </Grid>

        {/* Filtres */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Filtrer par statut</InputLabel>
                  <Select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="">Tous les statuts</MenuItem>
                    {Object.entries(statusLabels).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={8}>
                <Typography variant="body2" color="text.secondary">
                  {filteredCommandes.length} commande(s) trouvée(s)
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Table des commandes */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Liste des Commandes
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>N° Commande</TableCell>
                    <TableCell>Client</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Montant</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Priorité</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredCommandes.map((commande) => (
                    <TableRow key={commande.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          #{commande.numero_commande || commande.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {commande.client_nom}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {commande.client_telephone}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(commande.date_commande).toLocaleDateString('fr-FR')}
                        </Typography>
                        {commande.date_livraison_prevue && (
                          <Typography variant="caption" color="text.secondary">
                            Livraison: {new Date(commande.date_livraison_prevue).toLocaleDateString('fr-FR')}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {commande.montant_total} DA
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {commande.produits?.length || 0} article(s)
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={statusLabels[commande.statut]}
                          color={getStatutColor(commande.statut)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={commande.priorite}
                          color={getPrioriteColor(commande.priorite)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Modifier">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(commande)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Voir détails">
                          <IconButton
                            size="small"
                            onClick={() => {/* Voir détails */}}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Imprimer">
                          <IconButton
                            size="small"
                            onClick={() => {/* Imprimer */}}
                          >
                            <PrintIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Dialog pour créer/modifier une commande */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>
            {selectedCommande ? 'Modifier la Commande' : 'Nouvelle Commande'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={clients}
                  getOptionLabel={(option) => `${option.nom} - ${option.telephone}`}
                  value={clients.find(c => c.id === formData.client_id) || null}
                  onChange={(event, newValue) => {
                    setFormData({ ...formData, client_id: newValue?.id || '' });
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Client" fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Date de commande"
                  value={formData.date_commande}
                  onChange={(date) => setFormData({ ...formData, date_commande: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Date de livraison prévue"
                  value={formData.date_livraison_prevue}
                  onChange={(date) => setFormData({ ...formData, date_livraison_prevue: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Priorité</InputLabel>
                  <Select
                    value={formData.priorite}
                    onChange={(e) => setFormData({ ...formData, priorite: e.target.value })}
                  >
                    <MenuItem value="basse">Basse</MenuItem>
                    <MenuItem value="normale">Normale</MenuItem>
                    <MenuItem value="haute">Haute</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Adresse de livraison"
                  value={formData.adresse_livraison}
                  onChange={(e) => setFormData({ ...formData, adresse_livraison: e.target.value })}
                />
              </Grid>

              {/* Section produits */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Produits de la commande
                </Typography>
                <Divider sx={{ mb: 2 }} />

                {/* Ajouter un produit */}
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={12} md={4}>
                    <Autocomplete
                      options={produits}
                      getOptionLabel={(option) => option.nom}
                      value={produits.find(p => p.id === currentProduit.produit_id) || null}
                      onChange={(event, newValue) => {
                        setCurrentProduit({
                          ...currentProduit,
                          produit_id: newValue?.id || '',
                          prix_unitaire: newValue?.prix_unitaire || ''
                        });
                      }}
                      renderInput={(params) => (
                        <TextField {...params} label="Produit" size="small" />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      size="small"
                      fullWidth
                      label="Quantité"
                      type="number"
                      value={currentProduit.quantite}
                      onChange={(e) => setCurrentProduit({ ...currentProduit, quantite: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      size="small"
                      fullWidth
                      label="Prix unitaire (DA)"
                      type="number"
                      value={currentProduit.prix_unitaire}
                      onChange={(e) => setCurrentProduit({ ...currentProduit, prix_unitaire: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={handleAddProduit}
                      startIcon={<AddIcon />}
                    >
                      Ajouter
                    </Button>
                  </Grid>
                </Grid>

                {/* Liste des produits ajoutés */}
                <List>
                  {formData.produits.map((produit, index) => (
                    <ListItem key={produit.id || index} divider>
                      <ListItemText
                        primary={produit.nom_produit}
                        secondary={`Quantité: ${produit.quantite} - Prix: ${produit.prix_unitaire} DA - Sous-total: ${produit.sous_total} DA`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleRemoveProduit(produit.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>

                {/* Total de la commande */}
                {formData.produits.length > 0 && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                    <Typography variant="h6" align="right">
                      Total: {formData.produits.reduce((total, p) => total + p.sous_total, 0)} DA
                    </Typography>
                  </Box>
                )}
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedCommande ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default CommandesManagement;
