import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useTranslation } from 'react-i18next';
import { useEggProduction } from '../../../hooks/useEggProduction';
import { QUALITES_OEUFS } from '../../../utils/constants';

const DailyCollection = () => {
  const { t } = useTranslation();
  const {
    addProduction,
    calculateDailyAverage,
    getProductionQuality,
    productions,
    loading,
    error
  } = useEggProduction();

  const [formData, setFormData] = useState({
    date: new Date(),
    quantite: '',
    oeufs_intacts: '',
    oeufs_casses: '',
    oeufs_defectueux: '',
    notes: ''
  });

  const [success, setSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date
    }));
  };

  const validateForm = () => {
    const total = parseInt(formData.oeufs_intacts || 0) +
                 parseInt(formData.oeufs_casses || 0) +
                 parseInt(formData.oeufs_defectueux || 0);

    return total === parseInt(formData.quantite || 0);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!validateForm()) {
      alert(t('La somme des œufs par qualité doit être égale à la quantité totale'));
      return;
    }

    setSubmitting(true);
    try {
      await addProduction({
        ...formData,
        quantite: parseInt(formData.quantite),
        oeufs_intacts: parseInt(formData.oeufs_intacts),
        oeufs_casses: parseInt(formData.oeufs_casses),
        oeufs_defectueux: parseInt(formData.oeufs_defectueux)
      });
      setSuccess(true);
      setFormData({
        date: new Date(),
        quantite: '',
        oeufs_intacts: '',
        oeufs_casses: '',
        oeufs_defectueux: '',
        notes: ''
      });
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Erreur lors de l\'ajout de la production:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          {t('Collecte Quotidienne des Œufs')}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {t('Production enregistrée avec succès')}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <DatePicker
                label={t('Date de collecte')}
                value={formData.date}
                onChange={handleDateChange}
                renderInput={(params) => <TextField {...params} fullWidth />}
                maxDate={new Date()}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('Quantité totale')}
                name="quantite"
                type="number"
                value={formData.quantite}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label={t('Œufs intacts')}
                name="oeufs_intacts"
                type="number"
                value={formData.oeufs_intacts}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label={t('Œufs cassés')}
                name="oeufs_casses"
                type="number"
                value={formData.oeufs_casses}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label={t('Œufs défectueux')}
                name="oeufs_defectueux"
                type="number"
                value={formData.oeufs_defectueux}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('Notes')}
                name="notes"
                multiline
                rows={4}
                value={formData.notes}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={submitting}
                fullWidth
              >
                {submitting ? (
                  <CircularProgress size={24} />
                ) : (
                  t('Enregistrer la collecte')
                )}
              </Button>
            </Grid>
          </Grid>
        </form>

        {productions.length > 0 && (
          <Box mt={4}>
            <Typography variant="h6" gutterBottom>
              {t('Statistiques de production')}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  {t('Moyenne quotidienne')}: {calculateDailyAverage(productions).toFixed(2)} œufs
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  {t('Production du jour')}: {productions[0]?.quantite || 0} œufs
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default DailyCollection;
