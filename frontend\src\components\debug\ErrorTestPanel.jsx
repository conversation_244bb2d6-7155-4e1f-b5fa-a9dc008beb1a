import React from 'react';
import {
  <PERSON>,
  Button,
  Typography,
  Paper,
  Grid,
  Divider,
  Alert
} from '@mui/material';
import {
  Error,
  Warning,
  NetworkCheck,
  Security,
  BugReport
} from '@mui/icons-material';
import useErrorHandler from '../../hooks/useErrorHandler';
import { ERROR_TYPES, ERROR_CODES, StandardError } from '../../services/errorHandler';
import NetworkStatus from '../network/NetworkStatus';
import SyncStatus from '../sync/SyncStatus';
import { useDataSync, DATA_TYPES } from '../../hooks/useDataSync';

/**
 * Composant de test pour le système de gestion d'erreurs
 * Utilisé uniquement en mode développement
 */
const ErrorTestPanel = () => {
  const { handleError, clearAllErrors, errors } = useErrorHandler();

  // Test de synchronisation avec des données fictives
  const testSyncData = useDataSync(
    DATA_TYPES.DASHBOARD,
    async () => {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        timestamp: new Date().toISOString(),
        data: `Test data ${Math.random()}`
      };
    },
    {
      interval: 10000, // 10 secondes pour les tests
      immediate: false
    }
  );

  // Simuler différents types d'erreurs
  const simulateNetworkError = () => {
    const error = new StandardError(
      ERROR_TYPES.NETWORK,
      ERROR_CODES.CONNECTION_LOST,
      'Connexion réseau perdue (test)',
      null,
      { isTest: true }
    );
    handleError(error);
  };

  const simulateServerError = () => {
    const error = new StandardError(
      ERROR_TYPES.SERVER,
      ERROR_CODES.SERVER_UNAVAILABLE,
      'Serveur temporairement indisponible (test)',
      null,
      { isTest: true, statusCode: 503 }
    );
    handleError(error);
  };

  const simulateAuthError = () => {
    const error = new StandardError(
      ERROR_TYPES.AUTH,
      ERROR_CODES.TOKEN_EXPIRED,
      'Session expirée (test)',
      null,
      { isTest: true, statusCode: 401 }
    );
    handleError(error);
  };

  const simulateValidationError = () => {
    const error = new StandardError(
      ERROR_TYPES.VALIDATION,
      ERROR_CODES.VALIDATION_FAILED,
      'Données de formulaire invalides (test)',
      null,
      {
        isTest: true,
        statusCode: 422,
        validationErrors: {
          email: 'Format email invalide',
          password: 'Mot de passe trop court'
        }
      }
    );
    handleError(error);
  };

  const simulateTimeoutError = () => {
    const error = new StandardError(
      ERROR_TYPES.TIMEOUT,
      ERROR_CODES.REQUEST_TIMEOUT,
      'Délai d\'attente dépassé (test)',
      null,
      { isTest: true }
    );
    handleError(error);
  };

  const simulateRateLimitError = () => {
    const error = new StandardError(
      ERROR_TYPES.RATE_LIMIT,
      ERROR_CODES.RATE_LIMITED,
      'Trop de requêtes, veuillez patienter (test)',
      null,
      { isTest: true, statusCode: 429, retryAfter: 60 }
    );
    handleError(error);
  };

  const simulateAxiosError = () => {
    // Simuler une erreur Axios typique
    const axiosError = {
      response: {
        status: 500,
        data: {
          message: 'Erreur interne du serveur (test Axios)'
        }
      },
      config: {
        url: '/api/test',
        method: 'GET'
      }
    };
    handleError(axiosError);
  };

  const simulateNetworkFailure = () => {
    // Simuler une panne réseau complète
    const networkError = {
      code: 'NETWORK_ERROR',
      message: 'Network Error (test)',
      config: {
        url: '/api/test',
        method: 'POST'
      }
    };
    handleError(networkError);
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Paper elevation={2} sx={{ p: 3, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        🐛 Panneau de Test des Erreurs
      </Typography>

      <Alert severity="info" sx={{ mb: 2 }}>
        Ce panneau est uniquement visible en mode développement pour tester le système de gestion d'erreurs.
      </Alert>

      <Grid container spacing={2}>
        {/* Tests d'erreurs par type */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>
            Tests par Type d'Erreur
          </Typography>

          <Box display="flex" flexDirection="column" gap={1}>
            <Button
              variant="outlined"
              color="error"
              startIcon={<NetworkCheck />}
              onClick={simulateNetworkError}
              fullWidth
            >
              Erreur Réseau
            </Button>

            <Button
              variant="outlined"
              color="error"
              startIcon={<Error />}
              onClick={simulateServerError}
              fullWidth
            >
              Erreur Serveur (503)
            </Button>

            <Button
              variant="outlined"
              color="error"
              startIcon={<Security />}
              onClick={simulateAuthError}
              fullWidth
            >
              Erreur Auth (401)
            </Button>

            <Button
              variant="outlined"
              color="warning"
              startIcon={<Warning />}
              onClick={simulateValidationError}
              fullWidth
            >
              Erreur Validation (422)
            </Button>

            <Button
              variant="outlined"
              color="warning"
              onClick={simulateTimeoutError}
              fullWidth
            >
              Timeout
            </Button>

            <Button
              variant="outlined"
              color="warning"
              onClick={simulateRateLimitError}
              fullWidth
            >
              Rate Limit (429)
            </Button>
          </Box>
        </Grid>

        {/* Tests d'erreurs Axios */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>
            Tests d'Erreurs Axios
          </Typography>

          <Box display="flex" flexDirection="column" gap={1}>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<BugReport />}
              onClick={simulateAxiosError}
              fullWidth
            >
              Erreur Axios Standard
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              startIcon={<NetworkCheck />}
              onClick={simulateNetworkFailure}
              fullWidth
            >
              Panne Réseau Complète
            </Button>
          </Box>
        </Grid>

        {/* État du réseau */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" gutterBottom>
            🌐 État du Réseau
          </Typography>
          <NetworkStatus variant="detailed" />
        </Grid>

        {/* Tests de synchronisation */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" gutterBottom>
            🔄 Tests de Synchronisation
          </Typography>

          <Box display="flex" flexDirection="column" gap={2}>
            <SyncStatus variant="detailed" />

            <Box>
              <Typography variant="body2" gutterBottom>
                Test de synchronisation automatique:
              </Typography>
              <Box display="flex" alignItems="center" gap={2}>
                <Button
                  variant="outlined"
                  onClick={testSyncData.forceSync}
                  disabled={testSyncData.loading}
                >
                  Forcer la synchronisation
                </Button>

                <Typography variant="caption">
                  Dernière sync: {testSyncData.lastSync ?
                    new Date(testSyncData.lastSync).toLocaleTimeString() :
                    'Jamais'
                  }
                </Typography>

                {testSyncData.data && (
                  <Typography variant="caption" color="success.main">
                    ✅ Données reçues: {testSyncData.data.timestamp}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Grid>

        {/* Contrôles */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="subtitle1">
              Erreurs Actives: {errors.length}
            </Typography>

            <Button
              variant="contained"
              color="secondary"
              onClick={clearAllErrors}
              disabled={errors.length === 0}
            >
              Effacer Toutes les Erreurs
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ErrorTestPanel;
