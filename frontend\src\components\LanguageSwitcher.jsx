import React, { useState } from 'react';
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
} from '@mui/material';
import { Translate as TranslateIcon } from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';

const LanguageSwitcher = ({ variant = 'icon', color = 'inherit', size = 'medium' }) => {
  const { language, changeLanguage } = useLanguage();
  const [anchorEl, setAnchorEl] = useState(null);
  
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  const handleLanguageChange = (lang) => {
    changeLanguage(lang);
    handleClose();
  };
  
  const languages = [
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'ar', name: 'العربية', flag: '🇩🇿' },
  ];
  
  const currentLanguage = languages.find(lang => lang.code === language);
  
  return (
    <>
      {variant === 'icon' ? (
        <Tooltip title="Changer de langue">
          <Button
            color={color}
            size={size}
            onClick={handleClick}
            startIcon={<TranslateIcon />}
          >
            {currentLanguage?.code.toUpperCase()}
          </Button>
        </Tooltip>
      ) : (
        <Button
          color={color}
          size={size}
          onClick={handleClick}
          startIcon={<TranslateIcon />}
        >
          {currentLanguage?.name}
        </Button>
      )}
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            '& .MuiAvatar-root': {
              width: 32,
              height: 32,
              ml: -0.5,
              mr: 1,
            },
          },
        }}
      >
        {languages.map((lang) => (
          <MenuItem
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            selected={lang.code === language}
          >
            <ListItemIcon sx={{ fontSize: '1.25rem' }}>
              {lang.flag}
            </ListItemIcon>
            <ListItemText>{lang.name}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default LanguageSwitcher;
