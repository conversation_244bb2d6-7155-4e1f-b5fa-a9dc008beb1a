const express = require('express');
const app = express();
const PORT = 3003;

// CORS middleware simple
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');
  res.header('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Requête reçue sur /');
  res.json({
    message: 'Serveur de test CORS fonctionnel',
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// Route API de test
app.get('/api/test', (req, res) => {
  console.log('✅ Requête reçue sur /api/test');
  res.json({
    message: 'API de test fonctionnelle',
    cors: 'configuré pour localhost:5173'
  });
});

// Route d'authentification
app.post('/api/auth/login', (req, res) => {
  console.log('✅ Requête de connexion reçue:', req.body);
  console.log('📋 Headers reçus:', req.headers);

  const { email, password, firebase_token, firebase_uid } = req.body;

  // Supporter à la fois l'authentification simple et Firebase
  if (email === '<EMAIL>' && password === 'admin123') {
    console.log('✅ Authentification réussie');
    res.json({
      message: 'Connexion réussie',
      token: 'fake-jwt-token-for-testing',
      user: {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        nom: 'Admin',
        prenom: 'Poultray',
        first_name: 'Admin',
        last_name: 'Poultray'
      }
    });
  } else {
    console.log('❌ Authentification échouée pour:', { email, password });
    res.status(401).json({
      message: 'Identifiants invalides'
    });
  }
});

// Route pour obtenir les informations de l'utilisateur connecté
app.get('/api/auth/user', (req, res) => {
  console.log('✅ Requête /api/auth/user reçue');
  console.log('📋 Headers:', req.headers);

  const authHeader = req.headers.authorization || req.headers['x-auth-token'];
  if (!authHeader) {
    console.log('❌ Token manquant');
    return res.status(401).json({ message: 'Token manquant' });
  }

  // Simuler la vérification du token
  console.log('✅ Token valide, retour des informations utilisateur');
  res.json({
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
    nom: 'Admin',
    prenom: 'Poultray',
    first_name: 'Admin',
    last_name: 'Poultray',
    username: 'admin',
    phone: '+213 123 456 789',
    address: 'Alger, Algérie',
    created_at: '2024-01-01T00:00:00Z'
  });
});

// Route admin stats
app.get('/api/admin/stats', (req, res) => {
  console.log('✅ Requête /api/admin/stats reçue');

  const authHeader = req.headers.authorization || req.headers['x-auth-token'];
  if (!authHeader) {
    console.log('❌ Token manquant pour /api/admin/stats');
    return res.status(401).json({ message: 'Token manquant' });
  }

  console.log('✅ Retour des statistiques admin');
  res.json({
    totalEleveurs: 2,
    totalVolailles: 150,
    totalVentes: 25,
    totalAdmins: 1,
    totalVeterinaires: 0,
    totalMarchands: 0,
    totalUsers: 3,
    chiffreAffaires: 125000,
    users: [
      { role: 'admin', count: '1' },
      { role: 'eleveur', count: '2' }
    ],
    volailles: {
      total: 150,
      disponibles: 120,
      vendues: 30
    },
    ventes: {
      total: 25,
      montantTotal: 125000,
      moyenneParVente: 5000
    },
    ventesParMois: [
      { mois: 'Jan', ventes: 45 },
      { mois: 'Fév', ventes: 52 },
      { mois: 'Mar', ventes: 38 },
      { mois: 'Avr', ventes: 65 },
      { mois: 'Mai', ventes: 58 }
    ]
  });
});

// Route admin users
app.get('/api/admin/users', (req, res) => {
  console.log('✅ Requête /api/admin/users reçue');

  const authHeader = req.headers.authorization || req.headers['x-auth-token'];
  if (!authHeader) {
    console.log('❌ Token manquant pour /api/admin/users');
    return res.status(401).json({ message: 'Token manquant' });
  }

  const { page = 1, limit = 10, role } = req.query;
  console.log('📋 Paramètres de pagination:', { page, limit, role });

  let users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      first_name: 'Admin',
      last_name: 'Poultray',
      phone: '+213 123 456 789',
      address: 'Alger, Algérie',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      username: 'eleveur1',
      email: '<EMAIL>',
      role: 'eleveur',
      first_name: 'Jean',
      last_name: 'Dupont',
      phone: '+213 987 654 321',
      address: 'Oran, Algérie',
      created_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 3,
      username: 'eleveur2',
      email: '<EMAIL>',
      role: 'eleveur',
      first_name: 'Marie',
      last_name: 'Martin',
      phone: '+213 555 123 456',
      address: 'Constantine, Algérie',
      created_at: '2024-02-01T00:00:00Z'
    }
  ];

  // Filtrer par rôle si spécifié
  if (role) {
    users = users.filter(user => user.role === role);
  }

  const total = users.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedUsers = users.slice(startIndex, endIndex);

  console.log('✅ Retour des utilisateurs:', { total, page, limit });
  res.json({
    users: paginatedUsers,
    total: total,
    page: parseInt(page),
    limit: parseInt(limit),
    totalPages: Math.ceil(total / limit)
  });
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test CORS démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 API: http://localhost:${PORT}/api/test`);
  console.log(`🔐 Login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`🔧 CORS configuré pour: http://localhost:5173`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
