import { useState, useEffect } from 'react';
import axiosInstance from '../../../services/axiosConfig';
import api from '../../../services/api';

/**
 * Hook personnalisé pour gérer l'authentification et les tokens JWT
 * Résout le problème des erreurs 401 en implémentant un système de rafraîchissement automatique
 */
const useAuthToken = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);

  // Utiliser l'instance Axios configurée avec les intercepteurs pour les tokens
  useEffect(() => {

    // Vérifier l'authentification au chargement
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }

        // Vérifier la validité du token en appelant une API protégée
        const response = await axiosInstance.get('/auth/me');
        setUser(response.data);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'authentification:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Pas besoin de nettoyer les intercepteurs car ils sont gérés dans axiosConfig
  }, []);

  // Fonction de déconnexion
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user_credentials');
    setIsAuthenticated(false);
    setUser(null);
    window.location.href = '/login';
  };

  // Fonction pour sauvegarder les informations d'authentification
  const saveAuthInfo = (token, userInfo, credentials) => {
    localStorage.setItem('token', token);
    if (credentials) {
      localStorage.setItem('user_credentials', JSON.stringify(credentials));
    }
    setUser(userInfo);
    setIsAuthenticated(true);
  };

  return {
    isAuthenticated,
    loading,
    user,
    logout
  };
};

export default useAuthToken;
