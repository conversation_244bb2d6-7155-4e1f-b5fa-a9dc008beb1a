/**
 * Seed data script for alertes_stock table
 * This script adds sample alerts to test the veterinaire dashboard and notifications
 */

const { Sequelize, DataTypes } = require('sequelize');
const fs = require('fs');
const path = require('path');

// Read database config from config.json
const configPath = path.join(__dirname, '../config/config.json');
const configFile = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const config = configFile.development; // Use development config

// Create new Sequelize instance using the database config
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port || 5432,
    dialect: 'postgres',
    logging: (sql) => console.log('SQL Query:', sql)
  }
);

// Function to seed alertes_stock table
async function seedAlerteStockTable() {
  console.log('Database configuration:', {
    name: config.database,
    user: config.username,
    host: config.host,
    port: config.port || 5432
  });

  console.log('Seeding alertes_stock table...');

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Get eleveurs for reference
    const eleveursQuery = `
      SELECT e.id, v.id as veterinaire_id
      FROM eleveurs e
      JOIN veterinaires v ON v.id = e.veterinaire_id
      LIMIT 5
    `;

    const eleveurs = await sequelize.query(eleveursQuery, {
      type: Sequelize.QueryTypes.SELECT
    });

    if (eleveurs.length === 0) {
      console.log('❌ No eleveurs found. Cannot seed alertes_stock table without eleveur references.');
      return;
    }

    console.log(`✅ Found ${eleveurs.length} eleveurs for seeding.`);

    // Sample alert types from the model
    const alertTypes = [
      'mortalite_elevee',
      'production_baisse',
      'temperature_anormale',
      'stock_medicament_bas',
      'stock_aliment_bas'
    ];

    // Sample priorities
    const priorities = ['faible', 'normale', 'elevee', 'critique'];

    // Sample data for insertion
    const alerts = [];

    for (const eleveur of eleveurs) {
      // Create 2-3 alerts per eleveur
      const numAlerts = 2 + Math.floor(Math.random() * 2);

      for (let i = 0; i < numAlerts; i++) {
        const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)];
        const priority = priorities[Math.floor(Math.random() * priorities.length)];

        // Create alert data
        let title, message;

        switch (alertType) {
          case 'mortalite_elevee':
            title = 'Taux de mortalité élevé détecté';
            message = 'Le taux de mortalité a dépassé le seuil critique dans votre élevage. Une intervention vétérinaire est recommandée.';
            break;
          case 'production_baisse':
            title = 'Baisse de production détectée';
            message = 'Une baisse significative de la production a été détectée. Vérifiez les conditions d\'élevage.';
            break;
          case 'temperature_anormale':
            title = 'Température anormale détectée';
            message = 'La température dans le bâtiment d\'élevage est en dehors des valeurs optimales. Risque pour la santé des animaux.';
            break;
          case 'stock_medicament_bas':
            title = 'Stock de médicaments bas';
            message = 'Le stock de médicaments essentiels est bas. Pensez à renouveler vos approvisionnements.';
            break;
          case 'stock_aliment_bas':
            title = 'Stock d\'aliment bas';
            message = 'Le stock d\'aliment est presque épuisé. Commandez rapidement pour éviter une rupture.';
            break;
        }

        // Random date within last 14 days
        const daysAgo = Math.floor(Math.random() * 14);
        const date = new Date();
        date.setDate(date.getDate() - daysAgo);

        alerts.push({
          eleveur_id: eleveur.id,
          type_alerte: alertType,
          priorite: priority,
          statut: 'active',
          titre: title,
          message: message,
          date_declenchement: date,
          visible: true,
          created_at: date,
          updated_at: date
        });
      }
    }

    // Check if we have alerts to insert
    if (alerts.length === 0) {
      console.log('❌ No alerts created. Cannot proceed with seeding.');
      return;
    }

    console.log(`✅ Created ${alerts.length} sample alerts for insertion.`);

    // Insert alerts into database
    for (const alert of alerts) {
      // Build the SQL insert query dynamically
      const columns = Object.keys(alert).join(', ');
      const placeholders = Object.keys(alert).map((_, index) => `$${index + 1}`).join(', ');

      const insertQuery = `
        INSERT INTO alertes_stock (${columns})
        VALUES (${placeholders})
        ON CONFLICT DO NOTHING
      `;

      await sequelize.query(insertQuery, {
        bind: Object.values(alert),
        type: Sequelize.QueryTypes.INSERT
      });
    }

    console.log('✅ Sample data inserted successfully into alertes_stock table.');

    // Verify insertion
    const countQuery = `SELECT COUNT(*) FROM alertes_stock`;
    const countResult = await sequelize.query(countQuery, {
      type: Sequelize.QueryTypes.SELECT
    });

    console.log(`✅ Total alerts in table: ${countResult[0].count}`);

  } catch (error) {
    console.error('❌ Error seeding database table:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seeding function
seedAlerteStockTable();
