const { DataTypes } = require('sequelize');

/**
 * FeedAlert model - Represents alerts and notifications related to feed management
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedAlert = sequelize.define('FeedAlert', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    farm_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'fermes',
        key: 'id'
      }
    },
    feed_stock_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'feed_stock',
        key: 'id'
      },
      comment: 'related feed stock if applicable'
    },
    feed_plan_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'feed_plans',
        key: 'id'
      },
      comment: 'related feed plan if applicable'
    },
    alert_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        isIn: [[
          'low_stock', 'out_of_stock', 'expiry_warning', 'expired_feed',
          'quality_issue', 'feeding_schedule', 'fcr_alert', 'cost_alert',
          'supplier_issue', 'nutritional_deficiency', 'consumption_anomaly',
          'plan_completion', 'approval_required', 'maintenance_required'
        ]]
      },
      comment: 'type of alert'
    },
    severity: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'medium',
      validate: {
        isIn: [['low', 'medium', 'high', 'critical']]
      },
      comment: 'severity level of the alert'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [5, 200],
        notEmpty: true
      },
      comment: 'alert title'
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true
      },
      comment: 'detailed alert message'
    },
    alert_data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'additional data related to the alert'
    },
    threshold_value: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: true,
      comment: 'threshold value that triggered the alert'
    },
    current_value: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: true,
      comment: 'current value when alert was triggered'
    },
    recommended_action: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'recommended action to resolve the alert'
    },
    auto_resolve: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this alert can be auto-resolved'
    },
    is_resolved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether the alert has been resolved'
    },
    resolved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'user who resolved the alert'
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when the alert was resolved'
    },
    resolution_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'notes about how the alert was resolved'
    },
    is_acknowledged: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether the alert has been acknowledged'
    },
    acknowledged_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'user who acknowledged the alert'
    },
    acknowledged_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when the alert was acknowledged'
    },
    notification_sent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether notification has been sent'
    },
    notification_methods: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'methods used to send notifications (email, sms, push)'
    },
    repeat_interval: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1
      },
      comment: 'repeat interval in minutes for recurring alerts'
    },
    next_reminder: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when to send next reminder'
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when this alert expires if not resolved'
    },
    priority_score: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 50,
      validate: {
        min: 1,
        max: 100
      },
      comment: 'calculated priority score for sorting'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'tags for categorizing alerts'
    },
    related_alerts: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'IDs of related alerts'
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: [['active', 'resolved', 'dismissed', 'expired', 'suppressed']]
      },
      comment: 'current status of the alert'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_alerts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['farm_id', 'status']
      },
      {
        fields: ['alert_type', 'severity']
      },
      {
        fields: ['is_resolved', 'created_at']
      },
      {
        fields: ['feed_stock_id']
      },
      {
        fields: ['feed_plan_id']
      },
      {
        fields: ['priority_score', 'created_at']
      },
      {
        fields: ['next_reminder']
      },
      {
        fields: ['expires_at']
      }
    ],
    hooks: {
      beforeCreate: (alert) => {
        // Calculate priority score based on severity and type
        alert.priority_score = calculatePriorityScore(alert);
        
        // Set default title if not provided
        if (!alert.title) {
          alert.title = generateDefaultTitle(alert);
        }
        
        // Set expiration date for certain alert types
        if (!alert.expires_at && shouldAutoExpire(alert.alert_type)) {
          const expirationHours = getExpirationHours(alert.alert_type);
          alert.expires_at = new Date(Date.now() + expirationHours * 60 * 60 * 1000);
        }
      },
      
      beforeUpdate: (alert) => {
        // Recalculate priority score if severity changed
        if (alert.changed('severity') || alert.changed('alert_type')) {
          alert.priority_score = calculatePriorityScore(alert);
        }
        
        // Set resolved timestamp
        if (alert.changed('is_resolved') && alert.is_resolved && !alert.resolved_at) {
          alert.resolved_at = new Date();
          alert.status = 'resolved';
        }
        
        // Set acknowledged timestamp
        if (alert.changed('is_acknowledged') && alert.is_acknowledged && !alert.acknowledged_at) {
          alert.acknowledged_at = new Date();
        }
      }
    }
  });

  // Helper functions
  function calculatePriorityScore(alert) {
    let score = 50; // Base score
    
    // Severity multiplier
    const severityScores = {
      'critical': 40,
      'high': 30,
      'medium': 20,
      'low': 10
    };
    
    score += severityScores[alert.severity] || 20;
    
    // Alert type modifier
    const typeModifiers = {
      'out_of_stock': 15,
      'expired_feed': 15,
      'critical': 15,
      'low_stock': 10,
      'expiry_warning': 10,
      'quality_issue': 10,
      'fcr_alert': 5,
      'feeding_schedule': 5
    };
    
    score += typeModifiers[alert.alert_type] || 0;
    
    return Math.max(1, Math.min(100, score));
  }
  
  function generateDefaultTitle(alert) {
    const titleTemplates = {
      'low_stock': 'Stock faible détecté',
      'out_of_stock': 'Rupture de stock',
      'expiry_warning': 'Aliment proche de l\'expiration',
      'expired_feed': 'Aliment expiré',
      'quality_issue': 'Problème de qualité détecté',
      'feeding_schedule': 'Rappel d\'alimentation',
      'fcr_alert': 'Alerte FCR anormal',
      'cost_alert': 'Alerte coût d\'alimentation',
      'supplier_issue': 'Problème fournisseur',
      'nutritional_deficiency': 'Déficience nutritionnelle',
      'consumption_anomaly': 'Anomalie de consommation',
      'plan_completion': 'Plan d\'alimentation terminé',
      'approval_required': 'Approbation requise',
      'maintenance_required': 'Maintenance requise'
    };
    
    return titleTemplates[alert.alert_type] || 'Alerte système';
  }
  
  function shouldAutoExpire(alertType) {
    const autoExpireTypes = ['feeding_schedule', 'expiry_warning', 'cost_alert'];
    return autoExpireTypes.includes(alertType);
  }
  
  function getExpirationHours(alertType) {
    const expirationHours = {
      'feeding_schedule': 24,
      'expiry_warning': 168, // 7 days
      'cost_alert': 72 // 3 days
    };
    
    return expirationHours[alertType] || 168;
  }

  // Instance methods
  FeedAlert.prototype.acknowledge = function(userId, notes = null) {
    this.is_acknowledged = true;
    this.acknowledged_by = userId;
    this.acknowledged_at = new Date();
    
    if (notes) {
      this.resolution_notes = notes;
    }
    
    return this.save();
  };

  FeedAlert.prototype.resolve = function(userId, notes = null) {
    this.is_resolved = true;
    this.resolved_by = userId;
    this.resolved_at = new Date();
    this.status = 'resolved';
    
    if (notes) {
      this.resolution_notes = notes;
    }
    
    return this.save();
  };

  FeedAlert.prototype.dismiss = function(userId, reason = null) {
    this.status = 'dismissed';
    this.resolved_by = userId;
    this.resolved_at = new Date();
    
    if (reason) {
      this.resolution_notes = `Dismissed: ${reason}`;
    }
    
    return this.save();
  };

  FeedAlert.prototype.suppress = function(duration = 24) {
    this.status = 'suppressed';
    this.next_reminder = new Date(Date.now() + duration * 60 * 60 * 1000);
    
    return this.save();
  };

  FeedAlert.prototype.scheduleReminder = function(intervalMinutes) {
    this.repeat_interval = intervalMinutes;
    this.next_reminder = new Date(Date.now() + intervalMinutes * 60 * 1000);
    
    return this.save();
  };

  FeedAlert.prototype.markNotificationSent = function(methods = []) {
    this.notification_sent = true;
    this.notification_methods = methods;
    
    return this.save();
  };

  FeedAlert.prototype.isExpired = function() {
    return this.expires_at && new Date() > new Date(this.expires_at);
  };

  FeedAlert.prototype.shouldSendReminder = function() {
    if (!this.next_reminder || this.is_resolved || this.status !== 'active') {
      return false;
    }
    
    return new Date() >= new Date(this.next_reminder);
  };

  FeedAlert.prototype.getAgeInHours = function() {
    const now = new Date();
    const created = new Date(this.created_at);
    return Math.floor((now - created) / (1000 * 60 * 60));
  };

  FeedAlert.prototype.addRelatedAlert = function(alertId) {
    if (!this.related_alerts) {
      this.related_alerts = [];
    }
    
    if (!this.related_alerts.includes(alertId)) {
      this.related_alerts.push(alertId);
      return this.save();
    }
    
    return Promise.resolve(this);
  };

  FeedAlert.prototype.addTag = function(tag) {
    if (!this.tags) {
      this.tags = [];
    }
    
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
      return this.save();
    }
    
    return Promise.resolve(this);
  };

  // Class methods
  FeedAlert.findByFarm = function(farmId, options = {}) {
    return this.findAll({
      where: {
        farm_id: farmId,
        status: { [sequelize.Sequelize.Op.ne]: 'dismissed' }
      },
      include: [
        { model: sequelize.models.FeedStock, as: 'feed_stock' },
        { model: sequelize.models.FeedPlan, as: 'feed_plan' },
        { model: sequelize.models.User, as: 'resolver' }
      ],
      order: [['priority_score', 'DESC'], ['created_at', 'DESC']],
      ...options
    });
  };

  FeedAlert.findActive = function(farmId = null, options = {}) {
    const whereClause = {
      status: 'active',
      is_resolved: false
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.FeedStock, as: 'feed_stock' },
        { model: sequelize.models.FeedPlan, as: 'feed_plan' }
      ],
      order: [['priority_score', 'DESC'], ['created_at', 'DESC']],
      ...options
    });
  };

  FeedAlert.findBySeverity = function(severity, farmId = null, options = {}) {
    const whereClause = {
      severity: severity,
      status: 'active'
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.FeedStock, as: 'feed_stock' },
        { model: sequelize.models.FeedPlan, as: 'feed_plan' }
      ],
      order: [['created_at', 'DESC']],
      ...options
    });
  };

  FeedAlert.findByType = function(alertType, farmId = null, options = {}) {
    const whereClause = {
      alert_type: alertType,
      status: { [sequelize.Sequelize.Op.ne]: 'dismissed' }
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      ...options
    });
  };

  FeedAlert.findPendingReminders = function(options = {}) {
    return this.findAll({
      where: {
        next_reminder: {
          [sequelize.Sequelize.Op.lte]: new Date()
        },
        status: 'active',
        is_resolved: false
      },
      order: [['next_reminder', 'ASC']],
      ...options
    });
  };

  FeedAlert.findExpired = function(options = {}) {
    return this.findAll({
      where: {
        expires_at: {
          [sequelize.Sequelize.Op.lte]: new Date()
        },
        status: { [sequelize.Sequelize.Op.ne]: 'expired' }
      },
      order: [['expires_at', 'ASC']],
      ...options
    });
  };

  FeedAlert.getAlertStats = function(farmId = null, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const whereClause = {
      created_at: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      attributes: [
        'alert_type',
        'severity',
        [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'count'],
        [sequelize.Sequelize.fn('AVG', sequelize.Sequelize.col('priority_score')), 'avg_priority']
      ],
      group: ['alert_type', 'severity'],
      order: [[sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'DESC']]
    });
  };

  FeedAlert.createLowStockAlert = function(farmId, feedStockId, currentQuantity, threshold) {
    return this.create({
      farm_id: farmId,
      feed_stock_id: feedStockId,
      alert_type: 'low_stock',
      severity: currentQuantity <= (threshold * 0.5) ? 'high' : 'medium',
      title: 'Stock faible détecté',
      message: `Le stock d'aliment est faible. Quantité actuelle: ${currentQuantity} kg, Seuil: ${threshold} kg`,
      threshold_value: threshold,
      current_value: currentQuantity,
      recommended_action: 'Commandez de nouveaux stocks d\'aliment avant rupture complète.',
      auto_resolve: true
    });
  };

  FeedAlert.createExpiryAlert = function(farmId, feedStockId, expiryDate, daysUntilExpiry) {
    const severity = daysUntilExpiry <= 3 ? 'high' : daysUntilExpiry <= 7 ? 'medium' : 'low';
    
    return this.create({
      farm_id: farmId,
      feed_stock_id: feedStockId,
      alert_type: daysUntilExpiry <= 0 ? 'expired_feed' : 'expiry_warning',
      severity: severity,
      title: daysUntilExpiry <= 0 ? 'Aliment expiré' : 'Aliment proche de l\'expiration',
      message: daysUntilExpiry <= 0 
        ? `L'aliment a expiré le ${expiryDate}. Retirez-le immédiatement.`
        : `L'aliment expire dans ${daysUntilExpiry} jour(s) le ${expiryDate}.`,
      current_value: daysUntilExpiry,
      recommended_action: daysUntilExpiry <= 0 
        ? 'Retirez immédiatement l\'aliment expiré et vérifiez la santé des animaux.'
        : 'Utilisez cet aliment en priorité ou préparez son remplacement.',
      auto_resolve: false
    });
  };

  FeedAlert.createFCRAlert = function(farmId, feedPlanId, currentFCR, expectedFCR) {
    const deviation = ((currentFCR - expectedFCR) / expectedFCR) * 100;
    const severity = Math.abs(deviation) > 20 ? 'high' : Math.abs(deviation) > 10 ? 'medium' : 'low';
    
    return this.create({
      farm_id: farmId,
      feed_plan_id: feedPlanId,
      alert_type: 'fcr_alert',
      severity: severity,
      title: 'FCR anormal détecté',
      message: `Le FCR actuel (${currentFCR}) dévie de ${deviation.toFixed(1)}% par rapport à l'attendu (${expectedFCR}).`,
      threshold_value: expectedFCR,
      current_value: currentFCR,
      recommended_action: 'Vérifiez la qualité de l\'aliment, la santé des animaux et les conditions d\'élevage.',
      auto_resolve: false
    });
  };

  // Associations
  FeedAlert.associate = function(models) {
    // FeedAlert belongs to Farm (Eleveur)
    FeedAlert.belongsTo(models.Eleveur, {
      foreignKey: 'farm_id',
      as: 'farm'
    });

    // FeedAlert belongs to FeedStock
    FeedAlert.belongsTo(models.FeedStock, {
      foreignKey: 'feed_stock_id',
      as: 'feed_stock'
    });

    // FeedAlert belongs to FeedPlan
    FeedAlert.belongsTo(models.FeedPlan, {
      foreignKey: 'feed_plan_id',
      as: 'feed_plan'
    });

    // FeedAlert belongs to User (resolver)
    FeedAlert.belongsTo(models.User, {
      foreignKey: 'resolved_by',
      as: 'resolver'
    });

    // FeedAlert belongs to User (acknowledger)
    FeedAlert.belongsTo(models.User, {
      foreignKey: 'acknowledged_by',
      as: 'acknowledger'
    });
  };

  return FeedAlert;
};