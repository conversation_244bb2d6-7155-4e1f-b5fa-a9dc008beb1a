const { DataTypes } = require('sequelize');

/**
 * FeedComposition model - Represents the composition of feed items and plans
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedComposition = sequelize.define('FeedComposition', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    feed_item_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'feed_items',
        key: 'id'
      },
      comment: 'feed item this composition belongs to'
    },
    feed_plan_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'feed_plans',
        key: 'id'
      },
      comment: 'feed plan this composition belongs to'
    },
    ingredient_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      },
      comment: 'name of the ingredient'
    },
    ingredient_type: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        isIn: [[
          'cereal', 'protein_source', 'fat_source', 'mineral', 'vitamin',
          'additive', 'fiber_source', 'energy_source', 'amino_acid', 'other'
        ]]
      },
      comment: 'type/category of ingredient'
    },
    percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      validate: {
        min: 0.01,
        max: 100
      },
      comment: 'percentage of this ingredient in the feed'
    },
    quantity_per_kg: {
      type: DataTypes.DECIMAL(8, 3),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'quantity of ingredient per kg of feed'
    },
    unit_of_measure: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'g',
      validate: {
        isIn: [['g', 'kg', 'mg', 'ml', 'l', 'iu', 'ppm', '%']]
      },
      comment: 'unit of measurement for the ingredient'
    },
    nutritional_value: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'nutritional contribution of this ingredient'
    },
    cost_per_kg: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'cost per kg of this ingredient'
    },
    supplier_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'supplier information for this ingredient'
    },
    quality_grade: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        isIn: [['premium', 'standard', 'basic', 'organic', 'non_gmo']]
      },
      comment: 'quality grade of the ingredient'
    },
    origin_country: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [0, 50]
      },
      comment: 'country of origin'
    },
    processing_method: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [0, 100]
      },
      comment: 'how the ingredient is processed'
    },
    storage_requirements: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'special storage requirements for this ingredient'
    },
    shelf_life_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 3650
      },
      comment: 'shelf life in days'
    },
    minimum_inclusion: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'minimum inclusion percentage'
    },
    maximum_inclusion: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'maximum inclusion percentage'
    },
    is_essential: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this ingredient is essential'
    },
    is_organic: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this ingredient is organic'
    },
    is_gmo_free: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this ingredient is GMO-free'
    },
    allergen_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'allergen information'
    },
    regulatory_status: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        isIn: [['approved', 'restricted', 'banned', 'pending_approval']]
      },
      comment: 'regulatory status in Algeria'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'additional notes about this ingredient'
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: [['active', 'inactive', 'discontinued', 'seasonal']]
      },
      comment: 'status of this ingredient'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_composition',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['feed_item_id']
      },
      {
        fields: ['feed_plan_id']
      },
      {
        fields: ['ingredient_type', 'status']
      },
      {
        fields: ['percentage']
      },
      {
        fields: ['is_essential', 'status']
      },
      {
        fields: ['ingredient_name']
      }
    ],
    validate: {
      mustHaveFeedItemOrPlan() {
        if (!this.feed_item_id && !this.feed_plan_id) {
          throw new Error('Feed composition must belong to either a feed item or feed plan');
        }
      },
      validInclusionRange() {
        if (this.minimum_inclusion && this.maximum_inclusion && 
            this.minimum_inclusion > this.maximum_inclusion) {
          throw new Error('Minimum inclusion cannot be greater than maximum inclusion');
        }
      },
      validPercentageRange() {
        if (this.minimum_inclusion && this.percentage < this.minimum_inclusion) {
          throw new Error('Percentage cannot be less than minimum inclusion');
        }
        if (this.maximum_inclusion && this.percentage > this.maximum_inclusion) {
          throw new Error('Percentage cannot be greater than maximum inclusion');
        }
      }
    },
    hooks: {
      beforeValidate: (composition) => {
        // Auto-calculate quantity per kg based on percentage
        if (composition.percentage && !composition.quantity_per_kg) {
          composition.quantity_per_kg = (composition.percentage / 100) * 1000; // grams per kg
        }
        
        // Normalize ingredient name
        if (composition.ingredient_name) {
          composition.ingredient_name = composition.ingredient_name.trim();
        }
      }
    }
  });

  // Instance methods
  FeedComposition.prototype.calculateQuantityForBatch = function(totalFeedWeight) {
    return (this.percentage / 100) * totalFeedWeight;
  };

  FeedComposition.prototype.calculateCostForBatch = function(totalFeedWeight) {
    if (!this.cost_per_kg) return 0;
    const ingredientWeight = this.calculateQuantityForBatch(totalFeedWeight);
    return (ingredientWeight / 1000) * this.cost_per_kg; // Convert to kg
  };

  FeedComposition.prototype.getNutritionalContribution = function(nutrient) {
    if (!this.nutritional_value || !this.nutritional_value[nutrient]) return 0;
    return (this.percentage / 100) * this.nutritional_value[nutrient];
  };

  FeedComposition.prototype.isWithinInclusionLimits = function() {
    let withinLimits = true;
    
    if (this.minimum_inclusion && this.percentage < this.minimum_inclusion) {
      withinLimits = false;
    }
    
    if (this.maximum_inclusion && this.percentage > this.maximum_inclusion) {
      withinLimits = false;
    }
    
    return withinLimits;
  };

  FeedComposition.prototype.getInclusionStatus = function() {
    if (!this.minimum_inclusion && !this.maximum_inclusion) {
      return 'no_limits';
    }
    
    if (this.isWithinInclusionLimits()) {
      return 'within_limits';
    }
    
    if (this.minimum_inclusion && this.percentage < this.minimum_inclusion) {
      return 'below_minimum';
    }
    
    if (this.maximum_inclusion && this.percentage > this.maximum_inclusion) {
      return 'above_maximum';
    }
    
    return 'unknown';
  };

  FeedComposition.prototype.getQualityScore = function() {
    let score = 50; // Base score
    
    // Quality grade bonus
    const qualityBonuses = {
      'premium': 20,
      'organic': 15,
      'standard': 10,
      'basic': 5,
      'non_gmo': 10
    };
    
    if (this.quality_grade && qualityBonuses[this.quality_grade]) {
      score += qualityBonuses[this.quality_grade];
    }
    
    // Organic bonus
    if (this.is_organic) score += 10;
    
    // GMO-free bonus
    if (this.is_gmo_free) score += 5;
    
    // Essential ingredient bonus
    if (this.is_essential) score += 5;
    
    // Regulatory status penalty
    if (this.regulatory_status === 'restricted') score -= 10;
    if (this.regulatory_status === 'banned') score -= 50;
    
    return Math.max(0, Math.min(100, score));
  };

  // Class methods
  FeedComposition.findByFeedItem = function(feedItemId, options = {}) {
    return this.findAll({
      where: {
        feed_item_id: feedItemId,
        status: 'active'
      },
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.findByFeedPlan = function(feedPlanId, options = {}) {
    return this.findAll({
      where: {
        feed_plan_id: feedPlanId,
        status: 'active'
      },
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.findByIngredientType = function(ingredientType, options = {}) {
    return this.findAll({
      where: {
        ingredient_type: ingredientType,
        status: 'active'
      },
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.findEssentialIngredients = function(feedItemId = null, feedPlanId = null, options = {}) {
    const whereClause = {
      is_essential: true,
      status: 'active'
    };
    
    if (feedItemId) whereClause.feed_item_id = feedItemId;
    if (feedPlanId) whereClause.feed_plan_id = feedPlanId;
    
    return this.findAll({
      where: whereClause,
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.validateTotalPercentage = async function(feedItemId = null, feedPlanId = null, excludeId = null) {
    const whereClause = {
      status: 'active'
    };
    
    if (feedItemId) whereClause.feed_item_id = feedItemId;
    if (feedPlanId) whereClause.feed_plan_id = feedPlanId;
    if (excludeId) whereClause.id = { [sequelize.Sequelize.Op.ne]: excludeId };
    
    const compositions = await this.findAll({
      where: whereClause,
      attributes: ['percentage']
    });
    
    const totalPercentage = compositions.reduce((sum, comp) => sum + parseFloat(comp.percentage), 0);
    
    return {
      total: totalPercentage,
      isValid: totalPercentage <= 100,
      remaining: 100 - totalPercentage
    };
  };

  FeedComposition.calculateNutritionalProfile = async function(feedItemId = null, feedPlanId = null) {
    const whereClause = {
      status: 'active',
      nutritional_value: { [sequelize.Sequelize.Op.ne]: null }
    };
    
    if (feedItemId) whereClause.feed_item_id = feedItemId;
    if (feedPlanId) whereClause.feed_plan_id = feedPlanId;
    
    const compositions = await this.findAll({
      where: whereClause,
      attributes: ['percentage', 'nutritional_value']
    });
    
    const nutritionalProfile = {};
    
    compositions.forEach(comp => {
      const percentage = parseFloat(comp.percentage) / 100;
      
      if (comp.nutritional_value) {
        Object.keys(comp.nutritional_value).forEach(nutrient => {
          if (!nutritionalProfile[nutrient]) {
            nutritionalProfile[nutrient] = 0;
          }
          nutritionalProfile[nutrient] += percentage * comp.nutritional_value[nutrient];
        });
      }
    });
    
    return nutritionalProfile;
  };

  FeedComposition.findByQualityGrade = function(qualityGrade, options = {}) {
    return this.findAll({
      where: {
        quality_grade: qualityGrade,
        status: 'active'
      },
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.findOrganicIngredients = function(feedItemId = null, feedPlanId = null, options = {}) {
    const whereClause = {
      is_organic: true,
      status: 'active'
    };
    
    if (feedItemId) whereClause.feed_item_id = feedItemId;
    if (feedPlanId) whereClause.feed_plan_id = feedPlanId;
    
    return this.findAll({
      where: whereClause,
      order: [['percentage', 'DESC']],
      ...options
    });
  };

  FeedComposition.findByRegulatoryStatus = function(status, options = {}) {
    return this.findAll({
      where: {
        regulatory_status: status,
        status: 'active'
      },
      order: [['ingredient_name', 'ASC']],
      ...options
    });
  };

  FeedComposition.getIngredientUsageStats = function(ingredientName, options = {}) {
    return this.findAll({
      where: {
        ingredient_name: ingredientName,
        status: 'active'
      },
      attributes: [
        'ingredient_name',
        [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'usage_count'],
        [sequelize.Sequelize.fn('AVG', sequelize.Sequelize.col('percentage')), 'avg_percentage'],
        [sequelize.Sequelize.fn('MIN', sequelize.Sequelize.col('percentage')), 'min_percentage'],
        [sequelize.Sequelize.fn('MAX', sequelize.Sequelize.col('percentage')), 'max_percentage']
      ],
      group: ['ingredient_name'],
      ...options
    });
  };

  // Associations
  FeedComposition.associate = function(models) {
    // FeedComposition belongs to FeedItem
    FeedComposition.belongsTo(models.FeedItem, {
      foreignKey: 'feed_item_id',
      as: 'feed_item'
    });

    // FeedComposition belongs to FeedPlan
    FeedComposition.belongsTo(models.FeedPlan, {
      foreignKey: 'feed_plan_id',
      as: 'feed_plan'
    });
  };

  return FeedComposition;
};