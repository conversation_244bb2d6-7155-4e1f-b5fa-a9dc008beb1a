const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

/**
 * Modèle AlerteStock pour le système d'alertes intelligentes
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 *
 * Fonctionnalités:
 * - Alertes automatiques de stock
 * - Notifications intelligentes
 * - Gestion des seuils
 * - Historique des alertes
 */
module.exports = (sequelize, DataTypes) => {
  const { Op } = require('sequelize');
  const AlerteStock = sequelize.define('AlerteStock', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'eleveurs', // Ensure this model name is correct and defined elsewhere
        key: 'id'
      },
      comment: 'Référence vers l\'éleveur'
    },
    type_alerte: {
      type: DataTypes.ENUM(
        'stock_aliment_bas',
        'stock_medicament_bas',
        'stock_oeufs_eleve',
        'mortalite_elevee',
        'production_baisse',
        'temperature_anormale',
        'humidite_anormale',
        'vaccination_due',
        'visite_veterinaire_due',
        'nettoyage_du',
        'maintenance_equipement',
        'expiration_medicament',
        'surpopulation',
        'sous_population',
        'performance_baisse',
        'cout_eleve',
        'prix_marche_favorable',
        'meteo_defavorable',
        'panne_equipement',
        'securite_batiment'
      ),
      allowNull: false,
      comment: 'Type d\'alerte'
    },
    priorite: {
      type: DataTypes.ENUM('faible', 'normale', 'elevee', 'critique', 'urgente'),
      defaultValue: 'normale',
      comment: 'Niveau de priorité'
    },
    statut: {
      type: DataTypes.ENUM('active', 'vue', 'traitee', 'ignoree', 'resolue', 'expiree'),
      defaultValue: 'active',
      comment: 'Statut de l\'alerte'
    },
    titre: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: 'Titre de l\'alerte'
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Message détaillé de l\'alerte'
    },
    message_ar: {
      type: DataTypes.TEXT,
      comment: 'Message en arabe'
    },
    donnees_contexte: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Données contextuelles de l\'alerte'
    },
    seuil_declenche: {
      type: DataTypes.JSONB,
      defaultValue: {
        valeur_seuil: null,
        valeur_actuelle: null,
        unite: null,
        operateur: null // '<', '>', '<=', '>=', '==', '!='
      },
      comment: 'Informations sur le seuil qui a déclenché l\'alerte'
    },
    source_donnees: {
      type: DataTypes.JSONB,
      defaultValue: {
        table_source: null,
        id_enregistrement: null,
        champ_surveille: null,
        derniere_valeur: null
      },
      comment: 'Source des données pour l\'alerte'
    },
    date_creation: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: 'Date de création de l\'alerte'
    },
    date_resolution: {
      type: DataTypes.DATE,
      comment: 'Date de résolution de l\'alerte'
    },
    resolu_par_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Users', // Ensure this model name is correct
        key: 'id'
      },
      comment: 'Utilisateur ayant résolu l\'alerte'
    },
    commentaires_resolution: {
      type: DataTypes.TEXT,
      comment: 'Commentaires sur la résolution'
    },
    actions_suggerees: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Actions suggérées pour résoudre l\'alerte'
    },
    est_critique: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si l\'alerte est critique'
    },
    batiment_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'batiments', // Ensure this model name is correct
        key: 'id'
      },
      comment: 'Référence vers le bâtiment concerné (si applicable)'
    },
    lot_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'lots', // Ensure this model name is correct
        key: 'id'
      },
      comment: 'Référence vers le lot concerné (si applicable)'
    },
    equipement_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'equipements', // Ensure this model name is correct
        key: 'id'
      },
      comment: 'Référence vers l\'équipement concerné (si applicable)'
    },
    // Champs pour la gestion des notifications
    derniere_notification_envoyee: {
      type: DataTypes.DATE,
      comment: 'Date de la dernière notification envoyée pour cette alerte'
    },
    nombre_notifications_envoyees: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Nombre de notifications envoyées pour cette alerte'
    },
    // Champs pour l'analyse et le reporting
    impact_estime: {
      type: DataTypes.STRING,
      comment: 'Impact estimé de l\'alerte (ex: perte financière, risque sanitaire)'
    },
    cause_probable: {
      type: DataTypes.STRING,
      comment: 'Cause probable de l\'alerte'
    },
    // Champs pour l'intégration avec d'autres systèmes
    systeme_externe_id: {
      type: DataTypes.STRING,
      comment: 'ID de l\'alerte dans un système externe (si applicable)'
    },
    // Champs pour la personnalisation par l'utilisateur
    regles_personnalisees_appliquees: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Règles personnalisées qui ont contribué à cette alerte'
    },
    // Champs pour l'apprentissage automatique
    est_utilise_pour_apprentissage: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si cette alerte est utilisée pour l\'entraînement de modèles ML'
    },
    feedback_utilisateur: {
      type: DataTypes.TEXT,
      comment: 'Feedback de l\'utilisateur sur la pertinence de l\'alerte'
    }
  }, {
    sequelize,
    modelName: 'AlerteStock',
    tableName: 'alertes_stock',
    timestamps: true, // Sequelize gère createdAt et updatedAt
    paranoid: true, // Active la suppression logique (ajoute deletedAt)
    underscored: true, // Utilise snake_case pour les noms de colonnes et clés étrangères générés automatiquement
    indexes: [
      { fields: ['eleveur_id'] },
      { fields: ['type_alerte'] },
      { fields: ['statut'] },
      { fields: ['priorite'] },
      { fields: ['date_creation'] },
      { fields: ['est_critique'] }
    ],
    hooks: {
      beforeValidate: (alerte, options) => {
        // Logique de validation personnalisée avant la sauvegarde
        if (alerte.titre && alerte.titre.length > 200) {
          throw new Error('Le titre de l\'alerte ne doit pas dépasser 200 caractères.');
        }
      },
      afterCreate: (alerte, options) => {
        // Logique à exécuter après la création d'une alerte (ex: envoyer une notification)
        console.log(`Nouvelle alerte créée: ${alerte.titre} pour l\'éleveur ${alerte.eleveur_id}`);
        // Exemple: Envoyer une notification ici
        // notificationService.sendNotification(alerte);
      },
      afterUpdate: (alerte, options) => {
        // Logique à exécuter après la mise à jour d'une alerte
        console.log(`Alerte mise à jour: ${alerte.titre}`);
        if (alerte.changed('statut') && alerte.statut === 'resolue') {
          // Logique spécifique si l'alerte est résolue
          console.log(`Alerte ${alerte.id} marquée comme résolue.`);
          // Exemple: Archiver l'alerte ou notifier les parties concernées
        }
      },
      beforeDestroy: (alerte, options) => {
        // Logique à exécuter avant la suppression (logique ou physique) d'une alerte
        console.log(`Suppression de l\'alerte: ${alerte.titre}`);
      }
    },
    scopes: {
      actives: {
        where: { statut: 'active' }
      },
      critiques: {
        where: { priorite: 'critique' }
      },
      urgentes: {
        where: { priorite: 'urgente' }
      },
      nonResolues: {
        where: {
          statut: { [Op.notIn]: ['resolue', 'expiree'] }
        }
      },
      pourEleveur: (eleveurId) => ({
        where: { eleveur_id: eleveurId }
      })
    },
    comment: 'Table pour stocker les alertes de stock et autres notifications intelligentes pour les éleveurs.'
  });

  AlerteStock.associate = function(models) {
    // Définir les associations ici
    // Par exemple, si AlerteStock appartient à un Eleveur:
    AlerteStock.belongsTo(models.Eleveur, { // Assurez-vous que le modèle Eleveur est défini et importé
      foreignKey: 'eleveur_id',
      as: 'eleveur'
    });

    AlerteStock.belongsTo(models.User, { // Assurez-vous que le modèle User est défini et importé
      foreignKey: 'resolu_par_id',
      as: 'resoluPar'
    });

    // Si une alerte peut être liée à un bâtiment, lot, ou équipement spécifique
    // AlerteStock.belongsTo(models.Batiment, { // Modèle Batiment non défini - commenté temporairement
    //   foreignKey: 'batiment_id',
    //   as: 'batiment',
    //   required: false // Rendre cette association optionnelle
    // });

    // AlerteStock.belongsTo(models.Lot, { // Assurez-vous que le modèle Lot est défini
    //   foreignKey: 'lot_id',
    //   as: 'lot',
    //   required: false
    // });

    // AlerteStock.belongsTo(models.Equipement, { // Assurez-vous que le modèle Equipement est défini
    //   foreignKey: 'equipement_id',
    //   as: 'equipement',
    //   required: false
    // });
  };

  return AlerteStock;
};

// module.exports = AlerteStock; // This line should be removed or commented out
