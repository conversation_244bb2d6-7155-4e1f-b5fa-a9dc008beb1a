import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const useStockAlerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);

  const fetchAlerts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/stock-alerts');
      setAlerts(response.data);
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors de la récupération des alertes');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const response = await axios.get('/api/stock-alerts/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques:', err);
    }
  }, []);

  const addAlert = useCallback(async (alertData) => {
    try {
      const response = await axios.post('/api/stock-alerts', alertData);
      await fetchAlerts();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de l\'ajout de l\'alerte');
    }
  }, [fetchAlerts, fetchStats]);

  const updateAlert = useCallback(async (id, alertData) => {
    try {
      const response = await axios.put(`/api/stock-alerts/${id}`, alertData);
      await fetchAlerts();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la mise à jour de l\'alerte');
    }
  }, [fetchAlerts, fetchStats]);

  const deleteAlert = useCallback(async (id) => {
    try {
      await axios.delete(`/api/stock-alerts/${id}`);
      await fetchAlerts();
      await fetchStats();
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la suppression de l\'alerte');
    }
  }, [fetchAlerts, fetchStats]);

  const markAsResolved = useCallback(async (id, resolutionData) => {
    try {
      const response = await axios.post(`/api/stock-alerts/${id}/resolve`, resolutionData);
      await fetchAlerts();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la résolution de l\'alerte');
    }
  }, [fetchAlerts, fetchStats]);

  const getAlertSeverity = useCallback((alert) => {
    if (alert.type === 'rupture') return 'critique';
    if (alert.quantite_actuelle <= alert.seuil_critique) return 'haute';
    if (alert.quantite_actuelle <= alert.seuil_alerte) return 'moyenne';
    return 'basse';
  }, []);

  const calculateDaysUntilStockout = useCallback((alert) => {
    if (!alert.taux_consommation_quotidien || alert.taux_consommation_quotidien === 0) return null;
    return Math.floor(alert.quantite_actuelle / alert.taux_consommation_quotidien);
  }, []);

  const getRecommendedAction = useCallback((alert) => {
    const severity = getAlertSeverity(alert);
    const daysUntilStockout = calculateDaysUntilStockout(alert);

    if (severity === 'critique') {
      return 'Commander immédiatement';
    } else if (severity === 'haute') {
      return `Commander dans les ${Math.max(1, daysUntilStockout - 5)} jours`;
    } else if (severity === 'moyenne') {
      return 'Planifier une commande';
    }
    return 'Surveiller le niveau';
  }, [getAlertSeverity, calculateDaysUntilStockout]);

  useEffect(() => {
    fetchAlerts();
    fetchStats();
  }, [fetchAlerts, fetchStats]);

  return {
    alerts,
    loading,
    error,
    stats,
    fetchAlerts,
    fetchStats,
    addAlert,
    updateAlert,
    deleteAlert,
    markAsResolved,
    getAlertSeverity,
    calculateDaysUntilStockout,
    getRecommendedAction
  };
};

export default useStockAlerts;
