import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DateTimePicker } from '@mui/x-date-pickers';
import { fr } from 'date-fns/locale';

const VetConsultations = () => {
  const { t } = useTranslation();
  const [consultations, setConsultations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedConsultation, setSelectedConsultation] = useState(null);
  const [formData, setFormData] = useState({
    type: 'consultation',
    date_prevue: new Date(),
    statut: 'planifie',
    urgence: false,
    symptomes: '',
    diagnostic: '',
    traitement: '',
    vaccinations: '',
    cout: '',
    notes: ''
  });

  useEffect(() => {
    fetchConsultations();
  }, []);

  const fetchConsultations = async () => {
    try {
      const response = await axios.get('/api/suivi-veterinaire');
      setConsultations(response.data);
      setLoading(false);
    } catch (err) {
      setError(t('errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleOpenDialog = (consultation = null) => {
    if (consultation) {
      setSelectedConsultation(consultation);
      setFormData({
        type: consultation.type,
        date_prevue: new Date(consultation.date_prevue),
        statut: consultation.statut,
        urgence: consultation.urgence,
        symptomes: consultation.symptomes || '',
        diagnostic: consultation.diagnostic || '',
        traitement: consultation.traitement || '',
        vaccinations: consultation.vaccinations || '',
        cout: consultation.cout || '',
        notes: consultation.notes || ''
      });
    } else {
      setSelectedConsultation(null);
      setFormData({
        type: 'consultation',
        date_prevue: new Date(),
        statut: 'planifie',
        urgence: false,
        symptomes: '',
        diagnostic: '',
        traitement: '',
        vaccinations: '',
        cout: '',
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedConsultation(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (newDate) => {
    setFormData(prev => ({
      ...prev,
      date_prevue: newDate
    }));
  };

  const handleSubmit = async () => {
    try {
      if (selectedConsultation) {
        await axios.put(`/api/suivi-veterinaire/${selectedConsultation.id}`, formData);
      } else {
        await axios.post('/api/suivi-veterinaire', formData);
      }
      fetchConsultations();
      handleCloseDialog();
    } catch (err) {
      setError(t('errors.saveFailed'));
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('confirmations.deleteConsultation'))) {
      try {
        await axios.delete(`/api/suivi-veterinaire/${id}`);
        fetchConsultations();
      } catch (err) {
        setError(t('errors.deleteFailed'));
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'planifie': return 'info';
      case 'en_cours': return 'warning';
      case 'termine': return 'success';
      case 'annule': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'planifie': return <ScheduleIcon />;
      case 'en_cours': return <WarningIcon />;
      case 'termine': return <CheckCircleIcon />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          {t('vet.consultations')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('vet.addConsultation')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('vet.type')}</TableCell>
              <TableCell>{t('vet.date')}</TableCell>
              <TableCell>{t('vet.status')}</TableCell>
              <TableCell>{t('vet.urgency')}</TableCell>
              <TableCell>{t('vet.symptoms')}</TableCell>
              <TableCell>{t('vet.treatment')}</TableCell>
              <TableCell align="right">{t('vet.cost')}</TableCell>
              <TableCell align="center">{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {consultations.map((consultation) => (
              <TableRow key={consultation.id}>
                <TableCell>{t(`vet.types.${consultation.type}`)}</TableCell>
                <TableCell>
                  {new Date(consultation.date_prevue).toLocaleString()}
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(consultation.statut)}
                    label={t(`vet.status.${consultation.statut}`)}
                    color={getStatusColor(consultation.statut)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {consultation.urgence && (
                    <Chip
                      icon={<WarningIcon />}
                      label={t('vet.urgent')}
                      color="error"
                      size="small"
                    />
                  )}
                </TableCell>
                <TableCell>{consultation.symptomes}</TableCell>
                <TableCell>{consultation.traitement}</TableCell>
                <TableCell align="right">
                  {consultation.cout ? `${consultation.cout} DA` : '-'}
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    onClick={() => handleOpenDialog(consultation)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDelete(consultation.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedConsultation
            ? t('vet.editConsultation')
            : t('vet.addConsultation')}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('vet.type')}</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  label={t('vet.type')}
                >
                  <MenuItem value="consultation">{t('vet.types.consultation')}</MenuItem>
                  <MenuItem value="vaccination">{t('vet.types.vaccination')}</MenuItem>
                  <MenuItem value="traitement">{t('vet.types.traitement')}</MenuItem>
                  <MenuItem value="suivi">{t('vet.types.suivi')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
                <DateTimePicker
                  label={t('vet.date')}
                  value={formData.date_prevue}
                  onChange={handleDateChange}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('vet.status')}</InputLabel>
                <Select
                  name="statut"
                  value={formData.statut}
                  onChange={handleInputChange}
                  label={t('vet.status')}
                >
                  <MenuItem value="planifie">{t('vet.status.planifie')}</MenuItem>
                  <MenuItem value="en_cours">{t('vet.status.en_cours')}</MenuItem>
                  <MenuItem value="termine">{t('vet.status.termine')}</MenuItem>
                  <MenuItem value="annule">{t('vet.status.annule')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('vet.urgency')}</InputLabel>
                <Select
                  name="urgence"
                  value={formData.urgence}
                  onChange={handleInputChange}
                  label={t('vet.urgency')}
                >
                  <MenuItem value={false}>{t('common.no')}</MenuItem>
                  <MenuItem value={true}>{t('common.yes')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="symptomes"
                label={t('vet.symptoms')}
                multiline
                rows={2}
                value={formData.symptomes}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="diagnostic"
                label={t('vet.diagnosis')}
                multiline
                rows={2}
                value={formData.diagnostic}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="traitement"
                label={t('vet.treatment')}
                multiline
                rows={2}
                value={formData.traitement}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            {formData.type === 'vaccination' && (
              <Grid item xs={12}>
                <TextField
                  name="vaccinations"
                  label={t('vet.vaccines')}
                  multiline
                  rows={2}
                  value={formData.vaccinations}
                  onChange={handleInputChange}
                  fullWidth
                />
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <TextField
                name="cout"
                label={t('vet.cost')}
                type="number"
                value={formData.cout}
                onChange={handleInputChange}
                fullWidth
                InputProps={{ endAdornment: 'DA' }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="notes"
                label={t('common.notes')}
                multiline
                rows={3}
                value={formData.notes}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {selectedConsultation ? t('common.save') : t('common.add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VetConsultations;
