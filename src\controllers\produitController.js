const { Produit, User } = require('../models');
const { Op } = require('sequelize');

// Obtenir tous les produits d'un marchand
const getProduitsByMarchand = async (req, res) => {
  try {
    const { marchandId } = req.params;
    const { page = 1, limit = 10, categorie, statut, recherche } = req.query;
    
    const offset = (page - 1) * limit;
    
    // Construire les conditions de recherche
    const whereConditions = {
      marchand_id: marchandId
    };
    
    if (categorie) {
      whereConditions.categorie = categorie;
    }
    
    if (statut) {
      whereConditions.statut = statut;
    }
    
    if (recherche) {
      whereConditions[Op.or] = [
        {
          nom: {
            [Op.iLike]: `%${recherche}%`
          }
        },
        {
          description: {
            [Op.iLike]: `%${recherche}%`
          }
        },
        {
          reference_produit: {
            [Op.iLike]: `%${recherche}%`
          }
        }
      ];
    }
    
    const produits = await Produit.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        produits: produits.rows,
        pagination: {
          total: produits.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(produits.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des produits:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des produits',
      error: error.message
    });
  }
};

// Créer un nouveau produit
const createProduit = async (req, res) => {
  try {
    const {
      marchand_id,
      nom,
      description,
      categorie,
      sous_categorie,
      marque,
      prix_unitaire,
      prix_gros,
      quantite_minimum_gros,
      unite_mesure,
      stock_disponible,
      stock_minimum,
      stock_maximum,
      images,
      specifications,
      date_expiration,
      numero_lot,
      fournisseur,
      conditions_stockage,
      instructions_utilisation,
      poids,
      dimensions,
      tags
    } = req.body;
    
    // Validation des données requises
    if (!marchand_id || !nom || !categorie || !prix_unitaire || !unite_mesure) {
      return res.status(400).json({
        success: false,
        message: 'Les champs marchand_id, nom, categorie, prix_unitaire et unite_mesure sont requis'
      });
    }
    
    const nouveauProduit = await Produit.create({
      marchand_id,
      nom,
      description,
      categorie,
      sous_categorie,
      marque,
      prix_unitaire,
      prix_gros,
      quantite_minimum_gros,
      unite_mesure,
      stock_disponible: stock_disponible || 0,
      stock_minimum: stock_minimum || 0,
      stock_maximum,
      images,
      specifications,
      date_expiration,
      numero_lot,
      fournisseur,
      conditions_stockage,
      instructions_utilisation,
      poids,
      dimensions,
      tags
    });
    
    // Récupérer le produit créé avec les associations
    const produitComplet = await Produit.findByPk(nouveauProduit.id, {
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email']
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      message: 'Produit créé avec succès',
      data: produitComplet
    });
  } catch (error) {
    console.error('Erreur lors de la création du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du produit',
      error: error.message
    });
  }
};

// Mettre à jour un produit
const updateProduit = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const [updatedRowsCount] = await Produit.update(updateData, {
      where: { id }
    });
    
    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }
    
    // Récupérer le produit mis à jour
    const produitMisAJour = await Produit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email']
        }
      ]
    });
    
    res.json({
      success: true,
      message: 'Produit mis à jour avec succès',
      data: produitMisAJour
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du produit',
      error: error.message
    });
  }
};

// Supprimer un produit
const deleteProduit = async (req, res) => {
  try {
    const { id } = req.params;
    
    const deletedRowsCount = await Produit.destroy({
      where: { id }
    });
    
    if (deletedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }
    
    res.json({
      success: true,
      message: 'Produit supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du produit',
      error: error.message
    });
  }
};

// Obtenir les statistiques de produits pour un marchand
const getStatistiquesProduits = async (req, res) => {
  try {
    const { marchandId } = req.params;
    
    const stats = await Produit.getStatsByMarchand(marchandId);
    
    // Obtenir les produits en rupture de stock
    const produitsEnRupture = await Produit.getProduitsEnRupture(marchandId);
    
    // Obtenir les produits populaires
    const produitsPopulaires = await Produit.getProduitsPopulaires(marchandId, 5);
    
    // Calculer la valeur totale du stock
    const valeurStock = await Produit.sum('prix_unitaire', {
      where: {
        marchand_id: marchandId,
        statut: 'actif'
      }
    });
    
    res.json({
      success: true,
      data: {
        ...stats,
        produits_rupture_details: produitsEnRupture.length,
        produits_populaires: produitsPopulaires,
        valeur_stock_totale: valeurStock || 0,
        alertes: {
          rupture_stock: produitsEnRupture.slice(0, 5)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
};

// Obtenir un produit par ID
const getProduitById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const produit = await Produit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email', 'telephone']
        }
      ]
    });
    
    if (!produit) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }
    
    res.json({
      success: true,
      data: produit
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du produit',
      error: error.message
    });
  }
};

// Rechercher des produits
const rechercherProduits = async (req, res) => {
  try {
    const {
      terme,
      categorie,
      prixMin,
      prixMax,
      marchandId,
      enStock,
      promotion,
      page = 1,
      limit = 20
    } = req.query;
    
    const criteres = {
      terme,
      categorie,
      prixMin: prixMin ? parseFloat(prixMin) : undefined,
      prixMax: prixMax ? parseFloat(prixMax) : undefined,
      marchandId: marchandId ? parseInt(marchandId) : undefined,
      enStock: enStock === 'true',
      promotion: promotion === 'true',
      page: parseInt(page),
      limit: parseInt(limit)
    };
    
    const resultats = await Produit.rechercherProduits(criteres);
    
    res.json({
      success: true,
      data: {
        produits: resultats.rows,
        pagination: {
          total: resultats.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(resultats.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la recherche de produits:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la recherche de produits',
      error: error.message
    });
  }
};

// Mettre à jour le stock d'un produit
const updateStock = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantite, operation } = req.body; // operation: 'add' ou 'subtract'
    
    if (!quantite || !operation) {
      return res.status(400).json({
        success: false,
        message: 'Les champs quantite et operation sont requis'
      });
    }
    
    const produit = await Produit.findByPk(id);
    
    if (!produit) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }
    
    let nouveauStock;
    if (operation === 'add') {
      nouveauStock = produit.stock_disponible + parseInt(quantite);
    } else if (operation === 'subtract') {
      nouveauStock = Math.max(0, produit.stock_disponible - parseInt(quantite));
    } else {
      return res.status(400).json({
        success: false,
        message: 'Opération invalide. Utilisez "add" ou "subtract"'
      });
    }
    
    await produit.update({ stock_disponible: nouveauStock });
    
    const produitMisAJour = await Produit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email']
        }
      ]
    });
    
    res.json({
      success: true,
      message: 'Stock mis à jour avec succès',
      data: produitMisAJour
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du stock',
      error: error.message
    });
  }
};

// Activer/désactiver une promotion
const togglePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      prix_promotion,
      date_debut_promotion,
      date_fin_promotion,
      activer
    } = req.body;
    
    const produit = await Produit.findByPk(id);
    
    if (!produit) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }
    
    const updateData = {
      promotion_active: activer
    };
    
    if (activer) {
      if (!prix_promotion) {
        return res.status(400).json({
          success: false,
          message: 'Le prix de promotion est requis pour activer une promotion'
        });
      }
      
      updateData.prix_promotion = prix_promotion;
      updateData.date_debut_promotion = date_debut_promotion || new Date();
      updateData.date_fin_promotion = date_fin_promotion;
    } else {
      updateData.prix_promotion = null;
      updateData.date_debut_promotion = null;
      updateData.date_fin_promotion = null;
    }
    
    await produit.update(updateData);
    
    const produitMisAJour = await Produit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'marchand',
          attributes: ['id', 'nom', 'email']
        }
      ]
    });
    
    res.json({
      success: true,
      message: `Promotion ${activer ? 'activée' : 'désactivée'} avec succès`,
      data: produitMisAJour
    });
  } catch (error) {
    console.error('Erreur lors de la gestion de la promotion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la gestion de la promotion',
      error: error.message
    });
  }
};

module.exports = {
  getProduitsByMarchand,
  createProduit,
  updateProduit,
  deleteProduit,
  getStatistiquesProduits,
  getProduitById,
  rechercherProduits,
  updateStock,
  togglePromotion
};