import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import '../../models/chat/chat_thread.dart';
import '../../models/chat/chat_message.dart';
import '../../models/market/market_listing.dart';
import '../../providers/chat_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/chat/message_bubble.dart';
import '../../widgets/chat/typing_indicator.dart';
import '../../widgets/chat/offer_card.dart';
import '../../widgets/chat/location_message.dart';
import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../l10n/app_localizations.dart';
import '../market/listing_detail_screen.dart';
import '../market/user_profile_screen.dart';

class ConversationScreen extends StatefulWidget {
  final ChatThread thread;
  
  const ConversationScreen({
    Key? key,
    required this.thread,
  }) : super(key: key);

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen>
    with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _messageFocusNode = FocusNode();
  final ImagePicker _imagePicker = ImagePicker();
  
  // UI state
  bool _isComposing = false;
  bool _showAttachmentOptions = false;
  List<File> _selectedImages = [];
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController.addListener(_onScroll);
    _messageFocusNode.addListener(_onFocusChanged);
    _loadInitialData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    _messageController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _markMessagesAsRead();
    }
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<ChatProvider>();
      provider.setSelectedThread(widget.thread.id);
      provider.loadMessages(widget.thread.id);
      _markMessagesAsRead();
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final provider = context.read<ChatProvider>();
      if (!provider.isLoadingMoreMessages && provider.hasMoreMessages) {
        provider.loadMessages(widget.thread.id, loadMore: true);
      }
    }
  }

  void _onFocusChanged() {
    if (_messageFocusNode.hasFocus) {
      _setTypingIndicator(true);
    } else {
      _setTypingIndicator(false);
    }
  }

  void _markMessagesAsRead() {
    context.read<ChatProvider>().markMessagesAsRead(widget.thread.id);
  }

  void _setTypingIndicator(bool isTyping) {
    context.read<ChatProvider>().setTypingIndicator(
      widget.thread.id,
      isTyping,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: AppColors.chatBackground,
      appBar: _buildAppBar(l10n),
      body: Column(
        children: [
          if (widget.thread.relatedListing != null)
            _buildListingHeader(widget.thread.relatedListing!, l10n),
          Expanded(
            child: Consumer<ChatProvider>(
              builder: (context, provider, child) {
                return _buildMessagesList(provider, l10n);
              },
            ),
          ),
          _buildTypingIndicator(),
          _buildMessageInput(l10n),
          if (_showAttachmentOptions) _buildAttachmentOptions(l10n),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations l10n) {
    final otherUser = widget.thread.otherParticipant;
    
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 1,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back),
      ),
      title: GestureDetector(
        onTap: () => _viewUserProfile(otherUser!),
        child: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.white.withOpacity(0.2),
              backgroundImage: otherUser?.profileImage != null
                  ? NetworkImage(otherUser!.profileImage!)
                  : null,
              child: otherUser?.profileImage == null
                  ? Text(
                      otherUser?.name.substring(0, 1).toUpperCase() ?? '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    otherUser?.name ?? l10n.unknownUser,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Consumer<ChatProvider>(
                    builder: (context, provider, child) {
                      final typingUsers = provider.getTypingUsers(widget.thread.id);
                      if (typingUsers.isNotEmpty) {
                        return Text(
                          l10n.typing,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.8),
                            fontStyle: FontStyle.italic,
                          ),
                        );
                      }
                      
                      return Text(
                        _getLastSeenText(otherUser?.lastSeen),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        if (otherUser?.phoneNumber != null)
          IconButton(
            onPressed: () => _makePhoneCall(otherUser!.phoneNumber!),
            icon: const Icon(Icons.phone),
          ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'view_profile',
              child: Row(
                children: [
                  const Icon(Icons.person),
                  const SizedBox(width: 8),
                  Text(l10n.viewProfile),
                ],
              ),
            ),
            if (widget.thread.relatedListing != null)
              PopupMenuItem(
                value: 'view_listing',
                child: Row(
                  children: [
                    const Icon(Icons.shopping_bag),
                    const SizedBox(width: 8),
                    Text(l10n.viewListing),
                  ],
                ),
              ),
            PopupMenuItem(
              value: 'share_contact',
              child: Row(
                children: [
                  const Icon(Icons.share),
                  const SizedBox(width: 8),
                  Text(l10n.shareContact),
                ],
              ),
            ),
            const PopupMenuDivider(),
            PopupMenuItem(
              value: 'block_user',
              child: Row(
                children: [
                  const Icon(Icons.block, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(l10n.blockUser, style: const TextStyle(color: Colors.red)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'report',
              child: Row(
                children: [
                  const Icon(Icons.report, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(l10n.reportUser, style: const TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildListingHeader(MarketListing listing, AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _viewListing(listing),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  listing.images.isNotEmpty ? listing.images.first : '',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[200],
                    child: const Icon(Icons.image_not_supported),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      listing.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${listing.price.toStringAsFixed(0)} ${l10n.currency}',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${listing.quantity} ${listing.unit}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessagesList(ChatProvider provider, AppLocalizations l10n) {
    if (provider.isLoadingMessages && provider.currentMessages.isEmpty) {
      return const LoadingWidget();
    }

    final messages = provider.currentMessages;
    
    if (messages.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.chat_bubble_outline,
        title: l10n.noMessagesYet,
        description: l10n.startConversationNow,
      );
    }

    return ListView.builder(
      controller: _scrollController,
      reverse: true,
      padding: const EdgeInsets.all(16),
      itemCount: messages.length + (provider.isLoadingMoreMessages ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= messages.length) {
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }
        
        final message = messages[index];
        final isNextMessageFromSameUser = index < messages.length - 1 &&
            messages[index + 1].senderId == message.senderId;
        final isPreviousMessageFromSameUser = index > 0 &&
            messages[index - 1].senderId == message.senderId;
        
        return Padding(
          padding: EdgeInsets.only(
            bottom: isNextMessageFromSameUser ? 2 : 8,
            top: isPreviousMessageFromSameUser ? 2 : 8,
          ),
          child: _buildMessageWidget(message, l10n),
        );
      },
    );
  }

  Widget _buildMessageWidget(ChatMessage message, AppLocalizations l10n) {
    switch (message.type) {
      case MessageType.text:
        return MessageBubble(
          message: message,
          isCurrentUser: message.senderId == context.read<AuthProvider>().currentUser?.id,
        );
      case MessageType.image:
        return MessageBubble(
          message: message,
          isCurrentUser: message.senderId == context.read<AuthProvider>().currentUser?.id,
        );
      case MessageType.offer:
        return OfferCard(
          message: message,
          isCurrentUser: message.senderId == context.read<AuthProvider>().currentUser?.id,
          onAccept: () => _handleOfferResponse(message, true),
          onDecline: () => _handleOfferResponse(message, false),
          onCounter: () => _showCounterOfferDialog(message),
        );
      case MessageType.location:
        return LocationMessage(
          message: message,
          isCurrentUser: message.senderId == context.read<AuthProvider>().currentUser?.id,
          onViewMap: () => _viewLocationOnMap(message.location!),
        );
      default:
        return MessageBubble(
          message: message,
          isCurrentUser: message.senderId == context.read<AuthProvider>().currentUser?.id,
        );
    }
  }

  Widget _buildTypingIndicator() {
    return Consumer<ChatProvider>(
      builder: (context, provider, child) {
        final typingUsers = provider.getTypingUsers(widget.thread.id);
        if (typingUsers.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TypingIndicator(
            users: typingUsers,
          ),
        );
      },
    );
  }

  Widget _buildMessageInput(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            if (_selectedImages.isNotEmpty) _buildSelectedImages(),
            Row(
              children: [
                IconButton(
                  onPressed: _toggleAttachmentOptions,
                  icon: Icon(
                    _showAttachmentOptions ? Icons.close : Icons.add,
                    color: AppColors.primary,
                  ),
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    focusNode: _messageFocusNode,
                    decoration: InputDecoration(
                      hintText: l10n.typeMessage,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: AppColors.primary),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    maxLines: null,
                    textCapitalization: TextCapitalization.sentences,
                    onChanged: _onMessageChanged,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: _isComposing ? AppColors.primary : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _isComposing ? _sendMessage : null,
                    icon: Icon(
                      Icons.send,
                      color: _isComposing ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedImages() {
    return Container(
      height: 80,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          final image = _selectedImages[index];
          return Container(
            width: 80,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    image,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeSelectedImage(index),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAttachmentOptions(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildAttachmentOption(
            icon: Icons.camera_alt,
            label: l10n.camera,
            onTap: () => _pickImage(ImageSource.camera),
          ),
          _buildAttachmentOption(
            icon: Icons.photo_library,
            label: l10n.gallery,
            onTap: () => _pickImage(ImageSource.gallery),
          ),
          _buildAttachmentOption(
            icon: Icons.location_on,
            label: l10n.location,
            onTap: _shareLocation,
          ),
          _buildAttachmentOption(
            icon: Icons.local_offer,
            label: l10n.makeOffer,
            onTap: _showOfferDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers
  
  void _onMessageChanged(String text) {
    final wasComposing = _isComposing;
    setState(() {
      _isComposing = text.trim().isNotEmpty || _selectedImages.isNotEmpty;
    });
    
    if (_isComposing != wasComposing) {
      _setTypingIndicator(_isComposing);
    }
  }

  void _toggleAttachmentOptions() {
    setState(() {
      _showAttachmentOptions = !_showAttachmentOptions;
    });
  }

  Future<void> _sendMessage() async {
    if (!_isComposing) return;
    
    final content = _messageController.text.trim();
    final images = List<File>.from(_selectedImages);
    
    // Clear input
    _messageController.clear();
    setState(() {
      _isComposing = false;
      _selectedImages.clear();
      _showAttachmentOptions = false;
    });
    
    _setTypingIndicator(false);
    
    try {
      if (images.isNotEmpty) {
        // Send image message
        await context.read<ChatProvider>().sendImageMessage(
          widget.thread.id,
          images,
          content.isNotEmpty ? content : null,
        );
      } else if (content.isNotEmpty) {
        // Send text message
        await context.read<ChatProvider>().sendTextMessage(
          widget.thread.id,
          content,
        );
      }
      
      // Scroll to bottom
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failedToSendMessage),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
      
      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
          _isComposing = true;
          _showAttachmentOptions = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failedToPickImage),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _removeSelectedImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
      _isComposing = _messageController.text.trim().isNotEmpty || _selectedImages.isNotEmpty;
    });
  }

  Future<void> _shareLocation() async {
    setState(() {
      _showAttachmentOptions = false;
    });
    
    try {
      await context.read<ChatProvider>().sendLocationMessage(
        widget.thread.id,
        // This would typically get current location
        latitude: 36.7538,
        longitude: 3.0588,
        address: 'Algiers, Algeria',
      );
      
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failedToShareLocation),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showOfferDialog() {
    final l10n = AppLocalizations.of(context)!;
    final priceController = TextEditingController();
    final quantityController = TextEditingController();
    final messageController = TextEditingController();
    
    setState(() {
      _showAttachmentOptions = false;
    });
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.makeOffer),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: priceController,
              decoration: InputDecoration(
                labelText: l10n.offerPrice,
                suffixText: l10n.currency,
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              decoration: InputDecoration(
                labelText: l10n.quantity,
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              decoration: InputDecoration(
                labelText: l10n.message,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              final price = double.tryParse(priceController.text);
              final quantity = int.tryParse(quantityController.text);
              
              if (price != null && quantity != null) {
                try {
                  await context.read<ChatProvider>().sendOfferMessage(
                    widget.thread.id,
                    price,
                    quantity,
                    messageController.text.trim(),
                  );
                  
                  _scrollToBottom();
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(l10n.failedToSendOffer),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text(l10n.sendOffer),
          ),
        ],
      ),
    );
  }

  void _showCounterOfferDialog(ChatMessage originalOffer) {
    final l10n = AppLocalizations.of(context)!;
    final priceController = TextEditingController(
      text: originalOffer.offerPrice?.toString() ?? '',
    );
    final quantityController = TextEditingController(
      text: originalOffer.offerQuantity?.toString() ?? '',
    );
    final messageController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.counterOffer),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: priceController,
              decoration: InputDecoration(
                labelText: l10n.counterPrice,
                suffixText: l10n.currency,
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              decoration: InputDecoration(
                labelText: l10n.quantity,
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              decoration: InputDecoration(
                labelText: l10n.message,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              final price = double.tryParse(priceController.text);
              final quantity = int.tryParse(quantityController.text);
              
              if (price != null && quantity != null) {
                try {
                  await context.read<ChatProvider>().sendOfferMessage(
                    widget.thread.id,
                    price,
                    quantity,
                    messageController.text.trim(),
                  );
                  
                  _scrollToBottom();
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(l10n.failedToSendOffer),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text(l10n.sendCounter),
          ),
        ],
      ),
    );
  }

  Future<void> _handleOfferResponse(ChatMessage offer, bool accept) async {
    try {
      await context.read<ChatProvider>().respondToOffer(
        widget.thread.id,
        offer.id,
        accept,
      );
      
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(accept ? l10n.offerAccepted : l10n.offerDeclined),
          backgroundColor: accept ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failedToRespondToOffer),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'view_profile':
        _viewUserProfile(widget.thread.otherParticipant!);
        break;
      case 'view_listing':
        if (widget.thread.relatedListing != null) {
          _viewListing(widget.thread.relatedListing!);
        }
        break;
      case 'share_contact':
        _shareContact();
        break;
      case 'block_user':
        _confirmBlockUser();
        break;
      case 'report':
        _reportUser();
        break;
    }
  }

  void _viewUserProfile(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(user: user),
      ),
    );
  }

  void _viewListing(MarketListing listing) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ListingDetailScreen(listing: listing),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.cannotMakeCall),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareContact() {
    final otherUser = widget.thread.otherParticipant;
    if (otherUser != null) {
      Share.share(
        'Contact: ${otherUser.name}\nPhone: ${otherUser.phoneNumber ?? "N/A"}',
        subject: 'Contact Information',
      );
    }
  }

  void _confirmBlockUser() {
    final l10n = AppLocalizations.of(context)!;
    final otherUser = widget.thread.otherParticipant;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.blockUser),
        content: Text(l10n.blockUserConfirmation(otherUser?.name ?? '')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<ChatProvider>().blockUser(
                widget.thread.id,
                otherUser!.id,
              );
              Navigator.pop(context); // Exit conversation
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.block),
          ),
        ],
      ),
    );
  }

  void _reportUser() {
    final l10n = AppLocalizations.of(context)!;
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.reportUser),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(l10n.reportUserDescription),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                hintText: l10n.reportReason,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<ChatProvider>().reportContent(
                widget.thread.id,
                'inappropriate_user',
                reasonController.text.trim(),
              );
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.reportSubmitted)),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.report),
          ),
        ],
      ),
    );
  }

  Future<void> _viewLocationOnMap(Location location) async {
    final uri = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=${location.latitude},${location.longitude}',
    );
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.cannotOpenMap),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  String _getLastSeenText(DateTime? lastSeen) {
    final l10n = AppLocalizations.of(context)!;
    
    if (lastSeen == null) {
      return l10n.lastSeenUnknown;
    }
    
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return l10n.online;
    } else if (difference.inMinutes < 60) {
      return l10n.lastSeenMinutesAgo(difference.inMinutes);
    } else if (difference.inHours < 24) {
      return l10n.lastSeenHoursAgo(difference.inHours);
    } else {
      return l10n.lastSeenDaysAgo(difference.inDays);
    }
  }
}