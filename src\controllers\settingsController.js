const db = require('../models');
const { sequelize } = db;

// SMTP Configuration
exports.getSmtpConfig = async (req, res) => {
  try {
    // Log available models for debugging
    console.log('Available models:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));

    // Use raw query as a fallback if model method fails
    const [results] = await sequelize.query('SELECT * FROM smtp_configurations LIMIT 1');

    if (!results || results.length === 0) {
      return res.status(404).json({ message: 'SMTP configuration not found.' });
    }

    res.json(results[0]);
  } catch (error) {
    console.error('Error in getSmtpConfig:', error);
    res.status(500).json({ message: 'Error fetching SMTP configuration.', error: error.message });
  }
};

exports.updateSmtpConfig = async (req, res) => {
  try {
    // Use raw query as a fallback if model method fails
    const [results] = await sequelize.query('SELECT * FROM smtp_configurations LIMIT 1');

    if (results && results.length > 0) {
      // Update existing configuration
      const { id, ...updateData } = req.body;
      await sequelize.query(
        'UPDATE smtp_configurations SET ' +
        Object.keys(updateData).map(key => `"${key}" = :${key}`).join(', ') +
        ' WHERE id = :id',
        {
          replacements: { ...updateData, id: results[0].id },
          type: sequelize.QueryTypes.UPDATE
        }
      );

      // Fetch updated record
      const [updated] = await sequelize.query('SELECT * FROM smtp_configurations WHERE id = :id', {
        replacements: { id: results[0].id },
        type: sequelize.QueryTypes.SELECT
      });

      res.json({ message: 'SMTP configuration updated successfully.', config: updated });
    } else {
      // Create new configuration
      const columns = Object.keys(req.body).map(key => `"${key}"`).join(', ');
      const values = Object.keys(req.body).map(key => `:${key}`).join(', ');

      await sequelize.query(
        `INSERT INTO smtp_configurations (${columns}, "createdAt", "updatedAt")
         VALUES (${values}, NOW(), NOW())`,
        {
          replacements: req.body,
          type: sequelize.QueryTypes.INSERT
        }
      );

      // Fetch new record
      const [created] = await sequelize.query('SELECT * FROM smtp_configurations ORDER BY id DESC LIMIT 1');

      res.json({ message: 'SMTP configuration created successfully.', config: created[0] });
    }
  } catch (error) {
    console.error('Error in updateSmtpConfig:', error);
    res.status(500).json({ message: 'Error updating SMTP configuration.', error: error.message });
  }
};

// Test SMTP Configuration
exports.testSmtpConfig = async (req, res) => {
  try {
    console.log('[settingsController] Testing SMTP configuration');

    const nodemailer = require('nodemailer');

    // Use the provided settings or fetch from database
    let smtpConfig;

    if (req.body && Object.keys(req.body).length > 0) {
      // Use request body if provided
      smtpConfig = req.body;
    } else {
      // Otherwise fetch from database using raw query
      const [results] = await sequelize.query('SELECT * FROM smtp_configurations LIMIT 1');
      if (!results || results.length === 0) {
        return res.status(404).json({ message: 'SMTP configuration not found.' });
      }
      smtpConfig = results[0];
    }

    // Create a test account if we're not using real credentials
    const testAccount = !req.body && (!smtpConfig.host || !smtpConfig.user || !smtpConfig.pass)
      ? await nodemailer.createTestAccount()
      : null;

    // Configure transporter with provided settings or test settings
    const transporter = nodemailer.createTransport({
      host: smtpConfig.host || testAccount?.smtp.host,
      port: smtpConfig.port || testAccount?.smtp.port,
      secure: smtpConfig.secure || testAccount?.smtp.secure,
      auth: {
        user: smtpConfig.user || testAccount?.user,
        pass: smtpConfig.pass || testAccount?.pass,
      },
    });

    // Setup email data
    const mailOptions = {
      from: `"${smtpConfig.fromName || 'Poultray DZ Test'}" <${smtpConfig.fromEmail || '<EMAIL>'}>`,
      to: smtpConfig.testEmailRecipient || '<EMAIL>',
      subject: 'SMTP Configuration Test',
      text: 'If you received this email, your SMTP configuration is working correctly.',
      html: '<p>If you received this email, your SMTP configuration is working correctly.</p>',
    };

    // Send mail
    const info = await transporter.sendMail(mailOptions);

    console.log('[settingsController] Test email sent:', info.messageId);

    // Return success response
    res.json({
      message: 'Test email sent successfully',
      messageId: info.messageId,
      previewUrl: nodemailer.getTestMessageUrl(info)
    });
  } catch (error) {
    console.error('[settingsController] Error testing SMTP configuration:', error);
    res.status(500).json({
      message: 'Error testing SMTP configuration',
      error: error.message
    });
  }
};

// Security Settings
exports.getSecuritySettings = async (req, res) => {
  try {
    // Use raw query to fetch security settings
    const [results] = await sequelize.query('SELECT * FROM security_settings LIMIT 1');

    if (!results || results.length === 0) {
      return res.status(404).json({ message: 'Security settings not found.' });
    }

    res.json(results[0]);
  } catch (error) {
    console.error('Error in getSecuritySettings:', error);
    res.status(500).json({ message: 'Error fetching security settings.', error: error.message });
  }
};

exports.updateSecuritySettings = async (req, res) => {
  try {
    // Use raw query to fetch security settings
    const [results] = await sequelize.query('SELECT * FROM security_settings LIMIT 1');

    if (results && results.length > 0) {
      // Update existing settings
      const { id, ...updateData } = req.body;
      await sequelize.query(
        'UPDATE security_settings SET ' +
        Object.keys(updateData).map(key => `"${key}" = :${key}`).join(', ') +
        ' WHERE id = :id',
        {
          replacements: { ...updateData, id: results[0].id },
          type: sequelize.QueryTypes.UPDATE
        }
      );

      // Fetch updated record
      const [updated] = await sequelize.query('SELECT * FROM security_settings WHERE id = :id', {
        replacements: { id: results[0].id },
        type: sequelize.QueryTypes.SELECT
      });

      res.json({ message: 'Security settings updated successfully.', settings: updated });
    } else {
      // Create new settings
      const columns = Object.keys(req.body).map(key => `"${key}"`).join(', ');
      const values = Object.keys(req.body).map(key => `:${key}`).join(', ');

      await sequelize.query(
        `INSERT INTO security_settings (${columns}, "createdAt", "updatedAt")
         VALUES (${values}, NOW(), NOW())`,
        {
          replacements: req.body,
          type: sequelize.QueryTypes.INSERT
        }
      );

      // Fetch new record
      const [created] = await sequelize.query('SELECT * FROM security_settings ORDER BY id DESC LIMIT 1');

      res.json({ message: 'Security settings created successfully.', settings: created[0] });
    }
  } catch (error) {
    console.error('Error in updateSecuritySettings:', error);
    res.status(500).json({ message: 'Error updating security settings.', error: error.message });
  }
};

// API Key Configuration
exports.getApiConfig = async (req, res) => {
  try {
    console.log('[settingsController] Getting API configurations');
    const db = require('../models');
    const ApiConfig = db.ApiConfig; // Ensure we're using the initialized model from db

    if (!ApiConfig) {
      console.error('[settingsController] ApiConfig model not found');
      return res.status(500).json({ message: 'API Configuration model not initialized' });
    }

    const config = await ApiConfig.findAll();

    // Transform the response to a more frontend-friendly format
    const formattedConfig = {};
    config.forEach(entry => {
      formattedConfig[entry.serviceName] = entry.apiKey;
    });

    res.json(formattedConfig);
  } catch (error) {
    console.error('[settingsController] Error in getApiConfig:', error);
    res.status(500).json({ message: 'Error fetching API configuration.', error: error.message });
  }
};

exports.createApiConfig = async (req, res) => {
  try {
    console.log('[settingsController] Creating/updating API configurations', req.body);
    const { apiKeys } = req.body;

    if (!apiKeys || typeof apiKeys !== 'object') {
      console.error('[settingsController] Invalid apiKeys format:', apiKeys);
      return res.status(400).json({ message: 'API keys should be provided as an object' });
    }

    const db = require('../models');
    const ApiConfig = db.ApiConfig;

    if (!ApiConfig) {
      console.error('[settingsController] ApiConfig model not found');
      return res.status(500).json({ message: 'API Configuration model not initialized' });
    }

    // apiKeys is an object with service names as keys and API keys as values
    const results = [];

    try {
      // Use a transaction to ensure data integrity
      await db.sequelize.transaction(async (t) => {
        for (const [serviceName, apiKey] of Object.entries(apiKeys)) {
          console.log(`[settingsController] Processing service: ${serviceName}`);

          try {
            // Find the configuration
            let config = await ApiConfig.findOne({
              where: { serviceName },
              transaction: t
            });

            if (config) {
              // Update existing configuration
              await config.update({
                apiKey: apiKey || ''
              }, { transaction: t });

              console.log(`[settingsController] Updated config for ${serviceName}`);
              results.push({
                serviceName,
                updated: true
              });
            } else {
              // Create new configuration
              config = await ApiConfig.create({
                serviceName,
                apiKey: apiKey || '',
                apiSecret: null
              }, { transaction: t });

              console.log(`[settingsController] Created config for ${serviceName}`);
              results.push({
                serviceName,
                created: true
              });
            }
          } catch (serviceError) {
            console.error(`[settingsController] Error processing service ${serviceName}:`, serviceError);
            // Continue with other services even if one fails
            results.push({
              serviceName,
              error: serviceError.message
            });
          }
        }
      });

      console.log('[settingsController] All services processed successfully');
      res.status(200).json({
        message: 'API configuration(s) saved successfully.',
        results
      });
    } catch (txError) {
      console.error('[settingsController] Transaction error:', txError);
      throw txError; // Re-throw to be caught by the outer catch
    }
  } catch (error) {
    console.error('[settingsController] Error in createApiConfig:', error);
    res.status(500).json({
      message: 'Error saving API configuration.',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

exports.updateApiConfig = async (req, res) => {
  try {
    const { id } = req.params;
    const { serviceName, apiKey, apiSecret } = req.body;
    const config = await ApiConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({ message: 'API configuration not found.' });
    }
    await config.update({ serviceName, apiKey, apiSecret });
    res.json({ message: 'API configuration updated successfully.', config });
  } catch (error) {
    res.status(500).json({ message: 'Error updating API configuration.', error: error.message });
  }
};

exports.deleteApiConfig = async (req, res) => {
  try {
    const { id } = req.params;
    const config = await ApiConfig.findByPk(id);
    if (!config) {
      return res.status(404).json({ message: 'API configuration not found.' });
    }
    await config.destroy();
    res.json({ message: 'API configuration deleted successfully.' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting API configuration.', error: error.message });
  }
};

// General Settings
exports.getGeneralSettings = async (req, res) => {
  try {
    console.log('[settingsController] Getting general settings');

    // Use raw query to fetch general settings
    const [results] = await sequelize.query('SELECT * FROM general_config WHERE id = 1');

    if (!results || results.length === 0) {
      // If no settings exist, create default settings
      console.log('[settingsController] Creating default general settings');

      const defaults = {
        siteName: 'Poultray DZ',
        defaultLanguage: 'fr',
        availableLanguages: JSON.stringify(['fr', 'ar', 'en']),
        primaryColor: '#2c5530',
        secondaryColor: '#e7eae2',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        timezone: 'Africa/Algiers',
        maintenanceMode: false
      };

      // Create new settings
      const columns = Object.keys(defaults).map(key => `"${key}"`).join(', ');
      const values = Object.keys(defaults).map(key => `:${key}`).join(', ');

      await sequelize.query(
        `INSERT INTO general_config (${columns}, "createdAt", "updatedAt")
         VALUES (${values}, NOW(), NOW())`,
        {
          replacements: defaults,
          type: sequelize.QueryTypes.INSERT
        }
      );

      // Fetch new record
      const [created] = await sequelize.query('SELECT * FROM general_config WHERE id = 1');

      return res.json(created[0]);
    }

    res.json(results[0]);
  } catch (error) {
    console.error('[settingsController] Error in getGeneralSettings:', error);
    res.status(500).json({ message: 'Error fetching general settings.', error: error.message });
  }
};

exports.updateGeneralSettings = async (req, res) => {
  try {
    console.log('[settingsController] Updating general settings', req.body);

    // Use raw query to fetch general settings
    const [results] = await sequelize.query('SELECT * FROM general_config WHERE id = 1');

    if (results && results.length > 0) {
      // Update existing settings
      const { id, ...updateData } = req.body;
      await sequelize.query(
        'UPDATE general_config SET ' +
        Object.keys(updateData).map(key => `"${key}" = :${key}`).join(', ') +
        ', "updatedAt" = NOW() WHERE id = :id',
        {
          replacements: { ...updateData, id: results[0].id },
          type: sequelize.QueryTypes.UPDATE
        }
      );

      // Fetch updated record
      const [updated] = await sequelize.query('SELECT * FROM general_config WHERE id = :id', {
        replacements: { id: results[0].id },
        type: sequelize.QueryTypes.SELECT
      });

      return res.json({
        message: 'General settings updated successfully.',
        settings: updated
      });
    } else {
      // Create new settings with defaults + updates
      const defaults = {
        siteName: 'Poultray DZ',
        defaultLanguage: 'fr',
        availableLanguages: JSON.stringify(['fr', 'ar', 'en']),
        primaryColor: '#2c5530',
        secondaryColor: '#e7eae2',
        ...req.body
      };

      // Create new settings
      const columns = Object.keys(defaults).map(key => `"${key}"`).join(', ');
      const values = Object.keys(defaults).map(key => `:${key}`).join(', ');

      await sequelize.query(
        `INSERT INTO general_config (${columns}, "createdAt", "updatedAt")
         VALUES (${values}, NOW(), NOW())`,
        {
          replacements: defaults,
          type: sequelize.QueryTypes.INSERT
        }
      );

      // Fetch new record
      const [created] = await sequelize.query('SELECT * FROM general_config ORDER BY id DESC LIMIT 1');

      return res.json({
        message: 'General settings created successfully.',
        settings: created[0]
      });
    }
  } catch (error) {
    console.error('[settingsController] Error in updateGeneralSettings:', error);
    res.status(500).json({ message: 'Error updating general settings.', error: error.message });
  }
};
