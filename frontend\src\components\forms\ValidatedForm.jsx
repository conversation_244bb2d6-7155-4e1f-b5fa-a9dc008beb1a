import React from 'react';
import {
  Box,
  Button,
  Alert,
  CircularProgress,
  Grid,
  Typography,
  Divider
} from '@mui/material';
import { Save, Cancel, Refresh } from '@mui/icons-material';
import useFormValidation from '../../hooks/useFormValidation';

/**
 * Composant de formulaire générique avec validation
 */
const ValidatedForm = ({
  initialValues = {},
  validationSchema = {},
  onSubmit,
  onCancel,
  onReset,
  children,
  title,
  subtitle,
  submitText = 'Enregistrer',
  cancelText = 'Annuler',
  resetText = 'Réinitialiser',
  showCancel = false,
  showReset = false,
  disabled = false,
  loading = false,
  maxWidth = 'md',
  spacing = 2,
  submitButtonProps = {},
  cancelButtonProps = {},
  resetButtonProps = {},
  formProps = {},
  ...props
}) => {
  const {
    values,
    errors,
    isSubmitting,
    isValid,
    handleSubmit,
    resetForm,
    getFieldProps,
    getSelectProps,
    getCheckboxProps
  } = useFormValidation(initialValues, validationSchema, {
    validateOnChange: true,
    validateOnBlur: true,
    validateOnSubmit: true
  });

  const handleFormSubmit = handleSubmit(async (formValues, helpers) => {
    if (onSubmit) {
      await onSubmit(formValues, helpers);
    }
  });

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const handleReset = () => {
    resetForm();
    if (onReset) {
      onReset();
    }
  };

  const isFormDisabled = disabled || loading || isSubmitting;

  // Cloner les enfants en leur passant les props de validation
  const renderChildren = (childrenToRender = children) => {
    return React.Children.map(childrenToRender, (child) => {
      if (!React.isValidElement(child)) {
        return child;
      }

      const baseProps = {
        disabled: isFormDisabled || child.props?.disabled
      };

      // Si l'enfant a une prop 'name', lui passer les props de validation
      if (child.props?.name) {
        const fieldName = child.props.name;
        
        if (child.props.type === 'checkbox' || child.type?.name === 'Checkbox') {
          return React.cloneElement(child, {
            ...baseProps,
            ...getCheckboxProps(fieldName)
          });
        } 
        
        if (child.type?.name === 'Select' || child.props.select) {
          return React.cloneElement(child, {
            ...baseProps,
            ...getSelectProps(fieldName)
          });
        } 
        
        return React.cloneElement(child, {
          ...baseProps,
          ...getFieldProps(fieldName)
        });
      }

      // Si l'enfant a des enfants, les traiter récursivement
      if (child.props?.children && React.Children.count(child.props.children) > 0) {
        return React.cloneElement(child, {
          ...baseProps,
          children: renderChildren(child.props.children)
        });
      }

      return child;
    });
  };

  return (
    <Box component="form" onSubmit={handleFormSubmit} {...formProps} {...props}>
      {/* En-tête du formulaire */}
      {(title || subtitle) && (
        <Box mb={3}>
          {title && (
            <Typography variant="h5" component="h2" gutterBottom>
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
          <Divider sx={{ mt: 2 }} />
        </Box>
      )}

      {/* Erreur générale de soumission */}
      {errors.submit && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.submit}
        </Alert>
      )}

      {/* Indicateur de chargement global */}
      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" py={2}>
          <CircularProgress size={24} sx={{ mr: 2 }} />
          <Typography variant="body2">Chargement...</Typography>
        </Box>
      )}

      {/* Contenu du formulaire */}
      <Grid container spacing={spacing}>
        {renderChildren()}
      </Grid>

      {/* Boutons d'action */}
      <Box 
        display="flex" 
        justifyContent="flex-end" 
        gap={2} 
        mt={3}
        pt={2}
        borderTop={1}
        borderColor="divider"
      >
        {showReset && (
          <Button
            type="button"
            variant="outlined"
            color="secondary"
            startIcon={<Refresh />}
            onClick={handleReset}
            disabled={isFormDisabled}
            {...resetButtonProps}
          >
            {resetText}
          </Button>
        )}

        {showCancel && (
          <Button
            type="button"
            variant="outlined"
            color="secondary"
            startIcon={<Cancel />}
            onClick={handleCancel}
            disabled={isFormDisabled}
            {...cancelButtonProps}
          >
            {cancelText}
          </Button>
        )}

        <Button
          type="submit"
          variant="contained"
          color="primary"
          startIcon={isSubmitting ? <CircularProgress size={16} /> : <Save />}
          disabled={isFormDisabled || !isValid}
          {...submitButtonProps}
        >
          {isSubmitting ? 'Enregistrement...' : submitText}
        </Button>
      </Box>

      {/* Informations de débogage en mode développement */}
      {import.meta.env.DEV && (
        <Box mt={2} p={2} bgcolor="grey.100" borderRadius={1}>
          <Typography variant="caption" display="block">
            Debug: Valide = {isValid ? 'Oui' : 'Non'} | 
            Erreurs = {Object.keys(errors).length} | 
            Soumission = {isSubmitting ? 'En cours' : 'Prêt'}
          </Typography>
          {Object.keys(errors).length > 0 && (
            <Typography variant="caption" color="error" display="block">
              Erreurs: {Object.keys(errors).join(', ')}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default ValidatedForm;
