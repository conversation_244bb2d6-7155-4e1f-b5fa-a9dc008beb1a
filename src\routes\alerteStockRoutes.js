const express = require('express');
const router = express.Router();
const { body, validationResult, param, query } = require('express-validator');
const { auth } = require('../middleware/auth');
const { hasRole } = require('../middleware/roleCheck');
const AlerteStock = require('../models/alerteStock');
const Eleveur = require('../models/eleveur');

/**
 * Routes pour la gestion du système d'alertes intelligentes
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 */

// Middleware de validation pour la création/modification d'une alerte
const validateAlerte = [
  body('type_alerte')
    .isIn(['stock_faible', 'mortalite_elevee', 'production_baisse', 'sante_animaux', 'meteo_defavorable', 'maintenance_equipement', 'vaccination_due', 'controle_veterinaire', 'alimentation', 'temperature', 'humidite', 'ventilation', 'eclairage', 'eau', 'securite', 'financier', 'reglementaire', 'formation', 'autre'])
    .withMessage('Type d\'alerte invalide'),

  body('priorite')
    .isIn(['faible', 'moyenne', 'haute', 'critique', 'urgente'])
    .withMessage('Priorité invalide'),

  body('message_fr')
    .isString()
    .notEmpty()
    .withMessage('Le message en français est requis'),

  body('message_ar')
    .optional()
    .isString()
    .withMessage('Le message en arabe doit être une chaîne de caractères'),

  body('seuil_declenchement')
    .optional()
    .isFloat()
    .withMessage('Le seuil de déclenchement doit être un nombre'),

  body('date_expiration')
    .optional()
    .isISO8601()
    .withMessage('La date d\'expiration doit être une date valide')
];

// GET /api/alertes - Récupérer toutes les alertes d'un éleveur
router.get('/',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    query('statut')
      .optional()
      .isIn(['active', 'vue', 'traitee', 'ignoree', 'expiree', 'archivee'])
      .withMessage('Statut invalide'),
    query('type_alerte')
      .optional()
      .isIn(['stock_faible', 'mortalite_elevee', 'production_baisse', 'sante_animaux', 'meteo_defavorable', 'maintenance_equipement', 'vaccination_due', 'controle_veterinaire', 'alimentation', 'temperature', 'humidite', 'ventilation', 'eclairage', 'eau', 'securite', 'financier', 'reglementaire', 'formation', 'autre'])
      .withMessage('Type d\'alerte invalide'),
    query('priorite')
      .optional()
      .isIn(['faible', 'moyenne', 'haute', 'critique', 'urgente'])
      .withMessage('Priorité invalide'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('La page doit être un nombre entier positif'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('La limite doit être entre 1 et 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const { statut, type_alerte, priorite, page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      // Construire les conditions de recherche
      const whereConditions = {
        eleveur_id: eleveurId
      };

      if (statut) whereConditions.statut = statut;
      if (type_alerte) whereConditions.type_alerte = type_alerte;
      if (priorite) whereConditions.priorite = priorite;

      const { count, rows: alertes } = await AlerteStock.findAndCountAll({
        where: whereConditions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [
          ['priorite', 'DESC'],
          ['date_declenchement', 'DESC']
        ],
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          }
        ]
      });

      res.json({
        success: true,
        data: {
          alertes,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des alertes'
      });
    }
  }
);

// GET /api/alertes/actives - Récupérer les alertes actives
router.get('/actives',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      const alertes = await AlerteStock.findAlertesActives(eleveurId);

      res.json({
        success: true,
        data: alertes
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes actives:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des alertes actives'
      });
    }
  }
);

// GET /api/alertes/urgentes - Récupérer les alertes urgentes
router.get('/urgentes',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      const alertes = await AlerteStock.findAlertesUrgentes(eleveurId);

      res.json({
        success: true,
        data: alertes
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes urgentes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des alertes urgentes'
      });
    }
  }
);

// GET /api/alertes/rappels - Récupérer les alertes nécessitant un rappel
router.get('/rappels',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      const alertes = await AlerteStock.findAlertesRappelDu(eleveurId);

      res.json({
        success: true,
        data: alertes
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes de rappel:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des alertes de rappel'
      });
    }
  }
);

// GET /api/alertes/statistiques - Récupérer les statistiques des alertes
router.get('/statistiques',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    query('date_debut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    query('date_fin')
      .optional()
      .isISO8601()
      .withMessage('La date de fin doit être une date valide')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;
      const { date_debut, date_fin } = req.query;

      const dateDebutDefault = date_debut || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const dateFinDefault = date_fin || new Date().toISOString().split('T')[0];

      const statistiques = await AlerteStock.getStatistiques(eleveurId, dateDebutDefault, dateFinDefault);

      res.json({
        success: true,
        data: statistiques
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des statistiques'
      });
    }
  }
);

// GET /api/alertes/:id - Récupérer une alerte spécifique
router.get('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({
        where: whereConditions,
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          }
        ]
      });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      res.json({
        success: true,
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération de l\'alerte'
      });
    }
  }
);

// POST /api/alertes - Créer une nouvelle alerte
router.post('/',
  auth,
  hasRole(['eleveur', 'admin']),
  validateAlerte,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const eleveurId = req.user.role === 'admin' ? req.body.eleveur_id || req.user.id : req.user.id;

      // Vérifier que l'éleveur existe
      const eleveur = await Eleveur.findByPk(eleveurId);
      if (!eleveur) {
        return res.status(404).json({
          success: false,
          message: 'Éleveur non trouvé'
        });
      }

      const alerteData = {
        ...req.body,
        eleveur_id: eleveurId
      };

      const alerte = await AlerteStock.create(alerteData);

      res.status(201).json({
        success: true,
        message: 'Alerte créée avec succès',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de la création de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la création de l\'alerte'
      });
    }
  }
);

// POST /api/alertes/automatiques - Créer des alertes automatiques
router.post('/automatiques',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.body.eleveur_id || req.user.id : req.user.id;

      const alertes = await AlerteStock.creerAlertesAutomatiques(eleveurId);

      res.json({
        success: true,
        message: `${alertes.length} alertes automatiques créées`,
        data: alertes
      });
    } catch (error) {
      console.error('Erreur lors de la création des alertes automatiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la création des alertes automatiques'
      });
    }
  }
);

// PUT /api/alertes/:id - Mettre à jour une alerte
router.put('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    ...validateAlerte
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.update(req.body);

      res.json({
        success: true,
        message: 'Alerte mise à jour avec succès',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la mise à jour de l\'alerte'
      });
    }
  }
);

// POST /api/alertes/:id/marquer-vue - Marquer une alerte comme vue
router.post('/:id/marquer-vue',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.marquerCommeVue();

      res.json({
        success: true,
        message: 'Alerte marquée comme vue',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors du marquage de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors du marquage de l\'alerte'
      });
    }
  }
);

// POST /api/alertes/:id/marquer-traitee - Marquer une alerte comme traitée
router.post('/:id/marquer-traitee',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('notes_resolution')
      .optional()
      .isString()
      .withMessage('Les notes de résolution doivent être une chaîne de caractères')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.marquerCommeTraitee(req.body.notes_resolution);

      res.json({
        success: true,
        message: 'Alerte marquée comme traitée',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors du marquage de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors du marquage de l\'alerte'
      });
    }
  }
);

// POST /api/alertes/:id/ajouter-action - Ajouter une action à une alerte
router.post('/:id/ajouter-action',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('action')
      .isString()
      .notEmpty()
      .withMessage('L\'action est requise'),
    body('date_action')
      .optional()
      .isISO8601()
      .withMessage('La date d\'action doit être une date valide')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.ajouterAction(req.body.action, req.body.date_action);

      res.json({
        success: true,
        message: 'Action ajoutée avec succès',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'action:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'ajout de l\'action'
      });
    }
  }
);

// POST /api/alertes/:id/feedback - Ajouter un feedback à une alerte
router.post('/:id/feedback',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('feedback')
      .isString()
      .notEmpty()
      .withMessage('Le feedback est requis'),
    body('note_utilite')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('La note d\'utilité doit être entre 1 et 5')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.ajouterFeedback(req.body.feedback, req.body.note_utilite);

      res.json({
        success: true,
        message: 'Feedback ajouté avec succès',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout du feedback:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'ajout du feedback'
      });
    }
  }
);

// POST /api/alertes/:id/rappel - Envoyer un rappel pour une alerte
router.post('/:id/rappel',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.envoyerRappel();

      res.json({
        success: true,
        message: 'Rappel envoyé avec succès',
        data: alerte
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi du rappel:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'envoi du rappel'
      });
    }
  }
);

// DELETE /api/alertes/:id - Supprimer une alerte
router.delete('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const alerte = await AlerteStock.findOne({ where: whereConditions });

      if (!alerte) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }

      await alerte.destroy();

      res.json({
        success: true,
        message: 'Alerte supprimée avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'alerte:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la suppression de l\'alerte'
      });
    }
  }
);

// POST /api/alertes/nettoyer-anciennes - Nettoyer les anciennes alertes
router.post('/nettoyer-anciennes',
  auth,
  hasRole(['admin']),
  [
    body('jours_retention')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Le nombre de jours de rétention doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const joursRetention = req.body.jours_retention || 90;
      const nombreSupprimees = await AlerteStock.nettoyerAnciennesAlertes(joursRetention);

      res.json({
        success: true,
        message: `${nombreSupprimees} alertes anciennes supprimées`,
        data: { nombreSupprimees }
      });
    } catch (error) {
      console.error('Erreur lors du nettoyage des anciennes alertes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors du nettoyage des anciennes alertes'
      });
    }
  }
);

module.exports = router;
