import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { AlerteStock } from '../index';
import testDbService from '../../../../services/testDbService';

// Test data
const testAlertes = [
  {
    eleveur_id: 1,
    type_alerte: 'STOCK_BAS',
    priorite: 'HAUTE',
    statut: 'ACTIVE',
    titre: 'TEST Aliment Poussin',
    message: 'TEST Le stock d\'aliment pour poussins est bas (80kg restants). Seuil d\'alerte: 100kg.',
    date_declenchement: new Date('2024-01-01T08:00:00.000Z')
  },
  {
    eleveur_id: 1,
    type_alerte: 'EXPIRATION_PROCHE',
    priorite: 'MOYENNE',
    statut: 'ACTIVE',
    titre: 'TEST Vaccin Newcastle',
    message: 'TEST Le vaccin Newcastle expire bientôt (01/02/2024). <PERSON>uil d\'alerte: 30 jours.',
    date_declenchement: new Date('2024-01-02T10:00:00.000Z')
  }
];

describe('AlerteStock Component', () => {
  // Insérer les données de test avant tous les tests
  beforeAll(async () => {
    // Nettoyer les données de test existantes
    await testDbService.cleanTestData();
    
    // Insérer les données de test
    for (const alerte of testAlertes) {
      await testDbService.insertTestAlertesStock(alerte);
    }
  });
  
  // Nettoyer les données de test après tous les tests
  afterAll(async () => {
    await testDbService.cleanTestData();
  });
  
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };
  
  test('renders loading state initially', () => {
    renderWithI18n(<AlerteStock />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  test('renders alerts list after loading', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Vérifier que les données de test sont affichées
    expect(screen.getByText('TEST Aliment Poussin')).toBeInTheDocument();
    expect(screen.getByText('STOCK_BAS')).toBeInTheDocument();
    expect(screen.getByText('HAUTE')).toBeInTheDocument();
    
    expect(screen.getByText('TEST Vaccin Newcastle')).toBeInTheDocument();
    expect(screen.getByText('EXPIRATION_PROCHE')).toBeInTheDocument();
    expect(screen.getByText('MOYENNE')).toBeInTheDocument();
  });
  
  test('opens alert configuration dialog', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue de configuration
    fireEvent.click(screen.getByText('alert.configure'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('alert.threshold')).toBeInTheDocument();
  });
  
  test('handles alert configuration update', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue de configuration
    fireEvent.click(screen.getByText('alert.configure'));
    
    // Mettre à jour le seuil
    fireEvent.change(screen.getByLabelText('alert.threshold'), {
      target: { value: '150' }
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('common.save'));
    
    // Vérifier que la boîte de dialogue est fermée (la vérification réelle se fait via la base de données)
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });
  
  test('filters alerts by priority', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Filtrer par priorité haute
    fireEvent.click(screen.getByLabelText('alert.filterPriority'));
    fireEvent.click(screen.getByText('alert.priority.high'));
    
    // Vérifier que seule l'alerte de priorité haute est affichée
    expect(screen.getByText('TEST Aliment Poussin')).toBeInTheDocument();
    expect(screen.queryByText('TEST Vaccin Newcastle')).not.toBeInTheDocument();
  });
  
  test('marks alert as resolved', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Cliquer sur le bouton de résolution pour la première alerte
    fireEvent.click(screen.getByText('alert.resolve'));
    
    // Vérifier que l'alerte est marquée comme résolue (la vérification réelle se fait via la base de données)
    await waitFor(() => {
      expect(screen.queryByText('ACTIVE')).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });
  
  test('displays notification settings dialog', async () => {
    renderWithI18n(<AlerteStock />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue des paramètres de notification
    fireEvent.click(screen.getByText('alert.notificationSettings'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('alert.notifyEmail')).toBeInTheDocument();
    expect(screen.getByLabelText('alert.notifySMS')).toBeInTheDocument();
  });
  
  // Note: Le test d'erreur est commenté car il nécessiterait de simuler une erreur de base de données
  // test('handles error state', async () => {
  //   // Simuler une erreur de base de données
  //   jest.spyOn(testDbService, 'getAlertesStock').mockRejectedValueOnce(new Error('Failed to fetch'));
  //   
  //   renderWithI18n(<AlerteStock />);
  //   
  //   // Attendre que l'erreur soit affichée
  //   await waitFor(() => {
  //     expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  //   
  //   expect(screen.getByText('errors.fetchFailed')).toBeInTheDocument();
  //   
  //   // Restaurer le mock
  //   jest.restoreAllMocks();
  // });
});
