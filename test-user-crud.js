const axios = require('axios');

const API_URL = 'http://localhost:3003';
let authToken = null;

async function authenticate() {
  try {
    console.log('🔐 Authenticating admin user...');
    const response = await axios.post(`${API_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    authToken = response.data.token;
    console.log('✅ Authentication successful');
    return true;
  } catch (error) {
    console.error('❌ Authentication failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return false;
  }
}

function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'x-auth-token': authToken,
    'Content-Type': 'application/json'
  };
}

async function testCreateUser() {
  try {
    console.log('🧪 Testing POST /api/admin/users...');

    const testUser = {
      username: 'testuser' + Date.now(),
      email: 'testuser' + Date.now() + '@example.com',
      password: 'testpassword123',
      first_name: 'Test',
      last_name: 'User',
      phone: '0123456789',
      role: 'user',
      status: 'active'
    };

    const response = await axios.post(`${API_URL}/api/admin/users`, testUser, { headers: getHeaders() });

    console.log('✅ User created successfully:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    return response.data.user;
  } catch (error) {
    console.error('❌ Error creating user:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

async function testGetUsers() {
  try {
    console.log('\n🧪 Testing GET /api/admin/users...');

    const response = await axios.get(`${API_URL}/api/admin/users`, { headers: getHeaders() });

    console.log('✅ Users retrieved successfully:');
    console.log('Status:', response.status);
    console.log('Users count:', response.data.users?.length || 0);

    return response.data;
  } catch (error) {
    console.error('❌ Error getting users:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

async function main() {
  console.log('🚀 Testing User CRUD operations...\n');

  // First authenticate
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log('❌ Authentication failed, stopping tests');
    return;
  }

  // Test getting users first
  await testGetUsers();

  // Test creating a user
  const createdUser = await testCreateUser();

  if (createdUser) {
    console.log('\n✅ All tests completed successfully!');
  } else {
    console.log('\n❌ Some tests failed!');
  }
}

main().catch(console.error);
