/**
 * Middleware de rafraîchissement automatique des tokens JWT
 * Ce middleware intercepte les erreurs 401 et tente de rafraîchir le token
 */

const jwt = require('jsonwebtoken');

/**
 * Middleware pour vérifier et rafraîchir les tokens JWT expirés
 * À placer avant le middleware d'authentification standard
 */
const tokenRefresh = (req, res, next) => {
  // Récupérer le token depuis les en-têtes (vérifier les deux formats possibles)
  let token = req.header('x-auth-token');

  // Vérifier également l'en-tête Authorization (format Bearer)
  const authHeader = req.header('Authorization');
  if (!token && authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  }

  // Si pas de token, passer au middleware suivant
  if (!token) {
    console.log('Aucun token trouvé dans la requête:', req.originalUrl);
    return next();
  }

  try {
    // Vérifier si le token est valide
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'poultray_dz_secret_key_2023');

    // Vérifier la structure du token décodé
    if (decoded && decoded.id && decoded.role) {
      req.user = {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role
      };
      console.log('Token valide pour l\'utilisateur:', decoded.id, 'rôle:', decoded.role);
      return next();
    } else {
      console.log('Structure de token invalide');
      return next();
    }
  } catch (error) {
    console.log('Erreur de vérification du token:', error.name);

    // Si le token est expiré mais valide, générer un nouveau token
    if (error.name === 'TokenExpiredError') {
      try {
        // Décoder le token sans vérifier l'expiration
        const decoded = jwt.decode(token);

        if (decoded && decoded.id && decoded.role) {
          // Générer un nouveau token avec la même structure
          const newPayload = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role
          };

          const newToken = jwt.sign(
            newPayload,
            process.env.JWT_SECRET || 'poultray_dz_secret_key_2023',
            { expiresIn: '24h' }
          );

          // Ajouter le nouveau token à l'en-tête de réponse
          res.setHeader('x-auth-token-refreshed', newToken);

          // Mettre à jour le token dans la requête
          req.headers['x-auth-token'] = newToken;
          req.headers['Authorization'] = `Bearer ${newToken}`;
          req.user = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role
          };

          console.log('Token JWT rafraîchi automatiquement pour l\'utilisateur:', decoded.id);
          return next();
        }
      } catch (decodeError) {
        console.error('Erreur lors du décodage du token expiré:', decodeError);
        // Continuer vers le middleware d'authentification standard
        return next();
      }
    }

    // Pour les autres erreurs, passer au middleware suivant
    return next();
  }
};

module.exports = tokenRefresh;
