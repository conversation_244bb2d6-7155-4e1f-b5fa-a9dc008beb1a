const { DataTypes } = require('sequelize');

/**
 * FeedSupplier model - Represents feed suppliers
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedSupplier = sequelize.define('FeedSupplier', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    contact_person: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: /^[+]?[0-9\s\-\(\)]{8,20}$/
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    wilaya: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    commune: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    delivery_zones: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      validate: {
        isValidDeliveryZones(value) {
          if (value && !Array.isArray(value)) {
            throw new Error('Delivery zones must be an array');
          }
        }
      }
    },
    payment_terms: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    rating: {
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
      validate: {
        min: 1.00,
        max: 5.00
      },
      comment: 'rating from 1.00 to 5.00'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: [['active', 'inactive', 'suspended', 'blacklisted']]
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_suppliers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['wilaya', 'status']
      },
      {
        fields: ['delivery_zones'],
        using: 'gin'
      },
      {
        fields: ['rating']
      }
    ],
    hooks: {
      beforeValidate: (supplier) => {
        // Normalize phone number
        if (supplier.phone) {
          supplier.phone = supplier.phone.replace(/\s+/g, ' ').trim();
        }
        
        // Normalize email
        if (supplier.email) {
          supplier.email = supplier.email.toLowerCase().trim();
        }
        
        // Ensure delivery_zones is an array
        if (typeof supplier.delivery_zones === 'string') {
          supplier.delivery_zones = [supplier.delivery_zones];
        }
      }
    }
  });

  // Instance methods
  FeedSupplier.prototype.canDeliverTo = function(wilaya, commune = null) {
    if (!this.delivery_zones || this.delivery_zones.length === 0) {
      return true; // No restrictions
    }
    
    // Check if wilaya is in delivery zones
    const hasWilaya = this.delivery_zones.some(zone => 
      zone.toLowerCase().includes(wilaya.toLowerCase())
    );
    
    if (commune) {
      const hasCommune = this.delivery_zones.some(zone => 
        zone.toLowerCase().includes(commune.toLowerCase())
      );
      return hasWilaya || hasCommune;
    }
    
    return hasWilaya;
  };

  FeedSupplier.prototype.hasAvailableCredit = function(amount) {
    if (!this.credit_limit) {
      return false; // No credit available
    }
    return amount <= this.credit_limit;
  };

  FeedSupplier.prototype.updateRating = function(newRating, totalRatings) {
    if (!this.rating) {
      this.rating = newRating;
    } else {
      // Calculate weighted average
      const currentWeight = totalRatings - 1;
      this.rating = ((this.rating * currentWeight) + newRating) / totalRatings;
      this.rating = Math.round(this.rating * 100) / 100; // Round to 2 decimal places
    }
    return this.save();
  };

  FeedSupplier.prototype.getContactInfo = function() {
    return {
      name: this.name,
      contact_person: this.contact_person,
      phone: this.phone,
      email: this.email,
      address: this.address,
      wilaya: this.wilaya,
      commune: this.commune
    };
  };

  // Class methods
  FeedSupplier.findByLocation = function(wilaya, commune = null, options = {}) {
    const whereClause = {
      status: 'active'
    };
    
    if (wilaya) {
      whereClause[sequelize.Sequelize.Op.or] = [
        { wilaya: { [sequelize.Sequelize.Op.iLike]: `%${wilaya}%` } },
        { delivery_zones: { [sequelize.Sequelize.Op.contains]: [wilaya] } }
      ];
      
      if (commune) {
        whereClause[sequelize.Sequelize.Op.or].push(
          { commune: { [sequelize.Sequelize.Op.iLike]: `%${commune}%` } },
          { delivery_zones: { [sequelize.Sequelize.Op.contains]: [commune] } }
        );
      }
    }
    
    return this.findAll({
      where: whereClause,
      order: [['rating', 'DESC'], ['name', 'ASC']],
      ...options
    });
  };

  FeedSupplier.findTopRated = function(limit = 10, options = {}) {
    return this.findAll({
      where: {
        status: 'active',
        rating: {
          [sequelize.Sequelize.Op.gte]: 3.0
        }
      },
      order: [['rating', 'DESC'], ['name', 'ASC']],
      limit,
      ...options
    });
  };

  FeedSupplier.findWithCredit = function(minCreditLimit = 0, options = {}) {
    return this.findAll({
      where: {
        status: 'active',
        credit_limit: {
          [sequelize.Sequelize.Op.gte]: minCreditLimit
        }
      },
      order: [['credit_limit', 'DESC'], ['rating', 'DESC']],
      ...options
    });
  };

  FeedSupplier.searchByName = function(searchTerm, options = {}) {
    return this.findAll({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { name: { [sequelize.Sequelize.Op.iLike]: `%${searchTerm}%` } },
          { contact_person: { [sequelize.Sequelize.Op.iLike]: `%${searchTerm}%` } }
        ],
        status: 'active'
      },
      order: [['name', 'ASC']],
      ...options
    });
  };

  // Associations
  FeedSupplier.associate = function(models) {
    // FeedSupplier has many FeedStock
    FeedSupplier.hasMany(models.FeedStock, {
      foreignKey: 'supplier_id',
      as: 'feed_stock_entries'
    });
  };

  return FeedSupplier;
};