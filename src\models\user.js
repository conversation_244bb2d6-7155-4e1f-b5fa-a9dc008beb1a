const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
  static associate(models) {
    // Définir l'association avec le modèle Role
    User.belongsTo(models.Role, {
      foreignKey: 'role_id',
      as: 'role'
    });
  }
}

User.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'roles', // Nom de la table des rôles
      key: 'id'
    }
  },
  first_name: {
    type: DataTypes.STRING(100)
  },
  last_name: {
    type: DataTypes.STRING(100)
  },
  profile_id: {
    type: DataTypes.INTEGER
  },
  status: {
    type: DataTypes.STRING(20),
    defaultValue: 'active'
  },
  preferences: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  firebase_uid: {
    type: DataTypes.STRING(255),
    unique: true,
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'updated_at'
  }
}, {
  sequelize,
  modelName: 'User',
  timestamps: true,
  underscored: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, 10);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, 10);
      }
    }
  }
});

// Méthodes d'instance
User.prototype.generateToken = async function() {
  try {
    console.log('=== Début de la génération du token JWT ===');
    console.log('Utilisateur ID:', this.id);

    // Récupérer le rôle et les informations complètes de l'utilisateur
    const userWithRole = await User.findOne({
      where: { id: this.id },
      include: [{ model: sequelize.models.Role, as: 'role', attributes: ['name', 'id'] }]
    });

    if (!userWithRole) {
      console.error('Utilisateur non trouvé pour la génération du token:', this.id);
      throw new Error('Utilisateur non trouvé');
    }

    if (!userWithRole.role) {
      console.error('Rôle non trouvé pour l\'utilisateur:', this.id);
      throw new Error('Rôle non trouvé pour l\'utilisateur');
    }

    console.log('Rôle trouvé:', userWithRole.role.name);

    // Création du payload avec plus d'informations
    const payload = {
      id: this.id,
      email: this.email,
      username: this.username,
      role: userWithRole.role.name,
      role_id: userWithRole.role.id,
      first_name: this.first_name,
      last_name: this.last_name,
      status: this.status,
      firebase_uid: this.firebase_uid,
      iat: Math.floor(Date.now() / 1000)
    };

    console.log('Payload du token:', JSON.stringify(payload, null, 2));

    // Vérifier que JWT_SECRET est défini
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET non défini dans les variables d\'environnement');
      throw new Error('Configuration JWT manquante');
    }

    // Générer le token avec une durée de validité de 24h
    const token = jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: '24h',
      algorithm: 'HS256'
    });

    console.log('Token JWT généré avec succès');
    console.log('=== Fin de la génération du token JWT ===');

    return token;
  } catch (error) {
    console.error('=== ERREUR de génération du token JWT ===');
    console.error('Utilisateur ID:', this.id);
    console.error('Message:', error.message);
    console.error('Stack:', error.stack);
    throw new Error(`Erreur de génération du token: ${error.message}`);
  }
}

User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Méthodes statiques
User.register = async function(userData) {
  // Validation des données utilisateur
  if (!userData.email || !userData.password || !userData.username) {
    throw new Error('Email, mot de passe et nom d\'utilisateur requis');
  }

  // Vérifier si l'email existe déjà
  const existingUser = await this.findOne({ where: { email: userData.email } });
  if (existingUser) {
    throw new Error('Cet email est déjà utilisé');
  }

  // Vérifier si le nom d'utilisateur existe déjà
  const existingUsername = await this.findOne({ where: { username: userData.username } });
  if (existingUsername) {
    throw new Error('Ce nom d\'utilisateur est déjà utilisé');
  }

  return this.create(userData);
};

// Méthode statique pour l'authentification Firebase
User.loginWithFirebase = async function(email, firebase_token, firebase_uid) {
  try {
    console.log('=== Début de l\'authentification Firebase ===');
    console.log('Email:', email);
    console.log('Firebase UID:', firebase_uid);

    // Vérifier si l'utilisateur existe avec son rôle
    const user = await this.findOne({
      where: { email },
      include: [{ model: sequelize.models.Role, as: 'role', attributes: ['name', 'id'] }]
    });

    if (!user) {
      console.error('Utilisateur non trouvé dans la base de données:', email);
      throw new Error('Utilisateur non trouvé');
    }

    console.log('Utilisateur trouvé:', {
      id: user.id,
      email: user.email,
      role: user.role ? user.role.name : 'non défini'
    });

    // Mettre à jour les informations Firebase si nécessaire
    if (!user.firebase_uid || user.firebase_uid !== firebase_uid) {
      console.log('Mise à jour du Firebase UID pour l\'utilisateur');
      await user.update({
        firebase_uid: firebase_uid,
        last_login: new Date()
      });
    }

    // Vérifier que l'utilisateur a un rôle
    if (!user.role) {
      console.error('Utilisateur sans rôle:', email);
      throw new Error('Rôle utilisateur non défini');
    }

    // Générer un token JWT pour l'utilisateur
    console.log('Génération du token JWT...');
    const token = await user.generateToken();

    const userData = {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role.name,
      role_id: user.role.id,
      first_name: user.first_name,
      last_name: user.last_name,
      status: user.status,
      firebase_uid: user.firebase_uid,
      last_login: user.last_login
    };

    console.log('Authentification Firebase réussie pour:', email);
    console.log('=== Fin de l\'authentification Firebase ===');

    return { user: userData, token };
  } catch (error) {
    console.error('=== ERREUR d\'authentification Firebase ===');
    console.error('Email:', email);
    console.error('Erreur:', error.message);
    console.error('Stack:', error.stack);
    throw new Error(`Erreur d'authentification Firebase: ${error.message}`);
  }
};

  return User;
};
