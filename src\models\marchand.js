// filepath: k:\Projets_Sites_Web\Poultray-dz-TraeDev\Web_App\Poultraydz-Trae\src\models\marchand.js
const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Marchand extends Model {
    static associate(models) {
      // Un marchand peut avoir plusieurs produits
      Marchand.hasMany(models.Product, {
        foreignKey: 'marchand_id',
        as: 'products'
      });

      // Un marchand peut avoir plusieurs commandes
      Marchand.hasMany(models.Order, {
        foreignKey: 'marchand_id',
        as: 'orders'
      });
    }
  }

  Marchand.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    prenom: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    telephone: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    adresse: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type_commerce: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    licence_commerce: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    date_inscription: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'actif',
      validate: {
        isIn: [['actif', 'inactif', 'suspendu']]
      }
    }
  }, {
    sequelize,
    modelName: 'Marchand',
    tableName: 'marchands',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Marchand;
};
