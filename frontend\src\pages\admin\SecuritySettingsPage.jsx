import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  FormControlLabel,
  Switch,
  Divider,
  Slider,
  InputAdornment,
  MenuItem,
  Snackbar,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Security as SecurityIcon,
  LockClock as LockClockIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  VpnKey as VpnKeyIcon,
  VerifiedUser as VerifiedUserIcon
} from '@mui/icons-material';
import settingsService from '../../services/settingsService';
import { useLanguage } from '../../contexts/LanguageContext';

const SecuritySettingsPage = () => {
  const { t } = useLanguage();

  const [settings, setSettings] = useState({
    enable2FA: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    passwordComplexityRegex: '',
    passwordHistoryCount: 3,
    passwordExpiryDays: 90,
    contentSecurityPolicy: '',
    corsAllowedOrigins: '',
    logLevel: 'info',
    apiRateLimitingEnabled: true,
    apiRateLimitRequests: 100,
    apiRateLimitWindowMs: 900000 // 15 minutes in milliseconds
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        console.log('Fetching security settings via SecuritySettingsPage component...');
        const data = await settingsService.getSecuritySettings();
        console.log('Fetched security settings:', data);
        setSettings(data);
        setError('');
      } catch (err) {
        console.error('Error fetching security settings:', err);
        setError(t('settings.security.fetchError') || 'Failed to load security settings');
        showToast(t('settings.security.fetchError') || 'Failed to load security settings', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [t]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    const newValue = e.target.type === 'checkbox' ? checked : value;

    setSettings(prev => ({
      ...prev,
      [name]: newValue
    }));
  };

  const handleSliderChange = (name) => (_, newValue) => {
    setSettings(prev => ({
      ...prev,
      [name]: newValue
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError('');
      console.log('Submitting security settings:', settings);
      await settingsService.updateSecuritySettings(settings);
      console.log('Security settings updated successfully');
      setSuccess(t('settings.security.saveSuccess') || 'Security settings updated successfully');
      showToast(t('settings.security.saveSuccess') || 'Security settings updated successfully', 'success');
    } catch (err) {
      console.error('Error updating security settings:', err);
      setError(t('settings.security.saveError') || 'Failed to update security settings');
      showToast(t('settings.security.saveError') || 'Failed to update security settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const showToast = (message, severity) => {
    setToast({ open: true, message, severity });
  };

  const handleCloseToast = () => {
    setToast({ ...toast, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        {t('settings.security.title') || 'Security Settings'}
      </Typography>
      <Paper sx={{ p: 3, mt: 2 }}>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Authentication Section */}
            <Grid item xs={12}>
              <Typography variant="h5" component="h2" gutterBottom>
                <VerifiedUserIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                {t('settings.security.authentication.title') || 'Authentication'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.enable2FA}
                    onChange={handleChange}
                    name="enable2FA"
                    color="primary"
                  />
                }
                label={t('settings.security.authentication.enable2FA') || 'Enable Two-Factor Authentication'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography gutterBottom>
                {t('settings.security.authentication.sessionTimeout') || 'Session Timeout (minutes)'}
              </Typography>
              <Slider
                value={settings.sessionTimeout}
                onChange={handleSliderChange('sessionTimeout')}
                aria-labelledby="session-timeout-slider"
                valueLabelDisplay="auto"
                step={5}
                marks
                min={5}
                max={120}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.security.authentication.maxLoginAttempts') || 'Max Login Attempts'}
                name="maxLoginAttempts"
                type="number"
                value={settings.maxLoginAttempts}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockClockIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.security.authentication.lockoutDuration') || 'Lockout Duration (minutes)'}
                name="lockoutDuration"
                type="number"
                value={settings.lockoutDuration}
                onChange={handleChange}
              />
            </Grid>

            {/* Password Policy Section */}
            <Grid item xs={12}>
              <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2 }}>
                <VpnKeyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                {t('settings.security.passwordPolicy.title') || 'Password Policy'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('settings.security.passwordPolicy.complexityRegex') || 'Password Complexity Regex'}
                name="passwordComplexityRegex"
                value={settings.passwordComplexityRegex}
                onChange={handleChange}
                helperText={t('settings.security.passwordPolicy.complexityRegexHelp') || 'Regular expression for password validation (e.g., ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$)'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.security.passwordPolicy.historyCount') || 'Password History Count'}
                name="passwordHistoryCount"
                type="number"
                value={settings.passwordHistoryCount}
                onChange={handleChange}
                helperText={t('settings.security.passwordPolicy.historyCountHelp') || 'Number of previous passwords to remember to prevent reuse'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.security.passwordPolicy.expiryDays') || 'Password Expiry (days)'}
                name="passwordExpiryDays"
                type="number"
                value={settings.passwordExpiryDays}
                onChange={handleChange}
                helperText={t('settings.security.passwordPolicy.expiryDaysHelp') || 'Number of days after which passwords expire (0 to disable)'}
              />
            </Grid>

            {/* API Security Section */}
            <Grid item xs={12}>
              <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2 }}>
                <AdminPanelSettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                {t('settings.security.apiSecurity.title') || 'API Security'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('settings.security.apiSecurity.corsAllowedOrigins') || 'CORS Allowed Origins'}
                name="corsAllowedOrigins"
                value={settings.corsAllowedOrigins}
                onChange={handleChange}
                helperText={t('settings.security.apiSecurity.corsAllowedOriginsHelp') || 'Comma-separated list of allowed origins for CORS (e.g., http://localhost:3000,https://yourdomain.com)'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('settings.security.apiSecurity.logLevel') || 'Log Level'}</InputLabel>
                <Select
                  name="logLevel"
                  value={settings.logLevel}
                  label={t('settings.security.apiSecurity.logLevel') || 'Log Level'}
                  onChange={handleChange}
                >
                  <MenuItem value="debug">Debug</MenuItem>
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="warn">Warning</MenuItem>
                  <MenuItem value="error">Error</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.apiRateLimitingEnabled}
                    onChange={handleChange}
                    name="apiRateLimitingEnabled"
                    color="primary"
                  />
                }
                label={t('settings.security.apiSecurity.rateLimitingEnabled') || 'Enable API Rate Limiting'}
              />
            </Grid>

            {settings.apiRateLimitingEnabled && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label={t('settings.security.apiSecurity.rateLimitRequests') || 'Rate Limit Requests'}
                    name="apiRateLimitRequests"
                    type="number"
                    value={settings.apiRateLimitRequests}
                    onChange={handleChange}
                    helperText={t('settings.security.apiSecurity.rateLimitRequestsHelp') || 'Maximum number of requests in the time window'}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label={t('settings.security.apiSecurity.rateLimitWindowMs') || 'Rate Limit Window (ms)'}
                    name="apiRateLimitWindowMs"
                    type="number"
                    value={settings.apiRateLimitWindowMs}
                    onChange={handleChange}
                    helperText={t('settings.security.apiSecurity.rateLimitWindowMsHelp') || 'Time window for rate limiting in milliseconds (e.g., 900000 for 15 minutes)'}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <CircularProgress size={24} sx={{ mr: 1 }} />
                      {t('common.saving') || 'Saving...'}
                    </>
                  ) : (
                    t('common.save') || 'Save Changes'
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}>
          {toast.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SecuritySettingsPage;
