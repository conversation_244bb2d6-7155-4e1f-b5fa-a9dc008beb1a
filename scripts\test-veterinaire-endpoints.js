/**
 * Test script for the veterinaire dashboard and notifications endpoints
 * This script tests:
 * 1. User authentication
 * 2. Dashboard endpoint
 * 3. Notifications endpoint
 */

const axios = require('axios');
const dotenv = require('dotenv');
dotenv.config();

// Base URL for API
const API_URL = process.env.API_URL || 'http://localhost:3000/api';

// Authentication token (you'll need to replace this with a valid token)
// You can get this by logging in as a veterinaire user
let AUTH_TOKEN = process.env.AUTH_TOKEN;
const { performance } = require('perf_hooks');

// Load environment variables
dotenv.config();

// Config
const API_URL = process.env.API_URL || 'http://localhost:3000/api';
let token = null;
let userId = null;

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Helper for colored console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function logSuccess(message) {
  console.log(`${colors.green}✓ ${message}${colors.reset}`);
}

function logError(message, error) {
  console.error(`${colors.red}✗ ${message}${colors.reset}`);
  if (error) {
    if (error.response) {
      console.error(`${colors.red}Status: ${error.response.status}${colors.reset}`);
      console.error(`${colors.red}Data: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    } else if (error.request) {
      console.error(`${colors.red}No response received: ${error.message}${colors.reset}`);
    } else {
      console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
    }
  }
}

function logInfo(message) {
  console.log(`${colors.blue}ℹ ${message}${colors.reset}`);
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠ ${message}${colors.reset}`);
}

async function login() {
  try {
    logInfo('Attempting login...');
    const startTime = performance.now();

    const response = await axios.post(`${API_URL}/auth/login`, testUser);

    const endTime = performance.now();

    if (response.data && response.data.token) {
      token = response.data.token;
      userId = response.data.user.id;
      logSuccess(`Login successful for user: ${response.data.user.email} (${response.data.user.role})`);
      logInfo(`Response time: ${Math.round(endTime - startTime)}ms`);
      return true;
    } else {
      logError('Login response did not contain token');
      return false;
    }
  } catch (error) {
    logError('Login failed', error);
    return false;
  }
}

async function testDashboard() {
  try {
    logInfo('Testing veterinaire dashboard endpoint...');
    const startTime = performance.now();

    const response = await axios.get(`${API_URL}/veterinaire/dashboard`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const endTime = performance.now();

    if (response.data && response.data.status === 'success') {
      logSuccess('Dashboard endpoint returned success');
      logInfo(`Response time: ${Math.round(endTime - startTime)}ms`);

      // Log structure of response
      console.log('Dashboard response structure:');
      const data = response.data.data;

      if (data.stats) {
        console.log(`${colors.cyan}Stats:${colors.reset}`, Object.keys(data.stats).join(', '));
      }

      if (data.consultationsAVenir) {
        console.log(`${colors.cyan}Consultations à venir:${colors.reset} ${data.consultationsAVenir.length} items`);
      }

      if (data.prescriptionsRecentes) {
        console.log(`${colors.cyan}Prescriptions récentes:${colors.reset} ${data.prescriptionsRecentes.length} items`);
      }

      if (data.consultationsHistorique) {
        console.log(`${colors.cyan}Consultations historique:${colors.reset} ${data.consultationsHistorique.length} items`);
      }

      if (data.graphiques) {
        console.log(`${colors.cyan}Graphiques:${colors.reset} ${Object.keys(data.graphiques).join(', ')}`);
      }

      if (data.alertesSante) {
        console.log(`${colors.cyan}Alertes santé:${colors.reset} ${data.alertesSante.length} items`);
      }

      return true;
    } else {
      logError('Dashboard endpoint returned unexpected response', { response: response.data });
      return false;
    }
  } catch (error) {
    logError('Dashboard endpoint test failed', error);
    return false;
  }
}

async function testNotifications() {
  try {
    logInfo('Testing veterinaire notifications endpoint...');
    const startTime = performance.now();

    const response = await axios.get(`${API_URL}/veterinaire/notifications`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const endTime = performance.now();

    if (response.data && response.data.status === 'success') {
      logSuccess('Notifications endpoint returned success');
      logInfo(`Response time: ${Math.round(endTime - startTime)}ms`);

      // Log structure of response
      const notifications = response.data.data.notifications;
      console.log(`${colors.cyan}Notifications:${colors.reset} ${notifications.length} items`);

      if (notifications.length > 0) {
        console.log('Notification types:', [...new Set(notifications.map(n => n.type))].join(', '));
      }

      return true;
    } else {
      logError('Notifications endpoint returned unexpected response', { response: response.data });
      return false;
    }
  } catch (error) {
    logError('Notifications endpoint test failed', error);
    return false;
  }
}

async function runTests() {
  console.log(`${colors.magenta}==============================================${colors.reset}`);
  console.log(`${colors.magenta}  Testing Veterinaire Dashboard Endpoints${colors.reset}`);
  console.log(`${colors.magenta}==============================================${colors.reset}`);
  console.log(`API URL: ${API_URL}`);

  try {
    // Step 1: Login
    const loggedIn = await login();
    if (!loggedIn) {
      console.log(`${colors.red}Login failed, cannot continue tests${colors.reset}`);
      return;
    }

    // Step 2: Test Dashboard
    const dashboardOk = await testDashboard();

    // Step 3: Test Notifications
    const notificationsOk = await testNotifications();

    // Summary
    console.log(`${colors.magenta}==============================================${colors.reset}`);
    console.log(`${colors.magenta}  Test Summary${colors.reset}`);
    console.log(`${colors.magenta}==============================================${colors.reset}`);
    console.log(`Login: ${loggedIn ? colors.green + '✓' : colors.red + '✗'}${colors.reset}`);
    console.log(`Dashboard: ${dashboardOk ? colors.green + '✓' : colors.red + '✗'}${colors.reset}`);
    console.log(`Notifications: ${notificationsOk ? colors.green + '✓' : colors.red + '✗'}${colors.reset}`);

    const overall = loggedIn && dashboardOk && notificationsOk;
    console.log(`${colors.magenta}==============================================${colors.reset}`);
    console.log(`Overall: ${overall ? colors.green + 'PASS' : colors.red + 'FAIL'}${colors.reset}`);
    console.log(`${colors.magenta}==============================================${colors.reset}`);

  } catch (error) {
    console.error(`${colors.red}Unexpected error in test suite:${colors.reset}`, error);
  }
}

// Run the tests
runTests();
