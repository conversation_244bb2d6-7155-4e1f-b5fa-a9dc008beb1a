import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const SessionExpiredDialog = () => {
  const { sessionExpired, clearSessionExpired, error } = useAuth();
  const navigate = useNavigate();

  const handleLogin = () => {
    clearSessionExpired();
    navigate('/login?reason=session-expired');
  };

  const handleClose = () => {
    clearSessionExpired();
  };

  return (
    <Dialog
      open={sessionExpired}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          🔒 Session Expirée
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="warning" sx={{ mb: 2 }}>
          Votre session a expiré pour des raisons de sécurité.
        </Alert>
        
        <Typography variant="body1" gutterBottom>
          {error || 'Veuillez vous reconnecter pour continuer à utiliser l\'application.'}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Cette mesure de sécurité protège vos données personnelles et garantit 
          que seuls les utilisateurs autorisés peuvent accéder à votre compte.
        </Typography>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} color="secondary">
          Fermer
        </Button>
        <Button 
          onClick={handleLogin} 
          variant="contained" 
          color="primary"
          autoFocus
        >
          Se reconnecter
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionExpiredDialog;
