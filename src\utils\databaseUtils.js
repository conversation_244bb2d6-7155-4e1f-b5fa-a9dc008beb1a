const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

async function addMissingColumns() {
  const queryInterface = sequelize.getQueryInterface();

  try {
    // Vérifier si la table orders existe
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('orders')) {
      console.log('La table orders n\'existe pas encore');
      return;
    }

    // Vérifier si la colonne numero_commande existe
    const columns = await queryInterface.describeTable('orders');
    
    if (!columns.numero_commande) {
      // Supprimer l'index s'il existe déjà
      try {
        await queryInterface.removeIndex('orders', 'orders_numero_commande');
      } catch (error) {
        console.log('L\'index n\'existait pas, continuation...');
      }

      // Ajouter la colonne numero_commande
      await queryInterface.addColumn('orders', 'numero_commande', {
        type: DataTypes.STRING(50),
        allowNull: true // Temporairement permettre null
      });

      // Mettre à jour les enregistrements existants avec une valeur par défaut
      await sequelize.query(
        `UPDATE orders SET numero_commande = CONCAT('CMD-', id) WHERE numero_commande IS NULL`
      );

      // Modifier la colonne pour ne plus permettre null
      await queryInterface.changeColumn('orders', 'numero_commande', {
        type: DataTypes.STRING(50),
        allowNull: false
      });

      // Ajouter l'index unique
      await queryInterface.addIndex('orders', ['numero_commande'], {
        unique: true,
        name: 'orders_numero_commande'
      });

      console.log('Colonne numero_commande ajoutée avec succès');
    }

    // Vérifier et ajouter la colonne prix dans la table volailles
    const volaillesColumns = await queryInterface.describeTable('volailles');
    if (!volaillesColumns.prix) {
      await queryInterface.addColumn('volailles', 'prix', {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true // Temporairement permettre null
      });

      // Mettre à jour les enregistrements existants avec une valeur par défaut
      await sequelize.query(
        `UPDATE volailles SET prix = 0 WHERE prix IS NULL`
      );

      // Modifier la colonne pour ne plus permettre null
      await queryInterface.changeColumn('volailles', 'prix', {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: false,
        defaultValue: 0
      });

      console.log('Colonne prix ajoutée à la table volailles avec succès');
    }

    // Vérifier et ajouter les colonnes timestamps dans la table volailles
    if (!volaillesColumns.created_at) {
      await queryInterface.addColumn('volailles', 'created_at', {
        type: DataTypes.DATE,
        allowNull: true
      });
      await sequelize.query(
        `UPDATE volailles SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL`
      );
      await queryInterface.changeColumn('volailles', 'created_at', {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
      });
    }

    if (!volaillesColumns.updated_at) {
      await queryInterface.addColumn('volailles', 'updated_at', {
        type: DataTypes.DATE,
        allowNull: true
      });
      await sequelize.query(
        `UPDATE volailles SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL`
      );
      await queryInterface.changeColumn('volailles', 'updated_at', {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
      });
    }

    // Ajouter d'autres vérifications de colonnes manquantes ici si nécessaire

  } catch (error) {
    console.error('Erreur lors de l\'ajout des colonnes manquantes:', error);
    throw error;
  }
}

module.exports = {
  addMissingColumns
};