--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)
-- Dumped by pg_dump version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: enum_alertes_stock_priorite; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_alertes_stock_priorite AS ENUM (
    'basse',
    'normale',
    'haute',
    'critique'
);


ALTER TYPE public.enum_alertes_stock_priorite OWNER TO postgres;

--
-- Name: enum_alertes_stock_statut; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_alertes_stock_statut AS ENUM (
    'active',
    'traitee',
    'archivee'
);


ALTER TYPE public.enum_alertes_stock_statut OWNER TO postgres;

--
-- Name: enum_alertes_stock_type_alerte; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_alertes_stock_type_alerte AS ENUM (
    'stock_faible',
    'production_baisse',
    'sante_animale',
    'maintenance_equipement',
    'autre'
);


ALTER TYPE public.enum_alertes_stock_type_alerte OWNER TO postgres;

--
-- Name: enum_volailles_statut; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_volailles_statut AS ENUM (
    'actif',
    'vendu',
    'abattu',
    'transfere',
    'archive'
);


ALTER TYPE public.enum_volailles_statut OWNER TO postgres;

--
-- Name: enum_volailles_type_elevage; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_volailles_type_elevage AS ENUM (
    'chair',
    'pondeuse',
    'reproducteur',
    'mixte'
);


ALTER TYPE public.enum_volailles_type_elevage OWNER TO postgres;

--
-- Name: update_modified_column(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_modified_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_modified_column() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ApiConfigs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."ApiConfigs" (
    id integer NOT NULL,
    "serviceName" character varying(255) NOT NULL,
    "apiKey" text NOT NULL,
    "apiSecret" text,
    "createdAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    description text,
    "isEnabled" boolean DEFAULT true
);


ALTER TABLE public."ApiConfigs" OWNER TO postgres;

--
-- Name: ApiConfigs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."ApiConfigs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."ApiConfigs_id_seq" OWNER TO postgres;

--
-- Name: ApiConfigs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."ApiConfigs_id_seq" OWNED BY public."ApiConfigs".id;


--
-- Name: SequelizeMeta; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."SequelizeMeta" (
    name character varying(255) NOT NULL
);


ALTER TABLE public."SequelizeMeta" OWNER TO postgres;

--
-- Name: Users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Users" (
    id integer NOT NULL,
    username character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    password character varying(255) NOT NULL,
    role character varying(20) DEFAULT 'eleveur'::character varying NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    profile_id integer,
    status character varying(20) DEFAULT 'active'::character varying,
    preferences jsonb DEFAULT '{}'::jsonb,
    "createdAt" timestamp with time zone NOT NULL,
    "updatedAt" timestamp with time zone NOT NULL,
    role_id integer,
    subscription_plan_id integer,
    firebase_uid character varying(255),
    phone character varying(50),
    address text
);


ALTER TABLE public."Users" OWNER TO postgres;

--
-- Name: Users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."Users_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."Users_id_seq" OWNER TO postgres;

--
-- Name: Users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."Users_id_seq" OWNED BY public."Users".id;


--
-- Name: alertes_stock; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alertes_stock (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    type_alerte public.enum_alertes_stock_type_alerte NOT NULL,
    priorite public.enum_alertes_stock_priorite DEFAULT 'normale'::public.enum_alertes_stock_priorite,
    statut public.enum_alertes_stock_statut DEFAULT 'active'::public.enum_alertes_stock_statut,
    titre character varying(200) NOT NULL,
    message text NOT NULL,
    message_ar text,
    donnees_contexte jsonb DEFAULT '{}'::jsonb,
    seuil_declenche jsonb DEFAULT '{"unite": null, "operateur": null, "valeur_seuil": null, "valeur_actuelle": null}'::jsonb,
    source_donnees jsonb DEFAULT '{"table_source": null, "champ_surveille": null, "derniere_valeur": null, "id_enregistrement": null}'::jsonb,
    actions_recommandees jsonb DEFAULT '[]'::jsonb,
    actions_entreprises jsonb DEFAULT '[]'::jsonb,
    date_declenchement timestamp with time zone NOT NULL,
    date_vue timestamp with time zone,
    date_traitee timestamp with time zone,
    date_expiration timestamp with time zone,
    frequence_rappel integer,
    nombre_rappels integer DEFAULT 0,
    dernier_rappel timestamp with time zone,
    canaux_notification jsonb DEFAULT '{"sms": false, "push": false, "email": false, "whatsapp": false, "dashboard": true}'::jsonb,
    notifications_envoyees jsonb DEFAULT '[]'::jsonb,
    impact_estime jsonb DEFAULT '{"financier": null, "production": null, "duree_estimee": null, "sante_animaux": null}'::jsonb,
    cout_inaction numeric(12,2),
    cout_resolution numeric(12,2),
    automatique boolean DEFAULT true,
    recurrente boolean DEFAULT false,
    conditions_resolution jsonb DEFAULT '{}'::jsonb,
    liens_utiles jsonb DEFAULT '[]'::jsonb,
    contacts_urgence jsonb DEFAULT '[]'::jsonb,
    historique_similaires jsonb DEFAULT '[]'::jsonb,
    feedback_eleveur jsonb DEFAULT '{"timing": null, "utilite": null, "precision": null, "suggestions": null}'::jsonb,
    tags jsonb DEFAULT '[]'::jsonb,
    visible boolean DEFAULT true,
    archivee boolean DEFAULT false,
    date_modification timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.alertes_stock OWNER TO postgres;

--
-- Name: COLUMN alertes_stock.eleveur_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.eleveur_id IS 'Référence vers l''éleveur';


--
-- Name: COLUMN alertes_stock.type_alerte; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.type_alerte IS 'Type d''alerte';


--
-- Name: COLUMN alertes_stock.priorite; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.priorite IS 'Niveau de priorité';


--
-- Name: COLUMN alertes_stock.statut; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.statut IS 'Statut de l''alerte';


--
-- Name: COLUMN alertes_stock.titre; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.titre IS 'Titre de l''alerte';


--
-- Name: COLUMN alertes_stock.message; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.message IS 'Message détaillé de l''alerte';


--
-- Name: COLUMN alertes_stock.message_ar; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.message_ar IS 'Message en arabe';


--
-- Name: COLUMN alertes_stock.donnees_contexte; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.donnees_contexte IS 'Données contextuelles de l''alerte';


--
-- Name: COLUMN alertes_stock.seuil_declenche; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.seuil_declenche IS 'Informations sur le seuil qui a déclenché l''alerte';


--
-- Name: COLUMN alertes_stock.source_donnees; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.source_donnees IS 'Source des données qui ont déclenché l''alerte';


--
-- Name: COLUMN alertes_stock.actions_recommandees; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.actions_recommandees IS 'Actions recommandées pour résoudre l''alerte';


--
-- Name: COLUMN alertes_stock.actions_entreprises; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.actions_entreprises IS 'Actions déjà entreprises par l''éleveur';


--
-- Name: COLUMN alertes_stock.date_declenchement; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.date_declenchement IS 'Date de déclenchement de l''alerte';


--
-- Name: COLUMN alertes_stock.date_vue; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.date_vue IS 'Date à laquelle l''alerte a été vue';


--
-- Name: COLUMN alertes_stock.date_traitee; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.date_traitee IS 'Date de traitement de l''alerte';


--
-- Name: COLUMN alertes_stock.date_expiration; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.date_expiration IS 'Date d''expiration de l''alerte';


--
-- Name: COLUMN alertes_stock.frequence_rappel; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.frequence_rappel IS 'Fréquence de rappel en heures';


--
-- Name: COLUMN alertes_stock.nombre_rappels; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.nombre_rappels IS 'Nombre de rappels envoyés';


--
-- Name: COLUMN alertes_stock.dernier_rappel; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.dernier_rappel IS 'Date du dernier rappel';


--
-- Name: COLUMN alertes_stock.canaux_notification; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.canaux_notification IS 'Canaux de notification activés';


--
-- Name: COLUMN alertes_stock.notifications_envoyees; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.notifications_envoyees IS 'Historique des notifications envoyées';


--
-- Name: COLUMN alertes_stock.impact_estime; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.impact_estime IS 'Impact estimé de l''alerte';


--
-- Name: COLUMN alertes_stock.cout_inaction; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.cout_inaction IS 'Coût estimé de l''inaction en DA';


--
-- Name: COLUMN alertes_stock.cout_resolution; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.cout_resolution IS 'Coût estimé de la résolution en DA';


--
-- Name: COLUMN alertes_stock.automatique; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.automatique IS 'Alerte générée automatiquement';


--
-- Name: COLUMN alertes_stock.recurrente; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.recurrente IS 'Alerte récurrente';


--
-- Name: COLUMN alertes_stock.conditions_resolution; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.conditions_resolution IS 'Conditions pour considérer l''alerte comme résolue';


--
-- Name: COLUMN alertes_stock.liens_utiles; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.liens_utiles IS 'Liens vers des ressources utiles';


--
-- Name: COLUMN alertes_stock.contacts_urgence; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.contacts_urgence IS 'Contacts d''urgence pour ce type d''alerte';


--
-- Name: COLUMN alertes_stock.historique_similaires; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.historique_similaires IS 'Références vers des alertes similaires passées';


--
-- Name: COLUMN alertes_stock.feedback_eleveur; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.feedback_eleveur IS 'Feedback de l''éleveur sur l''alerte';


--
-- Name: COLUMN alertes_stock.tags; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.tags IS 'Tags pour catégoriser l''alerte';


--
-- Name: COLUMN alertes_stock.visible; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.visible IS 'Alerte visible dans le dashboard';


--
-- Name: COLUMN alertes_stock.archivee; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.alertes_stock.archivee IS 'Alerte archivée';


--
-- Name: alertes_stock_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.alertes_stock_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.alertes_stock_id_seq OWNER TO postgres;

--
-- Name: alertes_stock_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.alertes_stock_id_seq OWNED BY public.alertes_stock.id;


--
-- Name: annonces; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annonces (
    id integer NOT NULL,
    titre character varying(255) NOT NULL,
    description text,
    categorie character varying(100) NOT NULL,
    prix numeric(10,2) NOT NULL,
    localisation character varying(255),
    images text[] DEFAULT '{}'::text[],
    est_active boolean DEFAULT true,
    utilisateur_id integer NOT NULL,
    date_creation timestamp with time zone,
    date_mise_a_jour timestamp with time zone
);


ALTER TABLE public.annonces OWNER TO postgres;

--
-- Name: annonces_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.annonces_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.annonces_id_seq OWNER TO postgres;

--
-- Name: annonces_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.annonces_id_seq OWNED BY public.annonces.id;


--
-- Name: blog_posts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.blog_posts (
    id integer NOT NULL,
    title character varying(200) NOT NULL,
    slug character varying(200) NOT NULL,
    content text NOT NULL,
    excerpt text,
    author_id integer,
    status character varying(20) DEFAULT 'draft'::character varying,
    tags jsonb DEFAULT '[]'::jsonb,
    featured_image character varying(255),
    published_at timestamp with time zone,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.blog_posts OWNER TO postgres;

--
-- Name: blog_posts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.blog_posts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.blog_posts_id_seq OWNER TO postgres;

--
-- Name: blog_posts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.blog_posts_id_seq OWNED BY public.blog_posts.id;


--
-- Name: clients; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.clients (
    id integer NOT NULL,
    marchand_id integer,
    user_id integer,
    name character varying(100) NOT NULL,
    email character varying(255),
    phone character varying(20),
    address text,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.clients OWNER TO postgres;

--
-- Name: TABLE clients; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.clients IS 'Clients des marchands';


--
-- Name: clients_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.clients_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.clients_id_seq OWNER TO postgres;

--
-- Name: clients_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.clients_id_seq OWNED BY public.clients.id;


--
-- Name: consultations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.consultations (
    id integer NOT NULL,
    veterinaire_id integer NOT NULL,
    eleveur_id integer NOT NULL,
    diagnostic text,
    notes text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    volaille_id integer NOT NULL,
    date timestamp with time zone,
    symptomes text,
    traitement text,
    statut character varying(20) DEFAULT 'en_cours'::character varying,
    cout numeric(10,2)
);


ALTER TABLE public.consultations OWNER TO postgres;

--
-- Name: TABLE consultations; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.consultations IS 'Consultations vétérinaires';


--
-- Name: consultations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.consultations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.consultations_id_seq OWNER TO postgres;

--
-- Name: consultations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.consultations_id_seq OWNED BY public.consultations.id;


--
-- Name: eleveurs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.eleveurs (
    id integer NOT NULL,
    nom character varying(100) NOT NULL,
    prenom character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    telephone character varying(20),
    adresse text,
    date_inscription timestamp with time zone NOT NULL,
    statut character varying(20) DEFAULT 'actif'::character varying,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    date_modification timestamp with time zone NOT NULL,
    user_id integer
);


ALTER TABLE public.eleveurs OWNER TO postgres;

--
-- Name: eleveurs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.eleveurs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.eleveurs_id_seq OWNER TO postgres;

--
-- Name: eleveurs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.eleveurs_id_seq OWNED BY public.eleveurs.id;


--
-- Name: favoris; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.favoris (
    id integer NOT NULL,
    utilisateur_id integer NOT NULL,
    annonce_id integer NOT NULL,
    date_ajout timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.favoris OWNER TO postgres;

--
-- Name: TABLE favoris; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.favoris IS 'Annonces favorites des utilisateurs';


--
-- Name: favoris_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.favoris_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.favoris_id_seq OWNER TO postgres;

--
-- Name: favoris_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.favoris_id_seq OWNED BY public.favoris.id;


--
-- Name: feed_alerts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_alerts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.feed_alerts_id_seq OWNER TO postgres;

--
-- Name: feed_alerts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_alerts (
    id integer DEFAULT nextval('public.feed_alerts_id_seq'::regclass) NOT NULL,
    farm_id integer NOT NULL,
    feed_stock_id integer,
    alert_type character varying(50) NOT NULL,
    message text NOT NULL,
    severity character varying(20) DEFAULT 'medium'::character varying NOT NULL,
    is_read boolean DEFAULT false NOT NULL,
    is_resolved boolean DEFAULT false NOT NULL,
    resolved_at timestamp with time zone,
    resolved_by integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.feed_alerts OWNER TO postgres;

--
-- Name: COLUMN feed_alerts.alert_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_alerts.alert_type IS 'low_stock, expiry_warning, out_of_stock';


--
-- Name: COLUMN feed_alerts.severity; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_alerts.severity IS 'low, medium, high, critical';


--
-- Name: feed_composition_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_composition_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.feed_composition_id_seq OWNER TO postgres;

--
-- Name: feed_composition; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_composition (
    id integer DEFAULT nextval('public.feed_composition_id_seq'::regclass) NOT NULL,
    feed_plan_id integer NOT NULL,
    feed_item_id integer NOT NULL,
    age_week_start integer NOT NULL,
    age_week_end integer NOT NULL,
    daily_quantity_per_bird numeric(8,3) NOT NULL,
    percentage_of_diet numeric(5,2),
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.feed_composition OWNER TO postgres;

--
-- Name: COLUMN feed_composition.age_week_start; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_composition.age_week_start IS 'starting week for this feed';


--
-- Name: COLUMN feed_composition.age_week_end; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_composition.age_week_end IS 'ending week for this feed';


--
-- Name: COLUMN feed_composition.daily_quantity_per_bird; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_composition.daily_quantity_per_bird IS 'daily quantity per bird in grams';


--
-- Name: COLUMN feed_composition.percentage_of_diet; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_composition.percentage_of_diet IS 'percentage of total diet (0-100)';


--
-- Name: feed_consumption_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_consumption_logs (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    feed_plan_id integer,
    date date NOT NULL,
    quantite_distribuee numeric(10,2) NOT NULL,
    quantite_consommee numeric(10,2),
    gaspillage numeric(10,2),
    observations text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.feed_consumption_logs OWNER TO postgres;

--
-- Name: TABLE feed_consumption_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.feed_consumption_logs IS 'Logs de consommation d''aliments';


--
-- Name: feed_consumption_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_consumption_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.feed_consumption_logs_id_seq OWNER TO postgres;

--
-- Name: feed_consumption_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.feed_consumption_logs_id_seq OWNED BY public.feed_consumption_logs.id;


--
-- Name: feed_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.feed_items_id_seq OWNER TO postgres;

--
-- Name: feed_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_items (
    id integer DEFAULT nextval('public.feed_items_id_seq'::regclass) NOT NULL,
    name character varying(200) NOT NULL,
    brand character varying(100) NOT NULL,
    category character varying(50) NOT NULL,
    description text,
    unit_of_measure character varying(20) DEFAULT 'kg'::character varying NOT NULL,
    nutritional_info jsonb DEFAULT '{}'::jsonb,
    recommended_age_min integer,
    recommended_age_max integer,
    poultry_types jsonb DEFAULT '[]'::jsonb NOT NULL,
    daily_consumption_per_bird numeric(8,3),
    storage_instructions text,
    shelf_life_days integer DEFAULT 90,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.feed_items OWNER TO postgres;

--
-- Name: COLUMN feed_items.category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.category IS 'starter, grower, finisher, layer, breeder';


--
-- Name: COLUMN feed_items.unit_of_measure; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.unit_of_measure IS 'kg, tonnes, sacs';


--
-- Name: COLUMN feed_items.nutritional_info; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.nutritional_info IS 'protein, energy, fiber, etc.';


--
-- Name: COLUMN feed_items.recommended_age_min; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.recommended_age_min IS 'minimum age in days';


--
-- Name: COLUMN feed_items.recommended_age_max; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.recommended_age_max IS 'maximum age in days';


--
-- Name: COLUMN feed_items.poultry_types; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.poultry_types IS 'poulets, dindes, pondeuses, etc.';


--
-- Name: COLUMN feed_items.daily_consumption_per_bird; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_items.daily_consumption_per_bird IS 'average daily consumption per bird in grams';


--
-- Name: feed_plans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_plans (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    nom_plan character varying(200) NOT NULL,
    type_aliment character varying(100) NOT NULL,
    quantite_journaliere numeric(10,2) NOT NULL,
    heure_distribution time without time zone,
    batiment_cible character varying(100),
    date_debut date NOT NULL,
    date_fin date,
    est_actif boolean DEFAULT true,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.feed_plans OWNER TO postgres;

--
-- Name: TABLE feed_plans; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.feed_plans IS 'Plans d''alimentation pour les volailles';


--
-- Name: feed_plans_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_plans_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.feed_plans_id_seq OWNER TO postgres;

--
-- Name: feed_plans_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.feed_plans_id_seq OWNED BY public.feed_plans.id;


--
-- Name: feed_stock_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_stock_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.feed_stock_id_seq OWNER TO postgres;

--
-- Name: feed_stock; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_stock (
    id integer DEFAULT nextval('public.feed_stock_id_seq'::regclass) NOT NULL,
    farm_id integer NOT NULL,
    feed_item_id integer NOT NULL,
    supplier_id integer,
    batch_number character varying(50),
    quantity_received numeric(10,3) NOT NULL,
    quantity_current numeric(10,3) NOT NULL,
    unit_cost numeric(10,2) NOT NULL,
    total_cost numeric(12,2) NOT NULL,
    purchase_date date NOT NULL,
    expiry_date date,
    storage_location character varying(100),
    minimum_stock_alert numeric(10,3),
    notes text,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.feed_stock OWNER TO postgres;

--
-- Name: COLUMN feed_stock.quantity_received; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.quantity_received IS 'initial quantity received';


--
-- Name: COLUMN feed_stock.quantity_current; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.quantity_current IS 'current available quantity';


--
-- Name: COLUMN feed_stock.unit_cost; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.unit_cost IS 'cost per unit';


--
-- Name: COLUMN feed_stock.total_cost; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.total_cost IS 'total cost of the batch';


--
-- Name: COLUMN feed_stock.minimum_stock_alert; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.minimum_stock_alert IS 'minimum quantity before alert';


--
-- Name: COLUMN feed_stock.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_stock.status IS 'active, depleted, expired';


--
-- Name: feed_suppliers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.feed_suppliers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.feed_suppliers_id_seq OWNER TO postgres;

--
-- Name: feed_suppliers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.feed_suppliers (
    id integer DEFAULT nextval('public.feed_suppliers_id_seq'::regclass) NOT NULL,
    name character varying(200) NOT NULL,
    contact_person character varying(100),
    phone character varying(20),
    email character varying(255),
    address text,
    wilaya character varying(50),
    commune character varying(50),
    delivery_zones jsonb DEFAULT '[]'::jsonb,
    payment_terms character varying(100),
    credit_limit numeric(12,2),
    rating numeric(3,2),
    notes text,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.feed_suppliers OWNER TO postgres;

--
-- Name: COLUMN feed_suppliers.rating; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.feed_suppliers.rating IS 'rating from 1.00 to 5.00';


--
-- Name: fermes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.fermes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.fermes_id_seq OWNER TO postgres;

--
-- Name: fermes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.fermes (
    id integer DEFAULT nextval('public.fermes_id_seq'::regclass) NOT NULL,
    eleveur_id integer NOT NULL,
    nom character varying(200) NOT NULL,
    adresse text,
    superficie numeric(10,2),
    capacite_maximale integer,
    type_elevage character varying(50),
    coordonnees_gps jsonb DEFAULT '{}'::jsonb,
    equipements jsonb DEFAULT '[]'::jsonb,
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.fermes OWNER TO postgres;

--
-- Name: TABLE fermes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.fermes IS 'Table des fermes gÃ©rÃ©es par les Ã©leveurs';


--
-- Name: fermes_ouvriers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.fermes_ouvriers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.fermes_ouvriers_id_seq OWNER TO postgres;

--
-- Name: fermes_ouvriers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.fermes_ouvriers (
    id integer DEFAULT nextval('public.fermes_ouvriers_id_seq'::regclass) NOT NULL,
    ouvrier_id integer NOT NULL,
    ferme_id integer NOT NULL,
    date_assignation timestamp with time zone DEFAULT now(),
    permissions jsonb DEFAULT '{"lecture": true, "ecriture": true, "saisie_quotidienne": true}'::jsonb,
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.fermes_ouvriers OWNER TO postgres;

--
-- Name: TABLE fermes_ouvriers; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.fermes_ouvriers IS 'Association entre ouvriers et fermes';


--
-- Name: general_config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.general_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.general_config_id_seq OWNER TO postgres;

--
-- Name: general_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.general_config (
    id integer DEFAULT nextval('public.general_config_id_seq'::regclass) NOT NULL,
    "siteName" character varying(255) DEFAULT 'Poultray DZ'::character varying NOT NULL,
    "siteDescription" text,
    "contactEmail" character varying(255),
    "contactPhone" character varying(255),
    address text,
    logo character varying(255),
    favicon character varying(255),
    "primaryColor" character varying(255) DEFAULT '#2c5530'::character varying,
    "secondaryColor" character varying(255) DEFAULT '#e7eae2'::character varying,
    "defaultLanguage" character varying(255) DEFAULT 'fr'::character varying NOT NULL,
    "availableLanguages" text DEFAULT '["fr","ar","en"]'::text NOT NULL,
    "dateFormat" character varying(255) DEFAULT 'DD/MM/YYYY'::character varying,
    "timeFormat" character varying(255) DEFAULT 'HH:mm'::character varying,
    timezone character varying(255) DEFAULT 'Africa/Algiers'::character varying,
    "maintenanceMode" boolean DEFAULT false NOT NULL,
    "maintenanceMessage" text,
    "googleAnalyticsId" character varying(255),
    "facebookPixelId" character varying(255),
    "maxUploadSize" integer DEFAULT 5,
    "allowUserRegistration" boolean DEFAULT true NOT NULL,
    "defaultUserRole" character varying(255) DEFAULT 'user'::character varying,
    "footerText" text DEFAULT '© Poultray DZ'::text,
    "socialLinks" text,
    "createdAt" timestamp with time zone NOT NULL,
    "updatedAt" timestamp with time zone NOT NULL
);


ALTER TABLE public.general_config OWNER TO postgres;

--
-- Name: homepage_sections_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.homepage_sections_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.homepage_sections_id_seq OWNER TO postgres;

--
-- Name: marchands; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.marchands (
    id integer NOT NULL,
    nom_entreprise character varying(200) NOT NULL,
    contact_nom character varying(100),
    contact_prenom character varying(100),
    email character varying(255) NOT NULL,
    telephone character varying(20),
    adresse text,
    date_inscription timestamp with time zone NOT NULL,
    statut character varying(20) DEFAULT 'actif'::character varying,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    user_id integer
);


ALTER TABLE public.marchands OWNER TO postgres;

--
-- Name: marchands_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.marchands_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.marchands_id_seq OWNER TO postgres;

--
-- Name: marchands_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.marchands_id_seq OWNED BY public.marchands.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    user_id integer NOT NULL,
    type character varying(50) NOT NULL,
    message text NOT NULL,
    is_read boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: TABLE notifications; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.notifications IS 'Notifications pour les utilisateurs';


--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.order_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.order_items_id_seq OWNER TO postgres;

--
-- Name: order_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.order_items (
    id integer DEFAULT nextval('public.order_items_id_seq'::regclass) NOT NULL,
    order_id integer,
    product_id integer,
    quantity integer NOT NULL,
    unit_price numeric(10,2) NOT NULL,
    total_price numeric(10,2) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.order_items OWNER TO postgres;

--
-- Name: TABLE order_items; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.order_items IS 'Articles dans les commandes';


--
-- Name: orders_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.orders_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.orders_id_seq OWNER TO postgres;

--
-- Name: orders; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.orders (
    id integer DEFAULT nextval('public.orders_id_seq'::regclass) NOT NULL,
    marchand_id integer,
    client_id integer,
    order_number character varying(50) NOT NULL,
    total_amount numeric(10,2) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    payment_status character varying(20) DEFAULT 'unpaid'::character varying,
    payment_method character varying(50),
    shipping_address text,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    tracking_number character varying(50),
    shipping_method character varying(50),
    shipping_cost numeric(10,2) DEFAULT '0'::numeric,
    tax_amount numeric(10,2) DEFAULT '0'::numeric,
    estimated_delivery_date timestamp with time zone,
    actual_delivery_date timestamp with time zone
);


ALTER TABLE public.orders OWNER TO postgres;

--
-- Name: TABLE orders; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.orders IS 'Commandes passÃ©es aux marchands';


--
-- Name: ouvriers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ouvriers (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    nom character varying(100) NOT NULL,
    prenom character varying(100) NOT NULL,
    role character varying(100),
    telephone character varying(20),
    date_embauche date,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.ouvriers OWNER TO postgres;

--
-- Name: TABLE ouvriers; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ouvriers IS 'Ouvriers travaillant pour les éleveurs';


--
-- Name: ouvriers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ouvriers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.ouvriers_id_seq OWNER TO postgres;

--
-- Name: ouvriers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ouvriers_id_seq OWNED BY public.ouvriers.id;


--
-- Name: poussins; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.poussins (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    date_arrivee date NOT NULL,
    nombre_initial integer NOT NULL,
    race character varying(100),
    fournisseur character varying(200),
    mortalite_journaliere integer DEFAULT 0,
    poids_moyen_journalier numeric(10,2),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.poussins OWNER TO postgres;

--
-- Name: TABLE poussins; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.poussins IS 'Suivi des poussins';


--
-- Name: poussins_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.poussins_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.poussins_id_seq OWNER TO postgres;

--
-- Name: poussins_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.poussins_id_seq OWNED BY public.poussins.id;


--
-- Name: prescriptions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.prescriptions (
    id integer NOT NULL,
    consultation_id integer NOT NULL,
    medicament character varying(200) NOT NULL,
    dosage character varying(100),
    frequence character varying(100),
    duree character varying(100),
    notes text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.prescriptions OWNER TO postgres;

--
-- Name: TABLE prescriptions; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.prescriptions IS 'Prescriptions vétérinaires';


--
-- Name: prescriptions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.prescriptions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.prescriptions_id_seq OWNER TO postgres;

--
-- Name: prescriptions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.prescriptions_id_seq OWNED BY public.prescriptions.id;


--
-- Name: product_views_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.product_views_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.product_views_id_seq OWNER TO postgres;

--
-- Name: product_views; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.product_views (
    id integer DEFAULT nextval('public.product_views_id_seq'::regclass) NOT NULL,
    product_id integer,
    user_id integer,
    ip_address character varying(50),
    view_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.product_views OWNER TO postgres;

--
-- Name: TABLE product_views; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.product_views IS 'Statistiques de consultation des produits';


--
-- Name: production_oeufs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.production_oeufs (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    date date NOT NULL,
    nombre_oeufs integer NOT NULL,
    nombre_poules_pondeuses integer,
    taux_ponte numeric(5,2),
    observations text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.production_oeufs OWNER TO postgres;

--
-- Name: TABLE production_oeufs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.production_oeufs IS 'Enregistrements de production d''œufs';


--
-- Name: production_oeufs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.production_oeufs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.production_oeufs_id_seq OWNER TO postgres;

--
-- Name: production_oeufs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.production_oeufs_id_seq OWNED BY public.production_oeufs.id;


--
-- Name: products_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.products_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.products_id_seq OWNER TO postgres;

--
-- Name: produits; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.produits (
    id integer NOT NULL,
    marchand_id integer NOT NULL,
    nom character varying(200) NOT NULL,
    description text,
    categorie character varying(100),
    prix numeric(10,2) NOT NULL,
    quantite_stock integer NOT NULL,
    unite_mesure character varying(50),
    images text[] DEFAULT '{}'::text[],
    est_disponible boolean DEFAULT true,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.produits OWNER TO postgres;

--
-- Name: TABLE produits; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.produits IS 'Produits vendus par les marchands';


--
-- Name: produits_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.produits_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.produits_id_seq OWNER TO postgres;

--
-- Name: produits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.produits_id_seq OWNED BY public.produits.id;


--
-- Name: roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.roles (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.roles OWNER TO postgres;

--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.roles_id_seq OWNER TO postgres;

--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: saisies_quotidiennes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.saisies_quotidiennes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.saisies_quotidiennes_id_seq OWNER TO postgres;

--
-- Name: saisies_quotidiennes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.saisies_quotidiennes (
    id integer DEFAULT nextval('public.saisies_quotidiennes_id_seq'::regclass) NOT NULL,
    eleveur_id integer NOT NULL,
    ouvrier_id integer NOT NULL,
    ferme_id integer,
    volaille_id integer,
    date_saisie date NOT NULL,
    nombre_morts integer DEFAULT 0,
    nombre_malades integer DEFAULT 0,
    temperature_moyenne numeric(5,2),
    humidite_moyenne numeric(5,2),
    consommation_eau numeric(10,2),
    consommation_aliment numeric(10,2),
    incidents text,
    besoins_materiels text,
    observations text,
    donnees_supplementaires jsonb DEFAULT '{}'::jsonb,
    valide_par_eleveur boolean DEFAULT false,
    date_validation timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.saisies_quotidiennes OWNER TO postgres;

--
-- Name: TABLE saisies_quotidiennes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.saisies_quotidiennes IS 'Saisies quotidiennes effectuÃ©es par les ouvriers';


--
-- Name: security_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.security_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.security_settings_id_seq OWNER TO postgres;

--
-- Name: smtp_configurations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.smtp_configurations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.smtp_configurations_id_seq OWNER TO postgres;

--
-- Name: smtp_configurations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.smtp_configurations (
    id integer DEFAULT nextval('public.smtp_configurations_id_seq'::regclass) NOT NULL,
    host character varying(255) NOT NULL,
    port integer NOT NULL,
    secure boolean DEFAULT true,
    "user" character varying(255) NOT NULL,
    pass character varying(255) NOT NULL,
    "fromName" character varying(255),
    "fromEmail" character varying(255) NOT NULL,
    "replyTo" character varying(255),
    "testEmailRecipient" character varying(255),
    "isEnabled" boolean DEFAULT true,
    "createdAt" timestamp with time zone NOT NULL,
    "updatedAt" timestamp with time zone NOT NULL
);


ALTER TABLE public.smtp_configurations OWNER TO postgres;

--
-- Name: subscription_plans_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.subscription_plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.subscription_plans_id_seq OWNER TO postgres;

--
-- Name: subscription_plans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.subscription_plans (
    id integer DEFAULT nextval('public.subscription_plans_id_seq'::regclass) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    price numeric(10,2) NOT NULL,
    duration_days integer NOT NULL,
    features jsonb DEFAULT '[]'::jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.subscription_plans OWNER TO postgres;

--
-- Name: suivi_veterinaire; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.suivi_veterinaire (
    id integer NOT NULL,
    veterinaire_id integer NOT NULL,
    eleveur_id integer NOT NULL,
    date_visite date NOT NULL,
    observations text,
    recommandations text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.suivi_veterinaire OWNER TO postgres;

--
-- Name: TABLE suivi_veterinaire; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.suivi_veterinaire IS 'Suivi des visites vétérinaires';


--
-- Name: suivi_veterinaire_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.suivi_veterinaire_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.suivi_veterinaire_id_seq OWNER TO postgres;

--
-- Name: suivi_veterinaire_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.suivi_veterinaire_id_seq OWNED BY public.suivi_veterinaire.id;


--
-- Name: translations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.translations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.translations_id_seq OWNER TO postgres;

--
-- Name: translations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.translations (
    id integer DEFAULT nextval('public.translations_id_seq'::regclass) NOT NULL,
    key character varying(255) NOT NULL,
    value text NOT NULL,
    language character varying(10) DEFAULT 'fr'::character varying NOT NULL,
    category character varying(100) DEFAULT 'general'::character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.translations OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer DEFAULT nextval('public.users_id_seq'::regclass) NOT NULL,
    username character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    password character varying(255) NOT NULL,
    role character varying(20) DEFAULT 'eleveur'::character varying NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    profile_id integer,
    created_at timestamp with time zone NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying,
    preferences jsonb DEFAULT '{}'::jsonb,
    updated_at timestamp with time zone NOT NULL,
    role_id integer,
    firebase_uid character varying(255),
    phone character varying(50),
    address text
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: ventes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ventes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER TABLE public.ventes_id_seq OWNER TO postgres;

--
-- Name: ventes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ventes (
    id integer DEFAULT nextval('public.ventes_id_seq'::regclass) NOT NULL,
    eleveur_id integer,
    acheteur_id integer,
    volaille_id integer,
    quantite integer NOT NULL,
    prix_unitaire numeric(10,2) NOT NULL,
    montant_total numeric(10,2) NOT NULL,
    date_vente timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    statut character varying(20) DEFAULT 'completee'::character varying,
    notes text
);


ALTER TABLE public.ventes OWNER TO postgres;

--
-- Name: veterinaires; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.veterinaires (
    id integer NOT NULL,
    nom character varying(100) NOT NULL,
    prenom character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    telephone character varying(20),
    adresse_cabinet text,
    date_inscription timestamp with time zone NOT NULL,
    statut character varying(20) DEFAULT 'actif'::character varying,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    user_id integer
);


ALTER TABLE public.veterinaires OWNER TO postgres;

--
-- Name: veterinaires_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.veterinaires_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.veterinaires_id_seq OWNER TO postgres;

--
-- Name: veterinaires_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.veterinaires_id_seq OWNED BY public.veterinaires.id;


--
-- Name: volailles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.volailles (
    id integer NOT NULL,
    eleveur_id integer NOT NULL,
    type_volaille character varying(100) NOT NULL,
    race character varying(100),
    date_naissance date,
    sexe character varying(10),
    statut_sante character varying(50) DEFAULT 'sain'::character varying,
    poids_actuel numeric(10,2),
    date_acquisition date,
    source_acquisition character varying(200),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    date_modification timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lot_numero character varying(50),
    type_elevage public.enum_volailles_type_elevage DEFAULT 'chair'::public.enum_volailles_type_elevage,
    statut public.enum_volailles_statut DEFAULT 'actif'::public.enum_volailles_statut
);


ALTER TABLE public.volailles OWNER TO postgres;

--
-- Name: TABLE volailles; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.volailles IS 'Informations sur les volailles';


--
-- Name: volailles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.volailles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.volailles_id_seq OWNER TO postgres;

--
-- Name: volailles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.volailles_id_seq OWNED BY public.volailles.id;


--
-- Name: ApiConfigs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ApiConfigs" ALTER COLUMN id SET DEFAULT nextval('public."ApiConfigs_id_seq"'::regclass);


--
-- Name: Users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Users" ALTER COLUMN id SET DEFAULT nextval('public."Users_id_seq"'::regclass);


--
-- Name: alertes_stock id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alertes_stock ALTER COLUMN id SET DEFAULT nextval('public.alertes_stock_id_seq'::regclass);


--
-- Name: annonces id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annonces ALTER COLUMN id SET DEFAULT nextval('public.annonces_id_seq'::regclass);


--
-- Name: blog_posts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.blog_posts ALTER COLUMN id SET DEFAULT nextval('public.blog_posts_id_seq'::regclass);


--
-- Name: clients id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients ALTER COLUMN id SET DEFAULT nextval('public.clients_id_seq'::regclass);


--
-- Name: consultations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.consultations ALTER COLUMN id SET DEFAULT nextval('public.consultations_id_seq'::regclass);


--
-- Name: eleveurs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.eleveurs ALTER COLUMN id SET DEFAULT nextval('public.eleveurs_id_seq'::regclass);


--
-- Name: favoris id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.favoris ALTER COLUMN id SET DEFAULT nextval('public.favoris_id_seq'::regclass);


--
-- Name: feed_consumption_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_consumption_logs ALTER COLUMN id SET DEFAULT nextval('public.feed_consumption_logs_id_seq'::regclass);


--
-- Name: feed_plans id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_plans ALTER COLUMN id SET DEFAULT nextval('public.feed_plans_id_seq'::regclass);


--
-- Name: marchands id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.marchands ALTER COLUMN id SET DEFAULT nextval('public.marchands_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: ouvriers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ouvriers ALTER COLUMN id SET DEFAULT nextval('public.ouvriers_id_seq'::regclass);


--
-- Name: poussins id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.poussins ALTER COLUMN id SET DEFAULT nextval('public.poussins_id_seq'::regclass);


--
-- Name: prescriptions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.prescriptions ALTER COLUMN id SET DEFAULT nextval('public.prescriptions_id_seq'::regclass);


--
-- Name: production_oeufs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.production_oeufs ALTER COLUMN id SET DEFAULT nextval('public.production_oeufs_id_seq'::regclass);


--
-- Name: produits id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.produits ALTER COLUMN id SET DEFAULT nextval('public.produits_id_seq'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: suivi_veterinaire id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suivi_veterinaire ALTER COLUMN id SET DEFAULT nextval('public.suivi_veterinaire_id_seq'::regclass);


--
-- Name: veterinaires id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.veterinaires ALTER COLUMN id SET DEFAULT nextval('public.veterinaires_id_seq'::regclass);


--
-- Name: volailles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.volailles ALTER COLUMN id SET DEFAULT nextval('public.volailles_id_seq'::regclass);


--
-- Data for Name: ApiConfigs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."ApiConfigs" (id, "serviceName", "apiKey", "apiSecret", "createdAt", "updatedAt", description, "isEnabled") FROM stdin;
1	openai		\N	2025-06-20 15:05:48.94-04	2025-06-20 15:05:48.94-04	\N	t
2	google		\N	2025-06-20 15:05:48.953-04	2025-06-20 15:05:48.953-04	\N	t
3	azure_openai		\N	2025-06-20 15:05:48.958-04	2025-06-20 15:05:48.958-04	\N	t
4	claude		\N	2025-06-20 15:05:48.962-04	2025-06-20 15:05:48.962-04	\N	t
5	gemini		\N	2025-06-20 15:05:48.966-04	2025-06-20 15:05:48.966-04	\N	t
\.


--
-- Data for Name: SequelizeMeta; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."SequelizeMeta" (name) FROM stdin;
\.


--
-- Data for Name: Users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Users" (id, username, email, password, role, first_name, last_name, profile_id, status, preferences, "createdAt", "updatedAt", role_id, subscription_plan_id, firebase_uid, phone, address) FROM stdin;
\.


--
-- Data for Name: alertes_stock; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alertes_stock (id, eleveur_id, type_alerte, priorite, statut, titre, message, message_ar, donnees_contexte, seuil_declenche, source_donnees, actions_recommandees, actions_entreprises, date_declenchement, date_vue, date_traitee, date_expiration, frequence_rappel, nombre_rappels, dernier_rappel, canaux_notification, notifications_envoyees, impact_estime, cout_inaction, cout_resolution, automatique, recurrente, conditions_resolution, liens_utiles, contacts_urgence, historique_similaires, feedback_eleveur, tags, visible, archivee, date_modification, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: annonces; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annonces (id, titre, description, categorie, prix, localisation, images, est_active, utilisateur_id, date_creation, date_mise_a_jour) FROM stdin;
\.


--
-- Data for Name: blog_posts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.blog_posts (id, title, slug, content, excerpt, author_id, status, tags, featured_image, published_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: clients; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.clients (id, marchand_id, user_id, name, email, phone, address, notes, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: consultations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.consultations (id, veterinaire_id, eleveur_id, diagnostic, notes, created_at, updated_at, volaille_id, date, symptomes, traitement, statut, cout) FROM stdin;
\.


--
-- Data for Name: eleveurs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.eleveurs (id, nom, prenom, email, telephone, adresse, date_inscription, statut, created_at, updated_at, date_modification, user_id) FROM stdin;
\.


--
-- Data for Name: favoris; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.favoris (id, utilisateur_id, annonce_id, date_ajout) FROM stdin;
\.


--
-- Data for Name: feed_alerts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_alerts (id, farm_id, feed_stock_id, alert_type, message, severity, is_read, is_resolved, resolved_at, resolved_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_composition; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_composition (id, feed_plan_id, feed_item_id, age_week_start, age_week_end, daily_quantity_per_bird, percentage_of_diet, notes, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_consumption_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_consumption_logs (id, eleveur_id, feed_plan_id, date, quantite_distribuee, quantite_consommee, gaspillage, observations, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_items; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_items (id, name, brand, category, description, unit_of_measure, nutritional_info, recommended_age_min, recommended_age_max, poultry_types, daily_consumption_per_bird, storage_instructions, shelf_life_days, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_plans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_plans (id, eleveur_id, nom_plan, type_aliment, quantite_journaliere, heure_distribution, batiment_cible, date_debut, date_fin, est_actif, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_stock; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_stock (id, farm_id, feed_item_id, supplier_id, batch_number, quantity_received, quantity_current, unit_cost, total_cost, purchase_date, expiry_date, storage_location, minimum_stock_alert, notes, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: feed_suppliers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.feed_suppliers (id, name, contact_person, phone, email, address, wilaya, commune, delivery_zones, payment_terms, credit_limit, rating, notes, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: fermes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.fermes (id, eleveur_id, nom, adresse, superficie, capacite_maximale, type_elevage, coordonnees_gps, equipements, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: fermes_ouvriers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.fermes_ouvriers (id, ouvrier_id, ferme_id, date_assignation, permissions, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: general_config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.general_config (id, "siteName", "siteDescription", "contactEmail", "contactPhone", address, logo, favicon, "primaryColor", "secondaryColor", "defaultLanguage", "availableLanguages", "dateFormat", "timeFormat", timezone, "maintenanceMode", "maintenanceMessage", "googleAnalyticsId", "facebookPixelId", "maxUploadSize", "allowUserRegistration", "defaultUserRole", "footerText", "socialLinks", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: marchands; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.marchands (id, nom_entreprise, contact_nom, contact_prenom, email, telephone, adresse, date_inscription, statut, created_at, updated_at, user_id) FROM stdin;
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, user_id, type, message, is_read, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.order_items (id, order_id, product_id, quantity, unit_price, total_price, created_at) FROM stdin;
\.


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.orders (id, marchand_id, client_id, order_number, total_amount, status, payment_status, payment_method, shipping_address, notes, created_at, updated_at, tracking_number, shipping_method, shipping_cost, tax_amount, estimated_delivery_date, actual_delivery_date) FROM stdin;
\.


--
-- Data for Name: ouvriers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ouvriers (id, eleveur_id, nom, prenom, role, telephone, date_embauche, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: poussins; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.poussins (id, eleveur_id, date_arrivee, nombre_initial, race, fournisseur, mortalite_journaliere, poids_moyen_journalier, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: prescriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.prescriptions (id, consultation_id, medicament, dosage, frequence, duree, notes, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: product_views; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.product_views (id, product_id, user_id, ip_address, view_date) FROM stdin;
\.


--
-- Data for Name: production_oeufs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.production_oeufs (id, eleveur_id, date, nombre_oeufs, nombre_poules_pondeuses, taux_ponte, observations, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: produits; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.produits (id, marchand_id, nom, description, categorie, prix, quantite_stock, unite_mesure, images, est_disponible, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.roles (id, name, description, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: saisies_quotidiennes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.saisies_quotidiennes (id, eleveur_id, ouvrier_id, ferme_id, volaille_id, date_saisie, nombre_morts, nombre_malades, temperature_moyenne, humidite_moyenne, consommation_eau, consommation_aliment, incidents, besoins_materiels, observations, donnees_supplementaires, valide_par_eleveur, date_validation, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: smtp_configurations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.smtp_configurations (id, host, port, secure, "user", pass, "fromName", "fromEmail", "replyTo", "testEmailRecipient", "isEnabled", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: subscription_plans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.subscription_plans (id, name, description, price, duration_days, features, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: suivi_veterinaire; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.suivi_veterinaire (id, veterinaire_id, eleveur_id, date_visite, observations, recommandations, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: translations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.translations (id, key, value, language, category, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, username, email, password, role, first_name, last_name, profile_id, created_at, status, preferences, updated_at, role_id, firebase_uid, phone, address) FROM stdin;
2	eleveur	<EMAIL>	$2a$10$fcVrnlT6xUqW4QByKxPhHuCM1zt8rbME9Ic3stlW2b7RGhwRPGDEm	eleveur			\N	2025-05-12 19:21:44.316383-04	active	{}	2025-06-15 19:22:51.297-04	15	\N	\N	\N
1	admin	<EMAIL>	$2a$10$hVuz3sEoWGBLNJsIc3y6qOZGHLzsFJ0lXi01jfdwpqhcgYWQAaFfG	admin	Admin	Poultray	\N	2025-05-11 16:17:51.627143-04	active	{}	2025-06-16 18:35:46.379-04	1	96CSpHFAoKaAvBD018vRtRe8qW93	\N	\N
6	marchand	<EMAIL>	$2a$10$0Z3zFxovaLRTKCZqYUYK5u3MBqZ3k/eZSn5zqmufF7qvG8WVZeKKe	eleveur			\N	2025-06-16 18:52:20.797-04	active	{}	2025-06-16 18:52:20.797-04	17	xuWu3IFPx1eZPgUzSPGO3IGBNG32		
8	vetirinaire	<EMAIL>	$2a$10$.E8P3ZRU7VwKaDwBAZd1tewj0C.1mA9nhVCZgbJWFWXg.C00pOmHG	eleveur			\N	2025-06-17 18:47:44.955-04	active	{}	2025-06-17 18:47:44.955-04	16	gePyjBdy0uUKiTiQmV6KxmreNqz2		
\.


--
-- Data for Name: ventes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ventes (id, eleveur_id, acheteur_id, volaille_id, quantite, prix_unitaire, montant_total, date_vente, statut, notes) FROM stdin;
\.


--
-- Data for Name: veterinaires; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.veterinaires (id, nom, prenom, email, telephone, adresse_cabinet, date_inscription, statut, created_at, updated_at, user_id) FROM stdin;
\.


--
-- Data for Name: volailles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.volailles (id, eleveur_id, type_volaille, race, date_naissance, sexe, statut_sante, poids_actuel, date_acquisition, source_acquisition, created_at, updated_at, date_modification, lot_numero, type_elevage, statut) FROM stdin;
\.


--
-- Name: ApiConfigs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."ApiConfigs_id_seq"', 5, true);


--
-- Name: Users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."Users_id_seq"', 1, false);


--
-- Name: alertes_stock_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.alertes_stock_id_seq', 1, false);


--
-- Name: annonces_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.annonces_id_seq', 1, false);


--
-- Name: blog_posts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.blog_posts_id_seq', 1, false);


--
-- Name: clients_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.clients_id_seq', 1, false);


--
-- Name: consultations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.consultations_id_seq', 1, false);


--
-- Name: eleveurs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.eleveurs_id_seq', 1, false);


--
-- Name: favoris_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.favoris_id_seq', 1, false);


--
-- Name: feed_alerts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_alerts_id_seq', 1, false);


--
-- Name: feed_composition_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_composition_id_seq', 1, false);


--
-- Name: feed_consumption_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_consumption_logs_id_seq', 1, false);


--
-- Name: feed_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_items_id_seq', 1, false);


--
-- Name: feed_plans_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_plans_id_seq', 1, false);


--
-- Name: feed_stock_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_stock_id_seq', 1, false);


--
-- Name: feed_suppliers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.feed_suppliers_id_seq', 1, false);


--
-- Name: fermes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.fermes_id_seq', 1, false);


--
-- Name: fermes_ouvriers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.fermes_ouvriers_id_seq', 1, false);


--
-- Name: general_config_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.general_config_id_seq', 1, false);


--
-- Name: homepage_sections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.homepage_sections_id_seq', 1, false);


--
-- Name: marchands_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.marchands_id_seq', 1, false);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- Name: order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.order_items_id_seq', 1, false);


--
-- Name: orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.orders_id_seq', 1, false);


--
-- Name: ouvriers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ouvriers_id_seq', 1, false);


--
-- Name: poussins_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.poussins_id_seq', 1, false);


--
-- Name: prescriptions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.prescriptions_id_seq', 1, false);


--
-- Name: product_views_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.product_views_id_seq', 1, false);


--
-- Name: production_oeufs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.production_oeufs_id_seq', 1, false);


--
-- Name: products_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.products_id_seq', 1, false);


--
-- Name: produits_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.produits_id_seq', 1, false);


--
-- Name: roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.roles_id_seq', 1, false);


--
-- Name: saisies_quotidiennes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.saisies_quotidiennes_id_seq', 1, false);


--
-- Name: security_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.security_settings_id_seq', 1, false);


--
-- Name: smtp_configurations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.smtp_configurations_id_seq', 1, false);


--
-- Name: subscription_plans_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.subscription_plans_id_seq', 1, false);


--
-- Name: suivi_veterinaire_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.suivi_veterinaire_id_seq', 1, false);


--
-- Name: translations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.translations_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- Name: ventes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ventes_id_seq', 1, false);


--
-- Name: veterinaires_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.veterinaires_id_seq', 1, false);


--
-- Name: volailles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.volailles_id_seq', 1, false);


--
-- Name: ApiConfigs ApiConfigs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ApiConfigs"
    ADD CONSTRAINT "ApiConfigs_pkey" PRIMARY KEY (id);


--
-- Name: ApiConfigs ApiConfigs_serviceName_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."ApiConfigs"
    ADD CONSTRAINT "ApiConfigs_serviceName_key" UNIQUE ("serviceName");


--
-- Name: SequelizeMeta SequelizeMeta_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."SequelizeMeta"
    ADD CONSTRAINT "SequelizeMeta_pkey" PRIMARY KEY (name);


--
-- Name: Users Users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Users"
    ADD CONSTRAINT "Users_email_key" UNIQUE (email);


--
-- Name: Users Users_firebase_uid_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Users"
    ADD CONSTRAINT "Users_firebase_uid_key" UNIQUE (firebase_uid);


--
-- Name: Users Users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Users"
    ADD CONSTRAINT "Users_pkey" PRIMARY KEY (id);


--
-- Name: Users Users_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Users"
    ADD CONSTRAINT "Users_username_key" UNIQUE (username);


--
-- Name: alertes_stock alertes_stock_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alertes_stock
    ADD CONSTRAINT alertes_stock_pkey PRIMARY KEY (id);


--
-- Name: annonces annonces_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annonces
    ADD CONSTRAINT annonces_pkey PRIMARY KEY (id);


--
-- Name: blog_posts blog_posts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.blog_posts
    ADD CONSTRAINT blog_posts_pkey PRIMARY KEY (id);


--
-- Name: blog_posts blog_posts_slug_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.blog_posts
    ADD CONSTRAINT blog_posts_slug_key UNIQUE (slug);


--
-- Name: clients clients_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);


--
-- Name: consultations consultations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.consultations
    ADD CONSTRAINT consultations_pkey PRIMARY KEY (id);


--
-- Name: eleveurs eleveurs_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.eleveurs
    ADD CONSTRAINT eleveurs_email_key UNIQUE (email);


--
-- Name: eleveurs eleveurs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.eleveurs
    ADD CONSTRAINT eleveurs_pkey PRIMARY KEY (id);


--
-- Name: favoris favoris_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.favoris
    ADD CONSTRAINT favoris_pkey PRIMARY KEY (id);


--
-- Name: feed_alerts feed_alerts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_alerts
    ADD CONSTRAINT feed_alerts_pkey PRIMARY KEY (id);


--
-- Name: feed_composition feed_composition_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_composition
    ADD CONSTRAINT feed_composition_pkey PRIMARY KEY (id);


--
-- Name: feed_consumption_logs feed_consumption_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_consumption_logs
    ADD CONSTRAINT feed_consumption_logs_pkey PRIMARY KEY (id);


--
-- Name: feed_items feed_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_items
    ADD CONSTRAINT feed_items_pkey PRIMARY KEY (id);


--
-- Name: feed_plans feed_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_plans
    ADD CONSTRAINT feed_plans_pkey PRIMARY KEY (id);


--
-- Name: feed_stock feed_stock_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_stock
    ADD CONSTRAINT feed_stock_pkey PRIMARY KEY (id);


--
-- Name: feed_suppliers feed_suppliers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_suppliers
    ADD CONSTRAINT feed_suppliers_pkey PRIMARY KEY (id);


--
-- Name: fermes_ouvriers fermes_ouvriers_ouvrier_id_ferme_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.fermes_ouvriers
    ADD CONSTRAINT fermes_ouvriers_ouvrier_id_ferme_id_key UNIQUE (ouvrier_id, ferme_id);


--
-- Name: fermes_ouvriers fermes_ouvriers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.fermes_ouvriers
    ADD CONSTRAINT fermes_ouvriers_pkey PRIMARY KEY (id);


--
-- Name: fermes fermes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.fermes
    ADD CONSTRAINT fermes_pkey PRIMARY KEY (id);


--
-- Name: general_config general_config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.general_config
    ADD CONSTRAINT general_config_pkey PRIMARY KEY (id);


--
-- Name: marchands marchands_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.marchands
    ADD CONSTRAINT marchands_email_key UNIQUE (email);


--
-- Name: marchands marchands_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.marchands
    ADD CONSTRAINT marchands_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: order_items order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);


--
-- Name: orders orders_order_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_order_number_key UNIQUE (order_number);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: ouvriers ouvriers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ouvriers
    ADD CONSTRAINT ouvriers_pkey PRIMARY KEY (id);


--
-- Name: poussins poussins_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.poussins
    ADD CONSTRAINT poussins_pkey PRIMARY KEY (id);


--
-- Name: prescriptions prescriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.prescriptions
    ADD CONSTRAINT prescriptions_pkey PRIMARY KEY (id);


--
-- Name: product_views product_views_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.product_views
    ADD CONSTRAINT product_views_pkey PRIMARY KEY (id);


--
-- Name: production_oeufs production_oeufs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.production_oeufs
    ADD CONSTRAINT production_oeufs_pkey PRIMARY KEY (id);


--
-- Name: produits produits_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.produits
    ADD CONSTRAINT produits_pkey PRIMARY KEY (id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: saisies_quotidiennes saisies_quotidiennes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.saisies_quotidiennes
    ADD CONSTRAINT saisies_quotidiennes_pkey PRIMARY KEY (id);


--
-- Name: smtp_configurations smtp_configurations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.smtp_configurations
    ADD CONSTRAINT smtp_configurations_pkey PRIMARY KEY (id);


--
-- Name: subscription_plans subscription_plans_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.subscription_plans
    ADD CONSTRAINT subscription_plans_name_key UNIQUE (name);


--
-- Name: subscription_plans subscription_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.subscription_plans
    ADD CONSTRAINT subscription_plans_pkey PRIMARY KEY (id);


--
-- Name: suivi_veterinaire suivi_veterinaire_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suivi_veterinaire
    ADD CONSTRAINT suivi_veterinaire_pkey PRIMARY KEY (id);


--
-- Name: translations translations_key_language_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.translations
    ADD CONSTRAINT translations_key_language_key UNIQUE (key, language);


--
-- Name: translations translations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.translations
    ADD CONSTRAINT translations_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_firebase_uid_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_firebase_uid_key UNIQUE (firebase_uid);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: ventes ventes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ventes
    ADD CONSTRAINT ventes_pkey PRIMARY KEY (id);


--
-- Name: veterinaires veterinaires_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.veterinaires
    ADD CONSTRAINT veterinaires_email_key UNIQUE (email);


--
-- Name: veterinaires veterinaires_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.veterinaires
    ADD CONSTRAINT veterinaires_pkey PRIMARY KEY (id);


--
-- Name: volailles volailles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.volailles
    ADD CONSTRAINT volailles_pkey PRIMARY KEY (id);


--
-- Name: alertes_stock_archivee; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_archivee ON public.alertes_stock USING btree (archivee);


--
-- Name: alertes_stock_automatique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_automatique ON public.alertes_stock USING btree (automatique);


--
-- Name: alertes_stock_date_declenchement; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_date_declenchement ON public.alertes_stock USING btree (date_declenchement);


--
-- Name: alertes_stock_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_eleveur_id ON public.alertes_stock USING btree (eleveur_id);


--
-- Name: alertes_stock_eleveur_id_statut_visible; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_eleveur_id_statut_visible ON public.alertes_stock USING btree (eleveur_id, statut, visible);


--
-- Name: alertes_stock_priorite; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_priorite ON public.alertes_stock USING btree (priorite);


--
-- Name: alertes_stock_statut; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_statut ON public.alertes_stock USING btree (statut);


--
-- Name: alertes_stock_type_alerte; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_type_alerte ON public.alertes_stock USING btree (type_alerte);


--
-- Name: alertes_stock_visible; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX alertes_stock_visible ON public.alertes_stock USING btree (visible);


--
-- Name: feed_alerts_alert_type_severity; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_alerts_alert_type_severity ON public.feed_alerts USING btree (alert_type, severity);


--
-- Name: feed_alerts_farm_id_is_resolved; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_alerts_farm_id_is_resolved ON public.feed_alerts USING btree (farm_id, is_resolved);


--
-- Name: feed_composition_feed_plan_id_age_week_start; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_composition_feed_plan_id_age_week_start ON public.feed_composition USING btree (feed_plan_id, age_week_start);


--
-- Name: feed_items_category_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_items_category_status ON public.feed_items USING btree (category, status);


--
-- Name: feed_items_poultry_types; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_items_poultry_types ON public.feed_items USING btree (poultry_types);


--
-- Name: feed_stock_farm_id_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_stock_farm_id_status ON public.feed_stock USING btree (farm_id, status);


--
-- Name: feed_stock_feed_item_id_expiry_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_stock_feed_item_id_expiry_date ON public.feed_stock USING btree (feed_item_id, expiry_date);


--
-- Name: feed_stock_quantity_current_minimum_stock_alert; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_stock_quantity_current_minimum_stock_alert ON public.feed_stock USING btree (quantity_current, minimum_stock_alert);


--
-- Name: feed_suppliers_delivery_zones; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_suppliers_delivery_zones ON public.feed_suppliers USING btree (delivery_zones);


--
-- Name: feed_suppliers_wilaya_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX feed_suppliers_wilaya_status ON public.feed_suppliers USING btree (wilaya, status);


--
-- Name: idx_clients_marchand_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_marchand_id ON public.clients USING btree (marchand_id);


--
-- Name: idx_consultations_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_eleveur_id ON public.consultations USING btree (eleveur_id);


--
-- Name: idx_consultations_veterinaire_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_veterinaire_id ON public.consultations USING btree (veterinaire_id);


--
-- Name: idx_favoris_annonce_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favoris_annonce_id ON public.favoris USING btree (annonce_id);


--
-- Name: idx_favoris_utilisateur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favoris_utilisateur_id ON public.favoris USING btree (utilisateur_id);


--
-- Name: idx_feed_consumption_logs_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_feed_consumption_logs_eleveur_id ON public.feed_consumption_logs USING btree (eleveur_id);


--
-- Name: idx_feed_consumption_logs_feed_plan_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_feed_consumption_logs_feed_plan_id ON public.feed_consumption_logs USING btree (feed_plan_id);


--
-- Name: idx_feed_plans_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_feed_plans_eleveur_id ON public.feed_plans USING btree (eleveur_id);


--
-- Name: idx_fermes_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_fermes_eleveur_id ON public.fermes USING btree (eleveur_id);


--
-- Name: idx_fermes_ouvriers_ferme_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_fermes_ouvriers_ferme_id ON public.fermes_ouvriers USING btree (ferme_id);


--
-- Name: idx_fermes_ouvriers_ouvrier_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_fermes_ouvriers_ouvrier_id ON public.fermes_ouvriers USING btree (ouvrier_id);


--
-- Name: idx_notifications_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_user_id ON public.notifications USING btree (user_id);


--
-- Name: idx_order_items_order_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_order_items_order_id ON public.order_items USING btree (order_id);


--
-- Name: idx_order_items_product_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_order_items_product_id ON public.order_items USING btree (product_id);


--
-- Name: idx_orders_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_orders_created_at ON public.orders USING btree (created_at);


--
-- Name: idx_orders_marchand_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_orders_marchand_id ON public.orders USING btree (marchand_id);


--
-- Name: idx_orders_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_orders_status ON public.orders USING btree (status);


--
-- Name: idx_ouvriers_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ouvriers_eleveur_id ON public.ouvriers USING btree (eleveur_id);


--
-- Name: idx_poussins_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_poussins_eleveur_id ON public.poussins USING btree (eleveur_id);


--
-- Name: idx_prescriptions_consultation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_prescriptions_consultation_id ON public.prescriptions USING btree (consultation_id);


--
-- Name: idx_product_views_product_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_product_views_product_id ON public.product_views USING btree (product_id);


--
-- Name: idx_production_oeufs_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_production_oeufs_eleveur_id ON public.production_oeufs USING btree (eleveur_id);


--
-- Name: idx_produits_marchand_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_produits_marchand_id ON public.produits USING btree (marchand_id);


--
-- Name: idx_saisies_quotidiennes_date_saisie; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_saisies_quotidiennes_date_saisie ON public.saisies_quotidiennes USING btree (date_saisie);


--
-- Name: idx_saisies_quotidiennes_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_saisies_quotidiennes_eleveur_id ON public.saisies_quotidiennes USING btree (eleveur_id);


--
-- Name: idx_saisies_quotidiennes_ouvrier_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_saisies_quotidiennes_ouvrier_id ON public.saisies_quotidiennes USING btree (ouvrier_id);


--
-- Name: idx_suivi_veterinaire_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_suivi_veterinaire_eleveur_id ON public.suivi_veterinaire USING btree (eleveur_id);


--
-- Name: idx_suivi_veterinaire_veterinaire_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_suivi_veterinaire_veterinaire_id ON public.suivi_veterinaire USING btree (veterinaire_id);


--
-- Name: idx_translations_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_translations_category ON public.translations USING btree (category);


--
-- Name: idx_translations_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_translations_key ON public.translations USING btree (key);


--
-- Name: idx_translations_language; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_translations_language ON public.translations USING btree (language);


--
-- Name: idx_ventes_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ventes_date ON public.ventes USING btree (date_vente);


--
-- Name: idx_ventes_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ventes_eleveur_id ON public.ventes USING btree (eleveur_id);


--
-- Name: idx_volailles_eleveur_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_volailles_eleveur_id ON public.volailles USING btree (eleveur_id);


--
-- Name: orders_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX orders_client_id ON public.orders USING btree (client_id);


--
-- Name: orders_marchand_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX orders_marchand_id ON public.orders USING btree (marchand_id);


--
-- Name: clients update_clients_modtime; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_clients_modtime BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();


--
-- Name: feed_alerts feed_alerts_farm_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_alerts
    ADD CONSTRAINT feed_alerts_farm_id_fkey FOREIGN KEY (farm_id) REFERENCES public.fermes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: feed_alerts feed_alerts_feed_stock_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_alerts
    ADD CONSTRAINT feed_alerts_feed_stock_id_fkey FOREIGN KEY (feed_stock_id) REFERENCES public.feed_stock(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: feed_composition feed_composition_feed_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_composition
    ADD CONSTRAINT feed_composition_feed_item_id_fkey FOREIGN KEY (feed_item_id) REFERENCES public.feed_items(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: feed_stock feed_stock_farm_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_stock
    ADD CONSTRAINT feed_stock_farm_id_fkey FOREIGN KEY (farm_id) REFERENCES public.fermes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: feed_stock feed_stock_feed_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_stock
    ADD CONSTRAINT feed_stock_feed_item_id_fkey FOREIGN KEY (feed_item_id) REFERENCES public.feed_items(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: feed_stock feed_stock_supplier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.feed_stock
    ADD CONSTRAINT feed_stock_supplier_id_fkey FOREIGN KEY (supplier_id) REFERENCES public.feed_suppliers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: order_items order_items_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: orders orders_marchand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_marchand_id_fkey FOREIGN KEY (marchand_id) REFERENCES public.users(id);


--
-- Name: product_views product_views_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.product_views
    ADD CONSTRAINT product_views_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: ventes ventes_acheteur_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ventes
    ADD CONSTRAINT ventes_acheteur_id_fkey FOREIGN KEY (acheteur_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

