import { useState, useEffect, useCallback, useRef } from 'react';
import cacheService, { CACHE_STRATEGIES, CACHE_TYPES } from '../services/cacheService';
import { useAsyncPerformance } from './usePerformance';

/**
 * Hook pour les appels API avec mise en cache automatique
 */
export const useCachedApi = (
  apiFunction,
  dependencies = [],
  options = {}
) => {
  const {
    cacheKey: customCacheKey,
    cacheStrategy = CACHE_STRATEGIES.CACHE_FIRST,
    cacheType = CACHE_TYPES.MEMORY,
    cacheTTL = 300000, // 5 minutes
    enabled = true,
    immediate = true,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(immediate && enabled);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  const { measureAsync } = useAsyncPerformance();
  const abortControllerRef = useRef(null);
  const retryTimeoutRef = useRef(null);

  // Générer une clé de cache basée sur la fonction et les dépendances
  const cacheKey = customCacheKey || `api_${apiFunction.name}_${JSON.stringify(dependencies)}`;

  // Fonction pour exécuter l'appel API
  const executeApiCall = useCallback(async () => {
    // Annuler la requête précédente si elle existe
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Créer un nouveau contrôleur d'annulation
    abortControllerRef.current = new AbortController();

    try {
      const result = await measureAsync(
        `API_${cacheKey}`,
        () => apiFunction({ signal: abortControllerRef.current.signal })
      );
      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('Requête annulée:', cacheKey);
        return null;
      }
      throw error;
    }
  }, [apiFunction, cacheKey, measureAsync]);

  // Fonction pour récupérer les données avec cache
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cacheService.cachedRequest(
        executeApiCall,
        cacheKey,
        {
          strategy: cacheStrategy,
          cacheType,
          ttl: cacheTTL,
          forceRefresh
        }
      );

      if (result !== null) {
        setData(result);
        setLastFetch(new Date());
        
        if (onSuccess) {
          onSuccess(result);
        }
      }
    } catch (err) {
      console.error('Erreur lors de l\'appel API:', err);
      setError(err);
      
      if (onError) {
        onError(err);
      }

      // Retry automatique si configuré
      if (retryCount > 0) {
        retryTimeoutRef.current = setTimeout(() => {
          console.log(`Retry ${retryCount} pour ${cacheKey}`);
          fetchData(forceRefresh);
        }, retryDelay);
      }
    } finally {
      setLoading(false);
    }
  }, [
    enabled,
    executeApiCall,
    cacheKey,
    cacheStrategy,
    cacheType,
    cacheTTL,
    onSuccess,
    onError,
    retryCount,
    retryDelay
  ]);

  // Fonction pour rafraîchir les données
  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Fonction pour invalider le cache
  const invalidateCache = useCallback(() => {
    cacheService.delete(cacheKey, cacheType);
  }, [cacheKey, cacheType]);

  // Effet pour charger les données automatiquement
  useEffect(() => {
    if (immediate && enabled) {
      fetchData();
    }

    // Cleanup
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [fetchData, immediate, enabled, ...dependencies]);

  // Cleanup lors du démontage
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    lastFetch,
    fetchData,
    refresh,
    invalidateCache
  };
};

/**
 * Hook pour les mutations avec invalidation de cache
 */
export const useCachedMutation = (
  mutationFunction,
  options = {}
) => {
  const {
    onSuccess,
    onError,
    invalidateKeys = [],
    invalidatePatterns = [],
    cacheType = CACHE_TYPES.MEMORY
  } = options;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const { measureAsync } = useAsyncPerformance();

  const mutate = useCallback(async (variables) => {
    setLoading(true);
    setError(null);

    try {
      const result = await measureAsync(
        `MUTATION_${mutationFunction.name}`,
        () => mutationFunction(variables)
      );

      setData(result);

      // Invalider les clés de cache spécifiées
      invalidateKeys.forEach(key => {
        cacheService.delete(key, cacheType);
      });

      // Invalider par patterns
      invalidatePatterns.forEach(pattern => {
        cacheService.invalidatePattern(pattern, cacheType);
      });

      if (onSuccess) {
        onSuccess(result, variables);
      }

      return result;
    } catch (err) {
      setError(err);
      
      if (onError) {
        onError(err, variables);
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [
    mutationFunction,
    invalidateKeys,
    invalidatePatterns,
    cacheType,
    onSuccess,
    onError,
    measureAsync
  ]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    loading,
    error,
    data,
    reset
  };
};

/**
 * Hook pour la pagination avec cache
 */
export const useCachedPagination = (
  apiFunction,
  options = {}
) => {
  const {
    pageSize = 10,
    cacheStrategy = CACHE_STRATEGIES.CACHE_FIRST,
    cacheType = CACHE_TYPES.MEMORY,
    cacheTTL = 300000
  } = options;

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  const cacheKey = `pagination_${apiFunction.name}_${currentPage}_${pageSize}`;

  const {
    data,
    loading,
    error,
    fetchData,
    refresh
  } = useCachedApi(
    () => apiFunction({ page: currentPage, limit: pageSize }),
    [currentPage, pageSize],
    {
      cacheKey,
      cacheStrategy,
      cacheType,
      cacheTTL,
      onSuccess: (result) => {
        if (result.pagination) {
          setTotalPages(result.pagination.totalPages || 0);
          setTotalItems(result.pagination.totalItems || 0);
        }
      }
    }
  );

  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  }, [totalPages]);

  const nextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  const prevPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  const firstPage = useCallback(() => {
    goToPage(1);
  }, [goToPage]);

  const lastPage = useCallback(() => {
    goToPage(totalPages);
  }, [totalPages, goToPage]);

  return {
    data,
    loading,
    error,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    goToPage,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    refresh,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1
  };
};

/**
 * Hook pour précharger des données
 */
export const usePreloadData = () => {
  const preload = useCallback((apiFunction, cacheKey, options = {}) => {
    const {
      cacheType = CACHE_TYPES.MEMORY,
      cacheTTL = 300000
    } = options;

    // Vérifier si les données sont déjà en cache
    if (cacheService.has(cacheKey, cacheType)) {
      return Promise.resolve();
    }

    // Précharger les données
    return cacheService.cachedRequest(
      apiFunction,
      cacheKey,
      {
        strategy: CACHE_STRATEGIES.NETWORK_FIRST,
        cacheType,
        ttl: cacheTTL
      }
    );
  }, []);

  return { preload };
};

export default useCachedApi;
