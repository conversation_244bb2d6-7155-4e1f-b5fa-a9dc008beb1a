const { Sequelize } = require('sequelize');
const config = require('../src/config/config.json').development;

console.log('Testing database connection with these parameters:');
console.log({
  database: config.database,
  username: config.username,
  host: config.host,
  port: config.port,
  dialect: config.dialect
});

const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: console.log,
  }
);

async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Try to query the database to ensure full access
    try {
      const result = await sequelize.query('SELECT 1+1 as result');
      console.log('Query result:', result[0]);
    } catch (queryError) {
      console.error('Error querying database:', queryError);
    }

  } catch (error) {
    console.error('Unable to connect to the database:', error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

testConnection();
