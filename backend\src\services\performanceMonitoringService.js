/**
 * Service de monitoring des performances avancé
 */

const { Pool } = require('pg');
const Redis = require('redis');
const logger = require('../utils/logger');
const prometheus = require('prom-client');

class PerformanceMonitoringService {
  constructor() {
    this.pool = new Pool();
    this.redis = Redis.createClient();
    this.metrics = this.initializeMetrics();
    this.performanceData = new Map();
    this.alertThresholds = {
      responseTime: 2000, // 2 secondes
      memoryUsage: 85, // 85%
      cpuUsage: 80, // 80%
      errorRate: 5, // 5%
      dbConnectionPool: 80 // 80% du pool
    };
  }

  /**
   * Initialiser les métriques Prometheus
   */
  initializeMetrics() {
    // Créer un registre personnalisé
    const register = new prometheus.Registry();

    // Métriques HTTP
    const httpRequestDuration = new prometheus.Histogram({
      name: 'http_request_duration_seconds',
      help: 'Du<PERSON><PERSON> des requêtes HTTP en secondes',
      labelNames: ['method', 'route', 'status_code', 'user_role'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    });

    const httpRequestsTotal = new prometheus.Counter({
      name: 'http_requests_total',
      help: 'Nombre total de requêtes HTTP',
      labelNames: ['method', 'route', 'status_code', 'user_role']
    });

    // Métriques de base de données
    const dbQueryDuration = new prometheus.Histogram({
      name: 'db_query_duration_seconds',
      help: 'Durée des requêtes de base de données',
      labelNames: ['query_type', 'table', 'operation'],
      buckets: [0.01, 0.05, 0.1, 0.3, 0.5, 1, 2, 5]
    });

    const dbConnectionsActive = new prometheus.Gauge({
      name: 'db_connections_active',
      help: 'Nombre de connexions actives à la base de données'
    });

    const dbConnectionsIdle = new prometheus.Gauge({
      name: 'db_connections_idle',
      help: 'Nombre de connexions inactives à la base de données'
    });

    // Métriques Redis
    const redisOperationDuration = new prometheus.Histogram({
      name: 'redis_operation_duration_seconds',
      help: 'Durée des opérations Redis',
      labelNames: ['operation', 'key_pattern'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.3, 0.5, 1]
    });

    const redisMemoryUsage = new prometheus.Gauge({
      name: 'redis_memory_usage_bytes',
      help: 'Utilisation mémoire de Redis en bytes'
    });

    // Métriques système
    const memoryUsage = new prometheus.Gauge({
      name: 'nodejs_memory_usage_bytes',
      help: 'Utilisation mémoire Node.js',
      labelNames: ['type']
    });

    const cpuUsage = new prometheus.Gauge({
      name: 'nodejs_cpu_usage_percent',
      help: 'Utilisation CPU Node.js'
    });

    // Métriques métier
    const activeUsers = new prometheus.Gauge({
      name: 'active_users_total',
      help: 'Nombre d\'utilisateurs actifs',
      labelNames: ['role', 'time_window']
    });

    const businessOperations = new prometheus.Counter({
      name: 'business_operations_total',
      help: 'Nombre d\'opérations métier',
      labelNames: ['operation_type', 'status', 'user_role']
    });

    // Enregistrer toutes les métriques
    register.registerMetric(httpRequestDuration);
    register.registerMetric(httpRequestsTotal);
    register.registerMetric(dbQueryDuration);
    register.registerMetric(dbConnectionsActive);
    register.registerMetric(dbConnectionsIdle);
    register.registerMetric(redisOperationDuration);
    register.registerMetric(redisMemoryUsage);
    register.registerMetric(memoryUsage);
    register.registerMetric(cpuUsage);
    register.registerMetric(activeUsers);
    register.registerMetric(businessOperations);

    // Ajouter les métriques par défaut
    prometheus.collectDefaultMetrics({ register });

    return {
      register,
      httpRequestDuration,
      httpRequestsTotal,
      dbQueryDuration,
      dbConnectionsActive,
      dbConnectionsIdle,
      redisOperationDuration,
      redisMemoryUsage,
      memoryUsage,
      cpuUsage,
      activeUsers,
      businessOperations
    };
  }

  /**
   * Middleware pour tracker les performances HTTP
   */
  createHttpMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      const startHrTime = process.hrtime();

      // Intercepter la fin de la réponse
      const originalEnd = res.end;
      res.end = (...args) => {
        const duration = Date.now() - startTime;
        const hrDuration = process.hrtime(startHrTime);
        const durationSeconds = hrDuration[0] + hrDuration[1] / 1e9;

        // Enregistrer les métriques
        const labels = {
          method: req.method,
          route: req.route?.path || req.path,
          status_code: res.statusCode,
          user_role: req.user?.role || 'anonymous'
        };

        this.metrics.httpRequestDuration.observe(labels, durationSeconds);
        this.metrics.httpRequestsTotal.inc(labels);

        // Vérifier les seuils d'alerte
        if (duration > this.alertThresholds.responseTime) {
          this.triggerAlert('slow_response', {
            duration,
            route: req.path,
            method: req.method
          });
        }

        // Stocker les données de performance
        this.storePerformanceData('http_request', {
          ...labels,
          duration: durationSeconds,
          timestamp: Date.now()
        });

        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Wrapper pour tracker les requêtes de base de données
   */
  wrapDatabaseQuery(originalQuery) {
    return async (text, params, callback) => {
      const startTime = process.hrtime();
      const queryType = this.extractQueryType(text);
      const table = this.extractTableName(text);

      try {
        const result = await originalQuery.call(this.pool, text, params, callback);
        
        const duration = process.hrtime(startTime);
        const durationSeconds = duration[0] + duration[1] / 1e9;

        // Enregistrer les métriques
        this.metrics.dbQueryDuration.observe({
          query_type: queryType,
          table: table,
          operation: 'success'
        }, durationSeconds);

        // Stocker les données de performance
        this.storePerformanceData('db_query', {
          queryType,
          table,
          duration: durationSeconds,
          success: true,
          timestamp: Date.now()
        });

        return result;
      } catch (error) {
        const duration = process.hrtime(startTime);
        const durationSeconds = duration[0] + duration[1] / 1e9;

        this.metrics.dbQueryDuration.observe({
          query_type: queryType,
          table: table,
          operation: 'error'
        }, durationSeconds);

        // Stocker les données d'erreur
        this.storePerformanceData('db_query', {
          queryType,
          table,
          duration: durationSeconds,
          success: false,
          error: error.message,
          timestamp: Date.now()
        });

        throw error;
      }
    };
  }

  /**
   * Wrapper pour tracker les opérations Redis
   */
  wrapRedisOperation(operation, keyPattern) {
    return async (...args) => {
      const startTime = process.hrtime();

      try {
        const result = await this.redis[operation](...args);
        
        const duration = process.hrtime(startTime);
        const durationSeconds = duration[0] + duration[1] / 1e9;

        this.metrics.redisOperationDuration.observe({
          operation,
          key_pattern: keyPattern
        }, durationSeconds);

        return result;
      } catch (error) {
        logger.error(`Erreur Redis ${operation}:`, error);
        throw error;
      }
    };
  }

  /**
   * Collecter les métriques système
   */
  async collectSystemMetrics() {
    try {
      // Métriques mémoire Node.js
      const memUsage = process.memoryUsage();
      this.metrics.memoryUsage.set({ type: 'rss' }, memUsage.rss);
      this.metrics.memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
      this.metrics.memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
      this.metrics.memoryUsage.set({ type: 'external' }, memUsage.external);

      // Métriques CPU
      const cpuUsage = process.cpuUsage();
      const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convertir en secondes
      this.metrics.cpuUsage.set(cpuPercent);

      // Métriques de connexions base de données
      const dbStats = await this.getDatabaseStats();
      this.metrics.dbConnectionsActive.set(dbStats.active);
      this.metrics.dbConnectionsIdle.set(dbStats.idle);

      // Métriques Redis
      const redisInfo = await this.redis.info('memory');
      const redisMemory = this.parseRedisMemoryInfo(redisInfo);
      this.metrics.redisMemoryUsage.set(redisMemory.used_memory);

      // Métriques utilisateurs actifs
      const activeUsersStats = await this.getActiveUsersStats();
      Object.entries(activeUsersStats).forEach(([role, counts]) => {
        Object.entries(counts).forEach(([timeWindow, count]) => {
          this.metrics.activeUsers.set({ role, time_window: timeWindow }, count);
        });
      });

    } catch (error) {
      logger.error('Erreur lors de la collecte des métriques système:', error);
    }
  }

  /**
   * Analyser les requêtes lentes
   */
  async analyzeSlowQueries() {
    try {
      const slowQueries = await this.pool.query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          max_time,
          stddev_time
        FROM pg_stat_statements 
        WHERE mean_time > $1
        ORDER BY mean_time DESC 
        LIMIT 20
      `, [100]); // Requêtes avec temps moyen > 100ms

      const analysis = slowQueries.rows.map(query => ({
        ...query,
        optimization_suggestions: this.generateOptimizationSuggestions(query)
      }));

      // Stocker l'analyse
      await this.storeSlowQueryAnalysis(analysis);

      return analysis;
    } catch (error) {
      logger.error('Erreur lors de l\'analyse des requêtes lentes:', error);
      return [];
    }
  }

  /**
   * Générer des suggestions d'optimisation
   */
  generateOptimizationSuggestions(query) {
    const suggestions = [];

    // Analyser le type de requête
    const queryText = query.query.toLowerCase();

    if (queryText.includes('select') && !queryText.includes('limit')) {
      suggestions.push('Considérer l\'ajout d\'une clause LIMIT');
    }

    if (queryText.includes('where') && !queryText.includes('index')) {
      suggestions.push('Vérifier la présence d\'index sur les colonnes de la clause WHERE');
    }

    if (query.calls > 1000 && query.mean_time > 50) {
      suggestions.push('Requête fréquente et lente - candidat pour la mise en cache');
    }

    if (queryText.includes('order by') && !queryText.includes('limit')) {
      suggestions.push('Tri sans limite - considérer la pagination');
    }

    if (queryText.includes('join') && query.mean_time > 200) {
      suggestions.push('Jointure lente - vérifier les index sur les clés de jointure');
    }

    return suggestions;
  }

  /**
   * Créer un rapport de performance
   */
  async generatePerformanceReport(timeRange = '24h') {
    try {
      const endTime = Date.now();
      const startTime = endTime - this.parseTimeRange(timeRange);

      const report = {
        timeRange: {
          start: new Date(startTime).toISOString(),
          end: new Date(endTime).toISOString()
        },
        summary: await this.getPerformanceSummary(startTime, endTime),
        httpMetrics: await this.getHttpMetrics(startTime, endTime),
        databaseMetrics: await this.getDatabaseMetrics(startTime, endTime),
        systemMetrics: await this.getSystemMetrics(startTime, endTime),
        slowQueries: await this.analyzeSlowQueries(),
        recommendations: await this.generateRecommendations(startTime, endTime)
      };

      return report;
    } catch (error) {
      logger.error('Erreur lors de la génération du rapport:', error);
      throw error;
    }
  }

  /**
   * Déclencher une alerte
   */
  async triggerAlert(alertType, data) {
    const alert = {
      type: alertType,
      severity: this.getAlertSeverity(alertType, data),
      timestamp: Date.now(),
      data: data,
      message: this.generateAlertMessage(alertType, data)
    };

    // Envoyer l'alerte
    await this.sendAlert(alert);

    // Stocker l'alerte
    await this.storeAlert(alert);

    logger.warn(`Alerte de performance: ${alert.message}`, alert);
  }

  /**
   * Méthodes utilitaires privées
   */

  extractQueryType(query) {
    const match = query.trim().match(/^(\w+)/i);
    return match ? match[1].toUpperCase() : 'UNKNOWN';
  }

  extractTableName(query) {
    const patterns = [
      /FROM\s+(\w+)/i,
      /UPDATE\s+(\w+)/i,
      /INSERT\s+INTO\s+(\w+)/i,
      /DELETE\s+FROM\s+(\w+)/i
    ];

    for (const pattern of patterns) {
      const match = query.match(pattern);
      if (match) return match[1];
    }

    return 'unknown';
  }

  async getDatabaseStats() {
    const result = await this.pool.query(`
      SELECT 
        numbackends as active,
        (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') - numbackends as idle
      FROM pg_stat_database 
      WHERE datname = current_database()
    `);

    return result.rows[0] || { active: 0, idle: 0 };
  }

  parseRedisMemoryInfo(info) {
    const lines = info.split('\r\n');
    const memory = {};
    
    lines.forEach(line => {
      const [key, value] = line.split(':');
      if (key && value && key.includes('memory')) {
        memory[key] = parseInt(value) || 0;
      }
    });

    return memory;
  }

  async getActiveUsersStats() {
    const result = await this.pool.query(`
      SELECT 
        role,
        COUNT(CASE WHEN last_activity >= NOW() - INTERVAL '5 minutes' THEN 1 END) as active_5m,
        COUNT(CASE WHEN last_activity >= NOW() - INTERVAL '1 hour' THEN 1 END) as active_1h,
        COUNT(CASE WHEN last_activity >= NOW() - INTERVAL '24 hours' THEN 1 END) as active_24h
      FROM users 
      WHERE last_activity IS NOT NULL
      GROUP BY role
    `);

    const stats = {};
    result.rows.forEach(row => {
      stats[row.role] = {
        '5m': row.active_5m,
        '1h': row.active_1h,
        '24h': row.active_24h
      };
    });

    return stats;
  }

  storePerformanceData(type, data) {
    const key = `${type}_${Date.now()}`;
    this.performanceData.set(key, data);

    // Nettoyer les anciennes données (garder seulement les 1000 dernières)
    if (this.performanceData.size > 1000) {
      const oldestKey = this.performanceData.keys().next().value;
      this.performanceData.delete(oldestKey);
    }
  }

  parseTimeRange(timeRange) {
    const units = {
      'm': 60 * 1000,
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000
    };

    const match = timeRange.match(/^(\d+)([mhd])$/);
    if (match) {
      const [, amount, unit] = match;
      return parseInt(amount) * units[unit];
    }

    return 24 * 60 * 60 * 1000; // Par défaut 24h
  }

  getAlertSeverity(alertType, data) {
    const severityMap = {
      slow_response: data.duration > 5000 ? 'critical' : 'warning',
      high_memory: data.usage > 95 ? 'critical' : 'warning',
      high_cpu: data.usage > 90 ? 'critical' : 'warning',
      db_connection_limit: 'critical',
      redis_error: 'warning'
    };

    return severityMap[alertType] || 'info';
  }

  generateAlertMessage(alertType, data) {
    const messages = {
      slow_response: `Réponse lente détectée: ${data.duration}ms sur ${data.route}`,
      high_memory: `Utilisation mémoire élevée: ${data.usage}%`,
      high_cpu: `Utilisation CPU élevée: ${data.usage}%`,
      db_connection_limit: 'Limite de connexions base de données atteinte',
      redis_error: `Erreur Redis: ${data.error}`
    };

    return messages[alertType] || `Alerte: ${alertType}`;
  }

  async sendAlert(alert) {
    // Implémenter l'envoi d'alertes (Slack, email, etc.)
    // Pour l'instant, juste logger
    logger.warn('ALERTE PERFORMANCE:', alert);
  }

  async storeAlert(alert) {
    try {
      await this.pool.query(`
        INSERT INTO performance_alerts (type, severity, message, data, created_at)
        VALUES ($1, $2, $3, $4, NOW())
      `, [alert.type, alert.severity, alert.message, JSON.stringify(alert.data)]);
    } catch (error) {
      logger.error('Erreur lors du stockage de l\'alerte:', error);
    }
  }

  /**
   * Démarrer la collecte périodique des métriques
   */
  startPeriodicCollection() {
    // Collecter les métriques système toutes les 30 secondes
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Analyser les requêtes lentes toutes les 5 minutes
    setInterval(() => {
      this.analyzeSlowQueries();
    }, 5 * 60 * 1000);
  }

  /**
   * Obtenir les métriques au format Prometheus
   */
  async getMetrics() {
    return this.metrics.register.metrics();
  }
}

module.exports = new PerformanceMonitoringService();
