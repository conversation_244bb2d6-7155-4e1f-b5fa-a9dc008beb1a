import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Tooltip,
  Box
} from '@mui/material';
import { CheckCircle, Error } from '@mui/icons-material';

/**
 * Composant Select avec validation intégrée
 */
const ValidatedSelect = ({
  name,
  label,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  options = [],
  required = false,
  disabled = false,
  fullWidth = true,
  margin = 'normal',
  variant = 'outlined',
  size = 'medium',
  showValidationIcon = true,
  placeholder,
  multiple = false,
  tooltip,
  ...props
}) => {
  const hasValue = multiple ? (value && value.length > 0) : (value !== null && value !== undefined && value !== '');
  const isValid = hasValue && !error;

  const getValidationIcon = () => {
    if (!showValidationIcon || !hasValue) return null;

    if (isValid) {
      return (
        <Tooltip title="Valide">
          <CheckCircle color="success" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    } else if (error) {
      return (
        <Tooltip title={error}>
          <Error color="error" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    }

    return null;
  };

  const renderValue = (selected) => {
    if (multiple) {
      if (!selected || selected.length === 0) {
        return placeholder || '';
      }
      return selected.map(val => {
        const option = options.find(opt => opt.value === val);
        return option ? option.label : val;
      }).join(', ');
    }
    
    if (!selected && placeholder) {
      return placeholder;
    }
    
    const option = options.find(opt => opt.value === selected);
    return option ? option.label : selected;
  };

  return (
    <FormControl
      fullWidth={fullWidth}
      margin={margin}
      variant={variant}
      size={size}
      error={!!error}
      disabled={disabled}
      required={required}
    >
      <InputLabel id={`${name}-label`}>
        {label}
        {required && ' *'}
      </InputLabel>
      
      <Box display="flex" alignItems="center">
        <Select
          labelId={`${name}-label`}
          id={name}
          name={name}
          value={value || (multiple ? [] : '')}
          label={label}
          onChange={onChange}
          onBlur={onBlur}
          multiple={multiple}
          renderValue={renderValue}
          displayEmpty={!!placeholder}
          sx={{ flexGrow: 1 }}
          {...props}
        >
          {placeholder && !multiple && (
            <MenuItem value="" disabled>
              <em>{placeholder}</em>
            </MenuItem>
          )}
          
          {options.map((option) => (
            <MenuItem 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        
        {getValidationIcon()}
      </Box>

      {(error || helperText) && (
        <FormHelperText>
          {error || helperText}
        </FormHelperText>
      )}
      
      {tooltip && (
        <FormHelperText>
          <Tooltip title={tooltip}>
            <span style={{ cursor: 'help', textDecoration: 'underline' }}>
              Plus d'informations
            </span>
          </Tooltip>
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default ValidatedSelect;
