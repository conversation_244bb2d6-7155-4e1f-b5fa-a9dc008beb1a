/**
 * Composant de gestion de l'inventaire pour les marchands
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Badge,
  Tooltip,
  LinearProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Edit as EditIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Inventory as InventoryIcon,
  LocalShipping as ShippingIcon,
  Assessment as AssessmentIcon,
  History as HistoryIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { marchandAPI } from '../../services/marchandService';

const InventaireManagement = () => {
  const { user } = useAuth();
  const [inventaire, setInventaire] = useState([]);
  const [mouvements, setMouvements] = useState([]);
  const [alertes, setAlertes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduit, setSelectedProduit] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  
  const [formData, setFormData] = useState({
    type_mouvement: 'entree',
    quantite: '',
    motif: '',
    date_mouvement: new Date(),
    notes: ''
  });

  const [stats, setStats] = useState({
    valeurTotaleStock: 0,
    nombreProduitsStock: 0,
    produitsRuptureStock: 0,
    produitsStockFaible: 0,
    mouvementsRecents: 0
  });

  useEffect(() => {
    loadInventaire();
    loadMouvements();
    loadAlertes();
    loadStats();
  }, []);

  const loadInventaire = async () => {
    try {
      setLoading(true);
      const response = await marchandAPI.getInventaire();
      setInventaire(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement de l\'inventaire');
      console.error('Erreur inventaire:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadMouvements = async () => {
    try {
      const response = await marchandAPI.getStockMovements();
      setMouvements(response.data || []);
    } catch (err) {
      console.error('Erreur mouvements:', err);
    }
  };

  const loadAlertes = async () => {
    try {
      const response = await marchandAPI.getStockAlerts();
      setAlertes(response.data || []);
    } catch (err) {
      console.error('Erreur alertes:', err);
    }
  };

  const loadStats = async () => {
    try {
      // Calculer les stats à partir des données d'inventaire
      const valeurTotale = inventaire.reduce((total, item) => 
        total + (item.stock_actuel * item.prix_unitaire), 0);
      
      const produitsRupture = inventaire.filter(item => item.stock_actuel <= 0).length;
      const produitsStockFaible = inventaire.filter(item => 
        item.stock_actuel > 0 && item.stock_actuel <= item.stock_minimum).length;

      setStats({
        valeurTotaleStock: valeurTotale,
        nombreProduitsStock: inventaire.length,
        produitsRuptureStock: produitsRupture,
        produitsStockFaible: produitsStockFaible,
        mouvementsRecents: mouvements.filter(m => 
          new Date(m.date_mouvement) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        ).length
      });
    } catch (err) {
      console.error('Erreur stats inventaire:', err);
    }
  };

  const handleOpenDialog = (produit) => {
    setSelectedProduit(produit);
    setFormData({
      type_mouvement: 'entree',
      quantite: '',
      motif: '',
      date_mouvement: new Date(),
      notes: ''
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduit(null);
  };

  const handleSubmitMouvement = async () => {
    if (!selectedProduit) return;

    try {
      const mouvementData = {
        ...formData,
        produit_id: selectedProduit.id,
        marchand_id: user.profile_id
      };

      await marchandAPI.recordStockMovement(mouvementData);
      
      // Mettre à jour le stock du produit
      const newStock = formData.type_mouvement === 'entree' 
        ? selectedProduit.stock_actuel + parseInt(formData.quantite)
        : selectedProduit.stock_actuel - parseInt(formData.quantite);

      await marchandAPI.updateStock(selectedProduit.id, { stock_actuel: newStock });

      handleCloseDialog();
      loadInventaire();
      loadMouvements();
      loadAlertes();
      loadStats();
    } catch (err) {
      setError('Erreur lors de l\'enregistrement du mouvement');
      console.error('Erreur mouvement stock:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getStockStatus = (produit) => {
    if (produit.stock_actuel <= 0) return 'rupture';
    if (produit.stock_actuel <= produit.stock_minimum) return 'faible';
    if (produit.stock_actuel >= produit.stock_maximum) return 'exces';
    return 'normal';
  };

  const getStockColor = (status) => {
    switch (status) {
      case 'rupture': return 'error';
      case 'faible': return 'warning';
      case 'exces': return 'info';
      case 'normal': return 'success';
      default: return 'default';
    }
  };

  const getStockLabel = (status) => {
    switch (status) {
      case 'rupture': return 'Rupture';
      case 'faible': return 'Stock faible';
      case 'exces': return 'Excès';
      case 'normal': return 'Normal';
      default: return status;
    }
  };

  const getStockPercentage = (produit) => {
    if (produit.stock_maximum === 0) return 0;
    return (produit.stock_actuel / produit.stock_maximum) * 100;
  };

  const StatCard = ({ title, value, icon, color = 'primary', subtitle }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ 
            p: 1, 
            borderRadius: 1, 
            bgcolor: `${color}.light`, 
            color: `${color}.contrastText`,
            mr: 2 
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement de l'inventaire...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion de l'Inventaire
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              loadInventaire();
              loadMouvements();
              loadAlertes();
              loadStats();
            }}
          >
            Actualiser
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Alertes de stock */}
        {alertes.length > 0 && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2" fontWeight="bold">
              {alertes.length} alerte(s) de stock:
            </Typography>
            {alertes.slice(0, 3).map((alerte, index) => (
              <Typography key={index} variant="body2">
                • {alerte.produit_nom}: {alerte.message}
              </Typography>
            ))}
            {alertes.length > 3 && (
              <Typography variant="body2">
                ... et {alertes.length - 3} autre(s)
              </Typography>
            )}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Valeur du Stock"
              value={`${stats.valeurTotaleStock.toLocaleString()} DA`}
              icon={<AssessmentIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Produits en Stock"
              value={stats.nombreProduitsStock}
              icon={<InventoryIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Ruptures de Stock"
              value={stats.produitsRuptureStock}
              icon={<WarningIcon />}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Stock Faible"
              value={stats.produitsStockFaible}
              icon={<TrendingDownIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Mouvements (7j)"
              value={stats.mouvementsRecents}
              icon={<HistoryIcon />}
              color="secondary"
            />
          </Grid>
        </Grid>

        {/* Onglets */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
            <Tab label="État des Stocks" />
            <Tab label="Mouvements" />
            <Tab label="Alertes" />
          </Tabs>
        </Paper>

        {/* Contenu des onglets */}
        {tabValue === 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                État des Stocks
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Produit</TableCell>
                      <TableCell>Stock Actuel</TableCell>
                      <TableCell>Stock Min/Max</TableCell>
                      <TableCell>Niveau</TableCell>
                      <TableCell>Valeur</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {inventaire.map((produit) => {
                      const status = getStockStatus(produit);
                      const percentage = getStockPercentage(produit);
                      
                      return (
                        <TableRow key={produit.id}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              {produit.nom}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {produit.categorie}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              {produit.stock_actuel} {produit.unite_mesure}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              Min: {produit.stock_minimum}
                            </Typography>
                            <Typography variant="body2">
                              Max: {produit.stock_maximum}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ width: '100%', mr: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={Math.min(percentage, 100)} 
                                color={getStockColor(status)}
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {percentage.toFixed(0)}%
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {(produit.stock_actuel * produit.prix_unitaire).toLocaleString()} DA
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStockLabel(status)}
                              color={getStockColor(status)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Tooltip title="Mouvement de stock">
                              <IconButton
                                size="small"
                                onClick={() => handleOpenDialog(produit)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {tabValue === 1 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Historique des Mouvements
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Produit</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Quantité</TableCell>
                      <TableCell>Motif</TableCell>
                      <TableCell>Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mouvements.map((mouvement) => (
                      <TableRow key={mouvement.id}>
                        <TableCell>
                          {new Date(mouvement.date_mouvement).toLocaleDateString('fr-FR')}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {mouvement.produit_nom}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={mouvement.type_mouvement === 'entree' ? 'Entrée' : 'Sortie'}
                            color={mouvement.type_mouvement === 'entree' ? 'success' : 'error'}
                            size="small"
                            icon={mouvement.type_mouvement === 'entree' ? <AddIcon /> : <RemoveIcon />}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography 
                            variant="body2" 
                            color={mouvement.type_mouvement === 'entree' ? 'success.main' : 'error.main'}
                            fontWeight="bold"
                          >
                            {mouvement.type_mouvement === 'entree' ? '+' : '-'}{mouvement.quantite}
                          </Typography>
                        </TableCell>
                        <TableCell>{mouvement.motif}</TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {mouvement.notes || '-'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {tabValue === 2 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alertes de Stock
              </Typography>
              {alertes.length === 0 ? (
                <Alert severity="success">
                  Aucune alerte de stock actuellement.
                </Alert>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Produit</TableCell>
                        <TableCell>Type d'Alerte</TableCell>
                        <TableCell>Stock Actuel</TableCell>
                        <TableCell>Seuil</TableCell>
                        <TableCell>Message</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {alertes.map((alerte) => (
                        <TableRow key={alerte.id}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              {alerte.produit_nom}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={alerte.type_alerte}
                              color={alerte.type_alerte === 'rupture' ? 'error' : 'warning'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{alerte.stock_actuel}</TableCell>
                          <TableCell>{alerte.seuil}</TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {alerte.message}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        )}

        {/* Dialog pour mouvement de stock */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            Mouvement de Stock - {selectedProduit?.nom}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Stock actuel: {selectedProduit?.stock_actuel} {selectedProduit?.unite_mesure}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Type de mouvement</InputLabel>
                  <Select
                    value={formData.type_mouvement}
                    onChange={(e) => setFormData({ ...formData, type_mouvement: e.target.value })}
                  >
                    <MenuItem value="entree">Entrée de stock</MenuItem>
                    <MenuItem value="sortie">Sortie de stock</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Quantité"
                  type="number"
                  value={formData.quantite}
                  onChange={(e) => setFormData({ ...formData, quantite: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Motif"
                  value={formData.motif}
                  onChange={(e) => setFormData({ ...formData, motif: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <DatePicker
                  label="Date du mouvement"
                  value={formData.date_mouvement}
                  onChange={(date) => setFormData({ ...formData, date_mouvement: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmitMouvement} variant="contained">
              Enregistrer
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default InventaireManagement;
