name: poultray_dz_mobile
description: Application mobile pour la gestion avicole Poultray DZ
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Material Design
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0
  
  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  
  # Real-time & WebSocket
  web_socket_channel: ^2.4.0
  socket_io_client: ^2.0.3+1
  
  # Camera & Media
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  photo_view: ^0.14.0
  
  # Location & GPS
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.0
  
  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  
  # File Handling
  path_provider: ^2.1.1
  file_picker: ^6.1.1
  open_file: ^3.3.2
  
  # Charts & Visualization
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7
  
  # Internationalization
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter
  
  # Utils
  uuid: ^4.2.1
  logger: ^2.0.2+1
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  url_launcher: ^6.2.2
  
  # Date & Time
  timezone: ^0.9.2
  
  # Animations
  lottie: ^2.7.0
  animations: ^2.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  
  # Linting
  flutter_lints: ^3.0.1
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/sounds/
    - assets/animations/
    - assets/translations/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700

flutter_intl:
  enabled: true
  class_name: S
  main_locale: fr
  arb_dir: lib/l10n
  output_dir: lib/generated
