const admin = (req, res, next) => {
  try {
    // Vérifier si l'utilisateur est authentifié
    if (!req.user) {
      console.error('Middleware admin: Utilisateur non authentifié pour la route:', req.originalUrl);
      return res.status(401).json({
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    // Vérifier si l'utilisateur a le rôle d'administrateur
    if (req.user.role !== 'admin') {
      console.error('Middleware admin: Accès refusé pour l\'utilisateur:', req.user.id, 'avec le rôle:', req.user.role);
      return res.status(403).json({
        message: 'Accès refusé. Droits d\'administrateur requis.',
        userRole: req.user.role
      });
    }

    // Si l'utilisateur est un administrateur, permettre l'accès à la route
    console.log('Accès admin autorisé pour l\'utilisateur:', req.user.id, 'à la route:', req.originalUrl);
    next();
  } catch (error) {
    console.error('Erreur dans le middleware admin:', error, 'Route:', req.originalUrl);
    res.status(500).json({
      message: 'Erreur lors de la vérification des droits d\'administrateur',
      error: error.message
    });
  }
};

module.exports = admin;
