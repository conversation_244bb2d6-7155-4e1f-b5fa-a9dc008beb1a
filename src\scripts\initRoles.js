require('dotenv').config({ path: 'K:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/.env' });
const { Role } = require('../models');

async function initRoles() {
  try {
    // Vérifier si des rôles existent déjà
    const existingRoles = await Role.count();
    if (existingRoles > 0) {
      console.log('Des rôles existent déjà, pas besoin d\'en créer de nouveaux');
      return true;
    }

    // Définir les rôles de base avec leurs permissions
    const baseRoles = [
      {
        name: 'eleveur',
        description: 'Gestion des élevages et des volailles',
        permissions: [
          'manage_volailles',
          'view_statistics',
          'manage_profile',
          'access_marketplace'
        ]
      },
      {
        name: 'veterinaire',
        description: 'Services vétérinaires et consultations',
        permissions: [
          'manage_consultations',
          'view_volailles',
          'manage_profile',
          'provide_services'
        ]
      },
      {
        name: 'marchand',
        description: 'Gestion des ventes et du marketplace',
        permissions: [
          'manage_products',
          'manage_sales',
          'view_statistics',
          'manage_profile'
        ]
      },
      {
        name: 'admin',
        description: 'Administration complète de la plateforme',
        permissions: [
          'manage_users',
          'manage_roles',
          'manage_platform',
          'view_statistics',
          'manage_all'
        ]
      }
    ];

    // Créer les rôles dans la base de données
    for (const roleData of baseRoles) {
      await Role.create(roleData);
      console.log(`Rôle ${roleData.name} créé avec succès`);
    }

    console.log('Tous les rôles ont été créés avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création des rôles:', error);
    return false;
  }
}

// Exécuter l'initialisation
initRoles()
  .then(() => {
    console.log('Script d\'initialisation des rôles terminé');
    process.exit(0);
  })
  .catch(error => {
    console.error('Erreur lors de l\'initialisation des rôles:', error);
    process.exit(1);
  });