const { Sequelize } = require('sequelize');
require('dotenv').config();

// Create test script to verify database tables
async function testDatabaseTables() {
  console.log('Testing database tables...');

  // Create a Sequelize instance using environment variables
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: false,
    }
  );

  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');

    // Query each table
    const tables = ['general_config', 'security_settings', 'smtp_configurations'];

    for (const table of tables) {
      console.log(`\n🔍 Testing table: ${table}`);
      const results = await sequelize.query(`SELECT * FROM ${table}`, { type: Sequelize.QueryTypes.SELECT });

      if (results.length > 0) {
        console.log(`✅ Table '${table}' exists and contains ${results.length} record(s).`);
        console.log('Sample data:', JSON.stringify(results[0], null, 2).substring(0, 200) + '...');
      } else {
        console.log(`⚠️ Table '${table}' exists but contains no records.`);
      }
    }

  } catch (error) {
    console.error('❌ Error testing database tables:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the test
testDatabaseTables();
