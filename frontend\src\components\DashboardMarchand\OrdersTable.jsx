import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { updateOrderStatus } from '../../services/marchandService';

/**
 * Composant pour afficher les commandes récentes sous forme de tableau
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.orders - Liste des commandes
 */
const OrdersTable = ({ orders = [] }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  // Gestion du menu d'actions
  const handleMenuOpen = (event, order) => {
    setAnchorEl(event.currentTarget);
    setSelectedOrder(order);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Ouvrir le dialogue de notes
  const handleOpenNotesDialog = () => {
    setNotes(selectedOrder?.notes || '');
    setOpenDialog(true);
    handleMenuClose();
  };

  // Fermer le dialogue de notes
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Mettre à jour le statut d'une commande
  const handleStatusChange = async (newStatus) => {
    if (!selectedOrder) return;

    try {
      setLoading(true);
      await updateOrderStatus(selectedOrder.id, newStatus);
      // Dans un environnement réel, on rafraîchirait les données
      // ou on mettrait à jour l'état local
      setLoading(false);
      handleMenuClose();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      setLoading(false);
    }
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'shipped':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Traduire le statut en français
  const translateStatus = (status) => {
    const statusMap = {
      'pending': 'En attente',
      'processing': 'En traitement',
      'shipped': 'Expédiée',
      'delivered': 'Livrée',
      'cancelled': 'Annulée'
    };
    return statusMap[status] || status;
  };

  // Formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Si aucune commande, afficher un message
  if (orders.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          Aucune commande récente à afficher
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <TableContainer component={Paper} sx={{ maxHeight: 400, overflowY: 'auto' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>N° Commande</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Montant</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Paiement</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id} hover>
                <TableCell>{order.order_number}</TableCell>
                <TableCell>{order.client_name}</TableCell>
                <TableCell>{formatDate(order.created_at)}</TableCell>
                <TableCell>{parseFloat(order.total_amount).toFixed(2)} DA</TableCell>
                <TableCell>
                  <Chip
                    label={translateStatus(order.status)}
                    color={getStatusColor(order.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={order.payment_status === 'paid' ? 'Payée' : 'Non payée'}
                    color={order.payment_status === 'paid' ? 'success' : 'default'}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, order)}
                    disabled={loading}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Menu d'actions */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleStatusChange('processing')} disabled={selectedOrder?.status === 'processing'}>Marquer en traitement</MenuItem>
        <MenuItem onClick={() => handleStatusChange('shipped')} disabled={selectedOrder?.status === 'shipped'}>Marquer comme expédiée</MenuItem>
        <MenuItem onClick={() => handleStatusChange('delivered')} disabled={selectedOrder?.status === 'delivered'}>Marquer comme livrée</MenuItem>
        <MenuItem onClick={() => handleStatusChange('cancelled')} disabled={selectedOrder?.status === 'cancelled'}>Annuler la commande</MenuItem>
        <MenuItem onClick={handleOpenNotesDialog}>Voir/Modifier les notes</MenuItem>
      </Menu>

      {/* Dialogue de notes */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Notes de commande</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Notes"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Annuler
          </Button>
          <Button onClick={handleCloseDialog} color="primary" variant="contained">
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default OrdersTable;
