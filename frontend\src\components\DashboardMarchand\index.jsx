import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, Paper, Button, Chip, CircularProgress, Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import InventoryIcon from '@mui/icons-material/Inventory';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import WarningIcon from '@mui/icons-material/Warning';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import LightbulbIcon from '@mui/icons-material/Lightbulb';

// Composants du tableau de bord
import StatCard from './StatCard';
import RevenueChart from './RevenueChart';
import TopProductsChart from './TopProductsChart';
import OrdersTable from './OrdersTable';
import LowStockAlert from './LowStockAlert';
import AIRecommendations from './AIRecommendations';

// Services API
import { fetchDashboardSummary, fetchOrders, fetchProducts, fetchRevenueData, fetchAIRecommendations } from '../../services/marchandService';

const DashboardMarchand = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);
  const [orders, setOrders] = useState([]);
  const [products, setProducts] = useState([]);
  const [revenueData, setRevenueData] = useState([]);
  const [aiRecommendations, setAIRecommendations] = useState(null);
  const [timeRange, setTimeRange] = useState('month'); // 'week', 'month', 'year'

  // Charger les données du tableau de bord
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Charger le résumé du tableau de bord
        const summaryData = await fetchDashboardSummary();
        setDashboardData(summaryData);

        // Charger les commandes en attente
        const ordersData = await fetchOrders({ status: 'pending' });
        setOrders(ordersData);

        // Charger les produits
        const productsData = await fetchProducts();
        setProducts(productsData);

        // Charger les données de revenus
        const today = new Date();
        let startDate;

        if (timeRange === 'week') {
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 7);
        } else if (timeRange === 'month') {
          startDate = new Date(today);
          startDate.setMonth(today.getMonth() - 1);
        } else {
          startDate = new Date(today);
          startDate.setFullYear(today.getFullYear() - 1);
        }

        const revenueResponse = await fetchRevenueData(startDate, today);
        setRevenueData(revenueResponse);

        // Charger les recommandations IA
        const aiResponse = await fetchAIRecommendations();
        setAIRecommendations(aiResponse);

        setLoading(false);
      } catch (err) {
        console.error('Erreur lors du chargement des données du tableau de bord:', err);
        setError('Impossible de charger les données du tableau de bord. Veuillez réessayer plus tard.');
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [timeRange]);

  // Filtrer les produits avec stock faible
  const lowStockProducts = products.filter(product =>
    product.stock_quantity <= product.stock_alert_threshold
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>Chargement du tableau de bord...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 4 }}>
        Tableau de Bord Marchand
      </Typography>

      {/* Filtres de période */}
      <Box sx={{ mb: 4, display: 'flex', gap: 2 }}>
        <Button
          variant={timeRange === 'week' ? 'contained' : 'outlined'}
          onClick={() => setTimeRange('week')}
        >
          Cette semaine
        </Button>
        <Button
          variant={timeRange === 'month' ? 'contained' : 'outlined'}
          onClick={() => setTimeRange('month')}
        >
          Ce mois
        </Button>
        <Button
          variant={timeRange === 'year' ? 'contained' : 'outlined'}
          onClick={() => setTimeRange('year')}
        >
          Cette année
        </Button>
      </Box>

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Produits en stock"
            value={dashboardData?.total_products || 0}
            icon={<InventoryIcon />}
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Commandes en attente"
            value={dashboardData?.pending_orders || 0}
            icon={<ShoppingCartIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Chiffre d'affaires mensuel"
            value={`${dashboardData?.monthly_revenue?.toFixed(2) || 0} DA`}
            icon={<AttachMoneyIcon />}
            color={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Produits en alerte stock"
            value={dashboardData?.low_stock_products || 0}
            icon={<WarningIcon />}
            color={theme.palette.error.main}
          />
        </Grid>
      </Grid>

      {/* Graphiques et tableaux */}
      <Grid container spacing={3}>
        {/* Graphique d'évolution des revenus */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Évolution des revenus</Typography>
            <RevenueChart data={revenueData} timeRange={timeRange} />
          </Paper>
        </Grid>

        {/* Top produits */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Top 5 des produits</Typography>
            <TopProductsChart products={dashboardData?.top_products || []} />
          </Paper>
        </Grid>

        {/* Alertes de stock faible */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <WarningIcon sx={{ mr: 1, color: theme.palette.warning.main }} />
              Alertes de stock
            </Typography>
            <LowStockAlert products={lowStockProducts} />
          </Paper>
        </Grid>

        {/* Recommandations IA */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <LightbulbIcon sx={{ mr: 1, color: theme.palette.info.main }} />
              Recommandations intelligentes
            </Typography>
            <AIRecommendations data={aiRecommendations} />
          </Paper>
        </Grid>

        {/* Tableau des commandes récentes */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Commandes récentes</Typography>
            <OrdersTable orders={orders} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardMarchand;
