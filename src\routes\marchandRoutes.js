// Enhanced Marchand Dashboard Routes
const express = require('express');
const router = express.Router();
const multer = require('multer');
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const { auth, checkRole } = require('../middleware/auth');
const AIRecommendationService = require('../services/aiRecommendationService');

// Configuration de multer pour le téléchargement des images
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // Limite de 5MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Le fichier doit être une image'), false);
    }
  },
});

// Middleware pour vérifier si l'utilisateur est un marchand
const isMarchand = async (req, res, next) => {
  try {
    if (req.user.role !== 'marchand') {
      return res.status(403).json({ message: 'Accès refusé. Rôle marchand requis.' });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isMarchand:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Protéger toutes les routes avec authentification et vérification du rôle marchand
router.use(auth);
router.use(isMarchand);

// @route   GET /api/marchand/dashboard
// @desc    Get enhanced marchand dashboard data
// @access  Private/Marchand
router.get('/dashboard', async (req, res) => {
  try {
    const marchandId = req.user.id;

    // Statistiques globales avec commandes et produits
    const statsQuery = `
      WITH stats AS (
        SELECT
          COUNT(DISTINCT p.id) as total_produits,
          COUNT(DISTINCT p.id) FILTER (WHERE p.created_at >= NOW() - INTERVAL '30 days') as produits_ajoutes_mois,
          COUNT(DISTINCT o.id) as total_commandes,
          COUNT(DISTINCT o.id) FILTER (WHERE o.created_at >= NOW() - INTERVAL '30 days') as commandes_mois,
          COALESCE(SUM(o.total_amount), 0) as chiffre_affaires_total,
          COALESCE(SUM(o.total_amount) FILTER (WHERE o.created_at >= NOW() - INTERVAL '30 days'), 0) as chiffre_affaires_mois,
          COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'pending') as commandes_en_attente,
          COUNT(DISTINCT p.id) FILTER (WHERE p.stock_quantity <= COALESCE(p.stock_alert_threshold, 10)) as produits_stock_faible
        FROM products p
        LEFT JOIN order_items oi ON oi.product_id = p.id
        LEFT JOIN orders o ON o.id = oi.order_id AND o.marchand_id = :marchandId
        WHERE p.marchand_id = :marchandId
      )
      SELECT
        total_produits,
        produits_ajoutes_mois,
        total_commandes,
        commandes_mois,
        chiffre_affaires_total,
        chiffre_affaires_mois,
        commandes_en_attente,
        produits_stock_faible,
        ROUND(CAST(
          CASE
            WHEN total_commandes > 0 THEN chiffre_affaires_total / total_commandes
            ELSE 0
          END AS numeric
        ), 2) as panier_moyen
      FROM stats
    `;

    const [statsResult] = await sequelize.query(statsQuery, {
      replacements: { marchandId },
      type: QueryTypes.SELECT
    });

    // Commandes récentes
    const commandesRecentesQuery = `
      SELECT
        o.id,
        o.order_number as numero_commande,
        o.total_amount as montant_total,
        o.status as statut,
        o.created_at as date_commande,
        u.name as client_nom,
        u.email as client_email,
        COUNT(oi.id) as nombre_articles,
        COALESCE(SUM(oi.quantity), 0) as quantite_totale
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.marchand_id = :marchandId
      GROUP BY o.id, o.order_number, o.total_amount, o.status, o.created_at, u.name, u.email
      ORDER BY o.created_at DESC
      LIMIT 10
    `;

    const commandesRecentes = await sequelize.query(commandesRecentesQuery, {
      replacements: { marchandId },
      type: QueryTypes.SELECT
    });

    // Produits populaires
    const produitsPopulairesQuery = `
      SELECT
        p.id,
        p.name,
        p.description,
        p.price,
        p.stock_quantity,
        p.category,
        COALESCE(SUM(oi.quantity), 0) as quantite_vendue,
        COALESCE(SUM(oi.total_price), 0) as revenus_generes,
        COUNT(DISTINCT oi.order_id) as nombre_commandes
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
        AND o.marchand_id = :marchandId
        AND o.created_at >= NOW() - INTERVAL '30 days'
        AND o.status != 'cancelled'
      WHERE p.marchand_id = :marchandId
      GROUP BY p.id, p.name, p.description, p.price, p.stock_quantity, p.category
      ORDER BY quantite_vendue DESC
      LIMIT 5
    `;

    const produitsPopulaires = await sequelize.query(produitsPopulairesQuery, {
      replacements: { marchandId },
      type: QueryTypes.SELECT
    });

    res.json({
      stats: statsResult || {
        total_produits: 0,
        produits_ajoutes_mois: 0,
        total_commandes: 0,
        commandes_mois: 0,
        chiffre_affaires_total: 0,
        chiffre_affaires_mois: 0,
        commandes_en_attente: 0,
        produits_stock_faible: 0,
        panier_moyen: 0
      },
      commandesRecentes: commandesRecentes || [],
      produitsPopulaires: produitsPopulaires || []
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des données du tableau de bord:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des données du tableau de bord',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/marchand/dashboard/summary
// @desc    Get marchand dashboard summary (legacy endpoint)
// @access  Private/Marchand
router.get('/dashboard/summary', async (req, res) => {
  try {
    const marchandId = req.user.id;

    const summaryQuery = `
      SELECT
        COUNT(DISTINCT p.id) as total_produits,
        COUNT(DISTINCT o.id) as total_commandes,
        COALESCE(SUM(o.total_amount), 0) as chiffre_affaires_total,
        COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'pending') as commandes_en_attente,
        COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'delivered') as commandes_livrees,
        COUNT(DISTINCT p.id) FILTER (WHERE p.stock_quantity <= COALESCE(p.stock_alert_threshold, 10)) as produits_stock_faible,
        COALESCE(
          ROUND(AVG(
            CASE WHEN o.status = 'delivered'
            THEN o.total_amount
            ELSE NULL
            END
          ), 2),
          0
        ) as panier_moyen
      FROM products p
      LEFT JOIN order_items oi ON oi.product_id = p.id
      LEFT JOIN orders o ON o.id = oi.order_id AND o.marchand_id = $1
      WHERE p.marchand_id = $1
    `;

    const result = await sequelize.query(summaryQuery, {
      replacements: [marchandId],
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: result[0]
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du résumé:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/marchand/dashboard/revenue
// @desc    Get marchand revenue by period
// @access  Private/Marchand
router.get('/dashboard/revenue', async (req, res) => {
  try {
    const marchandId = req.user.id;
    const { start_date, end_date, groupBy = 'day' } = req.query;

    if (!start_date || !end_date) {
      return res.status(400).json({
        status: 'error',
        message: 'Les dates de début et de fin sont requises'
      });
    }

    let dateGrouping;
    switch (groupBy.toLowerCase()) {
      case 'week':
        dateGrouping = 'week';
        break;
      case 'month':
        dateGrouping = 'month';
        break;
      case 'year':
        dateGrouping = 'year';
        break;
      default:
        dateGrouping = 'day';
    }

    const revenueQuery = `
      SELECT
        DATE_TRUNC($4, o.created_at) as periode,
        COUNT(DISTINCT o.id) as nombre_commandes,
        COALESCE(SUM(o.total_amount), 0) as revenus,
        COALESCE(SUM(oi.quantity), 0) as articles_vendus,
        COALESCE(
          ROUND(AVG(
            CASE WHEN o.status != 'cancelled'
            THEN o.total_amount
            ELSE NULL
            END
          ), 2),
          0
        ) as panier_moyen,
        COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'delivered') as commandes_livrees,
        COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'cancelled') as commandes_annulees
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.marchand_id = $1
        AND o.created_at >= $2
        AND o.created_at <= $3
      GROUP BY DATE_TRUNC($4, o.created_at)
      ORDER BY periode
    `;

    const revenueData = await sequelize.query(revenueQuery, {
      replacements: [marchandId, start_date, end_date, dateGrouping],
      type: QueryTypes.SELECT
    });

    // Calculate period-over-period changes
    const periodComparison = revenueData.reduce((acc, cur, i, arr) => {
      if (i > 0) {
        const prev = arr[i - 1];
        return {
          revenue_change: ((cur.revenus - prev.revenus) / prev.revenus * 100).toFixed(2),
          order_change: ((cur.nombre_commandes - prev.nombre_commandes) / prev.nombre_commandes * 100).toFixed(2),
          avg_order_change: ((cur.panier_moyen - prev.panier_moyen) / prev.panier_moyen * 100).toFixed(2)
        };
      }
      return acc;
    }, { revenue_change: '0', order_change: '0', avg_order_change: '0' });

    res.json({
      status: 'success',
      data: {
        revenue: revenueData,
        period: { start_date, end_date, groupBy },
        trends: periodComparison
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des revenus:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/marchand/products/quick
// @desc    Quick action - Add new product
// @access  Private/Marchand
router.post('/products/quick', async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      stock_quantity,
      category,
      stock_alert_threshold = 10
    } = req.body;

    const marchandId = req.user.id;

    const insertQuery = `
      INSERT INTO products (
        marchand_id, name, description, price, stock_quantity,
        category, stock_alert_threshold, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: [marchandId, name, description, price, stock_quantity, category, stock_alert_threshold],
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Produit ajouté avec succès',
      data: {
        product: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de l\'ajout rapide de produit:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de l\'ajout du produit'
    });
  }
});

// @route   PATCH /api/marchand/products/:id/stock
// @desc    Quick action - Update product stock
// @access  Private/Marchand
router.patch('/products/:id/stock', async (req, res) => {
  try {
    const { id } = req.params;
    const { stock_quantity, operation = 'set' } = req.body;
    const marchandId = req.user.id;

    if (stock_quantity === undefined) {
      return res.status(400).json({
        status: 'error',
        message: 'La quantité de stock est requise'
      });
    }

    let updateQuery;
    let replacements;

    if (operation === 'add') {
      updateQuery = `
        UPDATE products
        SET stock_quantity = stock_quantity + $1, updated_at = NOW()
        WHERE id = $2 AND marchand_id = $3
        RETURNING *
      `;
      replacements = [stock_quantity, id, marchandId];
    } else if (operation === 'subtract') {
      updateQuery = `
        UPDATE products
        SET stock_quantity = GREATEST(0, stock_quantity - $1), updated_at = NOW()
        WHERE id = $2 AND marchand_id = $3
        RETURNING *
      `;
      replacements = [stock_quantity, id, marchandId];
    } else {
      updateQuery = `
        UPDATE products
        SET stock_quantity = $1, updated_at = NOW()
        WHERE id = $2 AND marchand_id = $3
        RETURNING *
      `;
      replacements = [stock_quantity, id, marchandId];
    }

    const result = await sequelize.query(updateQuery, {
      replacements,
      type: QueryTypes.UPDATE
    });

    if (result[1] === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Produit non trouvé'
      });
    }

    res.json({
      status: 'success',
      message: 'Stock mis à jour avec succès',
      data: {
        product: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du stock:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la mise à jour du stock'
    });
  }
});

// @route   GET /api/marchand/ai/recommendations
// @desc    Get AI recommendations for inventory and pricing
// @access  Private/Marchand
router.get('/ai/recommendations', async (req, res) => {
  try {
    const marchandId = req.user.id;
    // Use the already imported class and instantiate it
    const aiService = new AIRecommendationService();
    const [inventory, pricing, marketing] = await Promise.all([
      aiService.generateInventoryRecommendations(marchandId),
      aiService.generatePricingRecommendations(marchandId),
      aiService.generateMarketingRecommendations(marchandId)
    ]);
    res.json({
      inventory,
      pricing,
      marketing,
      generated_at: new Date(),
      summary: {
        urgent_actions: [
          ...inventory.filter(r => r.type === 'URGENT_RESTOCK'),
          ...pricing.filter(r => r.type === 'NO_SALES'),
          ...marketing.filter(r => r.type === 'REVENUE_TREND' && r.trend === 'NEGATIVE')
        ],
        opportunities: [
          ...inventory.filter(r => r.type === 'HIGH_DEMAND'),
          ...pricing.filter(r => r.type === 'PRICE_INCREASE'),
          ...marketing.filter(r => r.type === 'BUNDLE_OPPORTUNITY')
        ]
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des recommandations:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des recommandations',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
