import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  FormControlLabel,
  Switch,
  Divider,
  InputAdornment,
  MenuItem,
  Snackbar
} from '@mui/material';
import {
  Save as SaveIcon,
  Settings as SettingsIcon,
  Language as LanguageIcon,
  ColorLens as ColorLensIcon
} from '@mui/icons-material';
import settingsService from '../../services/settingsService';
import { useLanguage } from '../../contexts/LanguageContext';

function GeneralSettings() {
  const { t, language, changeLanguage } = useLanguage();

  const [settings, setSettings] = useState({
    siteName: 'Poultray DZ',
    siteDescription: '',
    contactEmail: '',
    contactPhone: '',
    address: '',
    logo: '',
    favicon: '',
    primaryColor: '#2c5530',
    secondaryColor: '#e7eae2',
    defaultLanguage: 'fr',
    availableLanguages: ['fr', 'ar', 'en'],
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'HH:mm',
    timezone: 'Africa/Algiers',
    maintenanceMode: false,
    maintenanceMessage: '',
    allowUserRegistration: true,
    defaultUserRole: 'user',
    footerText: '© Poultray DZ',
    maxUploadSize: 5,
    socialLinks: {},
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const data = await settingsService.fetchGeneralSettings();
        if (data) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...data
          }));
        }
        setError('');
      } catch (err) {
        setError(err.response?.data?.message || t('errors.fetchingGeneralSettings', 'Error fetching general settings'));
        console.error("Error fetchGeneralSettings:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchSettings();
  }, [t]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' || type === 'switch' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const response = await settingsService.updateGeneralSettings(settings);
      setSuccess(t('success.generalSettingsSaved', 'General settings saved successfully!'));
      setToast({
        open: true,
        message: t('success.generalSettingsSaved', 'General settings saved successfully!'),
        severity: 'success'
      });
    } catch (err) {
      setError(err.response?.data?.message || t('errors.savingGeneralSettings', 'Error saving general settings'));
      setToast({
        open: true,
        message: err.response?.data?.message || t('errors.savingGeneralSettings', 'Error saving general settings'),
        severity: 'error'
      });
      console.error("Error handleSubmit:", err);
    } finally {
      setSaving(false);
    }
  };

  const handleCloseToast = () => {
    setToast({ ...toast, open: false });
  };

  const timezones = [
    'Africa/Algiers',
    'Europe/Paris',
    'Europe/London',
    'America/New_York',
    'Asia/Dubai',
    'Asia/Tokyo',
    'Australia/Sydney'
  ];

  const dateFormats = [
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'DD.MM.YYYY'
  ];

  const timeFormats = [
    'HH:mm',
    'hh:mm A',
    'HH:mm:ss'
  ];

  const languages = [
    { code: 'fr', name: 'Français' },
    { code: 'ar', name: 'العربية' },
    { code: 'en', name: 'English' }
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4, m: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        {t('settings.general.title', 'General Settings')}
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      <form onSubmit={handleSubmit}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          {t('settings.general.siteIdentity', 'Site Identity')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.siteName', 'Site Name')}
              name="siteName"
              value={settings.siteName}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.siteDescription', 'Site Description')}
              name="siteDescription"
              value={settings.siteDescription}
              onChange={handleChange}
              variant="outlined"
              multiline
              rows={2}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.contactEmail', 'Contact Email')}
              name="contactEmail"
              value={settings.contactEmail}
              onChange={handleChange}
              variant="outlined"
              type="email"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.contactPhone', 'Contact Phone')}
              name="contactPhone"
              value={settings.contactPhone}
              onChange={handleChange}
              variant="outlined"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('settings.general.address', 'Address')}
              name="address"
              value={settings.address}
              onChange={handleChange}
              variant="outlined"
              multiline
              rows={2}
            />
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.general.appearance', 'Appearance')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.logo', 'Logo URL')}
              name="logo"
              value={settings.logo}
              onChange={handleChange}
              variant="outlined"
              placeholder="https://example.com/logo.png"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.favicon', 'Favicon URL')}
              name="favicon"
              value={settings.favicon}
              onChange={handleChange}
              variant="outlined"
              placeholder="https://example.com/favicon.ico"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.primaryColor', 'Primary Color')}
              name="primaryColor"
              value={settings.primaryColor}
              onChange={handleChange}
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        backgroundColor: settings.primaryColor,
                        borderRadius: '4px',
                        border: '1px solid #ccc'
                      }}
                    />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.secondaryColor', 'Secondary Color')}
              name="secondaryColor"
              value={settings.secondaryColor}
              onChange={handleChange}
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        backgroundColor: settings.secondaryColor,
                        borderRadius: '4px',
                        border: '1px solid #ccc'
                      }}
                    />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.footerText', 'Footer Text')}
              name="footerText"
              value={settings.footerText}
              onChange={handleChange}
              variant="outlined"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.general.maxUploadSize', 'Max Upload Size (MB)')}
              name="maxUploadSize"
              value={settings.maxUploadSize}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 1, max: 100 }
              }}
            />
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.general.localization', 'Localization')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              select
              fullWidth
              label={t('settings.general.defaultLanguage', 'Default Language')}
              name="defaultLanguage"
              value={settings.defaultLanguage}
              onChange={handleChange}
              variant="outlined"
            >
              {languages.map((lang) => (
                <MenuItem key={lang.code} value={lang.code}>
                  {lang.name}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              select
              fullWidth
              label={t('settings.general.timezone', 'Timezone')}
              name="timezone"
              value={settings.timezone}
              onChange={handleChange}
              variant="outlined"
            >
              {timezones.map((tz) => (
                <MenuItem key={tz} value={tz}>
                  {tz}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              select
              fullWidth
              label={t('settings.general.dateFormat', 'Date Format')}
              name="dateFormat"
              value={settings.dateFormat}
              onChange={handleChange}
              variant="outlined"
            >
              {dateFormats.map((format) => (
                <MenuItem key={format} value={format}>
                  {format}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              select
              fullWidth
              label={t('settings.general.timeFormat', 'Time Format')}
              name="timeFormat"
              value={settings.timeFormat}
              onChange={handleChange}
              variant="outlined"
            >
              {timeFormats.map((format) => (
                <MenuItem key={format} value={format}>
                  {format}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.general.siteOperation', 'Site Operation')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.maintenanceMode}
                  onChange={handleChange}
                  name="maintenanceMode"
                  color="warning"
                />
              }
              label={t('settings.general.maintenanceMode', 'Maintenance Mode')}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.allowUserRegistration}
                  onChange={handleChange}
                  name="allowUserRegistration"
                  color="primary"
                />
              }
              label={t('settings.general.allowRegistration', 'Allow User Registration')}
            />
          </Grid>

          {settings.maintenanceMode && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('settings.general.maintenanceMessage', 'Maintenance Message')}
                name="maintenanceMessage"
                value={settings.maintenanceMessage}
                onChange={handleChange}
                variant="outlined"
                multiline
                rows={2}
                placeholder={t('settings.general.maintenanceMessagePlaceholder', 'Site under maintenance. Please check back later.')}
              />
            </Grid>
          )}
        </Grid>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={saving}
          >
            {saving ? <CircularProgress size={24} /> : t('actions.saveSettings', 'Save Settings')}
          </Button>
        </Box>
      </form>

      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        message={toast.message}
        severity={toast.severity}
      />
    </Paper>
  );
}

export default GeneralSettings;
