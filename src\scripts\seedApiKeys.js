/**
 * Script pour initialiser les clés API par défaut
 */
const db = require('../models');

async function seedApiKeys() {
  try {
    console.log('Initializing default API keys...');
    const ApiConfig = db.ApiConfig;

    // Liste des services API par défaut
    const defaultApiServices = [
      { serviceName: 'openai', apiKey: '' },
      { serviceName: 'google', apiKey: '' },
      { serviceName: 'azure_openai', apiKey: '' },
      { serviceName: 'claude', apiKey: '' },
      { serviceName: 'gemini', apiKey: '' }
    ];

    // Créer ou mettre à jour chaque service
    for (const service of defaultApiServices) {
      const [apiConfig, created] = await ApiConfig.findOrCreate({
        where: { serviceName: service.serviceName },
        defaults: {
          apiKey: service.apiKey
        }
      });

      if (created) {
        console.log(`Created API configuration for ${service.serviceName}`);
      } else {
        console.log(`API configuration for ${service.serviceName} already exists`);
      }
    }

    console.log('Default API keys initialization completed');
  } catch (error) {
    console.error('Error seeding API keys:', error);
  }
}

module.exports = seedApiKeys;
