const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Vente extends Model {
    static associate(models) {
      // Une vente appartient à un éleveur
      Vente.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });

      // Une vente appartient à un acheteur (User)
      Vente.belongsTo(models.User, {
        foreignKey: 'acheteur_id',
        as: 'acheteur'
      });

      // Une vente concerne une volaille
      Vente.belongsTo(models.Volaille, {
        foreignKey: 'volaille_id',
        as: 'volaille'
      });
    }

    // Méthodes statiques personnalisées
    static async findByEleveur(eleveurId) {
      return await this.findAll({
        where: { eleveur_id: eleveurId },
        include: [
          { model: this.sequelize.models.User, as: 'acheteur' },
          { model: this.sequelize.models.Volaille, as: 'volaille' }
        ],
        order: [['date_vente', 'DESC']]
      });
    }

    static async findByAcheteur(acheteurId) {
      return await this.findAll({
        where: { acheteur_id: acheteurId },
        include: [
          { model: this.sequelize.models.Eleveur, as: 'eleveur' },
          { model: this.sequelize.models.Volaille, as: 'volaille' }
        ],
        order: [['date_vente', 'DESC']]
      });
    }

    static async getStatistiquesVentes(eleveurId = null) {
      const whereClause = eleveurId ? { eleveur_id: eleveurId } : {};

      return await this.findAll({
        attributes: [
          [this.sequelize.fn('COUNT', this.sequelize.col('id')), 'nombreVentes'],
          [this.sequelize.fn('SUM', this.sequelize.col('quantite')), 'quantiteTotale'],
          [this.sequelize.fn('SUM', this.sequelize.col('montant_total')), 'montantTotal'],
          [this.sequelize.fn('AVG', this.sequelize.col('prix_unitaire')), 'prixMoyen']
        ],
        where: whereClause,
        raw: true
      });
    }
  }

  Vente.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'eleveurs',
        key: 'id'
      }
    },
    acheteur_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    volaille_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'volailles',
        key: 'id'
      }
    },
    quantite: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    prix_unitaire: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    montant_total: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    date_vente: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    statut: {
      type: DataTypes.STRING(20),
      defaultValue: 'completee',
      validate: {
        isIn: [['en_attente', 'completee', 'annulee', 'remboursee']]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Vente',
    tableName: 'ventes',
    timestamps: false,
    indexes: [
      {
        fields: ['date_vente']
      },
      {
        fields: ['eleveur_id']
      },
      {
        fields: ['acheteur_id']
      }
    ],
    hooks: {
      beforeSave: (vente, options) => {
        // Calculer automatiquement le montant total
        if (vente.quantite && vente.prix_unitaire) {
          vente.montant_total = vente.quantite * vente.prix_unitaire;
        }
      }
    }
  });

  return Vente;
};
