const express = require('express');
const router = express.Router();
const { body, validationResult, param, query } = require('express-validator');
const { auth } = require('../middleware/auth');
const { hasRole } = require('../middleware/roleCheck');
const SuiviVeterinaire = require('../models/suiviVeterinaire');
const Eleveur = require('../models/eleveur');
const Veterinaire = require('../models/veterinaire');
const Volaille = require('../models/volaille');

/**
 * Routes pour la gestion du suivi vétérinaire
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 */

// Middleware de validation pour la création/modification d'un suivi
const validateSuivi = [
  body('type_intervention')
    .isIn(['consultation', 'vaccination', 'traitement', 'urgence', 'prevention', 'controle'])
    .withMessage('Type d\'intervention invalide'),

  body('date_intervention')
    .isISO8601()
    .withMessage('La date d\'intervention doit être une date valide'),

  body('statut')
    .optional()
    .isIn(['planifie', 'en_cours', 'termine', 'annule', 'reporte'])
    .withMessage('Statut invalide'),

  body('urgence')
    .optional()
    .isIn(['faible', 'moyenne', 'haute', 'critique'])
    .withMessage('Niveau d\'urgence invalide'),

  body('cout_total')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le coût total doit être un nombre positif'),

  body('volaille_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('L\'ID de volaille doit être un nombre entier positif')
];

// GET /api/suivi-veterinaire - Récupérer tous les suivis d'un éleveur
router.get('/',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    query('date_debut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    query('date_fin')
      .optional()
      .isISO8601()
      .withMessage('La date de fin doit être une date valide'),
    query('type_intervention')
      .optional()
      .isIn(['consultation', 'vaccination', 'traitement', 'urgence', 'prevention', 'controle'])
      .withMessage('Type d\'intervention invalide'),
    query('statut')
      .optional()
      .isIn(['planifie', 'en_cours', 'termine', 'annule', 'reporte'])
      .withMessage('Statut invalide'),
    query('urgence')
      .optional()
      .isIn(['faible', 'moyenne', 'haute', 'critique'])
      .withMessage('Niveau d\'urgence invalide'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('La page doit être un nombre entier positif'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('La limite doit être entre 1 et 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const { date_debut, date_fin, type_intervention, statut, urgence, page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      // Construire les conditions de recherche selon le rôle
      const whereConditions = {};

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      } else if (req.user.role === 'admin' && req.query.eleveur_id) {
        whereConditions.eleveur_id = req.query.eleveur_id;
      } else if (req.user.role === 'admin' && req.query.veterinaire_id) {
        whereConditions.veterinaire_id = req.query.veterinaire_id;
      }

      if (type_intervention) whereConditions.type_intervention = type_intervention;
      if (statut) whereConditions.statut = statut;
      if (urgence) whereConditions.urgence = urgence;

      if (date_debut && date_fin) {
        whereConditions.date_intervention = {
          [require('sequelize').Op.between]: [date_debut, date_fin]
        };
      } else if (date_debut) {
        whereConditions.date_intervention = {
          [require('sequelize').Op.gte]: date_debut
        };
      } else if (date_fin) {
        whereConditions.date_intervention = {
          [require('sequelize').Op.lte]: date_fin
        };
      }

      const { count, rows: suivis } = await SuiviVeterinaire.findAndCountAll({
        where: whereConditions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['date_intervention', 'DESC']],
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email', 'telephone']
          },
          {
            model: Veterinaire,
            as: 'veterinaire',
            attributes: ['id', 'nom', 'prenom', 'email', 'telephone', 'specialite'],
            required: false
          },
          {
            model: Volaille,
            as: 'volaille',
            attributes: ['id', 'lot_numero', 'race', 'quantite'],
            required: false
          }
        ]
      });

      res.json({
        success: true,
        data: {
          suivis,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des suivis:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des suivis'
      });
    }
  }
);

// GET /api/suivi-veterinaire/planifies - Récupérer les interventions planifiées
router.get('/planifies',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  async (req, res) => {
    try {
      const whereConditions = {
        statut: 'planifie',
        date_intervention: {
          [require('sequelize').Op.gte]: new Date()
        }
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivis = await SuiviVeterinaire.findInterventionsPlanifiees(
        req.user.role === 'eleveur' ? req.user.id : null,
        req.user.role === 'veterinaire' ? req.user.id : null
      );

      res.json({
        success: true,
        data: suivis
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des interventions planifiées:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des interventions planifiées'
      });
    }
  }
);

// GET /api/suivi-veterinaire/urgentes - Récupérer les interventions urgentes
router.get('/urgentes',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  async (req, res) => {
    try {
      const suivis = await SuiviVeterinaire.findInterventionsUrgentes(
        req.user.role === 'eleveur' ? req.user.id : null,
        req.user.role === 'veterinaire' ? req.user.id : null
      );

      res.json({
        success: true,
        data: suivis
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des interventions urgentes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des interventions urgentes'
      });
    }
  }
);

// GET /api/suivi-veterinaire/statistiques - Récupérer les statistiques
router.get('/statistiques',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    query('date_debut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    query('date_fin')
      .optional()
      .isISO8601()
      .withMessage('La date de fin doit être une date valide')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const { date_debut, date_fin } = req.query;
      const dateDebutDefault = date_debut || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const dateFinDefault = date_fin || new Date().toISOString().split('T')[0];

      let statistiques;

      if (req.user.role === 'eleveur') {
        statistiques = await SuiviVeterinaire.getStatistiques(req.user.id, null, dateDebutDefault, dateFinDefault);
      } else if (req.user.role === 'veterinaire') {
        statistiques = await SuiviVeterinaire.getStatistiques(null, req.user.id, dateDebutDefault, dateFinDefault);
      } else {
        const eleveurId = req.query.eleveur_id || null;
        const veterinaireId = req.query.veterinaire_id || null;
        statistiques = await SuiviVeterinaire.getStatistiques(eleveurId, veterinaireId, dateDebutDefault, dateFinDefault);
      }

      res.json({
        success: true,
        data: statistiques
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des statistiques'
      });
    }
  }
);

// GET /api/suivi-veterinaire/plan-vaccination - Récupérer le plan de vaccination
router.get('/plan-vaccination',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'eleveur' ? req.user.id : req.query.eleveur_id;

      if (!eleveurId) {
        return res.status(400).json({
          success: false,
          message: 'ID éleveur requis'
        });
      }

      const planVaccination = await SuiviVeterinaire.getPlanVaccination(eleveurId);

      res.json({
        success: true,
        data: planVaccination
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du plan de vaccination:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération du plan de vaccination'
      });
    }
  }
);

// GET /api/suivi-veterinaire/:id - Récupérer un suivi spécifique
router.get('/:id',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivi = await SuiviVeterinaire.findOne({
        where: whereConditions,
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email', 'telephone']
          },
          {
            model: Veterinaire,
            as: 'veterinaire',
            attributes: ['id', 'nom', 'prenom', 'email', 'telephone', 'specialite'],
            required: false
          },
          {
            model: Volaille,
            as: 'volaille',
            attributes: ['id', 'lot_numero', 'race', 'quantite'],
            required: false
          }
        ]
      });

      if (!suivi) {
        return res.status(404).json({
          success: false,
          message: 'Suivi vétérinaire non trouvé'
        });
      }

      res.json({
        success: true,
        data: suivi
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du suivi:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération du suivi'
      });
    }
  }
);

// POST /api/suivi-veterinaire - Créer un nouveau suivi
router.post('/',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  validateSuivi,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      let eleveurId, veterinaireId;

      if (req.user.role === 'eleveur') {
        eleveurId = req.user.id;
        veterinaireId = req.body.veterinaire_id || null;
      } else if (req.user.role === 'veterinaire') {
        eleveurId = req.body.eleveur_id;
        veterinaireId = req.user.id;
      } else {
        eleveurId = req.body.eleveur_id;
        veterinaireId = req.body.veterinaire_id || null;
      }

      if (!eleveurId) {
        return res.status(400).json({
          success: false,
          message: 'ID éleveur requis'
        });
      }

      // Vérifier que l'éleveur existe
      const eleveur = await Eleveur.findByPk(eleveurId);
      if (!eleveur) {
        return res.status(404).json({
          success: false,
          message: 'Éleveur non trouvé'
        });
      }

      // Vérifier que le vétérinaire existe si spécifié
      if (veterinaireId) {
        const veterinaire = await Veterinaire.findByPk(veterinaireId);
        if (!veterinaire) {
          return res.status(404).json({
            success: false,
            message: 'Vétérinaire non trouvé'
          });
        }
      }

      // Vérifier que le lot de volailles existe si spécifié
      if (req.body.volaille_id) {
        const volaille = await Volaille.findOne({
          where: {
            id: req.body.volaille_id,
            eleveur_id: eleveurId
          }
        });

        if (!volaille) {
          return res.status(404).json({
            success: false,
            message: 'Lot de volailles non trouvé'
          });
        }
      }

      const suiviData = {
        ...req.body,
        eleveur_id: eleveurId,
        veterinaire_id: veterinaireId
      };

      const suivi = await SuiviVeterinaire.create(suiviData);

      res.status(201).json({
        success: true,
        message: 'Suivi vétérinaire créé avec succès',
        data: suivi
      });
    } catch (error) {
      console.error('Erreur lors de la création du suivi:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la création du suivi'
      });
    }
  }
);

// PUT /api/suivi-veterinaire/:id - Mettre à jour un suivi
router.put('/:id',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    ...validateSuivi
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivi = await SuiviVeterinaire.findOne({ where: whereConditions });

      if (!suivi) {
        return res.status(404).json({
          success: false,
          message: 'Suivi vétérinaire non trouvé'
        });
      }

      await suivi.update(req.body);

      res.json({
        success: true,
        message: 'Suivi vétérinaire mis à jour avec succès',
        data: suivi
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du suivi:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la mise à jour du suivi'
      });
    }
  }
);

// POST /api/suivi-veterinaire/:id/statut - Mettre à jour le statut d'un suivi
router.post('/:id/statut',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('nouveau_statut')
      .isIn(['planifie', 'en_cours', 'termine', 'annule', 'reporte'])
      .withMessage('Statut invalide'),
    body('notes_statut')
      .optional()
      .isString()
      .withMessage('Les notes doivent être une chaîne de caractères')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivi = await SuiviVeterinaire.findOne({ where: whereConditions });

      if (!suivi) {
        return res.status(404).json({
          success: false,
          message: 'Suivi vétérinaire non trouvé'
        });
      }

      await suivi.mettreAJourStatut(req.body.nouveau_statut, req.body.notes_statut);

      res.json({
        success: true,
        message: 'Statut mis à jour avec succès',
        data: suivi
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la mise à jour du statut'
      });
    }
  }
);

// POST /api/suivi-veterinaire/:id/suivi - Ajouter un suivi
router.post('/:id/suivi',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('notes_suivi')
      .isString()
      .notEmpty()
      .withMessage('Les notes de suivi sont requises'),
    body('date_suivi')
      .optional()
      .isISO8601()
      .withMessage('La date de suivi doit être une date valide')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivi = await SuiviVeterinaire.findOne({ where: whereConditions });

      if (!suivi) {
        return res.status(404).json({
          success: false,
          message: 'Suivi vétérinaire non trouvé'
        });
      }

      await suivi.ajouterSuivi(req.body.notes_suivi, req.body.date_suivi);

      res.json({
        success: true,
        message: 'Suivi ajouté avec succès',
        data: suivi
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout du suivi:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'ajout du suivi'
      });
    }
  }
);

// DELETE /api/suivi-veterinaire/:id - Supprimer un suivi
router.delete('/:id',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      } else if (req.user.role === 'veterinaire') {
        whereConditions.veterinaire_id = req.user.id;
      }

      const suivi = await SuiviVeterinaire.findOne({ where: whereConditions });

      if (!suivi) {
        return res.status(404).json({
          success: false,
          message: 'Suivi vétérinaire non trouvé'
        });
      }

      // Vérifier que le suivi peut être supprimé (pas terminé avec des données importantes)
      if (suivi.statut === 'termine' && (suivi.diagnostic || suivi.traitement_prescrit)) {
        return res.status(400).json({
          success: false,
          message: 'Impossible de supprimer un suivi terminé avec diagnostic ou traitement'
        });
      }

      await suivi.destroy();

      res.json({
        success: true,
        message: 'Suivi vétérinaire supprimé avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la suppression du suivi:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la suppression du suivi'
      });
    }
  }
);

module.exports = router;
