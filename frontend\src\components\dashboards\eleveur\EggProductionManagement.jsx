import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const EggProductionManagement = () => {
  const { t } = useTranslation();
  const [productions, setProductions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduction, setSelectedProduction] = useState(null);
  const [formData, setFormData] = useState({
    date_production: new Date().toISOString().split('T')[0],
    quantite_totale: '',
    oeufs_intacts: '',
    oeufs_casses: '',
    calibre: 'moyen',
    notes: ''
  });

  useEffect(() => {
    fetchProductions();
  }, []);

  const fetchProductions = async () => {
    try {
      const response = await axios.get('/api/production-oeufs');
      setProductions(response.data);
      setLoading(false);
    } catch (err) {
      setError(t('errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleOpenDialog = (production = null) => {
    if (production) {
      setSelectedProduction(production);
      setFormData({
        date_production: production.date_production.split('T')[0],
        quantite_totale: production.quantite_totale,
        oeufs_intacts: production.oeufs_intacts,
        oeufs_casses: production.oeufs_casses,
        calibre: production.calibre,
        notes: production.notes || ''
      });
    } else {
      setSelectedProduction(null);
      setFormData({
        date_production: new Date().toISOString().split('T')[0],
        quantite_totale: '',
        oeufs_intacts: '',
        oeufs_casses: '',
        calibre: 'moyen',
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduction(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (selectedProduction) {
        await axios.put(`/api/production-oeufs/${selectedProduction.id}`, formData);
      } else {
        await axios.post('/api/production-oeufs', formData);
      }
      fetchProductions();
      handleCloseDialog();
    } catch (err) {
      setError(t('errors.saveFailed'));
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('confirmations.deleteProduction'))) {
      try {
        await axios.delete(`/api/production-oeufs/${id}`);
        fetchProductions();
      } catch (err) {
        setError(t('errors.deleteFailed'));
      }
    }
  };

  const getProductionStats = () => {
    if (!productions.length) return null;

    const total = productions.reduce((acc, curr) => acc + curr.quantite_totale, 0);
    const moyenne = total / productions.length;
    const intacts = productions.reduce((acc, curr) => acc + curr.oeufs_intacts, 0);
    const casses = productions.reduce((acc, curr) => acc + curr.oeufs_casses, 0);
    const tauxCasse = (casses / total) * 100;

    return { total, moyenne, intacts, casses, tauxCasse };
  };

  const stats = getProductionStats();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          {t('eggs.production')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('eggs.addProduction')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {stats && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  {t('eggs.totalProduction')}
                </Typography>
                <Typography variant="h5">
                  {stats.total}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  {t('eggs.averageProduction')}
                </Typography>
                <Typography variant="h5">
                  {stats.moyenne.toFixed(1)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  {t('eggs.intactEggs')}
                </Typography>
                <Typography variant="h5">
                  {stats.intacts}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  {t('eggs.breakageRate')}
                </Typography>
                <Typography variant="h5">
                  {stats.tauxCasse.toFixed(1)}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      <Box sx={{ height: 300, mb: 3 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={productions}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date_production"
              tickFormatter={(date) => new Date(date).toLocaleDateString()}
            />
            <YAxis />
            <Tooltip
              labelFormatter={(date) => new Date(date).toLocaleDateString()}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="quantite_totale"
              name={t('eggs.totalQuantity')}
              stroke="#8884d8"
            />
            <Line
              type="monotone"
              dataKey="oeufs_intacts"
              name={t('eggs.intactEggs')}
              stroke="#82ca9d"
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('eggs.date')}</TableCell>
              <TableCell align="right">{t('eggs.totalQuantity')}</TableCell>
              <TableCell align="right">{t('eggs.intactEggs')}</TableCell>
              <TableCell align="right">{t('eggs.brokenEggs')}</TableCell>
              <TableCell>{t('eggs.size')}</TableCell>
              <TableCell>{t('common.notes')}</TableCell>
              <TableCell align="center">{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {productions.map((production) => (
              <TableRow key={production.id}>
                <TableCell>
                  {new Date(production.date_production).toLocaleDateString()}
                </TableCell>
                <TableCell align="right">{production.quantite_totale}</TableCell>
                <TableCell align="right">{production.oeufs_intacts}</TableCell>
                <TableCell align="right">{production.oeufs_casses}</TableCell>
                <TableCell>{t(`eggs.sizes.${production.calibre}`)}</TableCell>
                <TableCell>{production.notes}</TableCell>
                <TableCell align="center">
                  <IconButton
                    onClick={() => handleOpenDialog(production)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDelete(production.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedProduction
            ? t('eggs.editProduction')
            : t('eggs.addProduction')}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="date_production"
                label={t('eggs.date')}
                type="date"
                value={formData.date_production}
                onChange={handleInputChange}
                fullWidth
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="quantite_totale"
                label={t('eggs.totalQuantity')}
                type="number"
                value={formData.quantite_totale}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="oeufs_intacts"
                label={t('eggs.intactEggs')}
                type="number"
                value={formData.oeufs_intacts}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="oeufs_casses"
                label={t('eggs.brokenEggs')}
                type="number"
                value={formData.oeufs_casses}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="calibre-label">{t('eggs.size')}</InputLabel>
                <Select
                  labelId="calibre-label"
                  name="calibre"
                  value={formData.calibre}
                  onChange={handleInputChange}
                  label={t('eggs.size')}
                >
                  <MenuItem value="petit">{t('eggs.sizes.petit')}</MenuItem>
                  <MenuItem value="moyen">{t('eggs.sizes.moyen')}</MenuItem>
                  <MenuItem value="gros">{t('eggs.sizes.gros')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="notes"
                label={t('common.notes')}
                multiline
                rows={3}
                value={formData.notes}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {selectedProduction ? t('common.save') : t('common.add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EggProductionManagement;
