import 'package:json_annotation/json_annotation.dart';

part 'chat_message.g.dart';

@JsonSerializable()
class ChatMessage {
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thread_id')
  final int threadId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id')
  final int senderId;
  @Json<PERSON><PERSON>(name: 'receiver_id')
  final int receiverId;
  final String content;
  @<PERSON>son<PERSON>ey(name: 'message_type')
  final String messageType; // 'text', 'image', 'offer', 'system', 'location'
  @<PERSON>son<PERSON>ey(name: 'attachment_url')
  final String? attachmentUrl;
  @Json<PERSON>ey(name: 'attachment_type')
  final String? attachmentType; // 'image', 'document'
  @JsonKey(name: 'offer_price')
  final double? offerPrice;
  @<PERSON>son<PERSON>ey(name: 'offer_quantity')
  final int? offerQuantity;
  @<PERSON>sonKey(name: 'offer_expires_at')
  final DateTime? offerExpiresAt;
  @<PERSON>sonKey(name: 'offer_status')
  final String? offerStatus; // 'pending', 'accepted', 'rejected', 'expired'
  final double? latitude;
  final double? longitude;
  @J<PERSON><PERSON><PERSON>(name: 'location_name')
  final String? locationName;
  @Json<PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @Json<PERSON>ey(name: 'read_at')
  final DateTime? readAt;
  @JsonKey(name: 'is_delivered')
  final bool isDelivered;
  @JsonKey(name: 'delivered_at')
  final DateTime? deliveredAt;
  @JsonKey(name: 'is_edited')
  final bool isEdited;
  @JsonKey(name: 'edited_at')
  final DateTime? editedAt;
  @JsonKey(name: 'reply_to_id')
  final int? replyToId;
  @JsonKey(name: 'reply_to_content')
  final String? replyToContent;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  
  // Additional fields for offline support
  @JsonKey(name: 'local_id')
  final String? localId;
  @JsonKey(name: 'is_synced')
  final bool isSynced;
  @JsonKey(name: 'sync_attempts')
  final int syncAttempts;
  @JsonKey(name: 'sync_error')
  final String? syncError;
  @JsonKey(name: 'temp_attachment_path')
  final String? tempAttachmentPath;
  
  // Sender information for display
  @JsonKey(name: 'sender_name')
  final String? senderName;
  @JsonKey(name: 'sender_avatar')
  final String? senderAvatar;

  const ChatMessage({
    this.id,
    required this.threadId,
    required this.senderId,
    required this.receiverId,
    required this.content,
    this.messageType = 'text',
    this.attachmentUrl,
    this.attachmentType,
    this.offerPrice,
    this.offerQuantity,
    this.offerExpiresAt,
    this.offerStatus,
    this.latitude,
    this.longitude,
    this.locationName,
    this.isRead = false,
    this.readAt,
    this.isDelivered = false,
    this.deliveredAt,
    this.isEdited = false,
    this.editedAt,
    this.replyToId,
    this.replyToContent,
    required this.createdAt,
    required this.updatedAt,
    this.localId,
    this.isSynced = true,
    this.syncAttempts = 0,
    this.syncError,
    this.tempAttachmentPath,
    this.senderName,
    this.senderAvatar,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);

  // Helper getters
  bool get isTextMessage => messageType == 'text';
  bool get isImageMessage => messageType == 'image';
  bool get isOfferMessage => messageType == 'offer';
  bool get isSystemMessage => messageType == 'system';
  bool get isLocationMessage => messageType == 'location';
  bool get hasAttachment => attachmentUrl != null || tempAttachmentPath != null;
  bool get hasOffer => offerPrice != null;
  bool get hasLocation => latitude != null && longitude != null;
  bool get isReply => replyToId != null;
  bool get isOfferPending => offerStatus == 'pending';
  bool get isOfferAccepted => offerStatus == 'accepted';
  bool get isOfferRejected => offerStatus == 'rejected';
  bool get isOfferExpired => offerStatus == 'expired' || (offerExpiresAt != null && DateTime.now().isAfter(offerExpiresAt!));
  bool get needsSync => !isSynced;
  bool get hasSyncError => syncError != null;
  bool get isLocalOnly => id == null && localId != null;
  
  String get displayContent {
    switch (messageType) {
      case 'image':
        return '📷 Image';
      case 'offer':
        return '💰 Offre: ${formattedOfferPrice}';
      case 'location':
        return '📍 ${locationName ?? 'Position partagée'}';
      case 'system':
        return content;
      default:
        return content;
    }
  }
  
  String get formattedOfferPrice {
    if (offerPrice == null) return '';
    return '${offerPrice!.toStringAsFixed(0)} DA';
  }
  
  String get formattedOfferQuantity {
    if (offerQuantity == null) return '';
    return '${offerQuantity!} unités';
  }
  
  String get offerStatusDisplay {
    if (offerStatus == null) return '';
    switch (offerStatus!) {
      case 'pending':
        return 'En attente';
      case 'accepted':
        return 'Acceptée';
      case 'rejected':
        return 'Refusée';
      case 'expired':
        return 'Expirée';
      default:
        return offerStatus!;
    }
  }
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}min';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}j';
    } else {
      return '${createdAt.day}/${createdAt.month}';
    }
  }
  
  String get formattedTime {
    return '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }
  
  String get deliveryStatus {
    if (!isSynced) {
      return 'En cours d\'envoi...';
    } else if (!isDelivered) {
      return 'Envoyé';
    } else if (!isRead) {
      return 'Livré';
    } else {
      return 'Lu';
    }
  }
  
  String get syncStatusDisplay {
    if (isSynced) {
      return 'Synchronisé';
    } else if (hasSyncError) {
      return 'Erreur: $syncError';
    } else {
      return 'En attente de synchronisation';
    }
  }
  
  String get attachmentDisplayUrl {
    return tempAttachmentPath ?? attachmentUrl ?? '';
  }

  // Copy with method
  ChatMessage copyWith({
    int? id,
    int? threadId,
    int? senderId,
    int? receiverId,
    String? content,
    String? messageType,
    String? attachmentUrl,
    String? attachmentType,
    double? offerPrice,
    int? offerQuantity,
    DateTime? offerExpiresAt,
    String? offerStatus,
    double? latitude,
    double? longitude,
    String? locationName,
    bool? isRead,
    DateTime? readAt,
    bool? isDelivered,
    DateTime? deliveredAt,
    bool? isEdited,
    DateTime? editedAt,
    int? replyToId,
    String? replyToContent,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? localId,
    bool? isSynced,
    int? syncAttempts,
    String? syncError,
    String? tempAttachmentPath,
    String? senderName,
    String? senderAvatar,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      threadId: threadId ?? this.threadId,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      messageType: messageType ?? this.messageType,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      attachmentType: attachmentType ?? this.attachmentType,
      offerPrice: offerPrice ?? this.offerPrice,
      offerQuantity: offerQuantity ?? this.offerQuantity,
      offerExpiresAt: offerExpiresAt ?? this.offerExpiresAt,
      offerStatus: offerStatus ?? this.offerStatus,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      isDelivered: isDelivered ?? this.isDelivered,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      replyToId: replyToId ?? this.replyToId,
      replyToContent: replyToContent ?? this.replyToContent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      localId: localId ?? this.localId,
      isSynced: isSynced ?? this.isSynced,
      syncAttempts: syncAttempts ?? this.syncAttempts,
      syncError: syncError ?? this.syncError,
      tempAttachmentPath: tempAttachmentPath ?? this.tempAttachmentPath,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
    );
  }
}

// Message templates for common scenarios
class MessageTemplates {
  static ChatMessage createTextMessage({
    required int threadId,
    required int senderId,
    required int receiverId,
    required String content,
    int? replyToId,
    String? replyToContent,
  }) {
    return ChatMessage(
      threadId: threadId,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      messageType: 'text',
      replyToId: replyToId,
      replyToContent: replyToContent,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      localId: DateTime.now().millisecondsSinceEpoch.toString(),
      isSynced: false,
    );
  }
  
  static ChatMessage createOfferMessage({
    required int threadId,
    required int senderId,
    required int receiverId,
    required double offerPrice,
    required int offerQuantity,
    DateTime? offerExpiresAt,
    String? additionalMessage,
  }) {
    final content = additionalMessage ?? 
        'Offre: ${offerPrice.toStringAsFixed(0)} DA pour $offerQuantity unités';
    
    return ChatMessage(
      threadId: threadId,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      messageType: 'offer',
      offerPrice: offerPrice,
      offerQuantity: offerQuantity,
      offerExpiresAt: offerExpiresAt ?? DateTime.now().add(const Duration(days: 3)),
      offerStatus: 'pending',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      localId: DateTime.now().millisecondsSinceEpoch.toString(),
      isSynced: false,
    );
  }
  
  static ChatMessage createImageMessage({
    required int threadId,
    required int senderId,
    required int receiverId,
    required String imagePath,
    String? caption,
  }) {
    return ChatMessage(
      threadId: threadId,
      senderId: senderId,
      receiverId: receiverId,
      content: caption ?? 'Image partagée',
      messageType: 'image',
      tempAttachmentPath: imagePath,
      attachmentType: 'image',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      localId: DateTime.now().millisecondsSinceEpoch.toString(),
      isSynced: false,
    );
  }
  
  static ChatMessage createLocationMessage({
    required int threadId,
    required int senderId,
    required int receiverId,
    required double latitude,
    required double longitude,
    String? locationName,
  }) {
    return ChatMessage(
      threadId: threadId,
      senderId: senderId,
      receiverId: receiverId,
      content: 'Position partagée: ${locationName ?? 'Localisation'}',
      messageType: 'location',
      latitude: latitude,
      longitude: longitude,
      locationName: locationName,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      localId: DateTime.now().millisecondsSinceEpoch.toString(),
      isSynced: false,
    );
  }
  
  static ChatMessage createSystemMessage({
    required int threadId,
    required String content,
  }) {
    return ChatMessage(
      threadId: threadId,
      senderId: 0, // System user ID
      receiverId: 0,
      content: content,
      messageType: 'system',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: true,
    );
  }
}

// Enums for message types
enum MessageType {
  text('text'),
  image('image'),
  offer('offer'),
  system('system'),
  location('location');

  const MessageType(this.value);
  final String value;
}

enum OfferStatus {
  pending('pending'),
  accepted('accepted'),
  rejected('rejected'),
  expired('expired');

  const OfferStatus(this.value);
  final String value;
}

enum AttachmentType {
  image('image'),
  document('document');

  const AttachmentType(this.value);
  final String value;
}