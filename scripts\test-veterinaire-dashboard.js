// Script pour tester les endpoints du tableau de bord vétérinaire
const axios = require('axios');
const config = require('../config/config.json');
const { sequelize } = require('../src/models');

// Configuration de l'API
const API_URL = config.apiUrl || 'http://localhost:3000/api';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';

async function validateDashboardResponse(data) {
  const requiredStats = [
    'totalPrescriptions', 'prescriptionsMois', 'totalConsultations',
    'consultationsMois', 'eleveursSuivis', 'consultationsSemaineProchaine',
    'prescriptionsActives', 'satisfactionMoyenne', 'coutMoyenConsultation'
  ];

  console.log('\n📊 Validation des statistiques:');
  for (const stat of requiredStats) {
    if (data.stats[stat] !== undefined) {
      console.log(`✅ ${stat}: ${data.stats[stat]}`);
    } else {
      console.error(`❌ Statistique manquante: ${stat}`);
    }
  }

  console.log('\n🗓️ Validation des consultations à venir:');
  if (Array.isArray(data.consultationsAVenir)) {
    console.log(`✅ ${data.consultationsAVenir.length} consultation(s) à venir trouvée(s)`);
  } else {
    console.error('❌ consultationsAVenir n\'est pas un tableau');
  }

  console.log('\n📋 Validation des prescriptions récentes:');
  if (Array.isArray(data.prescriptionsRecentes)) {
    console.log(`✅ ${data.prescriptionsRecentes.length} prescription(s) récente(s) trouvée(s)`);
  } else {
    console.error('❌ prescriptionsRecentes n\'est pas un tableau');
  }

  console.log('\n⚠️ Validation des alertes santé:');
  if (Array.isArray(data.alertesSante)) {
    console.log(`✅ ${data.alertesSante.length} alerte(s) trouvée(s)`);
  } else {
    console.error('❌ alertesSante n\'est pas un tableau');
  }
}

async function validateNotificationsResponse(data) {
  console.log('\n🔔 Validation des notifications:');
  if (!Array.isArray(data.notifications)) {
    console.error('❌ notifications n\'est pas un tableau');
    return;
  }

  console.log(`✅ ${data.notifications.length} notification(s) trouvée(s)`);

  const types = data.notifications.reduce((acc, notif) => {
    acc[notif.type] = (acc[notif.type] || 0) + 1;
    return acc;
  }, {});

  console.log('\nTypes de notifications:');
  Object.entries(types).forEach(([type, count]) => {
    console.log(`📌 ${type}: ${count}`);
  });
}

async function main() {
  try {
    console.log('🔍 Test des endpoints du tableau de bord vétérinaire...\n');

    // Login
    console.log('🔐 Tentative de connexion...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (!loginResponse.data || !loginResponse.data.token) {
      console.error('❌ Échec de la connexion:', loginResponse.data);
      return;
    }

    console.log('✅ Connexion réussie');
    const token = loginResponse.data.token;

    // Configuration d'axios avec le token
    const api = axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    // Test du dashboard
    console.log('\n📊 Test de l\'endpoint du tableau de bord...');
    try {
      const dashboardResponse = await api.get('/veterinaire/dashboard');
      console.log('✅ Réponse du tableau de bord reçue');
      await validateDashboardResponse(dashboardResponse.data.data);
    } catch (error) {
      console.error('❌ Erreur dashboard:', error.response?.data || error.message);
      console.error('Status:', error.response?.status);
    }

    // Test des notifications
    console.log('\n🔔 Test de l\'endpoint des notifications...');
    try {
      const notificationsResponse = await api.get('/veterinaire/notifications');
      console.log('✅ Réponse des notifications reçue');
      await validateNotificationsResponse(notificationsResponse.data.data);
    } catch (error) {
      console.error('❌ Erreur notifications:', error.response?.data || error.message);
      console.error('Status:', error.response?.status);
    }

  } catch (error) {
    console.error('❌ Erreur globale:', error.message);
    if (error.response) {
      console.error('Détails:', error.response.data);
      console.error('Status:', error.response.status);
    }
  } finally {
    await sequelize.close();
  }
}

// Execute if running directly
if (require.main === module) {
  main().catch(console.error);
}
