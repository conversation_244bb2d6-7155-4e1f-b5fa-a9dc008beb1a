require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = process.env.PORT || 3003;

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

// Middleware de base
app.use(cors());
app.use(express.json());

// Route de base
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur de test Poultray DZ avec nouvelles routes',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Test de connexion à la base de données
app.get('/api/test/db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW() as current_time');
    res.json({
      message: 'Connexion à la base de données réussie',
      time: result.rows[0].current_time
    });
  } catch (error) {
    console.error('Erreur de connexion à la base de données:', error);
    res.status(500).json({
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Route de connexion simplifiée
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, firebase_token, firebase_uid } = req.body;
    
    console.log('Tentative de connexion pour:', email);
    
    // Chercher l'utilisateur dans la base de données
    const userQuery = 'SELECT * FROM users WHERE email = $1';
    const userResult = await pool.query(userQuery, [email]);
    
    if (userResult.rows.length === 0) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }
    
    const user = userResult.rows[0];
    console.log('Utilisateur trouvé:', user.username, 'Rôle:', user.role);
    
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      first_name: user.first_name,
      last_name: user.last_name,
      firebase_uid: user.firebase_uid
    });
  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    res.status(500).json({
      message: 'Erreur lors de la connexion',
      error: error.message
    });
  }
});

// Route pour récupérer l'utilisateur connecté
app.get('/api/auth/user', async (req, res) => {
  try {
    // Simulation - dans un vrai système, on vérifierait le token JWT
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ message: 'Token manquant' });
    }
    
    // Pour le test, on retourne un utilisateur admin
    res.json({
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      first_name: 'Admin',
      last_name: 'Poultray'
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour les statistiques admin
app.get('/api/admin/stats', async (req, res) => {
  try {
    // Récupérer le nombre d'utilisateurs par rôle
    const userStats = await pool.query(
      `SELECT role, COUNT(*) as count
       FROM users
       GROUP BY role`
    );
    
    const stats = {
      totalUsers: 0,
      totalAdmins: 0,
      totalEleveurs: 0,
      totalVeterinaires: 0,
      totalMarchands: 0,
      users: []
    };
    
    userStats.rows.forEach(row => {
      stats.users.push(row);
      const count = parseInt(row.count);
      stats.totalUsers += count;
      
      switch(row.role) {
        case 'admin':
          stats.totalAdmins = count;
          break;
        case 'eleveur':
          stats.totalEleveurs = count;
          break;
        case 'veterinaire':
          stats.totalVeterinaires = count;
          break;
        case 'marchand':
          stats.totalMarchands = count;
          break;
      }
    });
    
    // Ajouter des données simulées pour les volailles et ventes
    stats.volailles = {
      total: 150,
      disponibles: 120,
      vendues: 30
    };
    
    stats.ventes = {
      total: 25,
      montantTotal: 125000,
      moyenneParVente: 5000
    };
    
    res.json(stats);
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
});

// Route pour récupérer les utilisateurs avec pagination
app.get('/api/admin/users', async (req, res) => {
  try {
    const { role, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let countWhereClause = '';
    const params = [];
    const countParams = [];

    if (role) {
      whereClause = ' WHERE role = $1';
      countWhereClause = ' WHERE role = $1';
      params.push(role);
      countParams.push(role);
    }

    // Requête pour compter le total
    const countQuery = `SELECT COUNT(*) as total FROM users${countWhereClause}`;
    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    // Requête pour récupérer les utilisateurs
    const usersQuery = `
      SELECT id, username, email, first_name, last_name, role, phone, address, created_at, updated_at
      FROM users${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;
    
    params.push(limit, offset);
    const usersResult = await pool.query(usersQuery, params);

    res.json({
      users: usersResult.rows,
      total: total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
});

// Route pour les volailles
app.get('/api/volailles', async (req, res) => {
  try {
    const volaillesQuery = `
      SELECT v.*, u.username as eleveur_nom, u.first_name as eleveur_prenom
      FROM volailles v
      LEFT JOIN users u ON v.eleveur_id = u.id
      ORDER BY v.created_at DESC
      LIMIT 50
    `;
    
    const result = await pool.query(volaillesQuery);
    res.json(result.rows);
  } catch (error) {
    console.error('Erreur lors de la récupération des volailles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des volailles',
      error: error.message
    });
  }
});

// Route pour les éleveurs
app.get('/api/eleveurs', async (req, res) => {
  try {
    const eleveursQuery = `
      SELECT u.id, u.username as nom, u.first_name as prenom, u.email, u.phone as telephone, u.address as adresse
      FROM users u
      WHERE u.role = 'eleveur'
      ORDER BY u.username
    `;
    
    const result = await pool.query(eleveursQuery);
    res.json(result.rows);
  } catch (error) {
    console.error('Erreur lors de la récupération des éleveurs:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des éleveurs',
      error: error.message
    });
  }
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route non trouvée' });
});

// Démarrer le serveur
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Serveur de test Poultray DZ démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 API: http://localhost:${PORT}/api/test/db`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
  process.exit(1);
});
