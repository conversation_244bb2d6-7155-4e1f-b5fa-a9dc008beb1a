const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class BlogPost extends Model {
    static associate(models) {
      BlogPost.belongsTo(models.User, { // Assurez-vous que le modèle User est correctement défini et accessible via models.User
        foreignKey: 'author_id',
        as: 'author'
      });
    }

    static async findBySlug(slug) { // Correction: findBySlug est une méthode statique
      return await this.findOne({
        where: { slug },
        include: ['author']
      });
    }

    static async findPublished() { // Correction: findPublished est une méthode statique
      return await this.findAll({
        where: { status: 'published' },
        order: [['published_at', 'DESC']],
        include: ['author']
      });
    }

    static async findByAuthor(authorId) { // Correction: findByAuthor est une méthode statique
      return await this.findAll({
        where: { author_id: authorId },
        order: [['created_at', 'DESC']]
      });
    }

    static async findByTag(tag) { // Correction: findByTag est une méthode statique
      return await this.findAll({
        where: sequelize.literal(`tags ? '${tag}'`), // sequelize est disponible ici
        order: [['published_at', 'DESC']],
        include: ['author']
      });
    }
  }

  BlogPost.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    slug: {
      type: DataTypes.STRING(200),
      allowNull: false,
      unique: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    excerpt: {
      type: DataTypes.TEXT
    },
    author_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users', // Nom de la table Users
        key: 'id'
      }
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'draft'
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: []
    },
    featured_image: {
      type: DataTypes.STRING(255)
    },
    published_at: {
      type: DataTypes.DATE
    }
  }, {
    sequelize, // Passer l'instance de sequelize
    modelName: 'BlogPost',
    tableName: 'blog_posts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: (post) => {
        if (post.status === 'published' && !post.published_at) {
          post.published_at = new Date();
        }
      },
      beforeUpdate: (post) => {
        if (post.changed('status') && post.status === 'published' && !post.published_at) {
          post.published_at = new Date();
        }
      }
    }
  });

  return BlogPost;
};
