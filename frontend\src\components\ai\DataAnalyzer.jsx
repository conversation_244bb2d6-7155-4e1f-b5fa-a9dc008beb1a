import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Lightbulb as LightbulbIcon,
  Storefront as StoreIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const DataAnalyzer = () => {
  const [formData, setFormData] = useState({
    language: 'fr',
    dataType: 'all',
  });

  const [platformData, setPlatformData] = useState(null);
  const [analysis, setAnalysis] = useState('');
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch platform data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setDataLoading(true);

        // In a real application, you would fetch this data from your API
        // For now, we'll use mock data
        setTimeout(() => {
          const mockData = {
            eleveurs: {
              total: 120,
              active: 98,
              inactive: 22,
              byRegion: {
                'Alger': 35,
                'Oran': 28,
                'Constantine': 22,
                'Annaba': 15,
                'Autres': 20,
              },
              growth: '+15% depuis le dernier trimestre',
            },
            volailles: {
              total: 25000,
              byType: {
                'Poulets': 15000,
                'Dindes': 5000,
                'Canards': 3000,
                'Cailles': 2000,
              },
              averagePrice: {
                'Poulets': 450,
                'Dindes': 1200,
                'Canards': 600,
                'Cailles': 150,
              },
              totalValue: 12500000,
            },
            ventes: {
              totalAmount: 8500000,
              totalTransactions: 450,
              averageOrderValue: 18889,
              byMonth: {
                'Janvier': 1200000,
                'Février': 1350000,
                'Mars': 1500000,
                'Avril': 1400000,
                'Mai': 1550000,
                'Juin': 1500000,
              },
            },
            veterinaires: {
              total: 35,
              consultations: 320,
              prescriptions: 280,
            },
            marchands: {
              total: 45,
              products: 120,
              averageSales: 189000,
            },
          };

          setPlatformData(mockData);
          setDataLoading(false);
        }, 1000);

      } catch (err) {
        console.error('Error fetching platform data:', err);
        setError('Erreur lors de la récupération des données');
        setDataLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Prepare data based on selected data type
      let dataToAnalyze = platformData;
      if (formData.dataType !== 'all') {
        dataToAnalyze = { [formData.dataType]: platformData[formData.dataType] };
      }

      const response = await axiosInstance.post('/ai/analyze', {
        data: dataToAnalyze,
        language: formData.language,
      });

      setAnalysis(response.data.analysis);
      setSuccess('Analyse générée avec succès !');
    } catch (err) {
      console.error('Error generating analysis:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération de l\'analyse');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(analysis);
    setSuccess('Analyse copiée dans le presse-papier !');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleReset = () => {
    setFormData({
      language: 'fr',
      dataType: 'all',
    });
    setAnalysis('');
    setError('');
    setSuccess('');
  };

  // Function to render data cards
  const renderDataCards = () => {
    if (!platformData) return null;

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Éleveurs" />
            <CardContent>
              <Typography variant="h4">{platformData.eleveurs.total}</Typography>
              <Typography variant="body2" color="text.secondary">
                {platformData.eleveurs.active} actifs, {platformData.eleveurs.inactive} inactifs
              </Typography>
              <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                {platformData.eleveurs.growth}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Volailles" />
            <CardContent>
              <Typography variant="h4">{platformData.volailles.total}</Typography>
              <Typography variant="body2" color="text.secondary">
                Valeur totale: {platformData.volailles.totalValue.toLocaleString()} DA
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Ventes" />
            <CardContent>
              <Typography variant="h4">{platformData.ventes.totalAmount.toLocaleString()} DA</Typography>
              <Typography variant="body2" color="text.secondary">
                {platformData.ventes.totalTransactions} transactions
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Function to parse and structure the analysis
  const renderStructuredAnalysis = () => {
    if (!analysis) return null;

    // Simple parsing to identify sections in the analysis
    // In a real app, you might want to structure the AI response more formally
    const sections = [
      { title: 'Tendances et Modèles', icon: <TrendingUpIcon />, keyword: 'tendance' },
      { title: 'Points d\'Attention', icon: <WarningIcon />, keyword: 'attention' },
      { title: 'Recommandations', icon: <LightbulbIcon />, keyword: 'recommand' },
      { title: 'Opportunités de Marché', icon: <StoreIcon />, keyword: 'opportunité' },
    ];

    return (
      <Box sx={{ mt: 3 }}>
        {sections.map((section, index) => (
          <Accordion key={index} defaultExpanded={index === 0}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {section.icon}
                <Typography variant="subtitle1">{section.title}</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {analysis}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AnalyticsIcon color="primary" /> Analyse des Données par IA
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Utilisez l'intelligence artificielle pour analyser les données de la plateforme et obtenir des insights précieux.
        </Typography>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        {dataLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {renderDataCards()}

            <form onSubmit={handleSubmit}>
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Type de données</InputLabel>
                  <Select
                    name="dataType"
                    value={formData.dataType}
                    onChange={handleChange}
                    label="Type de données"
                  >
                    <MenuItem value="all">Toutes les données</MenuItem>
                    <MenuItem value="eleveurs">Éleveurs uniquement</MenuItem>
                    <MenuItem value="volailles">Volailles uniquement</MenuItem>
                    <MenuItem value="ventes">Ventes uniquement</MenuItem>
                    <MenuItem value="veterinaires">Vétérinaires uniquement</MenuItem>
                    <MenuItem value="marchands">Marchands uniquement</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel>Langue</InputLabel>
                  <Select
                    name="language"
                    value={formData.language}
                    onChange={handleChange}
                    label="Langue"
                  >
                    <MenuItem value="fr">Français</MenuItem>
                    <MenuItem value="ar">Arabe</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <AnalyticsIcon />}
                >
                  {loading ? 'Analyse en cours...' : 'Analyser les données'}
                </Button>

                <Button
                  type="button"
                  variant="outlined"
                  color="secondary"
                  onClick={handleReset}
                  startIcon={<RefreshIcon />}
                >
                  Réinitialiser
                </Button>
              </Box>
            </form>
          </>
        )}
      </Paper>

      {analysis && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Résultats de l'Analyse</Typography>
            <Button
              variant="outlined"
              size="small"
              startIcon={<CopyIcon />}
              onClick={handleCopyToClipboard}
            >
              Copier
            </Button>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mb: 2 }}>
            <Chip
              label={`Langue: ${formData.language === 'fr' ? 'Français' : 'Arabe'}`}
              size="small"
              sx={{ mr: 1 }}
            />
            <Chip
              label={`Données: ${
                formData.dataType === 'all' ? 'Complètes' :
                formData.dataType === 'eleveurs' ? 'Éleveurs' :
                formData.dataType === 'volailles' ? 'Volailles' :
                formData.dataType === 'ventes' ? 'Ventes' :
                formData.dataType === 'veterinaires' ? 'Vétérinaires' : 'Marchands'
              }`}
              size="small"
            />
          </Box>

          {renderStructuredAnalysis()}
        </Paper>
      )}
    </Box>
  );
};

export default DataAnalyzer;
