const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
const Produit = sequelize.define('Produit', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  marchand_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  nom: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  categorie: {
    type: DataTypes.ENUM(
      'aliments',
      'medicaments',
      'equipements',
      'accessoires',
      'supplements',
      'materiaux',
      'outils',
      'autres'
    ),
    allowNull: false
  },
  sous_categorie: {
    type: DataTypes.STRING,
    allowNull: true
  },
  marque: {
    type: DataTypes.STRING,
    allowNull: true
  },
  reference_produit: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  prix_unitaire: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  prix_gros: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  quantite_minimum_gros: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  unite_mesure: {
    type: DataTypes.ENUM('kg', 'g', 'l', 'ml', 'piece', 'paquet', 'boite', 'sac'),
    allowNull: false,
    defaultValue: 'piece'
  },
  stock_disponible: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  stock_minimum: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  stock_maximum: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0
    }
  },
  statut: {
    type: DataTypes.ENUM('actif', 'inactif', 'rupture_stock', 'discontinue'),
    allowNull: false,
    defaultValue: 'actif'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'URLs des images du produit'
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Spécifications techniques du produit'
  },
  date_expiration: {
    type: DataTypes.DATE,
    allowNull: true
  },
  numero_lot: {
    type: DataTypes.STRING,
    allowNull: true
  },
  fournisseur: {
    type: DataTypes.STRING,
    allowNull: true
  },
  conditions_stockage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  instructions_utilisation: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  poids: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true,
    comment: 'Poids en kg'
  },
  dimensions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Dimensions (longueur, largeur, hauteur) en cm'
  },
  promotion_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  prix_promotion: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  date_debut_promotion: {
    type: DataTypes.DATE,
    allowNull: true
  },
  date_fin_promotion: {
    type: DataTypes.DATE,
    allowNull: true
  },
  note_moyenne: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 5
    }
  },
  nombre_avis: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Tags pour la recherche et le filtrage'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'produits',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['marchand_id']
    },
    {
      fields: ['categorie']
    },
    {
      fields: ['statut']
    },
    {
      fields: ['reference_produit']
    },
    {
      fields: ['nom']
    },
    {
      fields: ['prix_unitaire']
    },
    {
      fields: ['stock_disponible']
    },
    {
      fields: ['promotion_active']
    }
  ],
  hooks: {
    beforeCreate: async (produit) => {
      // Générer une référence produit unique si non fournie
      if (!produit.reference_produit) {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        produit.reference_produit = `PROD-${year}${month}-${random}`;
      }

      // Vérifier le statut du stock
      if (produit.stock_disponible <= produit.stock_minimum) {
        produit.statut = 'rupture_stock';
      }
    },

    beforeUpdate: async (produit) => {
      // Mettre à jour le statut en fonction du stock
      if (produit.changed('stock_disponible')) {
        if (produit.stock_disponible <= 0) {
          produit.statut = 'rupture_stock';
        } else if (produit.stock_disponible > produit.stock_minimum && produit.statut === 'rupture_stock') {
          produit.statut = 'actif';
        }
      }

      // Vérifier les promotions expirées
      if (produit.promotion_active && produit.date_fin_promotion) {
        const now = new Date();
        if (now > produit.date_fin_promotion) {
          produit.promotion_active = false;
          produit.prix_promotion = null;
        }
      }
    }
  }
});

// Associations - Temporairement désactivées
// Produit.associate = (models) => {
//   // Un produit appartient à un marchand
//   Produit.belongsTo(models.User, {
//     foreignKey: 'marchand_id',
//     as: 'marchand'
//   });
//
//   // Un produit peut avoir plusieurs commandes
//   Produit.hasMany(models.CommandeItem, {
//     foreignKey: 'produit_id',
//     as: 'commandes'
//   });
//
//   // Un produit peut avoir plusieurs avis
//   Produit.hasMany(models.AvisProduit, {
//     foreignKey: 'produit_id',
//     as: 'avis'
//   });
// };

// Méthodes d'instance
Produit.prototype.getPrixEffectif = function() {
  if (this.promotion_active && this.prix_promotion) {
    const now = new Date();
    if ((!this.date_debut_promotion || now >= this.date_debut_promotion) &&
        (!this.date_fin_promotion || now <= this.date_fin_promotion)) {
      return this.prix_promotion;
    }
  }
  return this.prix_unitaire;
};

Produit.prototype.isEnRuptureStock = function() {
  return this.stock_disponible <= this.stock_minimum;
};

Produit.prototype.getPourcentageReduction = function() {
  if (this.promotion_active && this.prix_promotion && this.prix_unitaire > 0) {
    return Math.round(((this.prix_unitaire - this.prix_promotion) / this.prix_unitaire) * 100);
  }
  return 0;
};

// Méthodes statiques
Produit.getStatsByMarchand = async function(marchandId) {
  const [totalProduits, produitsActifs, produitsRupture, produitsPromotion] = await Promise.all([
    this.count({
      where: { marchand_id: marchandId }
    }),
    this.count({
      where: {
        marchand_id: marchandId,
        statut: 'actif'
      }
    }),
    this.count({
      where: {
        marchand_id: marchandId,
        statut: 'rupture_stock'
      }
    }),
    this.count({
      where: {
        marchand_id: marchandId,
        promotion_active: true
      }
    })
  ]);

  return {
    total_produits: totalProduits,
    produits_actifs: produitsActifs,
    produits_rupture: produitsRupture,
    produits_promotion: produitsPromotion
  };
};

Produit.getProduitsEnRupture = async function(marchandId) {
  return await this.findAll({
    where: {
      marchand_id: marchandId,
      [sequelize.Sequelize.Op.or]: [
        { statut: 'rupture_stock' },
        {
          stock_disponible: {
            [sequelize.Sequelize.Op.lte]: sequelize.Sequelize.col('stock_minimum')
          }
        }
      ]
    },
    order: [['stock_disponible', 'ASC']]
  });
};

Produit.getProduitsPopulaires = async function(marchandId, limit = 10) {
  return await this.findAll({
    where: {
      marchand_id: marchandId,
      statut: 'actif'
    },
    order: [
      ['nombre_avis', 'DESC'],
      ['note_moyenne', 'DESC']
    ],
    limit
  });
};

Produit.rechercherProduits = async function(criteres) {
  const {
    terme,
    categorie,
    prixMin,
    prixMax,
    marchandId,
    enStock,
    promotion,
    page = 1,
    limit = 20
  } = criteres;

  const offset = (page - 1) * limit;
  const whereConditions = {};

  if (terme) {
    whereConditions[sequelize.Sequelize.Op.or] = [
      {
        nom: {
          [sequelize.Sequelize.Op.iLike]: `%${terme}%`
        }
      },
      {
        description: {
          [sequelize.Sequelize.Op.iLike]: `%${terme}%`
        }
      }
    ];
  }

  if (categorie) {
    whereConditions.categorie = categorie;
  }

  if (prixMin || prixMax) {
    whereConditions.prix_unitaire = {};
    if (prixMin) whereConditions.prix_unitaire[sequelize.Sequelize.Op.gte] = prixMin;
    if (prixMax) whereConditions.prix_unitaire[sequelize.Sequelize.Op.lte] = prixMax;
  }

  if (marchandId) {
    whereConditions.marchand_id = marchandId;
  }

  if (enStock) {
    whereConditions.stock_disponible = {
      [sequelize.Sequelize.Op.gt]: 0
    };
  }

  if (promotion) {
    whereConditions.promotion_active = true;
  }

  return await this.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: sequelize.models.User,
        as: 'marchand',
        attributes: ['id', 'nom', 'email']
      }
    ],
    order: [['created_at', 'DESC']],
    limit,
    offset
  });
};

  return Produit;
};
