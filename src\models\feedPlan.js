const { DataTypes } = require('sequelize');

/**
 * FeedPlan model - Represents feeding plans and schedules for poultry
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedPlan = sequelize.define('FeedPlan', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    farm_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'fermes',
        key: 'id'
      }
    },
    poultry_batch_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'poussins',
        key: 'id'
      },
      comment: 'specific poultry batch for this plan'
    },
    plan_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [3, 100],
        notEmpty: true
      }
    },
    plan_type: {
      type: DataTypes.STRING(30),
      allowNull: false,
      validate: {
        isIn: [['starter', 'grower', 'finisher', 'layer', 'breeder', 'custom']]
      },
      comment: 'starter, grower, finisher, layer, breeder, custom'
    },
    poultry_type: {
      type: DataTypes.STRING(30),
      allowNull: false,
      validate: {
        isIn: [['poussins', 'chairs', 'pondeuses', 'dindes', 'mixed']]
      },
      comment: 'type of poultry this plan is for'
    },
    age_range_start: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
        max: 365
      },
      comment: 'starting age in days'
    },
    age_range_end: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 730
      },
      comment: 'ending age in days'
    },
    target_weight_start: {
      type: DataTypes.DECIMAL(6, 3),
      allowNull: true,
      validate: {
        min: 0.001
      },
      comment: 'target weight at start in kg'
    },
    target_weight_end: {
      type: DataTypes.DECIMAL(6, 3),
      allowNull: true,
      validate: {
        min: 0.001
      },
      comment: 'target weight at end in kg'
    },
    daily_feed_per_bird: {
      type: DataTypes.DECIMAL(6, 3),
      allowNull: false,
      validate: {
        min: 0.001
      },
      comment: 'daily feed quantity per bird in kg'
    },
    feeding_frequency: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 2,
      validate: {
        min: 1,
        max: 10
      },
      comment: 'number of feedings per day'
    },
    feeding_times: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'array of feeding times ["07:00", "12:00", "18:00"]'
    },
    feed_composition: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'feed composition requirements and percentages'
    },
    nutritional_requirements: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'required nutritional values (protein, energy, etc.)'
    },
    estimated_fcr: {
      type: DataTypes.DECIMAL(5, 3),
      allowNull: true,
      validate: {
        min: 0.5,
        max: 10
      },
      comment: 'estimated feed conversion ratio'
    },
    estimated_cost_per_bird: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'estimated total feed cost per bird'
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: true
      },
      comment: 'when this plan starts'
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: true,
        isAfterStart(value) {
          if (value && this.start_date && new Date(value) <= new Date(this.start_date)) {
            throw new Error('End date must be after start date');
          }
        }
      },
      comment: 'when this plan ends'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'whether this plan is currently active'
    },
    is_template: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this is a reusable template'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'user who created this plan'
    },
    approved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'veterinarian or expert who approved this plan'
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when this plan was approved'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'additional notes about this plan'
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'draft',
      validate: {
        isIn: [['draft', 'active', 'completed', 'suspended', 'archived']]
      },
      comment: 'draft, active, completed, suspended, archived'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_plans',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['farm_id', 'status']
      },
      {
        fields: ['poultry_batch_id']
      },
      {
        fields: ['plan_type', 'poultry_type']
      },
      {
        fields: ['age_range_start', 'age_range_end']
      },
      {
        fields: ['is_template', 'status']
      },
      {
        fields: ['start_date', 'end_date']
      }
    ],
    hooks: {
      beforeValidate: (plan) => {
        // Validate age range
        if (plan.age_range_start >= plan.age_range_end) {
          throw new Error('Age range start must be less than age range end');
        }
        
        // Set default feeding times if not provided
        if (!plan.feeding_times && plan.feeding_frequency) {
          plan.feeding_times = generateDefaultFeedingTimes(plan.feeding_frequency);
        }
        
        // Validate feeding times count matches frequency
        if (plan.feeding_times && Array.isArray(plan.feeding_times)) {
          if (plan.feeding_times.length !== plan.feeding_frequency) {
            throw new Error('Number of feeding times must match feeding frequency');
          }
        }
      },
      
      beforeCreate: (plan) => {
        // Auto-generate plan name if not provided
        if (!plan.plan_name) {
          plan.plan_name = `${plan.plan_type}_${plan.poultry_type}_${plan.age_range_start}-${plan.age_range_end}j`;
        }
      },
      
      afterUpdate: async (plan, options) => {
        // If plan is activated, deactivate other plans for the same batch
        if (plan.status === 'active' && plan.poultry_batch_id && 
            options.fields && options.fields.includes('status')) {
          await FeedPlan.update(
            { status: 'suspended' },
            {
              where: {
                poultry_batch_id: plan.poultry_batch_id,
                id: { [sequelize.Sequelize.Op.ne]: plan.id },
                status: 'active'
              }
            }
          );
        }
      }
    }
  });

  // Helper function to generate default feeding times
  function generateDefaultFeedingTimes(frequency) {
    const times = [];
    const startHour = 7; // Start at 7 AM
    const endHour = 18; // End at 6 PM
    const interval = (endHour - startHour) / (frequency - 1);
    
    for (let i = 0; i < frequency; i++) {
      const hour = Math.floor(startHour + (interval * i));
      const minute = Math.floor(((startHour + (interval * i)) % 1) * 60);
      times.push(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
    }
    
    return times;
  }

  // Instance methods
  FeedPlan.prototype.calculateDailyFeedForBatch = function(numberOfBirds) {
    return this.daily_feed_per_bird * numberOfBirds;
  };

  FeedPlan.prototype.calculateTotalFeedNeeded = function(numberOfBirds, durationDays = null) {
    const days = durationDays || (this.age_range_end - this.age_range_start);
    return this.daily_feed_per_bird * numberOfBirds * days;
  };

  FeedPlan.prototype.calculateEstimatedCost = function(numberOfBirds, feedCostPerKg) {
    const totalFeed = this.calculateTotalFeedNeeded(numberOfBirds);
    return totalFeed * feedCostPerKg;
  };

  FeedPlan.prototype.isValidForAge = function(ageInDays) {
    return ageInDays >= this.age_range_start && ageInDays <= this.age_range_end;
  };

  FeedPlan.prototype.isValidForPoultryType = function(poultryType) {
    return this.poultry_type === poultryType || this.poultry_type === 'mixed';
  };

  FeedPlan.prototype.getNextFeedingTime = function() {
    if (!this.feeding_times || !Array.isArray(this.feeding_times)) return null;
    
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    // Find next feeding time today
    const nextTime = this.feeding_times.find(time => time > currentTime);
    
    if (nextTime) {
      const [hours, minutes] = nextTime.split(':');
      const nextFeeding = new Date();
      nextFeeding.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      return nextFeeding;
    }
    
    // If no more feedings today, return first feeding tomorrow
    const [hours, minutes] = this.feeding_times[0].split(':');
    const nextFeeding = new Date();
    nextFeeding.setDate(nextFeeding.getDate() + 1);
    nextFeeding.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return nextFeeding;
  };

  FeedPlan.prototype.activate = async function() {
    // Deactivate other plans for the same batch
    if (this.poultry_batch_id) {
      await FeedPlan.update(
        { status: 'suspended' },
        {
          where: {
            poultry_batch_id: this.poultry_batch_id,
            id: { [sequelize.Sequelize.Op.ne]: this.id },
            status: 'active'
          }
        }
      );
    }
    
    this.status = 'active';
    this.is_active = true;
    if (!this.start_date) {
      this.start_date = new Date();
    }
    
    return this.save();
  };

  FeedPlan.prototype.complete = function() {
    this.status = 'completed';
    this.is_active = false;
    if (!this.end_date) {
      this.end_date = new Date();
    }
    
    return this.save();
  };

  FeedPlan.prototype.suspend = function(reason = null) {
    this.status = 'suspended';
    this.is_active = false;
    
    if (reason) {
      this.notes = this.notes ? `${this.notes}\n\nSuspended: ${reason}` : `Suspended: ${reason}`;
    }
    
    return this.save();
  };

  FeedPlan.prototype.approve = async function(approvedBy) {
    this.approved_by = approvedBy;
    this.approved_at = new Date();
    
    if (this.status === 'draft') {
      this.status = 'active';
      this.is_active = true;
    }
    
    return this.save();
  };

  FeedPlan.prototype.createFromTemplate = function(farmId, poultryBatchId = null) {
    if (!this.is_template) {
      throw new Error('This plan is not a template');
    }
    
    const planData = this.toJSON();
    delete planData.id;
    delete planData.created_at;
    delete planData.updated_at;
    
    planData.farm_id = farmId;
    planData.poultry_batch_id = poultryBatchId;
    planData.is_template = false;
    planData.status = 'draft';
    planData.start_date = null;
    planData.end_date = null;
    planData.approved_by = null;
    planData.approved_at = null;
    
    return FeedPlan.create(planData);
  };

  // Class methods
  FeedPlan.findByFarm = function(farmId, options = {}) {
    return this.findAll({
      where: {
        farm_id: farmId,
        is_template: false
      },
      include: [
        { model: sequelize.models.Poussin, as: 'poultry_batch' },
        { model: sequelize.models.User, as: 'creator' },
        { model: sequelize.models.User, as: 'approver' }
      ],
      order: [['created_at', 'DESC']],
      ...options
    });
  };

  FeedPlan.findActiveByFarm = function(farmId, options = {}) {
    return this.findAll({
      where: {
        farm_id: farmId,
        status: 'active',
        is_template: false
      },
      include: [
        { model: sequelize.models.Poussin, as: 'poultry_batch' },
        { model: sequelize.models.User, as: 'creator' }
      ],
      order: [['start_date', 'ASC']],
      ...options
    });
  };

  FeedPlan.findByPoultryBatch = function(poultryBatchId, options = {}) {
    return this.findAll({
      where: {
        poultry_batch_id: poultryBatchId
      },
      include: [
        { model: sequelize.models.User, as: 'creator' },
        { model: sequelize.models.User, as: 'approver' }
      ],
      order: [['created_at', 'DESC']],
      ...options
    });
  };

  FeedPlan.findTemplates = function(poultryType = null, planType = null, options = {}) {
    const whereClause = { is_template: true };
    
    if (poultryType) {
      whereClause.poultry_type = { [sequelize.Sequelize.Op.in]: [poultryType, 'mixed'] };
    }
    
    if (planType) {
      whereClause.plan_type = planType;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.User, as: 'creator' },
        { model: sequelize.models.User, as: 'approver' }
      ],
      order: [['plan_name', 'ASC']],
      ...options
    });
  };

  FeedPlan.findByAgeRange = function(ageInDays, poultryType = null, options = {}) {
    const whereClause = {
      age_range_start: { [sequelize.Sequelize.Op.lte]: ageInDays },
      age_range_end: { [sequelize.Sequelize.Op.gte]: ageInDays },
      status: { [sequelize.Sequelize.Op.in]: ['active', 'draft'] }
    };
    
    if (poultryType) {
      whereClause.poultry_type = { [sequelize.Sequelize.Op.in]: [poultryType, 'mixed'] };
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.User, as: 'creator' }
      ],
      order: [['plan_type', 'ASC'], ['age_range_start', 'ASC']],
      ...options
    });
  };

  FeedPlan.findPendingApproval = function(farmId = null, options = {}) {
    const whereClause = {
      status: 'draft',
      approved_by: null
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.Eleveur, as: 'farm' },
        { model: sequelize.models.Poussin, as: 'poultry_batch' },
        { model: sequelize.models.User, as: 'creator' }
      ],
      order: [['created_at', 'ASC']],
      ...options
    });
  };

  FeedPlan.getRecommendedPlan = function(poultryType, ageInDays, targetWeight = null) {
    const whereClause = {
      poultry_type: { [sequelize.Sequelize.Op.in]: [poultryType, 'mixed'] },
      age_range_start: { [sequelize.Sequelize.Op.lte]: ageInDays },
      age_range_end: { [sequelize.Sequelize.Op.gte]: ageInDays },
      status: { [sequelize.Sequelize.Op.in]: ['active'] },
      approved_by: { [sequelize.Sequelize.Op.ne]: null }
    };
    
    if (targetWeight) {
      whereClause.target_weight_start = { [sequelize.Sequelize.Op.lte]: targetWeight };
      whereClause.target_weight_end = { [sequelize.Sequelize.Op.gte]: targetWeight };
    }
    
    return this.findOne({
      where: whereClause,
      include: [
        { model: sequelize.models.User, as: 'approver' }
      ],
      order: [['approved_at', 'DESC'], ['estimated_fcr', 'ASC']]
    });
  };

  // Associations
  FeedPlan.associate = function(models) {
    // FeedPlan belongs to Farm (Eleveur)
    FeedPlan.belongsTo(models.Eleveur, {
      foreignKey: 'farm_id',
      as: 'farm'
    });

    // FeedPlan belongs to Poultry Batch
    FeedPlan.belongsTo(models.Poussin, {
      foreignKey: 'poultry_batch_id',
      as: 'poultry_batch'
    });

    // FeedPlan belongs to User (creator)
    FeedPlan.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // FeedPlan belongs to User (approver)
    FeedPlan.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver'
    });

    // FeedPlan has many FeedComposition
    FeedPlan.hasMany(models.FeedComposition, {
      foreignKey: 'feed_plan_id',
      as: 'compositions'
    });
  };

  return FeedPlan;
};