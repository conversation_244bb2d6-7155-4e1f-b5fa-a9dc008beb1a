import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abel,
  StepContent,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Fade,
  Slide
} from '@mui/material';
import {
  Close,
  ArrowForward,
  ArrowBack,
  CheckCircle,
  PlayArrow,
  Pause,
  Replay,
  School,
  Agriculture,
  LocalHospital,
  Store
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useAdvancedLanguage';
import abTestingService from '../../services/abTestingService';
import onboardingService from '../../services/onboardingService';

/**
 * Composant principal du flux d'onboarding
 */
const OnboardingFlow = ({ open, onClose, userRole }) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [onboardingData, setOnboardingData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Charger les données d'onboarding selon le rôle
  useEffect(() => {
    const loadOnboardingData = async () => {
      try {
        setLoading(true);
        const data = await onboardingService.getOnboardingFlow(userRole);
        setOnboardingData(data);
        
        // Tracker l'ouverture de l'onboarding
        await abTestingService.trackConversion('onboarding_flow_test', 'onboarding_started', {
          userRole,
          userId: user.id
        });
      } catch (error) {
        console.error('Erreur lors du chargement de l\'onboarding:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open && userRole) {
      loadOnboardingData();
    }
  }, [open, userRole, user.id]);

  // Gestion de la lecture automatique
  useEffect(() => {
    let interval;
    if (isPlaying && onboardingData) {
      interval = setInterval(() => {
        if (currentStep < onboardingData.steps.length - 1) {
          handleNext();
        } else {
          setIsPlaying(false);
        }
      }, 5000); // 5 secondes par étape
    }
    return () => clearInterval(interval);
  }, [isPlaying, currentStep, onboardingData]);

  const handleNext = useCallback(() => {
    if (onboardingData && currentStep < onboardingData.steps.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      
      // Tracker la progression
      abTestingService.trackConversion('onboarding_flow_test', 'step_completed', {
        stepIndex: currentStep,
        stepId: onboardingData.steps[currentStep].id
      });
    }
  }, [currentStep, onboardingData]);

  const handleBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleStepClick = useCallback((stepIndex) => {
    setCurrentStep(stepIndex);
    setIsPlaying(false);
  }, []);

  const handleComplete = useCallback(async () => {
    try {
      // Marquer l'onboarding comme terminé
      await onboardingService.markOnboardingComplete(user.id, userRole);
      
      // Tracker la completion
      await abTestingService.trackGoalConversion('onboarding_flow_test', 'onboarding_completion', 1);
      
      onClose();
    } catch (error) {
      console.error('Erreur lors de la completion de l\'onboarding:', error);
    }
  }, [user.id, userRole, onClose]);

  const handleSkip = useCallback(async () => {
    // Tracker le skip
    await abTestingService.trackConversion('onboarding_flow_test', 'onboarding_skipped', {
      stepIndex: currentStep,
      totalSteps: onboardingData?.steps.length
    });
    
    onClose();
  }, [currentStep, onboardingData, onClose]);

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  const restart = useCallback(() => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setIsPlaying(false);
  }, []);

  if (loading || !onboardingData) {
    return (
      <Dialog open={open} maxWidth="md" fullWidth>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <LinearProgress sx={{ width: '50%' }} />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  const currentStepData = onboardingData.steps[currentStep];
  const progress = ((currentStep + 1) / onboardingData.steps.length) * 100;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: getRoleColor(userRole) }}>
              {getRoleIcon(userRole)}
            </Avatar>
            <Box>
              <Typography variant="h6">
                {t(`onboarding.welcome.${userRole}`)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('onboarding.subtitle')}
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title={isPlaying ? t('onboarding.pause') : t('onboarding.play')}>
              <IconButton onClick={togglePlayPause}>
                {isPlaying ? <Pause /> : <PlayArrow />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title={t('onboarding.restart')}>
              <IconButton onClick={restart}>
                <Replay />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={t('onboarding.close')}>
              <IconButton onClick={onClose}>
                <Close />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        {/* Barre de progression */}
        <Box mt={2}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="body2" color="text.secondary">
              {t('onboarding.progress')}: {currentStep + 1}/{onboardingData.steps.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {Math.round(progress)}%
            </Typography>
          </Box>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
      </Box>

      <DialogContent sx={{ p: 0, display: 'flex', height: '60vh' }}>
        {/* Stepper latéral */}
        <Box sx={{ width: 300, borderRight: 1, borderColor: 'divider', p: 2 }}>
          <Stepper activeStep={currentStep} orientation="vertical">
            {onboardingData.steps.map((step, index) => (
              <Step key={step.id} completed={completedSteps.has(index)}>
                <StepLabel 
                  onClick={() => handleStepClick(index)}
                  sx={{ cursor: 'pointer' }}
                >
                  <Typography variant="body2">
                    {step.title}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                  {step.duration && (
                    <Chip 
                      label={`${step.duration}min`} 
                      size="small" 
                      sx={{ mt: 1 }} 
                    />
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Contenu principal */}
        <Box sx={{ flex: 1, p: 3, overflow: 'auto' }}>
          <Fade in={true} key={currentStep}>
            <Box>
              <OnboardingStepContent 
                step={currentStepData}
                userRole={userRole}
                onActionComplete={(actionId) => {
                  abTestingService.trackConversion('onboarding_flow_test', 'action_completed', {
                    stepId: currentStepData.id,
                    actionId
                  });
                }}
              />
            </Box>
          </Fade>
        </Box>
      </DialogContent>

      {/* Actions */}
      <DialogActions sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Button onClick={handleSkip} color="inherit">
          {t('onboarding.skip')}
        </Button>
        
        <Box sx={{ flex: 1 }} />
        
        <Button 
          onClick={handleBack} 
          disabled={currentStep === 0}
          startIcon={<ArrowBack />}
        >
          {t('onboarding.previous')}
        </Button>
        
        {currentStep === onboardingData.steps.length - 1 ? (
          <Button 
            onClick={handleComplete}
            variant="contained"
            startIcon={<CheckCircle />}
          >
            {t('onboarding.complete')}
          </Button>
        ) : (
          <Button 
            onClick={handleNext}
            variant="contained"
            endIcon={<ArrowForward />}
          >
            {t('onboarding.next')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

/**
 * Composant pour le contenu d'une étape
 */
const OnboardingStepContent = ({ step, userRole, onActionComplete }) => {
  const { t } = useTranslation();

  const renderContent = () => {
    switch (step.type) {
      case 'welcome':
        return <WelcomeStep step={step} userRole={userRole} />;
      case 'feature_tour':
        return <FeatureTourStep step={step} onActionComplete={onActionComplete} />;
      case 'interactive':
        return <InteractiveStep step={step} onActionComplete={onActionComplete} />;
      case 'video':
        return <VideoStep step={step} />;
      case 'checklist':
        return <ChecklistStep step={step} onActionComplete={onActionComplete} />;
      default:
        return <DefaultStep step={step} />;
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        {step.title}
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        {step.description}
      </Typography>
      {renderContent()}
    </Box>
  );
};

/**
 * Étape de bienvenue
 */
const WelcomeStep = ({ step, userRole }) => {
  const { t } = useTranslation();

  return (
    <Card elevation={2}>
      <CardContent>
        <Box textAlign="center" py={4}>
          <Avatar 
            sx={{ 
              width: 80, 
              height: 80, 
              bgcolor: getRoleColor(userRole),
              mx: 'auto',
              mb: 2
            }}
          >
            {getRoleIcon(userRole)}
          </Avatar>
          
          <Typography variant="h6" gutterBottom>
            {t(`onboarding.welcome.title.${userRole}`)}
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            {t(`onboarding.welcome.description.${userRole}`)}
          </Typography>
          
          <Box display="flex" justifyContent="center" gap={1} mt={3}>
            {step.benefits?.map((benefit, index) => (
              <Chip 
                key={index}
                label={benefit}
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

/**
 * Étape de visite guidée des fonctionnalités
 */
const FeatureTourStep = ({ step, onActionComplete }) => {
  return (
    <Box>
      {step.features?.map((feature, index) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar sx={{ bgcolor: 'primary.light' }}>
                {feature.icon}
              </Avatar>
              <Box flex={1}>
                <Typography variant="h6">
                  {feature.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {feature.description}
                </Typography>
              </Box>
              <Button 
                variant="outlined"
                onClick={() => onActionComplete(`feature_${index}`)}
              >
                Essayer
              </Button>
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
};

/**
 * Étape interactive
 */
const InteractiveStep = ({ step, onActionComplete }) => {
  const [completedActions, setCompletedActions] = useState(new Set());

  const handleActionComplete = (actionId) => {
    setCompletedActions(prev => new Set([...prev, actionId]));
    onActionComplete(actionId);
  };

  return (
    <Box>
      {step.actions?.map((action, index) => (
        <Card 
          key={index} 
          sx={{ 
            mb: 2,
            border: completedActions.has(action.id) ? 2 : 1,
            borderColor: completedActions.has(action.id) ? 'success.main' : 'divider'
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <CheckCircle 
                color={completedActions.has(action.id) ? 'success' : 'disabled'}
              />
              <Box flex={1}>
                <Typography variant="subtitle1">
                  {action.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {action.instruction}
                </Typography>
              </Box>
              <Button 
                variant={completedActions.has(action.id) ? 'contained' : 'outlined'}
                color={completedActions.has(action.id) ? 'success' : 'primary'}
                onClick={() => handleActionComplete(action.id)}
                disabled={completedActions.has(action.id)}
              >
                {completedActions.has(action.id) ? 'Terminé' : 'Faire'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
};

/**
 * Étape vidéo
 */
const VideoStep = ({ step }) => {
  return (
    <Box>
      <Card>
        <CardContent>
          <video 
            width="100%" 
            height="400" 
            controls
            poster={step.poster}
          >
            <source src={step.videoUrl} type="video/mp4" />
            Votre navigateur ne supporte pas la lecture vidéo.
          </video>
        </CardContent>
      </Card>
    </Box>
  );
};

/**
 * Étape checklist
 */
const ChecklistStep = ({ step, onActionComplete }) => {
  const [checkedItems, setCheckedItems] = useState(new Set());

  const handleItemCheck = (itemId) => {
    const newCheckedItems = new Set(checkedItems);
    if (newCheckedItems.has(itemId)) {
      newCheckedItems.delete(itemId);
    } else {
      newCheckedItems.add(itemId);
      onActionComplete(itemId);
    }
    setCheckedItems(newCheckedItems);
  };

  return (
    <Box>
      {step.checklist?.map((item, index) => (
        <Card key={index} sx={{ mb: 1 }}>
          <CardContent sx={{ py: 2 }}>
            <Box 
              display="flex" 
              alignItems="center" 
              gap={2}
              sx={{ cursor: 'pointer' }}
              onClick={() => handleItemCheck(item.id)}
            >
              <CheckCircle 
                color={checkedItems.has(item.id) ? 'success' : 'disabled'}
              />
              <Typography 
                variant="body1"
                sx={{ 
                  textDecoration: checkedItems.has(item.id) ? 'line-through' : 'none',
                  opacity: checkedItems.has(item.id) ? 0.7 : 1
                }}
              >
                {item.text}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
};

/**
 * Étape par défaut
 */
const DefaultStep = ({ step }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="body1">
          {step.content}
        </Typography>
      </CardContent>
    </Card>
  );
};

/**
 * Fonctions utilitaires
 */
const getRoleColor = (role) => {
  const colors = {
    eleveur: 'primary.main',
    veterinaire: 'success.main',
    marchand: 'warning.main',
    admin: 'error.main'
  };
  return colors[role] || 'grey.500';
};

const getRoleIcon = (role) => {
  const icons = {
    eleveur: <Agriculture />,
    veterinaire: <LocalHospital />,
    marchand: <Store />,
    admin: <School />
  };
  return icons[role] || <School />;
};

export default OnboardingFlow;
