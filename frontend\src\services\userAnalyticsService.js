/**
 * Service d'analytics comportementales utilisateur
 */

import { v4 as uuidv4 } from 'uuid';
import apiService from './apiService';
import storageService from './storageService';

class UserAnalyticsService {
  constructor() {
    this.sessionId = null;
    this.userId = null;
    this.eventQueue = [];
    this.sessionData = {
      startTime: null,
      pageViews: [],
      interactions: [],
      errors: []
    };
    this.isTracking = false;
    this.heatmapData = new Map();
    this.scrollDepth = new Map();
  }

  /**
   * Initialiser le service d'analytics
   */
  async initialize(userId) {
    this.userId = userId;
    this.sessionId = uuidv4();
    this.sessionData.startTime = Date.now();
    this.isTracking = true;

    // Charger les préférences de tracking
    const trackingPrefs = await storageService.getItem('tracking_preferences') || {
      analytics: true,
      heatmap: true,
      performance: true
    };

    if (trackingPrefs.analytics) {
      this.startTracking();
    }

    console.log('📊 Service d\'analytics initialisé pour l\'utilisateur:', userId);
  }

  /**
   * Démarrer le tracking
   */
  startTracking() {
    // Tracker les vues de page
    this.trackPageView();
    
    // Tracker les clics
    this.setupClickTracking();
    
    // Tracker le scroll
    this.setupScrollTracking();
    
    // Tracker les erreurs
    this.setupErrorTracking();
    
    // Tracker les performances
    this.setupPerformanceTracking();
    
    // Tracker les interactions avec les formulaires
    this.setupFormTracking();
    
    // Envoyer les données périodiquement
    this.startPeriodicFlush();
    
    // Envoyer les données avant la fermeture
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });
  }

  /**
   * Tracker une vue de page
   */
  trackPageView(pageName = null, additionalData = {}) {
    if (!this.isTracking) return;

    const pageView = {
      id: uuidv4(),
      sessionId: this.sessionId,
      userId: this.userId,
      pageName: pageName || this.getCurrentPageName(),
      url: window.location.href,
      referrer: document.referrer,
      timestamp: Date.now(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      userAgent: navigator.userAgent,
      ...additionalData
    };

    this.sessionData.pageViews.push(pageView);
    this.eventQueue.push({
      type: 'page_view',
      data: pageView
    });

    // Réinitialiser les données de scroll pour la nouvelle page
    this.scrollDepth.set(pageView.pageName, {
      maxDepth: 0,
      timeSpent: 0,
      startTime: Date.now()
    });

    console.log('📄 Page vue trackée:', pageView.pageName);
  }

  /**
   * Tracker un événement personnalisé
   */
  trackEvent(eventName, eventData = {}, category = 'user_action') {
    if (!this.isTracking) return;

    const event = {
      id: uuidv4(),
      sessionId: this.sessionId,
      userId: this.userId,
      eventName,
      category,
      data: eventData,
      timestamp: Date.now(),
      pageName: this.getCurrentPageName(),
      url: window.location.href
    };

    this.sessionData.interactions.push(event);
    this.eventQueue.push({
      type: 'custom_event',
      data: event
    });

    console.log('🎯 Événement tracké:', eventName, eventData);
  }

  /**
   * Tracker les clics
   */
  setupClickTracking() {
    document.addEventListener('click', (event) => {
      if (!this.isTracking) return;

      const element = event.target;
      const clickData = {
        elementType: element.tagName.toLowerCase(),
        elementId: element.id,
        elementClass: element.className,
        elementText: element.textContent?.substring(0, 100),
        position: {
          x: event.clientX,
          y: event.clientY
        },
        timestamp: Date.now()
      };

      // Ajouter aux données de heatmap
      const pageName = this.getCurrentPageName();
      if (!this.heatmapData.has(pageName)) {
        this.heatmapData.set(pageName, []);
      }
      this.heatmapData.get(pageName).push({
        x: event.clientX,
        y: event.clientY,
        type: 'click',
        timestamp: Date.now()
      });

      this.trackEvent('click', clickData, 'interaction');
    });
  }

  /**
   * Tracker le scroll
   */
  setupScrollTracking() {
    let scrollTimeout;
    
    window.addEventListener('scroll', () => {
      if (!this.isTracking) return;

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        const pageName = this.getCurrentPageName();
        const scrollData = this.scrollDepth.get(pageName);
        
        if (scrollData) {
          const currentDepth = Math.round(
            (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
          );
          
          if (currentDepth > scrollData.maxDepth) {
            scrollData.maxDepth = currentDepth;
            
            this.trackEvent('scroll_depth', {
              depth: currentDepth,
              scrollY: window.scrollY,
              documentHeight: document.body.scrollHeight
            }, 'engagement');
          }
        }
      }, 250);
    });
  }

  /**
   * Tracker les erreurs
   */
  setupErrorTracking() {
    window.addEventListener('error', (event) => {
      if (!this.isTracking) return;

      const errorData = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href
      };

      this.sessionData.errors.push(errorData);
      this.trackEvent('javascript_error', errorData, 'error');
    });

    // Tracker les erreurs de promesses non gérées
    window.addEventListener('unhandledrejection', (event) => {
      if (!this.isTracking) return;

      const errorData = {
        reason: event.reason?.toString(),
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href
      };

      this.sessionData.errors.push(errorData);
      this.trackEvent('unhandled_promise_rejection', errorData, 'error');
    });
  }

  /**
   * Tracker les performances
   */
  setupPerformanceTracking() {
    // Tracker les métriques de performance Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        this.trackEvent('performance_lcp', {
          value: lastEntry.startTime,
          element: lastEntry.element?.tagName
        }, 'performance');
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          this.trackEvent('performance_fid', {
            value: entry.processingStart - entry.startTime,
            eventType: entry.name
          }, 'performance');
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      new PerformanceObserver((entryList) => {
        let clsValue = 0;
        const entries = entryList.getEntries();
        
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });

        if (clsValue > 0) {
          this.trackEvent('performance_cls', {
            value: clsValue
          }, 'performance');
        }
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // Tracker les temps de chargement des ressources
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        
        this.trackEvent('page_load_performance', {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstByte: navigation.responseStart - navigation.requestStart,
          domInteractive: navigation.domInteractive - navigation.navigationStart
        }, 'performance');
      }, 0);
    });
  }

  /**
   * Tracker les interactions avec les formulaires
   */
  setupFormTracking() {
    // Tracker les focus sur les champs
    document.addEventListener('focusin', (event) => {
      if (!this.isTracking) return;
      
      const element = event.target;
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
        this.trackEvent('form_field_focus', {
          fieldType: element.type,
          fieldName: element.name,
          fieldId: element.id,
          formId: element.form?.id
        }, 'form_interaction');
      }
    });

    // Tracker les soumissions de formulaires
    document.addEventListener('submit', (event) => {
      if (!this.isTracking) return;
      
      const form = event.target;
      const formData = new FormData(form);
      const fields = {};
      
      for (let [key, value] of formData.entries()) {
        // Ne pas tracker les valeurs sensibles
        if (!this.isSensitiveField(key)) {
          fields[key] = typeof value === 'string' ? value.length : 'file';
        }
      }

      this.trackEvent('form_submit', {
        formId: form.id,
        formAction: form.action,
        fieldCount: Object.keys(fields).length,
        fields: fields
      }, 'form_interaction');
    });
  }

  /**
   * Tracker le temps passé sur une page
   */
  trackTimeOnPage() {
    const pageName = this.getCurrentPageName();
    const scrollData = this.scrollDepth.get(pageName);
    
    if (scrollData) {
      const timeSpent = Date.now() - scrollData.startTime;
      
      this.trackEvent('time_on_page', {
        pageName,
        timeSpent,
        maxScrollDepth: scrollData.maxDepth
      }, 'engagement');
    }
  }

  /**
   * Tracker une conversion d'objectif
   */
  trackGoal(goalName, value = 1, additionalData = {}) {
    this.trackEvent('goal_conversion', {
      goalName,
      value,
      ...additionalData
    }, 'conversion');
  }

  /**
   * Tracker l'engagement utilisateur
   */
  trackEngagement(engagementType, data = {}) {
    this.trackEvent('user_engagement', {
      engagementType,
      ...data
    }, 'engagement');
  }

  /**
   * Obtenir les données de session actuelles
   */
  getSessionData() {
    return {
      ...this.sessionData,
      sessionDuration: Date.now() - this.sessionData.startTime,
      currentPage: this.getCurrentPageName(),
      heatmapData: Object.fromEntries(this.heatmapData),
      scrollDepth: Object.fromEntries(this.scrollDepth)
    };
  }

  /**
   * Obtenir les données de heatmap pour une page
   */
  getHeatmapData(pageName = null) {
    const page = pageName || this.getCurrentPageName();
    return this.heatmapData.get(page) || [];
  }

  /**
   * Envoyer les données au serveur
   */
  async flushEvents() {
    if (this.eventQueue.length === 0) return;

    try {
      const events = [...this.eventQueue];
      this.eventQueue = [];

      await apiService.post('/api/analytics/events', {
        sessionId: this.sessionId,
        events: events
      });

      console.log('📤 Événements analytics envoyés:', events.length);

    } catch (error) {
      console.error('Erreur lors de l\'envoi des analytics:', error);
      // Remettre les événements dans la queue
      this.eventQueue.unshift(...events);
    }
  }

  /**
   * Terminer la session
   */
  async endSession() {
    if (!this.isTracking) return;

    // Tracker le temps sur la page actuelle
    this.trackTimeOnPage();

    // Envoyer les données de session
    const sessionSummary = {
      sessionId: this.sessionId,
      userId: this.userId,
      duration: Date.now() - this.sessionData.startTime,
      pageViews: this.sessionData.pageViews.length,
      interactions: this.sessionData.interactions.length,
      errors: this.sessionData.errors.length,
      endTime: Date.now()
    };

    this.trackEvent('session_end', sessionSummary, 'session');
    
    // Envoyer toutes les données en attente
    await this.flushEvents();

    this.isTracking = false;
    console.log('📊 Session analytics terminée');
  }

  /**
   * Démarrer l'envoi périodique
   */
  startPeriodicFlush() {
    setInterval(() => {
      this.flushEvents();
    }, 30000); // Envoyer toutes les 30 secondes
  }

  /**
   * Méthodes utilitaires
   */

  getCurrentPageName() {
    return window.location.pathname.replace(/^\//, '') || 'home';
  }

  isSensitiveField(fieldName) {
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'ssn', 'credit'];
    return sensitiveFields.some(field => 
      fieldName.toLowerCase().includes(field)
    );
  }

  /**
   * Configurer les préférences de tracking
   */
  async setTrackingPreferences(preferences) {
    await storageService.setItem('tracking_preferences', preferences);
    
    if (!preferences.analytics && this.isTracking) {
      this.endSession();
    } else if (preferences.analytics && !this.isTracking) {
      this.startTracking();
    }
  }

  /**
   * Obtenir les métriques de performance
   */
  getPerformanceMetrics() {
    if (!('performance' in window)) return null;

    const navigation = performance.getEntriesByType('navigation')[0];
    const paint = performance.getEntriesByType('paint');

    return {
      navigation: {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstByte: navigation.responseStart - navigation.requestStart
      },
      paint: paint.reduce((acc, entry) => {
        acc[entry.name] = entry.startTime;
        return acc;
      }, {}),
      memory: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null
    };
  }
}

// Instance singleton
const userAnalyticsService = new UserAnalyticsService();

export default userAnalyticsService;
