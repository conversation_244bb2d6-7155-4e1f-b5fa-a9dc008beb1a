import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import {
  Box,
  Paper,
  TextField,
  IconButton,
  Typography,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Badge,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Fab,
  Collapse
} from '@mui/material';
import {
  Send,
  AttachFile,
  EmojiEmotions,
  MoreVert,
  Close,
  Chat as ChatIcon,
  Minimize,
  Maximize
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useAdvancedLanguage';
import { useChatNotifications } from '../../hooks/useNotifications';
import websocketService, { WS_EVENTS } from '../../services/websocketService';
import { useOptimizedMemo } from '../../hooks/usePerformance';

/**
 * Composant de message individuel
 */
const ChatMessage = memo(({ 
  message, 
  isOwn = false, 
  showAvatar = true,
  onReply,
  onEdit,
  onDelete 
}) => {
  const { t } = useTranslation();
  const [menuAnchor, setMenuAnchor] = useState(null);

  const handleMenuOpen = (event) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const messageTime = useOptimizedMemo(() => {
    return new Date(message.timestamp).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }, [message.timestamp], 'ChatMessage.messageTime');

  return (
    <ListItem
      sx={{
        flexDirection: isOwn ? 'row-reverse' : 'row',
        alignItems: 'flex-start',
        px: 1,
        py: 0.5
      }}
    >
      {showAvatar && (
        <ListItemAvatar sx={{ minWidth: isOwn ? 'auto' : 56, ml: isOwn ? 1 : 0, mr: isOwn ? 0 : 1 }}>
          <Avatar
            src={message.sender_avatar}
            sx={{ width: 32, height: 32 }}
          >
            {message.sender_name?.charAt(0)}
          </Avatar>
        </ListItemAvatar>
      )}
      
      <Box
        sx={{
          maxWidth: '70%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: isOwn ? 'flex-end' : 'flex-start'
        }}
      >
        {!isOwn && (
          <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5 }}>
            {message.sender_name}
          </Typography>
        )}
        
        <Paper
          elevation={1}
          sx={{
            p: 1.5,
            backgroundColor: isOwn ? 'primary.main' : 'grey.100',
            color: isOwn ? 'primary.contrastText' : 'text.primary',
            borderRadius: 2,
            position: 'relative'
          }}
        >
          <Typography variant="body2">
            {message.content}
          </Typography>
          
          {message.attachments && message.attachments.length > 0 && (
            <Box mt={1}>
              {message.attachments.map((attachment, index) => (
                <Chip
                  key={index}
                  label={attachment.name}
                  size="small"
                  onClick={() => window.open(attachment.url)}
                  sx={{ mr: 0.5, mb: 0.5 }}
                />
              ))}
            </Box>
          )}
          
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mt={0.5}
          >
            <Typography variant="caption" sx={{ opacity: 0.7 }}>
              {messageTime}
            </Typography>
            
            {isOwn && (
              <IconButton
                size="small"
                onClick={handleMenuOpen}
                sx={{ ml: 1, color: 'inherit' }}
              >
                <MoreVert fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Paper>
        
        {message.status && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
            {message.status === 'sent' && '✓'}
            {message.status === 'delivered' && '✓✓'}
            {message.status === 'read' && '✓✓'}
          </Typography>
        )}
      </Box>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { onReply?.(message); handleMenuClose(); }}>
          {t('chat.reply')}
        </MenuItem>
        <MenuItem onClick={() => { onEdit?.(message); handleMenuClose(); }}>
          {t('chat.edit')}
        </MenuItem>
        <MenuItem onClick={() => { onDelete?.(message); handleMenuClose(); }}>
          {t('chat.delete')}
        </MenuItem>
      </Menu>
    </ListItem>
  );
});

/**
 * Indicateur de frappe
 */
const TypingIndicator = memo(({ users = [] }) => {
  const { t } = useTranslation();

  if (users.length === 0) return null;

  return (
    <ListItem sx={{ py: 0.5 }}>
      <ListItemAvatar>
        <Avatar sx={{ width: 24, height: 24 }}>
          <Typography variant="caption">...</Typography>
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={
          <Typography variant="caption" color="text.secondary">
            {users.length === 1 
              ? t('chat.userTyping', { user: users[0].name })
              : t('chat.usersTyping', { count: users.length })
            }
          </Typography>
        }
      />
    </ListItem>
  );
});

/**
 * Composant principal de chat
 */
const RealTimeChat = memo(({ 
  chatId,
  participants = [],
  title = "Chat",
  minimizable = true,
  closable = true,
  onClose,
  height = 400
}) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { notifyNewMessage } = useChatNotifications(chatId);
  
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [typingUsers, setTypingUsers] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  
  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const inputRef = useRef(null);

  // Faire défiler vers le bas
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Charger les messages initiaux
  useEffect(() => {
    if (chatId) {
      // Simuler le chargement des messages
      // En production, ceci ferait un appel API
      setMessages([
        {
          id: '1',
          content: 'Bonjour ! Comment allez-vous ?',
          sender_id: 'other_user',
          sender_name: 'Dr. Martin',
          sender_avatar: null,
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'read'
        },
        {
          id: '2',
          content: 'Très bien merci ! J\'ai une question concernant mes volailles.',
          sender_id: user.id,
          sender_name: user.first_name,
          sender_avatar: user.avatar,
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          status: 'read'
        }
      ]);
    }
  }, [chatId, user]);

  // Écouter les événements WebSocket
  useEffect(() => {
    const handleMessageReceived = (data) => {
      if (data.chat_id === chatId) {
        setMessages(prev => [...prev, data.message]);
        
        // Notifier si le chat est minimisé
        if (isMinimized && data.message.sender_id !== user.id) {
          notifyNewMessage(data.message);
          setUnreadCount(prev => prev + 1);
        }
        
        scrollToBottom();
      }
    };

    const handleUserTyping = (data) => {
      if (data.chat_id === chatId && data.user_id !== user.id) {
        setTypingUsers(prev => {
          const existing = prev.find(u => u.id === data.user_id);
          if (!existing) {
            return [...prev, { id: data.user_id, name: data.user_name }];
          }
          return prev;
        });

        // Supprimer l'utilisateur de la liste après 3 secondes
        setTimeout(() => {
          setTypingUsers(prev => prev.filter(u => u.id !== data.user_id));
        }, 3000);
      }
    };

    const handleConnectionChange = (connected) => {
      setIsConnected(connected);
    };

    websocketService.on(WS_EVENTS.MESSAGE_RECEIVED, handleMessageReceived);
    websocketService.on(WS_EVENTS.USER_TYPING, handleUserTyping);
    websocketService.on(WS_EVENTS.CONNECTED, () => handleConnectionChange(true));
    websocketService.on(WS_EVENTS.DISCONNECTED, () => handleConnectionChange(false));

    return () => {
      websocketService.removeListener(WS_EVENTS.MESSAGE_RECEIVED, handleMessageReceived);
      websocketService.removeListener(WS_EVENTS.USER_TYPING, handleUserTyping);
    };
  }, [chatId, user.id, isMinimized, notifyNewMessage, scrollToBottom]);

  // Envoyer un message
  const sendMessage = useCallback(() => {
    if (newMessage.trim() && chatId) {
      const message = {
        id: Date.now().toString(),
        content: newMessage.trim(),
        sender_id: user.id,
        sender_name: user.first_name,
        sender_avatar: user.avatar,
        timestamp: new Date().toISOString(),
        status: 'sending'
      };

      // Ajouter le message localement
      setMessages(prev => [...prev, message]);
      
      // Envoyer via WebSocket
      websocketService.send('send_message', {
        chat_id: chatId,
        message: message
      });

      setNewMessage('');
      scrollToBottom();
    }
  }, [newMessage, chatId, user, scrollToBottom]);

  // Gérer la frappe
  const handleTyping = useCallback(() => {
    if (chatId) {
      websocketService.send('user_typing', {
        chat_id: chatId,
        user_id: user.id,
        user_name: user.first_name
      });

      // Arrêter l'indicateur de frappe après 1 seconde d'inactivité
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      typingTimeoutRef.current = setTimeout(() => {
        websocketService.send('user_stop_typing', {
          chat_id: chatId,
          user_id: user.id
        });
      }, 1000);
    }
  }, [chatId, user]);

  // Gérer l'appui sur Entrée
  const handleKeyPress = useCallback((event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  // Marquer comme lu quand le chat est ouvert
  useEffect(() => {
    if (!isMinimized) {
      setUnreadCount(0);
    }
  }, [isMinimized]);

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return (
    <Paper
      elevation={8}
      sx={{
        position: 'fixed',
        bottom: 0,
        right: 20,
        width: 350,
        height: isMinimized ? 'auto' : height,
        display: 'flex',
        flexDirection: 'column',
        zIndex: 1300
      }}
    >
      {/* En-tête du chat */}
      <Box
        sx={{
          p: 1,
          backgroundColor: 'primary.main',
          color: 'primary.contrastText',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: minimizable ? 'pointer' : 'default'
        }}
        onClick={minimizable ? toggleMinimize : undefined}
      >
        <Box display="flex" alignItems="center">
          <ChatIcon sx={{ mr: 1 }} />
          <Typography variant="subtitle2">
            {title}
          </Typography>
          {unreadCount > 0 && (
            <Badge
              badgeContent={unreadCount}
              color="error"
              sx={{ ml: 1 }}
            />
          )}
        </Box>
        
        <Box>
          <Chip
            label={isConnected ? t('chat.online') : t('chat.offline')}
            size="small"
            color={isConnected ? 'success' : 'error'}
            sx={{ mr: 1 }}
          />
          
          {minimizable && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                toggleMinimize();
              }}
              sx={{ color: 'inherit' }}
            >
              {isMinimized ? <Maximize /> : <Minimize />}
            </IconButton>
          )}
          
          {closable && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onClose?.();
              }}
              sx={{ color: 'inherit' }}
            >
              <Close />
            </IconButton>
          )}
        </Box>
      </Box>

      <Collapse in={!isMinimized}>
        {/* Zone des messages */}
        <Box
          sx={{
            flex: 1,
            overflow: 'auto',
            maxHeight: height - 120,
            minHeight: 200
          }}
        >
          <List sx={{ py: 0 }}>
            {messages.map((message, index) => {
              const isOwn = message.sender_id === user.id;
              const showAvatar = index === 0 || 
                messages[index - 1].sender_id !== message.sender_id;
              
              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isOwn={isOwn}
                  showAvatar={showAvatar}
                />
              );
            })}
            
            <TypingIndicator users={typingUsers} />
            <div ref={messagesEndRef} />
          </List>
        </Box>

        <Divider />

        {/* Zone de saisie */}
        <Box sx={{ p: 1, display: 'flex', alignItems: 'center' }}>
          <TextField
            ref={inputRef}
            fullWidth
            size="small"
            placeholder={t('chat.typeMessage')}
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              handleTyping();
            }}
            onKeyPress={handleKeyPress}
            multiline
            maxRows={3}
            sx={{ mr: 1 }}
          />
          
          <IconButton
            size="small"
            onClick={sendMessage}
            disabled={!newMessage.trim() || !isConnected}
            color="primary"
          >
            <Send />
          </IconButton>
        </Box>
      </Collapse>
    </Paper>
  );
});

/**
 * Bouton flottant pour ouvrir le chat
 */
export const ChatFab = memo(({ onClick, unreadCount = 0 }) => {
  return (
    <Fab
      color="primary"
      onClick={onClick}
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: 1000
      }}
    >
      <Badge badgeContent={unreadCount} color="error">
        <ChatIcon />
      </Badge>
    </Fab>
  );
});

ChatMessage.displayName = 'ChatMessage';
TypingIndicator.displayName = 'TypingIndicator';
RealTimeChat.displayName = 'RealTimeChat';
ChatFab.displayName = 'ChatFab';

export default RealTimeChat;
