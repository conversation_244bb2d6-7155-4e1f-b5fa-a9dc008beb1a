import React, { useEffect, useState, useRef, lazy, Suspense } from 'react';
import { Box, Container, Typography, Button, Grid, Card, CardContent, CardMedia, Accordion, AccordionSummary, AccordionDetails, TextField, Paper, useTheme, useMediaQuery, CircularProgress, Snackbar, Alert, FormControl, InputLabel, Select, MenuItem, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AgricultureIcon from '@mui/icons-material/Agriculture';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PeopleIcon from '@mui/icons-material/People';
import SendIcon from '@mui/icons-material/Send';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import InstagramIcon from '@mui/icons-material/Instagram';
import heroImage from '../../assets/images/hero-image.svg';
import LandingNavbar from './LandingNavbar';
import '../../assets/css/landing-animations.css';
import { useLanguage } from '../../contexts/LanguageContext';
import { landingTranslations } from '../../translations/landing';

// Styles personnalisés
const HeroSection = styled(Box)(({ theme }) => ({
  minHeight: '80vh',
  display: 'flex',
  alignItems: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
  color: theme.palette.common.white,
  padding: theme.spacing(4, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '30px',
  padding: theme.spacing(1, 4),
  fontWeight: 'bold',
  boxShadow: '0 4px 10px rgba(0, 0, 0, 0.15)',
  transition: 'transform 0.2s',
  '&:hover': {
    transform: 'translateY(-3px)',
  },
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s, box-shadow 0.3s',
  '&:hover': {
    transform: 'translateY(-10px)',
    boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  position: 'relative',
  marginBottom: theme.spacing(6),
  '&:after': {
    content: '""',
    position: 'absolute',
    bottom: '-10px',
    left: '50%',
    width: '80px',
    height: '4px',
    background: theme.palette.primary.main,
    transform: 'translateX(-50%)',
  },
}));

const PricingCard = styled(Paper)(({ theme, featured }) => ({
  padding: theme.spacing(4),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  transition: 'transform 0.3s',
  border: featured ? `2px solid ${theme.palette.primary.main}` : 'none',
  transform: featured ? 'scale(1.05)' : 'none',
  '&:hover': {
    transform: featured ? 'scale(1.08)' : 'scale(1.03)',
  },
}));

const TestimonialCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  position: 'relative',
  '&:before': {
    content: '"\u201C"',
    position: 'absolute',
    top: '-20px',
    left: '20px',
    fontSize: '60px',
    color: theme.palette.primary.light,
    opacity: 0.5,
  },
}));

const ContactForm = styled(Box)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0 5px 20px rgba(0, 0, 0, 0.1)',
  background: theme.palette.background.paper,
}));

// Données pour les sections
const features = [
  {
    title: 'Gestion des Éleveurs',
    description: 'Suivez et gérez facilement tous vos éleveurs partenaires avec des profils détaillés et des statistiques en temps réel.',
    icon: <PeopleIcon fontSize="large" color="primary" />,
  },
  {
    title: 'Suivi Vétérinaire',
    description: 'Accédez à des services vétérinaires à distance et suivez la santé de vos volailles avec des rapports détaillés.',
    icon: <MonitorHeartIcon fontSize="large" color="primary" />,
  },
  {
    title: 'Marketplace',
    description: 'Vendez et achetez des volailles directement sur la plateforme avec un système de paiement sécurisé.',
    icon: <ShoppingCartIcon fontSize="large" color="primary" />,
  },
  {
    title: 'Agriculture Intelligente',
    description: 'Optimisez votre production grâce à des outils d\'analyse avancés et des recommandations personnalisées.',
    icon: <AgricultureIcon fontSize="large" color="primary" />,
  },
  {
    title: 'Notifications',
    description: 'Restez informé des événements importants avec notre système de notifications en temps réel.',
    icon: <NotificationsActiveIcon fontSize="large" color="primary" />,
  },
  {
    title: 'Intelligence Artificielle',
    description: 'Bénéficiez de prédictions et d\'analyses basées sur l\'IA pour améliorer votre productivité.',
    icon: <SmartToyIcon fontSize="large" color="primary" />,
  },
];

const pricingPlans = [
  {
    title: 'Gratuit',
    price: '0 DA',
    features: [
      'Accès limité au tableau de bord',
      'Gestion de base des éleveurs',
      'Notifications essentielles',
      'Support communautaire',
    ],
    buttonText: 'Commencer',
    featured: false,
  },
  {
    title: 'Standard',
    price: '2 500 DA/mois',
    features: [
      'Tableau de bord complet',
      'Gestion avancée des éleveurs',
      'Suivi vétérinaire de base',
      'Marketplace avec commission réduite',
      'Support par email',
    ],
    buttonText: 'Essai Gratuit',
    featured: true,
  },
  {
    title: 'Premium',
    price: '5 000 DA/mois',
    features: [
      'Toutes les fonctionnalités Standard',
      'Analyses IA avancées',
      'Suivi vétérinaire illimité',
      'Marketplace sans commission',
      'Support prioritaire 24/7',
      'Formation personnalisée',
    ],
    buttonText: 'Contacter les Ventes',
    featured: false,
  },
];

const testimonials = [
  {
    name: 'Ahmed Benali',
    role: 'Éleveur de poulets de chair',
    content: 'Depuis que j\'utilise Poultray DZ, la gestion de mon élevage est devenue beaucoup plus simple. Je peux suivre la croissance de mes volailles et anticiper les problèmes avant qu\'ils ne surviennent.',
  },
  {
    name: 'Samira Hadj',
    role: 'Vétérinaire avicole',
    content: 'La plateforme me permet de suivre à distance plusieurs élevages et d\'intervenir rapidement en cas de besoin. Un outil indispensable pour les professionnels de la santé animale.',
  },
  {
    name: 'Karim Meziane',
    role: 'Distributeur de produits avicoles',
    content: 'Grâce à Poultray DZ, j\'ai pu élargir ma clientèle et optimiser mes livraisons. La marketplace est intuitive et sécurisée.',
  },
];

const faqs = [
  {
    question: 'Comment puis-je m\'inscrire sur Poultray DZ ?',
    answer: 'L\'inscription est simple et rapide. Cliquez sur le bouton "Créer un compte" en haut de la page, remplissez le formulaire avec vos informations et choisissez votre type de profil (éleveur, vétérinaire, acheteur, etc.).',
  },
  {
    question: 'Quels types d\'élevages sont supportés par la plateforme ?',
    answer: 'Poultray DZ prend en charge tous les types d\'élevages avicoles : poulets de chair, poules pondeuses, dindes, cailles, et autres volailles. Chaque type d\'élevage bénéficie de fonctionnalités spécifiques adaptées à ses besoins.',
  },
  {
    question: 'Comment fonctionne le système de suivi vétérinaire ?',
    answer: 'Les éleveurs peuvent solliciter des consultations vétérinaires directement via la plateforme. Les vétérinaires reçoivent les demandes, consultent les données de l\'élevage et peuvent fournir des recommandations ou planifier des visites si nécessaire.',
  },
  {
    question: 'La plateforme est-elle accessible sur mobile ?',
    answer: 'Oui, Poultray DZ est entièrement responsive et fonctionne sur tous les appareils. Nous proposons également une application mobile dédiée pour Android et iOS pour une expérience optimisée.',
  },
  {
    question: 'Comment sont sécurisées mes données sur la plateforme ?',
    answer: 'Nous utilisons des protocoles de cryptage avancés pour protéger toutes les données. Nos serveurs sont sécurisés et nous effectuons des sauvegardes régulières. Nous respectons également les normes internationales de protection des données.',
  },
];

// Composant principal de la Landing Page
function LandingPage() {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isMedium = useMediaQuery(theme.breakpoints.down('md'));
  const contactRef = useRef(null);
  const { language } = useLanguage();
  const t = landingTranslations[language];

  // État pour le formulaire de contact
  const [contactForm, setContactForm] = useState({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    sujet: '',
    message: ''
  });

  // État pour la validation et les notifications
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Gestion des changements dans le formulaire
  const handleContactChange = (e) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer l'erreur lorsque l'utilisateur commence à corriger
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validation du formulaire
  const validateForm = () => {
    const errors = {};
    if (!contactForm.nom.trim()) errors.nom = 'Le nom est requis';
    if (!contactForm.prenom.trim()) errors.prenom = 'Le prénom est requis';

    if (!contactForm.email.trim()) {
      errors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactForm.email)) {
      errors.email = 'Format d\'email invalide';
    }

    if (!contactForm.message.trim()) errors.message = 'Le message est requis';

    return errors;
  };

  // Soumission du formulaire
  const handleContactSubmit = (e) => {
    e.preventDefault();
    const errors = validateForm();

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    // Simulation d'envoi à une API (à remplacer par un vrai appel API)
    setTimeout(() => {
      setIsSubmitting(false);
      setSnackbar({
        open: true,
        message: t.contact.form.success,
        severity: 'success'
      });

      // Réinitialiser le formulaire
      setContactForm({
        nom: '',
        prenom: '',
        email: '',
        telephone: '',
        sujet: '',
        message: ''
      });
    }, 1500);
  };

  // Fermeture de la notification
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Défilement vers la section de contact
  const scrollToContact = () => {
    contactRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Effet pour animer les éléments au chargement de la page
  useEffect(() => {
    const animatedElements = document.querySelectorAll('.animate-fadeInUp');
    animatedElements.forEach((element, index) => {
      element.style.animationDelay = `${0.1 * index}s`;
    });

    // Observer pour les animations au défilement
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    // Sélectionner tous les éléments à animer au défilement
    const scrollAnimElements = document.querySelectorAll('.scroll-anim');
    scrollAnimElements.forEach(el => observer.observe(el));

    return () => {
      scrollAnimElements.forEach(el => observer.unobserve(el));
    };
  }, []);

  return (
    <Box>
      <LandingNavbar />
      {/* Hero Section */}
      <HeroSection id="accueil">
        <Container>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h2" component="h1" gutterBottom fontWeight="bold" className="animate-fadeInUp">
                {t.hero.title}
              </Typography>
              <Typography variant="h5" paragraph className="animate-fadeInUp delay-200">
                {t.hero.subtitle}
              </Typography>
              <Box sx={{ mt: 4, display: 'flex', flexDirection: isMobile ? 'column' : 'row', gap: 2 }} className="animate-fadeInUp delay-300">
                <StyledButton
                  variant="contained"
                  color="secondary"
                  size="large"
                  component={RouterLink}
                  to="/register"
                  className="cta-button animate-pulse"
                >
                  {t.hero.createAccount}
                </StyledButton>
                <StyledButton
                  variant="outlined"
                  color="inherit"
                  size="large"
                  onClick={scrollToContact}
                >
                  {t.hero.contactUs}
                </StyledButton>
              </Box>
            </Grid>
            <Grid item xs={12} md={6} sx={{ display: { xs: 'none', md: 'block' } }}>
              <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}><CircularProgress /></Box>}>
                <Box
                  component="img"
                  src={heroImage}
                  alt="Poultray DZ - Gestion d'élevage de volailles"
                  className="animate-float"
                  loading="lazy"
                  sx={{
                    width: '100%',
                    maxWidth: '500px',
                    display: 'block',
                    margin: '0 auto',
                    transition: 'transform 0.5s ease-in-out',
                    '&:hover': {
                      transform: 'scale(1.05)',
                    },
                  }}
                />
              </Suspense>
            </Grid>
          </Grid>
        </Container>
      </HeroSection>

      {/* À propos Section */}
      <Box id="about" sx={{ py: 10, backgroundColor: theme.palette.background.paper }}>
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.about.title}
          </SectionTitle>
          <Grid container spacing={4} justifyContent="center">
            <Grid item xs={12} md={8}>
              <Typography variant="body1" paragraph align="center">
                {t.about.paragraph1}
              </Typography>
              <Typography variant="body1" paragraph align="center">
                {t.about.paragraph2}
              </Typography>
            </Grid>
          </Grid>
          <Box sx={{ mt: 6 }}>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h2" color="primary" fontWeight="bold">
                    500+
                  </Typography>
                  <Typography variant="body1">{t.about.stats.activeBreeder}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h2" color="primary" fontWeight="bold">
                    1200+
                  </Typography>
                  <Typography variant="body1">{t.about.stats.poultryBatches}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h2" color="primary" fontWeight="bold">
                    50+
                  </Typography>
                  <Typography variant="body1">{t.about.stats.veterinarians}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h2" color="primary" fontWeight="bold">
                    15
                  </Typography>
                  <Typography variant="body1">{t.about.stats.coveredRegions}</Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* Fonctionnalités Section */}
      <Box sx={{ py: 10 }} id="fonctionnalites" className="scroll-anim fade-in">
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.features.title}
          </SectionTitle>
          <Grid container spacing={4}>
            {[
              {
                title: t.features.breederManagement.title,
                description: t.features.breederManagement.description,
                icon: <PeopleIcon fontSize="large" color="primary" />
              },
              {
                title: t.features.veterinaryMonitoring.title,
                description: t.features.veterinaryMonitoring.description,
                icon: <MonitorHeartIcon fontSize="large" color="primary" />
              },
              {
                title: t.features.marketplace.title,
                description: t.features.marketplace.description,
                icon: <ShoppingCartIcon fontSize="large" color="primary" />
              },
              {
                title: t.features.smartAgriculture.title,
                description: t.features.smartAgriculture.description,
                icon: <AgricultureIcon fontSize="large" color="primary" />
              },
              {
                title: t.features.notifications.title,
                description: t.features.notifications.description,
                icon: <NotificationsActiveIcon fontSize="large" color="primary" />
              },
              {
                title: t.features.artificialIntelligence.title,
                description: t.features.artificialIntelligence.description,
                icon: <SmartToyIcon fontSize="large" color="primary" />
              }
            ].map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index} className="scroll-anim slide-in-up" sx={{ animationDelay: `${0.1 * index}s` }}>
                <FeatureCard className={`feature-card animate-fadeInUp delay-${index * 100}`}>
                  <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                    <Box sx={{ mb: 2 }} className="animate-float">{feature.icon}</Box>
                    <Typography variant="h5" component="h3" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </FeatureCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Témoignages Section */}
      <Box sx={{ py: 10, backgroundColor: theme.palette.grey[50] }}>
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.testimonials.title}
          </SectionTitle>
          <Grid container spacing={4}>
            {[
              t.testimonials.testimonial1,
              t.testimonials.testimonial2,
              t.testimonials.testimonial3
            ].map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <TestimonialCard>
                  <Typography variant="body1" paragraph>
                    {testimonial.content}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box
                      sx={{
                        width: 50,
                        height: 50,
                        borderRadius: '50%',
                        backgroundColor: theme.palette.primary.main,
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        mr: 2,
                      }}
                    >
                      {testimonial.name.charAt(0)}
                    </Box>
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {testimonial.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.role}
                      </Typography>
                    </Box>
                  </Box>
                </TestimonialCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Tarification Section */}
      <Box sx={{ py: 10 }} id="tarifs" className="scroll-anim fade-in">
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.pricing.title}
          </SectionTitle>
          <Grid container spacing={4} justifyContent="center">
            {[
              { ...t.pricing.free, featured: false },
              { ...t.pricing.standard, featured: true },
              { ...t.pricing.premium, featured: false }
            ].map((plan, index) => (
              <Grid item xs={12} sm={6} md={4} key={index} className="scroll-anim slide-in-up" sx={{ animationDelay: `${0.2 * index}s` }}>
                <PricingCard featured={plan.featured ? "true" : "false"} elevation={plan.featured ? 8 : 2}>
                  <Typography variant="h4" component="h3" gutterBottom>
                    {plan.title}
                  </Typography>
                  <Typography variant="h3" color="primary" gutterBottom fontWeight="bold">
                    {plan.price}
                  </Typography>
                  <Box sx={{ my: 3, width: '100%' }}>
                    {plan.features.map((feature, idx) => (
                      <Typography key={idx} variant="body2" sx={{ py: 1, borderBottom: `1px solid ${theme.palette.divider}` }}>
                        {feature}
                      </Typography>
                    ))}
                  </Box>
                  <Box sx={{ mt: 'auto', pt: 2, width: '100%' }}>
                    <StyledButton
                      variant={plan.featured ? 'contained' : 'outlined'}
                      color={plan.featured ? 'primary' : 'primary'}
                      fullWidth
                      onClick={scrollToContact}
                      className={plan.featured ? 'animate-pulse' : ''}
                    >
                      {plan.buttonText}
                    </StyledButton>
                  </Box>
                </PricingCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box sx={{ py: 10, backgroundColor: theme.palette.grey[50] }} id="faq" className="scroll-anim fade-in">
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.faq.title}
          </SectionTitle>
          <Grid container spacing={2} justifyContent="center">
            <Grid item xs={12} md={8}>
              {t.faq.questions.map((faq, index) => (
                <Accordion
                  key={index}
                  sx={{ mb: 2 }}
                  className="scroll-anim slide-in-left"
                  style={{ animationDelay: `${0.1 * index}s` }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`panel${index}-content`}
                    id={`panel${index}-header`}
                  >
                    <Typography variant="subtitle1" fontWeight="medium">
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2">
                      {faq.answer}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Contact Section */}
      <Box sx={{ py: 10 }} id="contact" ref={contactRef} className="scroll-anim fade-in">
        <Container>
          <SectionTitle variant="h3" component="h2" align="center">
            {t.contact.title}
          </SectionTitle>
          <Grid container spacing={4} justifyContent="center">
            <Grid item xs={12} md={6}>
              <ContactForm component="form" onSubmit={handleContactSubmit} className="animate-fadeInUp">
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t.contact.form.lastName}
                      name="nom"
                      variant="outlined"
                      required
                      value={contactForm.nom}
                      onChange={handleContactChange}
                      error={!!formErrors.nom}
                      helperText={formErrors.nom}
                      disabled={isSubmitting}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t.contact.form.firstName}
                      name="prenom"
                      variant="outlined"
                      required
                      value={contactForm.prenom}
                      onChange={handleContactChange}
                      error={!!formErrors.prenom}
                      helperText={formErrors.prenom}
                      disabled={isSubmitting}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t.contact.form.email}
                      name="email"
                      variant="outlined"
                      type="email"
                      required
                      value={contactForm.email}
                      onChange={handleContactChange}
                      error={!!formErrors.email}
                      helperText={formErrors.email}
                      disabled={isSubmitting}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t.contact.form.phone}
                      name="telephone"
                      variant="outlined"
                      value={contactForm.telephone}
                      onChange={handleContactChange}
                      disabled={isSubmitting}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel id="sujet-label">{t.contact.form.subject}</InputLabel>
                      <Select
                        labelId="sujet-label"
                        name="sujet"
                        value={contactForm.sujet}
                        onChange={handleContactChange}
                        label={t.contact.form.subject}
                        disabled={isSubmitting}
                      >
                        <MenuItem value=""><em>Sélectionnez un sujet</em></MenuItem>
                        <MenuItem value="information">Demande d'information</MenuItem>
                        <MenuItem value="partenariat">Proposition de partenariat</MenuItem>
                        <MenuItem value="technique">Support technique</MenuItem>
                        <MenuItem value="autre">Autre</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t.contact.form.message}
                      name="message"
                      variant="outlined"
                      multiline
                      rows={4}
                      required
                      value={contactForm.message}
                      onChange={handleContactChange}
                      error={!!formErrors.message}
                      helperText={formErrors.message}
                      disabled={isSubmitting}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <StyledButton
                      variant="contained"
                      color="primary"
                      size="large"
                      fullWidth
                      type="submit"
                      disabled={isSubmitting}
                      startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                      className="animate-pulse"
                    >
                      {isSubmitting ? 'Envoi en cours...' : t.contact.form.send}
                    </StyledButton>
                  </Grid>
                </Grid>
              </ContactForm>
            </Grid>
            <Grid item xs={12} md={6} className="scroll-anim slide-in-right">
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <Typography variant="h5" gutterBottom fontWeight="bold" color="primary">
                  {t.contact.title}
                </Typography>
                <Typography variant="body1" paragraph>
                  N'hésitez pas à nous contacter directement par téléphone ou email, ou en utilisant le formulaire ci-contre.
                </Typography>
                <Box sx={{ my: 3 }}>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>{t.contact.address}:</strong> 123 Rue des Volailles, Alger, Algérie
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>{t.contact.phone}:</strong> +213 XX XX XX XX
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>{t.contact.email}:</strong> <EMAIL>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Heures d'ouverture:</strong> Dim-Jeu, 9h-17h
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>

      {/* Footer */}
      <Box sx={{ py: 5, backgroundColor: theme.palette.primary.dark, color: 'white' }} className="scroll-anim fade-in">
        <Container>
          <Grid container spacing={4}>
            <Grid item xs={12} sm={6} md={3} className="scroll-anim fade-in">
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Poultray DZ
              </Typography>
              <Typography variant="body2" paragraph>
                La plateforme de gestion d'élevage de volailles en Algérie
              </Typography>
              <Typography variant="body2">
                Connecter les éleveurs, vétérinaires et acheteurs pour une filière avicole plus efficace.
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3} className="scroll-anim fade-in" sx={{ animationDelay: '0.2s' }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Liens Rapides
              </Typography>
              <Typography variant="body2" component="div">
                <Box component="ul" sx={{ pl: 2, m: 0, listStyleType: 'none' }}>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <RouterLink to="/" style={{ color: 'white', textDecoration: 'none', display: 'flex', alignItems: 'center', '&:hover': { color: theme.palette.secondary.light } }}>
                      Accueil
                    </RouterLink>
                  </Box>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <Box component="a" href="#fonctionnalites" sx={{ color: 'white', textDecoration: 'none', display: 'flex', alignItems: 'center', '&:hover': { color: theme.palette.secondary.light } }}>
                      Fonctionnalités
                    </Box>
                  </Box>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <Box component="a" href="#tarifs" sx={{ color: 'white', textDecoration: 'none', display: 'flex', alignItems: 'center', '&:hover': { color: theme.palette.secondary.light } }}>
                      Tarifs
                    </Box>
                  </Box>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <Box component="a" onClick={scrollToContact} sx={{ color: 'white', textDecoration: 'none', display: 'flex', alignItems: 'center', cursor: 'pointer', '&:hover': { color: theme.palette.secondary.light } }}>
                      Contact
                    </Box>
                  </Box>
                </Box>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3} className="scroll-anim fade-in" sx={{ animationDelay: '0.4s' }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Légal
              </Typography>
              <Typography variant="body2" component="div">
                <Box component="ul" sx={{ pl: 2, m: 0, listStyleType: 'none' }}>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <RouterLink to="/terms" style={{ color: 'white', textDecoration: 'none', '&:hover': { color: theme.palette.secondary.light } }}>
                      Conditions d'utilisation
                    </RouterLink>
                  </Box>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <RouterLink to="/privacy" style={{ color: 'white', textDecoration: 'none', '&:hover': { color: theme.palette.secondary.light } }}>
                      Politique de confidentialité
                    </RouterLink>
                  </Box>
                  <Box component="li" sx={{ mb: 1.5 }}>
                    <RouterLink to="/faq" style={{ color: 'white', textDecoration: 'none', '&:hover': { color: theme.palette.secondary.light } }}>
                      FAQ
                    </RouterLink>
                  </Box>
                </Box>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3} className="scroll-anim fade-in" sx={{ animationDelay: '0.6s' }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Suivez-nous
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                {/* Icônes de réseaux sociaux avec animations */}
                <IconButton
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    '&:hover': {
                      backgroundColor: '#1877F2',
                      transform: 'translateY(-5px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                  aria-label="Facebook"
                  component="a"
                  href="https://facebook.com"
                  target="_blank"
                  className="animate-float"
                >
                  <FacebookIcon />
                </IconButton>
                <IconButton
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    '&:hover': {
                      backgroundColor: '#0A66C2',
                      transform: 'translateY(-5px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                  aria-label="LinkedIn"
                  component="a"
                  href="https://linkedin.com"
                  target="_blank"
                  className="animate-float"
                >
                  <LinkedInIcon />
                </IconButton>
                <IconButton
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    '&:hover': {
                      backgroundColor: '#1DA1F2',
                      transform: 'translateY(-5px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                  aria-label="Twitter"
                  component="a"
                  href="https://twitter.com"
                  target="_blank"
                  className="animate-float"
                >
                  <TwitterIcon />
                </IconButton>
                <IconButton
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    '&:hover': {
                      backgroundColor: '#E4405F',
                      transform: 'translateY(-5px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                  aria-label="Instagram"
                  component="a"
                  href="https://instagram.com"
                  target="_blank"
                  className="animate-float"
                >
                  <InstagramIcon />
                </IconButton>
              </Box>
              <Box sx={{ mt: 3 }}>
                <Typography variant="body2" gutterBottom>
                  Inscrivez-vous à notre newsletter
                </Typography>
                <Box sx={{ display: 'flex', mt: 1 }}>
                  <TextField
                    size="small"
                    placeholder="Votre email"
                    variant="outlined"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: 1,
                      input: { color: 'white' },
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': { borderColor: 'rgba(255,255,255,0.3)' },
                        '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.5)' },
                        '&.Mui-focused fieldset': { borderColor: theme.palette.secondary.main }
                      }
                    }}
                  />
                  <Button
                    variant="contained"
                    color="secondary"
                    sx={{ ml: 1 }}
                  >
                    OK
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid rgba(255,255,255,0.1)', textAlign: 'center' }}>
            <Typography variant="body2">
              © {new Date().getFullYear()} Poultray DZ. Tous droits réservés.
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  );
}

export default LandingPage;
