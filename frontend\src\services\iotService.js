/**
 * Service API pour les dispositifs IoT
 */

import axiosInstance from '../utils/axiosConfig';

const iotAPI = {
  // === GESTION DES DISPOSITIFS ===
  
  // Récupérer tous les dispositifs IoT
  getDevices: () => {
    return axiosInstance.get('/api/integrations/iot/devices');
  },

  // Récupérer un dispositif spécifique
  getDevice: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}`);
  },

  // Créer un nouveau dispositif
  createDevice: (deviceData) => {
    return axiosInstance.post('/api/integrations/iot/devices', deviceData);
  },

  // Mettre à jour un dispositif
  updateDevice: (deviceId, deviceData) => {
    return axiosInstance.put(`/api/integrations/iot/devices/${deviceId}`, deviceData);
  },

  // Supprimer un dispositif
  deleteDevice: (deviceId) => {
    return axiosInstance.delete(`/api/integrations/iot/devices/${deviceId}`);
  },

  // Activer/désactiver un dispositif
  toggleDevice: (deviceId, active) => {
    return axiosInstance.put(`/api/integrations/iot/devices/${deviceId}/toggle`, { active });
  },

  // === DONNÉES DES CAPTEURS ===
  
  // Récupérer les données en temps réel d'un dispositif
  getDeviceData: (deviceId, period = '1hour') => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/data?period=${period}`);
  },

  // Récupérer les dernières lectures de tous les dispositifs
  getLatestReadings: () => {
    return axiosInstance.get('/api/integrations/iot/data/latest');
  },

  // Récupérer l'historique des données
  getDataHistory: (deviceId, startDate, endDate) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/history`, {
      params: { start_date: startDate, end_date: endDate }
    });
  },

  // === ALERTES IoT ===
  
  // Récupérer les alertes actives
  getAlerts: () => {
    return axiosInstance.get('/api/integrations/iot/alerts');
  },

  // Récupérer les alertes d'un dispositif spécifique
  getDeviceAlerts: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/alerts`);
  },

  // Marquer une alerte comme résolue
  resolveAlert: (alertId) => {
    return axiosInstance.put(`/api/integrations/iot/alerts/${alertId}/resolve`);
  },

  // Créer une alerte personnalisée
  createAlert: (alertData) => {
    return axiosInstance.post('/api/integrations/iot/alerts', alertData);
  },

  // === SEUILS ET CONFIGURATION ===
  
  // Configurer les seuils d'alerte pour un dispositif
  setDeviceThresholds: (deviceId, thresholds) => {
    return axiosInstance.put(`/api/integrations/iot/devices/${deviceId}/thresholds`, thresholds);
  },

  // Récupérer les seuils configurés
  getDeviceThresholds: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/thresholds`);
  },

  // === COMMANDES À DISTANCE ===
  
  // Envoyer une commande à un dispositif
  sendCommand: (deviceId, command, parameters = {}) => {
    return axiosInstance.post(`/api/integrations/iot/devices/${deviceId}/command`, {
      command,
      parameters
    });
  },

  // Récupérer l'historique des commandes
  getCommandHistory: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/commands/history`);
  },

  // === STATISTIQUES ET MÉTRIQUES ===
  
  // Récupérer les statistiques générales des dispositifs IoT
  getStats: () => {
    return axiosInstance.get('/api/integrations/iot/stats');
  },

  // Récupérer les métriques de performance d'un dispositif
  getDeviceMetrics: (deviceId, period = '24hours') => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/metrics?period=${period}`);
  },

  // Récupérer les statistiques de connectivité
  getConnectivityStats: () => {
    return axiosInstance.get('/api/integrations/iot/connectivity/stats');
  },

  // === GROUPES DE DISPOSITIFS ===
  
  // Créer un groupe de dispositifs
  createDeviceGroup: (groupData) => {
    return axiosInstance.post('/api/integrations/iot/groups', groupData);
  },

  // Récupérer tous les groupes
  getDeviceGroups: () => {
    return axiosInstance.get('/api/integrations/iot/groups');
  },

  // Ajouter un dispositif à un groupe
  addDeviceToGroup: (groupId, deviceId) => {
    return axiosInstance.post(`/api/integrations/iot/groups/${groupId}/devices`, { device_id: deviceId });
  },

  // Retirer un dispositif d'un groupe
  removeDeviceFromGroup: (groupId, deviceId) => {
    return axiosInstance.delete(`/api/integrations/iot/groups/${groupId}/devices/${deviceId}`);
  },

  // === AUTOMATISATION ===
  
  // Créer une règle d'automatisation
  createAutomationRule: (ruleData) => {
    return axiosInstance.post('/api/integrations/iot/automation/rules', ruleData);
  },

  // Récupérer toutes les règles d'automatisation
  getAutomationRules: () => {
    return axiosInstance.get('/api/integrations/iot/automation/rules');
  },

  // Activer/désactiver une règle
  toggleAutomationRule: (ruleId, active) => {
    return axiosInstance.put(`/api/integrations/iot/automation/rules/${ruleId}/toggle`, { active });
  },

  // === CALIBRATION ===
  
  // Calibrer un capteur
  calibrateDevice: (deviceId, calibrationData) => {
    return axiosInstance.post(`/api/integrations/iot/devices/${deviceId}/calibrate`, calibrationData);
  },

  // Récupérer l'historique de calibration
  getCalibrationHistory: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/calibration/history`);
  },

  // === MAINTENANCE ===
  
  // Programmer une maintenance
  scheduleMaintenance: (deviceId, maintenanceData) => {
    return axiosInstance.post(`/api/integrations/iot/devices/${deviceId}/maintenance`, maintenanceData);
  },

  // Récupérer le planning de maintenance
  getMaintenanceSchedule: () => {
    return axiosInstance.get('/api/integrations/iot/maintenance/schedule');
  },

  // Marquer une maintenance comme terminée
  completeMaintenance: (maintenanceId, notes) => {
    return axiosInstance.put(`/api/integrations/iot/maintenance/${maintenanceId}/complete`, { notes });
  },

  // === RAPPORTS IoT ===
  
  // Générer un rapport de performance
  generatePerformanceReport: (reportConfig) => {
    return axiosInstance.post('/api/integrations/iot/reports/performance', reportConfig);
  },

  // Générer un rapport d'utilisation
  generateUsageReport: (reportConfig) => {
    return axiosInstance.post('/api/integrations/iot/reports/usage', reportConfig);
  },

  // Télécharger un rapport
  downloadReport: (reportId) => {
    return axiosInstance.get(`/api/integrations/iot/reports/${reportId}/download`, {
      responseType: 'blob'
    });
  },

  // === INTÉGRATION TEMPS RÉEL ===
  
  // Établir une connexion WebSocket pour les données en temps réel
  connectWebSocket: (deviceIds = []) => {
    const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:3001'}/iot/realtime`;
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      // S'abonner aux dispositifs spécifiés
      if (deviceIds.length > 0) {
        ws.send(JSON.stringify({
          type: 'subscribe',
          devices: deviceIds
        }));
      }
    };
    
    return ws;
  },

  // === DIAGNOSTIC ===
  
  // Tester la connectivité d'un dispositif
  testDeviceConnectivity: (deviceId) => {
    return axiosInstance.post(`/api/integrations/iot/devices/${deviceId}/test`);
  },

  // Récupérer les logs de diagnostic
  getDiagnosticLogs: (deviceId, level = 'all') => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/logs?level=${level}`);
  },

  // === FIRMWARE ET MISES À JOUR ===
  
  // Vérifier les mises à jour firmware disponibles
  checkFirmwareUpdates: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/firmware/updates`);
  },

  // Lancer une mise à jour firmware
  updateFirmware: (deviceId, firmwareVersion) => {
    return axiosInstance.post(`/api/integrations/iot/devices/${deviceId}/firmware/update`, {
      version: firmwareVersion
    });
  },

  // Récupérer le statut de mise à jour
  getFirmwareUpdateStatus: (deviceId) => {
    return axiosInstance.get(`/api/integrations/iot/devices/${deviceId}/firmware/status`);
  }
};

export { iotAPI };
export default iotAPI;
