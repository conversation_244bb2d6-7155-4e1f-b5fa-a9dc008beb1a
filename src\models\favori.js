const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Favori extends Model {
    static associate(models) {
      Favori.belongsTo(models.User, {
        foreignKey: 'utilisateur_id',
        as: 'utilisateur'
      });
      Favori.belongsTo(models.Annonce, {
        foreignKey: 'annonce_id',
        as: 'annonce'
      });
    }
  }

  Favori.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  utilisateur_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  annonce_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'annonces',
      key: 'id'
    }
  },
  date_ajout: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Favori',
  tableName: 'favoris',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['utilisateur_id', 'annonce_id']
    }
  ]
});

  return Favori;
};
