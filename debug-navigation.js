const models = require('./src/models');
const jwt = require('jsonwebtoken');

async function debugNavigation() {
  console.log('=== Debug Navigation Issue ===');

  try {
    // Test avec un utilisateur admin existant
    const testUser = await models.User.findOne({
      where: { email: '<EMAIL>' },
      include: [{ model: models.Role, as: 'role' }]
    });

    if (testUser) {
      console.log('✅ Utilisateur trouvé en base:');
      console.log('- ID:', testUser.id);
      console.log('- Email:', testUser.email);
      console.log('- Rôle ID:', testUser.role_id);
      console.log('- Rôle nom:', testUser.role ? testUser.role.name : 'Non défini');

      // Générer un token
      const token = jwt.sign(
        {
          id: testUser.id,
          email: testUser.email,
          role: testUser.role ? testUser.role.name : 'user',
          role_id: testUser.role_id
        },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      console.log('✅ Token généré:', token.substring(0, 50) + '...');

      // Décoder le token pour vérifier
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('✅ Token décodé:');
      console.log('- ID:', decoded.id);
      console.log('- Email:', decoded.email);
      console.log('- Rôle:', decoded.role);

      // Test de navigation
      console.log('\n=== Test Navigation Logic ===');
      const userRole = decoded.role;

      if (userRole === 'admin') {
        console.log('✅ Navigation vers /admin/dashboard');
      } else if (userRole === 'eleveur') {
        console.log('✅ Navigation vers /eleveur/dashboard');
      } else if (userRole === 'veterinaire') {
        console.log('✅ Navigation vers /veterinaire/dashboard');
      } else if (userRole === 'marchand') {
        console.log('✅ Navigation vers /marchand/dashboard');
      } else {
        console.log('⚠️ Navigation vers /dashboard (fallback)');
      }

      // Test DashboardLayout logic
      console.log('\n=== Test DashboardLayout ===');
      const requiredRole = 'admin'; // Pour tester l'accès admin
      const isAuthenticated = !!decoded;

      console.log('- isAuthenticated:', isAuthenticated);
      console.log('- requiredRole:', requiredRole);
      console.log('- userRole:', userRole);

      if (!isAuthenticated) {
        console.log('❌ Redirection vers /login');
      } else if (requiredRole && userRole !== requiredRole) {
        console.log(`❌ Redirection vers /${userRole}/dashboard`);
      } else {
        console.log('✅ Accès autorisé');
      }

    } else {
      console.log('❌ Aucun utilisateur admin trouvé');
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exécuter uniquement si appelé directement
if (require.main === module) {
  debugNavigation().then(() => {
    console.log('\n=== Fin du debug ===');
    process.exit(0);
  }).catch(console.error);
}
