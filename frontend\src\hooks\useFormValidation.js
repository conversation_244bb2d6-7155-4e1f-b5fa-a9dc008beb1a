import { useState, useCallback, useEffect } from 'react';
import { validateForm, validateField, ValidationUtils } from '../utils/validation';
import { useFormErrorHandler } from './useErrorHandler';

/**
 * Hook personnalisé pour la gestion des formulaires avec validation
 * @param {Object} initialValues - Valeurs initiales du formulaire
 * @param {Object} validationSchema - Schéma de validation
 * @param {Object} options - Options de configuration
 */
export const useFormValidation = (initialValues = {}, validationSchema = {}, options = {}) => {
  const {
    validateOnChange = true,
    validateOnBlur = true,
    validateOnSubmit = true,
    sanitizeOnChange = true,
    debounceMs = 300
  } = options;

  // États du formulaire
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [submitCount, setSubmitCount] = useState(0);

  // Debounce timer pour la validation
  const [debounceTimer, setDebounceTimer] = useState(null);

  // Gestionnaire d'erreurs pour les formulaires
  const { handleFormError, clearFormErrors } = useFormErrorHandler();

  // Vérifier si le formulaire est valide
  useEffect(() => {
    const formErrors = validateForm(values, validationSchema);
    setIsValid(Object.keys(formErrors).length === 0);
  }, [values, validationSchema]);

  // Fonction de nettoyage/sanitisation des données
  const sanitizeValue = useCallback((name, value) => {
    switch (name) {
      case 'email':
        return ValidationUtils.formatEmail(value);
      case 'telephone':
      case 'phone':
        return ValidationUtils.formatPhone(value);
      case 'nom':
      case 'prenom':
      case 'first_name':
      case 'last_name':
        return value ? value.trim().replace(/\s+/g, ' ') : '';
      default:
        return typeof value === 'string' ? value.trim() : value;
    }
  }, []);

  // Validation d'un champ spécifique
  const validateSingleField = useCallback((name, value, allValues = values) => {
    const fieldRules = validationSchema[name];
    if (!fieldRules) return null;

    return validateField(value, fieldRules, allValues);
  }, [validationSchema, values]);

  // Validation complète du formulaire
  const validateAllFields = useCallback(() => {
    const formErrors = validateForm(values, validationSchema);
    setErrors(formErrors);
    return formErrors;
  }, [values, validationSchema]);

  // Gestion du changement de valeur
  const handleChange = useCallback((event) => {
    const { name, value, type, checked } = event.target;
    const newValue = type === 'checkbox' ? checked : value;

    // Sanitiser la valeur si nécessaire
    const sanitizedValue = sanitizeOnChange ? sanitizeValue(name, newValue) : newValue;

    // Mettre à jour les valeurs
    setValues(prev => ({
      ...prev,
      [name]: sanitizedValue
    }));

    // Validation en temps réel avec debounce
    if (validateOnChange && validationSchema[name]) {
      // Nettoyer le timer précédent
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Créer un nouveau timer
      const timer = setTimeout(() => {
        const error = validateSingleField(name, sanitizedValue, {
          ...values,
          [name]: sanitizedValue
        });

        setErrors(prev => ({
          ...prev,
          [name]: error
        }));
      }, debounceMs);

      setDebounceTimer(timer);
    }
  }, [validateOnChange, sanitizeOnChange, sanitizeValue, validateSingleField, debounceMs, debounceTimer, values]);

  // Gestion du blur (perte de focus)
  const handleBlur = useCallback((event) => {
    const { name } = event.target;

    // Marquer le champ comme touché
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validation au blur
    if (validateOnBlur && validationSchema[name]) {
      const error = validateSingleField(name, values[name]);
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  }, [validateOnBlur, validationSchema, validateSingleField, values]);

  // Gestion de la soumission
  const handleSubmit = useCallback((onSubmit) => {
    return async (event) => {
      if (event) {
        event.preventDefault();
      }

      setIsSubmitting(true);
      setSubmitCount(prev => prev + 1);

      try {
        // Validation complète si activée
        let formErrors = {};
        if (validateOnSubmit) {
          formErrors = validateAllFields();

          // Marquer tous les champs comme touchés
          const allTouched = Object.keys(validationSchema).reduce((acc, key) => {
            acc[key] = true;
            return acc;
          }, {});
          setTouched(allTouched);

          // Arrêter si il y a des erreurs
          if (Object.keys(formErrors).length > 0) {
            setIsSubmitting(false);
            return false;
          }
        }

        // Appeler la fonction de soumission
        if (onSubmit) {
          await onSubmit(values, { setErrors, setValues, resetForm });
        }

        return true;
      } catch (error) {
        console.error('Erreur lors de la soumission:', error);

        // Utiliser le gestionnaire d'erreurs pour traiter l'erreur
        const formErrors = handleFormError(error);
        setErrors(formErrors);

        return false;
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [validateOnSubmit, validateAllFields, validationSchema, values]);

  // Réinitialiser le formulaire
  const resetForm = useCallback((newValues = initialValues) => {
    setValues(newValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setSubmitCount(0);
    clearFormErrors(); // Nettoyer aussi les erreurs du gestionnaire global
  }, [initialValues, clearFormErrors]);

  // Définir une valeur spécifique
  const setValue = useCallback((name, value) => {
    const sanitizedValue = sanitizeOnChange ? sanitizeValue(name, value) : value;

    setValues(prev => ({
      ...prev,
      [name]: sanitizedValue
    }));

    // Validation immédiate si le champ a été touché
    if (touched[name] && validationSchema[name]) {
      const error = validateSingleField(name, sanitizedValue);
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  }, [sanitizeOnChange, sanitizeValue, touched, validationSchema, validateSingleField]);

  // Définir plusieurs valeurs
  const setFormValues = useCallback((newValues) => {
    if (typeof newValues === 'function') {
      setValues(prev => {
        const updated = newValues(prev);
        return updated;
      });
    } else {
      setValues(newValues);
    }
  }, []);

  // Définir une erreur spécifique
  const setFieldError = useCallback((name, error) => {
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, []);

  // Nettoyer une erreur spécifique
  const clearFieldError = useCallback((name) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }, []);

  // Nettoyer toutes les erreurs
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Vérifier si un champ a une erreur et a été touché
  const getFieldError = useCallback((name) => {
    return touched[name] ? errors[name] : null;
  }, [touched, errors]);

  // Vérifier si un champ est valide
  const isFieldValid = useCallback((name) => {
    return !errors[name];
  }, [errors]);

  // Nettoyer les timers lors du démontage
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    // Valeurs et état
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    submitCount,

    // Gestionnaires d'événements
    handleChange,
    handleBlur,
    handleSubmit,

    // Fonctions utilitaires
    setValue,
    setFormValues,
    setFieldError,
    clearFieldError,
    clearErrors,
    resetForm,
    validateAllFields,
    validateSingleField,
    getFieldError,
    isFieldValid,

    // Helpers pour les composants
    getFieldProps: (name) => ({
      name,
      value: values[name] || '',
      onChange: handleChange,
      onBlur: handleBlur,
      error: !!getFieldError(name),
      helperText: getFieldError(name)
    }),

    getCheckboxProps: (name) => ({
      name,
      checked: !!values[name],
      onChange: handleChange,
      onBlur: handleBlur
    }),

    getSelectProps: (name) => ({
      name,
      value: values[name] || '',
      onChange: handleChange,
      onBlur: handleBlur,
      error: !!getFieldError(name)
    })
  };
};

export default useFormValidation;
