{"name": "poultray-dz-backend", "version": "1.0.0", "description": "Backend API for Poultray DZ application", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "setup-db": "node scripts/setup-database.js", "create-tables": "node src/database/createMissingTables.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "firebase-admin": "^11.11.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "openai": "^4.20.1", "pg": "^8.16.2", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "slugify": "^1.6.6", "swagger-ui-express": "^5.0.1", "umzug": "^3.8.2", "yamljs": "^0.3.0"}, "devDependencies": {"colors": "^1.4.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^7.1.1"}}