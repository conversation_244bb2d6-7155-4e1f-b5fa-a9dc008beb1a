import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Save as SaveIcon, Visibility, VisibilityOff } from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const ApiConfig = () => {
  const [apiKeys, setApiKeys] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showKeys, setShowKeys] = useState({});

  useEffect(() => {
    const fetchApiKeys = async () => {
      try {
        setLoading(true);
        // Corriger le chemin en supprimant le préfixe '/api' qui est déjà dans baseURL
        const response = await axiosInstance.get('/admin/settings/api-keys');
        setApiKeys(response.data || {});
        setError('');
      } catch (err) {
        setError(err.response?.data?.message || "Erreur lors de la récupération des clés API.");
        console.error("Erreur fetchApiKeys:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchApiKeys();
  }, []);

  const handleChange = (service, value) => {
    setApiKeys(prev => ({ ...prev, [service]: value }));
  };

  const toggleShowKey = (service) => {
    setShowKeys(prev => ({ ...prev, [service]: !prev[service] }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      // Log what we're sending to the server
      console.log("Saving API keys:", { apiKeys });

      // Corriger le chemin en supprimant le préfixe '/api' qui est déjà dans baseURL
      const response = await axiosInstance.post('/admin/settings/api-keys', { apiKeys });
      console.log("Server response:", response.data);
      setSuccess('Clés API sauvegardées avec succès !');
    } catch (err) {
      console.error("Erreur handleSubmit détaillée:", {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      setError(err.response?.data?.message || err.response?.data?.error || "Erreur lors de la sauvegarde des clés API.");
      console.error("Erreur handleSubmit:", err);
    } finally {
      setLoading(false);
    }
  };

  // Map friendly names for API services
  const serviceLabels = {
    openai: 'OpenAI API Key',
    google: 'Google AI API Key',
    azure_openai: 'Azure OpenAI API Key',
    claude: 'Anthropic Claude API Key',
    gemini: 'Google Gemini API Key'
  };

  const renderApiKeyField = (service, label) => (
    <Grid item xs={12} key={service}>
      <TextField
        fullWidth
        label={label || serviceLabels[service] || `${service} API Key`}
        variant="outlined"
        type={showKeys[service] ? 'text' : 'password'}
        value={apiKeys[service] || ''}
        onChange={(e) => handleChange(service, e.target.value)}
        InputProps={{
          endAdornment: (
            <Tooltip title={showKeys[service] ? "Cacher la clé" : "Afficher la clé"}>
              <IconButton onClick={() => toggleShowKey(service)} edge="end">
                {showKeys[service] ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </Tooltip>
          ),
        }}
        sx={{ mb: 2 }}
      />
    </Grid>
  );

  if (loading && !Object.keys(apiKeys).length) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4, m: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Configuration des Clés API pour l'IA
      </Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
      <Typography variant="body1" sx={{ mb: 3 }}>
        Configurez les clés API pour les services d'intelligence artificielle utilisés par l'application.
        Ces clés sont stockées de manière sécurisée et chiffrées dans la base de données.
      </Typography>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          {/* Afficher dynamiquement tous les services API définis */}
          {Object.keys(serviceLabels).map(service =>
            renderApiKeyField(service)
          )}
        </Grid>
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Sauvegarder les Clés API'}
          </Button>
        </Box>
      </form>
    </Paper>
  );
};

export default ApiConfig;


