const express = require('express');
const router = express.Router();
const BlogController = require('../controllers/blogController');
const { auth } = require('../middleware/auth');
const admin = require('../middleware/admin');

// Middleware pour vérifier si l'utilisateur peut gérer les blogs
const canManageBlogs = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'editor')) {
    next();
  } else {
    res.status(403).json({ error: 'Accès non autorisé à la gestion des blogs' });
  }
};

// Routes publiques
router.get('/posts', BlogController.getPosts); // Liste tous les posts publiés
router.get('/posts/:id', BlogController.getPostById); // Détails d'un post

// Routes protégées nécessitant une authentification
router.post('/posts',
  auth,
  canManageBlogs,
  BlogController.createPost
);

router.put('/posts/:id',
  auth,
  canManageBlogs,
  BlogController.updatePost
);

router.delete('/posts/:id',
  auth,
  canManageBlogs,
  BlogController.deletePost
);

// Routes admin pour la gestion globale des blogs
router.get('/admin/posts',
  auth,
  admin,
  BlogController.getAdminPosts // Utilise la méthode spécifique pour les admin
);

router.post('/admin/posts',
  auth,
  admin,
  BlogController.createPost
);

module.exports = router;
