/**
 * Composant de gestion des rendez-vous pour les vétérinaires
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Box,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Chip,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  Warning as WarningIcon,
  Phone as PhoneIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale/fr';
import { useAuth } from '../../contexts/AuthContext';
import { veterinaireAPI } from '../../services/veterinaireService';

// Configuration de moment en français
moment.locale('fr');
const localizer = momentLocalizer(moment);

const AppointmentsManagement = () => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [patients, setPatients] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [selectedSlot, setSelectedSlot] = useState(null);

  const [formData, setFormData] = useState({
    patient_id: '',
    eleveur_id: '',
    date_debut: new Date(),
    date_fin: new Date(Date.now() + 60 * 60 * 1000), // +1 heure
    motif: '',
    type: 'consultation',
    statut: 'programme',
    urgence: false,
    notes: '',
    rappel: true
  });

  const [stats, setStats] = useState({
    totalRendezVous: 0,
    rendezVousAujourdhui: 0,
    rendezVousEnAttente: 0,
    rendezVousConfirmes: 0
  });

  useEffect(() => {
    loadAppointments();
    loadPatients();
    loadEleveurs();
    loadStats();
  }, []);

  const loadAppointments = async () => {
    try {
      setLoading(true);
      const response = await veterinaireAPI.getAppointments();
      const appointmentsData = response.data || [];

      // Transformer les données pour react-big-calendar
      const calendarEvents = appointmentsData.map(apt => ({
        ...apt,
        id: apt.id,
        title: `${apt.patient_nom} - ${apt.motif}`,
        start: new Date(apt.date_debut),
        end: new Date(apt.date_fin),
        resource: apt
      }));

      setAppointments(calendarEvents);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des rendez-vous');
      console.error('Erreur rendez-vous:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    try {
      const response = await veterinaireAPI.getPatients();
      setPatients(response.data || []);
    } catch (err) {
      console.error('Erreur patients:', err);
    }
  };

  const loadEleveurs = async () => {
    try {
      const response = await veterinaireAPI.getEleveurs();
      setEleveurs(response.data || []);
    } catch (err) {
      console.error('Erreur éleveurs:', err);
    }
  };

  const loadStats = async () => {
    try {
      const response = await veterinaireAPI.getAppointmentsStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats rendez-vous:', err);
    }
  };

  const handleSelectSlot = ({ start, end }) => {
    setSelectedSlot({ start, end });
    setSelectedAppointment(null);
    setFormData({
      patient_id: '',
      eleveur_id: '',
      date_debut: start,
      date_fin: end,
      motif: '',
      type: 'consultation',
      statut: 'programme',
      urgence: false,
      notes: '',
      rappel: true
    });
    setOpenDialog(true);
  };

  const handleSelectEvent = (event) => {
    const appointment = event.resource;
    setSelectedAppointment(appointment);
    setSelectedSlot(null);
    setFormData({
      patient_id: appointment.patient_id || '',
      eleveur_id: appointment.eleveur_id || '',
      date_debut: new Date(appointment.date_debut),
      date_fin: new Date(appointment.date_fin),
      motif: appointment.motif || '',
      type: appointment.type || 'consultation',
      statut: appointment.statut || 'programme',
      urgence: appointment.urgence || false,
      notes: appointment.notes || '',
      rappel: appointment.rappel !== false
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAppointment(null);
    setSelectedSlot(null);
  };

  const handleSubmit = async () => {
    try {
      const appointmentData = {
        ...formData,
        veterinaire_id: user.profile_id
      };

      if (selectedAppointment) {
        await veterinaireAPI.updateAppointment(selectedAppointment.id, appointmentData);
      } else {
        await veterinaireAPI.createAppointment(appointmentData);
      }

      handleCloseDialog();
      loadAppointments();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde du rendez-vous');
      console.error('Erreur sauvegarde rendez-vous:', err);
    }
  };

  const handleConfirmAppointment = async (appointmentId) => {
    try {
      await veterinaireAPI.confirmAppointment(appointmentId);
      loadAppointments();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la confirmation du rendez-vous');
      console.error('Erreur confirmation:', err);
    }
  };

  const handleCancelAppointment = async (appointmentId, reason = 'Annulé par le vétérinaire') => {
    try {
      await veterinaireAPI.cancelAppointment(appointmentId, reason);
      loadAppointments();
      loadStats();
    } catch (err) {
      setError('Erreur lors de l\'annulation du rendez-vous');
      console.error('Erreur annulation:', err);
    }
  };

  const getEventStyle = (event) => {
    const appointment = event.resource;
    let backgroundColor = '#3174ad';

    switch (appointment.statut) {
      case 'programme':
        backgroundColor = '#3174ad';
        break;
      case 'confirme':
        backgroundColor = '#28a745';
        break;
      case 'en_cours':
        backgroundColor = '#ffc107';
        break;
      case 'termine':
        backgroundColor = '#6c757d';
        break;
      case 'annule':
        backgroundColor = '#dc3545';
        break;
    }

    if (appointment.urgence) {
      backgroundColor = '#dc3545';
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '5px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  const StatCard = ({ title, value, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const messages = {
    allDay: 'Toute la journée',
    previous: 'Précédent',
    next: 'Suivant',
    today: 'Aujourd\'hui',
    month: 'Mois',
    week: 'Semaine',
    day: 'Jour',
    agenda: 'Agenda',
    date: 'Date',
    time: 'Heure',
    event: 'Événement',
    noEventsInRange: 'Aucun rendez-vous dans cette période',
    showMore: total => `+ ${total} de plus`
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Rendez-vous
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleSelectSlot({
              start: new Date(),
              end: new Date(Date.now() + 60 * 60 * 1000)
            })}
          >
            Nouveau Rendez-vous
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Rendez-vous"
              value={stats.totalRendezVous}
              icon={<ScheduleIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Aujourd'hui"
              value={stats.rendezVousAujourdhui}
              icon={<ScheduleIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="En Attente"
              value={stats.rendezVousEnAttente}
              icon={<WarningIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Confirmés"
              value={stats.rendezVousConfirmes}
              icon={<CheckIcon />}
              color="success"
            />
          </Grid>
        </Grid>

        {/* Calendrier */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Planning des Rendez-vous
            </Typography>
            <Box sx={{ height: 600 }}>
              <Calendar
                localizer={localizer}
                events={appointments}
                startAccessor="start"
                endAccessor="end"
                messages={messages}
                onSelectSlot={handleSelectSlot}
                onSelectEvent={handleSelectEvent}
                selectable
                eventPropGetter={getEventStyle}
                views={['month', 'week', 'day', 'agenda']}
                defaultView="week"
                step={30}
                timeslots={2}
                min={new Date(2024, 0, 1, 8, 0)} // 8h00
                max={new Date(2024, 0, 1, 18, 0)} // 18h00
                formats={{
                  timeGutterFormat: 'HH:mm',
                  eventTimeRangeFormat: ({ start, end }) =>
                    `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`
                }}
              />
            </Box>
          </CardContent>
        </Card>

        {/* Dialog pour créer/modifier un rendez-vous */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {selectedAppointment ? 'Modifier le Rendez-vous' : 'Nouveau Rendez-vous'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={eleveurs}
                  getOptionLabel={(option) => option.nom}
                  value={eleveurs.find(e => e.id === formData.eleveur_id) || null}
                  onChange={(event, newValue) => {
                    setFormData({ ...formData, eleveur_id: newValue?.id || '', patient_id: '' });
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Éleveur" fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={patients.filter(p => !formData.eleveur_id || p.eleveur_id === formData.eleveur_id)}
                  getOptionLabel={(option) => `${option.nom} (${option.espece})`}
                  value={patients.find(p => p.id === formData.patient_id) || null}
                  onChange={(event, newValue) => {
                    setFormData({ ...formData, patient_id: newValue?.id || '' });
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Patient" fullWidth />
                  )}
                  disabled={!formData.eleveur_id}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label="Date et heure de début"
                  value={formData.date_debut}
                  onChange={(date) => setFormData({ ...formData, date_debut: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label="Date et heure de fin"
                  value={formData.date_fin}
                  onChange={(date) => setFormData({ ...formData, date_fin: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Motif du rendez-vous"
                  value={formData.motif}
                  onChange={(e) => setFormData({ ...formData, motif: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  >
                    <MenuItem value="consultation">Consultation</MenuItem>
                    <MenuItem value="vaccination">Vaccination</MenuItem>
                    <MenuItem value="chirurgie">Chirurgie</MenuItem>
                    <MenuItem value="controle">Contrôle</MenuItem>
                    <MenuItem value="urgence">Urgence</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={formData.statut}
                    onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                  >
                    <MenuItem value="programme">Programmé</MenuItem>
                    <MenuItem value="confirme">Confirmé</MenuItem>
                    <MenuItem value="en_cours">En cours</MenuItem>
                    <MenuItem value="termine">Terminé</MenuItem>
                    <MenuItem value="annule">Annulé</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Urgence</InputLabel>
                  <Select
                    value={formData.urgence}
                    onChange={(e) => setFormData({ ...formData, urgence: e.target.value })}
                  >
                    <MenuItem value={false}>Non</MenuItem>
                    <MenuItem value={true}>Oui - Urgent</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            {selectedAppointment && (
              <>
                <Button
                  onClick={() => handleConfirmAppointment(selectedAppointment.id)}
                  color="success"
                >
                  Confirmer
                </Button>
                <Button
                  onClick={() => handleCancelAppointment(selectedAppointment.id)}
                  color="error"
                >
                  Annuler RDV
                </Button>
              </>
            )}
            <Button onClick={handleSubmit} variant="contained">
              {selectedAppointment ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default AppointmentsManagement;
