#!/bin/bash

# 1. Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib -y

# 2. Fix locale issues
sudo locale-gen en_US.UTF-8
sudo update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# 3. Start and enable PostgreSQL
sudo service postgresql start
sudo systemctl enable postgresql

# 4. Create database and user
sudo -u postgres psql -c "CREATE DATABASE poultraydz;" 2>/dev/null
sudo -u postgres psql -c "CREATE USER postgres WITH PASSWORD 'root';" 2>/dev/null
sudo -u postgres psql -c "ALTER USER postgres CREATEDB;" 2>/dev/null
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE poultraydz TO postgres;" 2>/dev/null

# 5. Configure authentication
sudo sed -i "s|local   all             all                                     peer|local   all             postgres                                trust\nlocal   all             all                                     md5|" /etc/postgresql/*/main/pg_hba.conf
sudo sed -i "/host    all             all             127.0.0.1\/32            md5/d" /etc/postgresql/*/main/pg_hba.conf
sudo echo "host    all             all             127.0.0.1/32            md5" >> /etc/postgresql/*/main/pg_hba.conf

# 6. Reload configuration
sudo service postgresql reload

# 7. Create .env file
cat <<EOF > ~/Poultraydz-Trae/.env
DB_USER=postgres
DB_HOST=localhost
DB_NAME=poultraydz
DB_PASSWORD=root
DB_PORT=5432
EOF

# 8. Verify setup
PGPASSWORD=root psql -h localhost -U postgres -d poultraydz -c "SELECT 'Database ready!' AS status"
