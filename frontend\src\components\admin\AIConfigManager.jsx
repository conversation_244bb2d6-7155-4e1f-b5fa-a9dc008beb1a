import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Tab,
  Tabs,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar
} from '@mui/material';

const AIConfigManager = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [config, setConfig] = useState({
    analysis: {
      enabled: true,
      model: 'gpt-4',
      temperature: 0.7,
      prompts: {
        marketAnalysis: '',
        userBehavior: '',
        salesPrediction: ''
      }
    },
    contentGeneration: {
      enabled: true,
      model: 'gpt-4',
      temperature: 0.8,
      prompts: {
        blogPost: '',
        productDescription: '',
        marketingCopy: ''
      }
    }
  });
  const [openDialog, setOpenDialog] = useState(false);
  const [testPrompt, setTestPrompt] = useState('');
  const [testResult, setTestResult] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      // Implémenter le chargement de la configuration depuis l'API
      console.log('Configuration chargée');
    } catch (error) {
      console.error('Erreur lors du chargement de la configuration:', error);
      showSnackbar('Erreur lors du chargement de la configuration', 'error');
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleConfigChange = (section, subsection, field, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: typeof value === 'object'
          ? { ...prev[section][subsection], ...value }
          : value
      }
    }));
  };

  const handleSaveConfig = async () => {
    try {
      // Implémenter la sauvegarde de la configuration via l'API
      showSnackbar('Configuration enregistrée avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      showSnackbar('Erreur lors de la sauvegarde', 'error');
    }
  };

  const handleTestPrompt = async () => {
    try {
      // Implémenter le test du prompt via l'API
      setTestResult('Résultat du test: [Réponse de l\'IA]');
    } catch (error) {
      console.error('Erreur lors du test:', error);
      setTestResult('Erreur lors du test du prompt');
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const renderPromptFields = (section) => {
    const prompts = config[section].prompts;
    return Object.entries(prompts).map(([key, value]) => (
      <TextField
        key={key}
        fullWidth
        multiline
        rows={4}
        label={key.charAt(0).toUpperCase() + key.slice(1)}
        value={value}
        onChange={(e) =>
          handleConfigChange(section, 'prompts', key, e.target.value)
        }
        margin="normal"
      />
    ));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Configuration de l'IA
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Analyse" />
          <Tab label="Génération de Contenu" />
        </Tabs>
      </Box>

      <Card>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={config[activeTab === 0 ? 'analysis' : 'contentGeneration'].enabled}
                    onChange={(e) =>
                      handleConfigChange(
                        activeTab === 0 ? 'analysis' : 'contentGeneration',
                        'enabled',
                        e.target.checked
                      )
                    }
                  />
                }
                label="Activer"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Modèle"
                value={config[activeTab === 0 ? 'analysis' : 'contentGeneration'].model}
                onChange={(e) =>
                  handleConfigChange(
                    activeTab === 0 ? 'analysis' : 'contentGeneration',
                    'model',
                    e.target.value
                  )
                }
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Température"
                value={config[activeTab === 0 ? 'analysis' : 'contentGeneration'].temperature}
                onChange={(e) =>
                  handleConfigChange(
                    activeTab === 0 ? 'analysis' : 'contentGeneration',
                    'temperature',
                    parseFloat(e.target.value)
                  )
                }
                inputProps={{ min: 0, max: 1, step: 0.1 }}
                margin="normal"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Prompts
              </Typography>
              {renderPromptFields(activeTab === 0 ? 'analysis' : 'contentGeneration')}
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveConfig}
                >
                  Enregistrer
                </Button>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setOpenDialog(true)}
                >
                  Tester un Prompt
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Tester un Prompt</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Prompt de test"
            value={testPrompt}
            onChange={(e) => setTestPrompt(e.target.value)}
            margin="normal"
          />
          {testResult && (
            <Typography variant="body1" sx={{ mt: 2 }}>
              {testResult}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Fermer</Button>
          <Button onClick={handleTestPrompt} variant="contained" color="primary">
            Tester
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AIConfigManager;
