import React, { useState, useEffect } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis, restrictToHorizontalAxis } from '@dnd-kit/modifiers';

// Hook personnalisé pour le drag and drop
export const useDragAndDrop = (initialItems = []) => {
  const [items, setItems] = useState(initialItems);
  const [activeId, setActiveId] = useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setItems((items) => {
        const oldIndex = items.findIndex(item =>
          typeof item === 'object' ? item.id === active.id : item === active.id
        );
        const newIndex = items.findIndex(item =>
          typeof item === 'object' ? item.id === over.id : item === over.id
        );

        return arrayMove(items, oldIndex, newIndex);
      });
    }

    setActiveId(null);
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  return {
    items,
    setItems,
    activeId,
    sensors,
    handleDragStart,
    handleDragEnd,
    handleDragCancel,
  };
};

// Composant SortableItem réutilisable
export const SortableItem = ({ id, children, className, style = {}, disabled = false }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id, disabled });

  const itemStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: disabled ? 'default' : 'grab',
    ...style,
  };

  return (
    <div
      ref={setNodeRef}
      style={itemStyle}
      className={className}
      {...attributes}
      {...(disabled ? {} : listeners)}
    >
      {children}
    </div>
  );
};

// Composant DragDropContainer réutilisable
export const DragDropContainer = ({
  children,
  items,
  onDragStart,
  onDragEnd,
  onDragCancel,
  sensors,
  strategy = verticalListSortingStrategy,
  modifiers = [restrictToVerticalAxis],
  activeId,
  dragOverlay,
  disabled = false
}) => {
  if (disabled) {
    return <div>{children}</div>;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDragCancel={onDragCancel}
      modifiers={modifiers}
    >
      <SortableContext items={items} strategy={strategy}>
        {children}
      </SortableContext>
      <DragOverlay>
        {activeId && dragOverlay ? dragOverlay(activeId) : null}
      </DragOverlay>
    </DndContext>
  );
};

// Composant prêt à l'emploi pour les listes simples
export const SimpleDragDropList = ({
  items,
  onItemsChange,
  renderItem,
  itemKeyExtractor = (item, index) => item.id || index,
  direction = 'vertical',
  disabled = false
}) => {
  const {
    items: currentItems,
    setItems,
    activeId,
    sensors,
    handleDragStart,
    handleDragEnd,
    handleDragCancel,
  } = useDragAndDrop(items);

  useEffect(() => {
    if (onItemsChange) {
      onItemsChange(currentItems);
    }
  }, [currentItems, onItemsChange]);

  useEffect(() => {
    setItems(items);
  }, [items, setItems]);

  const strategy = direction === 'horizontal'
    ? horizontalListSortingStrategy
    : verticalListSortingStrategy;

  const modifiers = direction === 'horizontal'
    ? [restrictToHorizontalAxis]
    : [restrictToVerticalAxis];

  return (
    <DragDropContainer
      items={currentItems.map(itemKeyExtractor)}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
      sensors={sensors}
      strategy={strategy}
      modifiers={modifiers}
      activeId={activeId}
      disabled={disabled}
    >
      {currentItems.map((item, index) => {
        const key = itemKeyExtractor(item, index);
        return (
          <SortableItem key={key} id={key} disabled={disabled}>
            {renderItem(item, index)}
          </SortableItem>
        );
      })}
    </DragDropContainer>
  );
};
