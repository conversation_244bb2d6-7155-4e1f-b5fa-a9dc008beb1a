import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:local_auth/local_auth.dart';
import 'package:crypto/crypto.dart';

import '../models/user_model.dart';
import '../models/auth_models.dart';
import 'api_service.dart';
import 'storage_service.dart';
import 'biometric_service.dart';

/// Service d'authentification avec support biométrique
class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;
  AuthService._internal();

  // État de l'authentification
  User? _currentUser;
  String? _accessToken;
  String? _refreshToken;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  Timer? _tokenRefreshTimer;

  // Getters
  User? get currentUser => _currentUser;
  String? get accessToken => _accessToken;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  /// Initialiser le service d'authentification
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Charger les tokens sauvegardés
      await _loadStoredTokens();
      
      // Vérifier la validité du token
      if (_accessToken != null) {
        await _validateToken();
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de l\'auth: $e');
      await logout();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Connexion avec email/mot de passe
  Future<AuthResult> login(String email, String password) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await ApiService.instance.post('/auth/login', {
        'email': email,
        'password': password,
        'device_info': await _getDeviceInfo(),
      });

      if (response.success) {
        final authData = AuthResponse.fromJson(response.data);
        await _setAuthData(authData);
        
        return AuthResult.success(user: _currentUser);
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Erreur de connexion',
          errors: response.errors,
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la connexion: $e');
      return AuthResult.failure(message: 'Erreur de connexion: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Inscription
  Future<AuthResult> register(RegisterRequest request) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await ApiService.instance.post('/auth/register', {
        ...request.toJson(),
        'device_info': await _getDeviceInfo(),
      });

      if (response.success) {
        final authData = AuthResponse.fromJson(response.data);
        await _setAuthData(authData);
        
        return AuthResult.success(user: _currentUser);
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Erreur lors de l\'inscription',
          errors: response.errors,
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'inscription: $e');
      return AuthResult.failure(message: 'Erreur d\'inscription: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Connexion biométrique
  Future<AuthResult> loginWithBiometrics() async {
    try {
      // Vérifier si les données biométriques sont disponibles
      final storedCredentials = await StorageService.instance.getSecure('biometric_credentials');
      if (storedCredentials == null) {
        return AuthResult.failure(message: 'Aucune donnée biométrique enregistrée');
      }

      // Authentification biométrique
      final biometricResult = await BiometricService.instance.authenticate(
        reason: 'Authentifiez-vous pour accéder à votre compte',
      );

      if (!biometricResult.success) {
        return AuthResult.failure(message: biometricResult.message);
      }

      // Décrypter et utiliser les identifiants
      final credentials = jsonDecode(storedCredentials);
      return await login(credentials['email'], credentials['password']);
      
    } catch (e) {
      debugPrint('Erreur lors de l\'authentification biométrique: $e');
      return AuthResult.failure(message: 'Erreur d\'authentification biométrique');
    }
  }

  /// Activer l'authentification biométrique
  Future<bool> enableBiometricAuth(String email, String password) async {
    try {
      // Vérifier la disponibilité des biométriques
      final isAvailable = await BiometricService.instance.isAvailable();
      if (!isAvailable) {
        return false;
      }

      // Demander l'authentification biométrique
      final biometricResult = await BiometricService.instance.authenticate(
        reason: 'Activez l\'authentification biométrique',
      );

      if (!biometricResult.success) {
        return false;
      }

      // Sauvegarder les identifiants de manière sécurisée
      final credentials = jsonEncode({
        'email': email,
        'password': _hashPassword(password),
        'enabled_at': DateTime.now().toIso8601String(),
      });

      await StorageService.instance.setSecure('biometric_credentials', credentials);
      await StorageService.instance.setBool('biometric_enabled', true);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'activation biométrique: $e');
      return false;
    }
  }

  /// Désactiver l'authentification biométrique
  Future<void> disableBiometricAuth() async {
    await StorageService.instance.remove('biometric_credentials');
    await StorageService.instance.setBool('biometric_enabled', false);
  }

  /// Vérifier si l'authentification biométrique est activée
  Future<bool> isBiometricEnabled() async {
    return await StorageService.instance.getBool('biometric_enabled') ?? false;
  }

  /// Rafraîchir le token
  Future<bool> refreshToken() async {
    try {
      if (_refreshToken == null) {
        await logout();
        return false;
      }

      final response = await ApiService.instance.post('/auth/refresh', {
        'refresh_token': _refreshToken,
      });

      if (response.success) {
        final authData = AuthResponse.fromJson(response.data);
        await _setAuthData(authData);
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (e) {
      debugPrint('Erreur lors du rafraîchissement du token: $e');
      await logout();
      return false;
    }
  }

  /// Déconnexion
  Future<void> logout() async {
    try {
      // Notifier le serveur de la déconnexion
      if (_accessToken != null) {
        await ApiService.instance.post('/auth/logout', {});
      }
    } catch (e) {
      debugPrint('Erreur lors de la déconnexion: $e');
    } finally {
      await _clearAuthData();
    }
  }

  /// Mot de passe oublié
  Future<bool> forgotPassword(String email) async {
    try {
      final response = await ApiService.instance.post('/auth/forgot-password', {
        'email': email,
      });

      return response.success;
    } catch (e) {
      debugPrint('Erreur lors de la demande de réinitialisation: $e');
      return false;
    }
  }

  /// Réinitialiser le mot de passe
  Future<bool> resetPassword(String token, String newPassword) async {
    try {
      final response = await ApiService.instance.post('/auth/reset-password', {
        'token': token,
        'password': newPassword,
      });

      return response.success;
    } catch (e) {
      debugPrint('Erreur lors de la réinitialisation: $e');
      return false;
    }
  }

  /// Changer le mot de passe
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await ApiService.instance.post('/auth/change-password', {
        'current_password': currentPassword,
        'new_password': newPassword,
      });

      return response.success;
    } catch (e) {
      debugPrint('Erreur lors du changement de mot de passe: $e');
      return false;
    }
  }

  /// Mettre à jour le profil
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await ApiService.instance.put('/auth/profile', data);

      if (response.success) {
        _currentUser = User.fromJson(response.data);
        await _saveUserData();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du profil: $e');
      return false;
    }
  }

  /// Méthodes privées

  Future<void> _setAuthData(AuthResponse authData) async {
    _currentUser = authData.user;
    _accessToken = authData.accessToken;
    _refreshToken = authData.refreshToken;
    _isAuthenticated = true;

    // Sauvegarder les données
    await _saveAuthData();
    
    // Programmer le rafraîchissement automatique du token
    _scheduleTokenRefresh();

    notifyListeners();
  }

  Future<void> _clearAuthData() async {
    _currentUser = null;
    _accessToken = null;
    _refreshToken = null;
    _isAuthenticated = false;
    
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = null;

    // Supprimer les données sauvegardées
    await StorageService.instance.remove('access_token');
    await StorageService.instance.remove('refresh_token');
    await StorageService.instance.remove('user_data');

    notifyListeners();
  }

  Future<void> _saveAuthData() async {
    if (_accessToken != null) {
      await StorageService.instance.setString('access_token', _accessToken!);
    }
    if (_refreshToken != null) {
      await StorageService.instance.setString('refresh_token', _refreshToken!);
    }
    await _saveUserData();
  }

  Future<void> _saveUserData() async {
    if (_currentUser != null) {
      await StorageService.instance.setString('user_data', jsonEncode(_currentUser!.toJson()));
    }
  }

  Future<void> _loadStoredTokens() async {
    _accessToken = await StorageService.instance.getString('access_token');
    _refreshToken = await StorageService.instance.getString('refresh_token');
    
    final userData = await StorageService.instance.getString('user_data');
    if (userData != null) {
      _currentUser = User.fromJson(jsonDecode(userData));
    }

    if (_accessToken != null && _currentUser != null) {
      _isAuthenticated = true;
      _scheduleTokenRefresh();
    }
  }

  Future<void> _validateToken() async {
    try {
      final response = await ApiService.instance.get('/auth/validate');
      if (!response.success) {
        await refreshToken();
      }
    } catch (e) {
      await refreshToken();
    }
  }

  void _scheduleTokenRefresh() {
    _tokenRefreshTimer?.cancel();
    
    // Programmer le rafraîchissement 5 minutes avant l'expiration
    const refreshInterval = Duration(minutes: 55); // Token valide 1h
    
    _tokenRefreshTimer = Timer(refreshInterval, () async {
      await refreshToken();
    });
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    // Implémenter la collecte d'informations sur l'appareil
    return {
      'platform': defaultTargetPlatform.name,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
