import { useState, useEffect } from 'react';
import axios from 'axios';

/**
 * Hook personnalisé pour récupérer les données météorologiques
 * Utilise l'API OpenWeatherMap pour obtenir les conditions météo actuelles et les prévisions
 * 
 * @param {string} location - Localisation pour laquelle récupérer les données météo (ville ou coordonnées)
 * @param {Object} options - Options supplémentaires
 * @param {boolean} options.useMockData - Utiliser des données simulées au lieu de l'API (pour le développement)
 * @param {number} options.refreshInterval - Intervalle de rafraîchissement en millisecondes (par défaut: 1 heure)
 * @returns {Object} - Données météo, état de chargement et erreurs éventuelles
 */
const useWeatherData = (location = 'Alger, Algérie', options = {}) => {
  const { 
    useMockData = true, // Par défaut, utiliser des données simulées
    refreshInterval = 60 * 60 * 1000 // 1 heure par défaut
  } = options;
  
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Clé API OpenWeatherMap (à remplacer par votre propre clé)
  // const API_KEY = process.env.REACT_APP_OPENWEATHER_API_KEY || 'YOUR_API_KEY';
  
  useEffect(() => {
    // Fonction pour récupérer les données météo
    const fetchWeatherData = async () => {
      try {
        setLoading(true);
        
        if (useMockData) {
          // Utiliser des données simulées pour le développement
          setTimeout(() => {
            const mockData = generateMockWeatherData(location);
            setWeatherData(mockData);
            setError(null);
            setLoading(false);
          }, 1000);
        } else {
          // Appel à l'API OpenWeatherMap pour les données réelles
          // Format de l'URL: https://api.openweathermap.org/data/2.5/weather?q={location}&appid={API_KEY}&units=metric&lang=fr
          
          // Récupérer les conditions météo actuelles
          const currentWeatherResponse = await axios.get(
            `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(location)}&appid=${API_KEY}&units=metric&lang=fr`
          );
          
          // Récupérer les prévisions sur 5 jours
          const forecastResponse = await axios.get(
            `https://api.openweathermap.org/data/2.5/forecast?q=${encodeURIComponent(location)}&appid=${API_KEY}&units=metric&lang=fr`
          );
          
          // Formater les données
          const formattedData = formatWeatherData(currentWeatherResponse.data, forecastResponse.data);
          
          setWeatherData(formattedData);
          setError(null);
          setLoading(false);
        }
      } catch (err) {
        console.error('Erreur lors de la récupération des données météo:', err);
        setError('Impossible de charger les données météo');
        setLoading(false);
      }
    };
    
    // Appel initial
    fetchWeatherData();
    
    // Rafraîchir les données à intervalles réguliers
    const intervalId = setInterval(fetchWeatherData, refreshInterval);
    
    // Nettoyage
    return () => clearInterval(intervalId);
  }, [location, useMockData, refreshInterval]);
  
  // Fonction pour générer des données météo simulées
  const generateMockWeatherData = (location) => {
    // Extraire le nom de la ville de la localisation
    const city = location.split(',')[0].trim();
    
    // Générer une température aléatoire entre 15 et 30°C
    const temperature = Math.floor(Math.random() * 15) + 15;
    
    // Conditions météo possibles
    const conditions = ['sunny', 'cloudy', 'rainy', 'stormy', 'snowy'];
    const condition = conditions[Math.floor(Math.random() * 3)]; // Limiter aux 3 premières pour plus de réalisme
    
    // Descriptions correspondantes
    const descriptions = {
      sunny: 'Ensoleillé',
      cloudy: 'Nuageux',
      rainy: 'Pluvieux',
      stormy: 'Orageux',
      snowy: 'Neigeux'
    };
    
    // Générer des prévisions pour les 3 prochains jours
    const forecast = [];
    const today = new Date();
    
    for (let i = 0; i < 3; i++) {
      const forecastDate = new Date(today);
      forecastDate.setDate(today.getDate() + i);
      
      const dayNames = ['Aujourd\'hui', 'Demain', 'Après-demain'];
      const dayName = i < 3 ? dayNames[i] : forecastDate.toLocaleDateString('fr-FR', { weekday: 'long' });
      
      // Température avec une légère variation
      const tempVariation = Math.floor(Math.random() * 7) - 3; // -3 à +3
      const forecastTemp = temperature + tempVariation;
      
      // Condition avec une chance de changement
      const conditionIndex = Math.floor(Math.random() * conditions.length);
      const forecastCondition = conditions[conditionIndex];
      
      forecast.push({
        day: dayName,
        date: forecastDate.toLocaleDateString('fr-FR'),
        temp: forecastTemp,
        weather: forecastCondition,
        description: descriptions[forecastCondition]
      });
    }
    
    // Générer des alertes si la température est élevée ou basse
    const alerts = [];
    if (temperature > 25) {
      alerts.push({ 
        type: 'heat', 
        message: 'Températures élevées prévues pour les 3 prochains jours',
        impact: 'Assurez une ventilation adéquate des bâtiments d\'élevage'
      });
    } else if (temperature < 18) {
      alerts.push({ 
        type: 'cold', 
        message: 'Températures fraîches prévues pour les 3 prochains jours',
        impact: 'Vérifiez le chauffage des bâtiments d\'élevage'
      });
    }
    
    // Évaluer l'impact sur l'élevage
    let productionImpact = '';
    let healthImpact = '';
    
    if (condition === 'sunny' && temperature >= 20 && temperature <= 25) {
      productionImpact = 'Positif - Conditions idéales pour la croissance';
    } else if (condition === 'sunny' && temperature > 25) {
      productionImpact = 'Négatif - Risque de stress thermique';
      healthImpact = 'Attention - Surveillez l\'hydratation des volailles';
    } else if (condition === 'cloudy') {
      productionImpact = 'Neutre - Conditions normales';
      healthImpact = 'Normal - Aucune mesure particulière requise';
    } else if (condition === 'rainy' || condition === 'stormy') {
      productionImpact = 'Négatif - Risque d\'humidité excessive';
      healthImpact = 'Attention - Surveillez les conditions respiratoires';
    } else if (condition === 'snowy' || temperature < 15) {
      productionImpact = 'Négatif - Températures basses';
      healthImpact = 'Attention - Risque de stress thermique froid';
    }
    
    return {
      current: {
        temp: temperature,
        humidity: Math.floor(Math.random() * 30) + 50, // 50-80%
        wind_speed: Math.floor(Math.random() * 20) + 5, // 5-25 km/h
        weather: condition,
        description: descriptions[condition],
        location: city
      },
      forecast,
      alerts,
      impact: {
        production: productionImpact,
        health: healthImpact
      }
    };
  };
  
  // Fonction pour formater les données de l'API OpenWeatherMap
  const formatWeatherData = (currentData, forecastData) => {
    // Mapper les conditions météo de l'API aux types internes
    const mapWeatherCondition = (weatherId) => {
      if (weatherId >= 200 && weatherId < 300) return 'stormy'; // Orages
      if (weatherId >= 300 && weatherId < 600) return 'rainy';  // Pluie
      if (weatherId >= 600 && weatherId < 700) return 'snowy';  // Neige
      if (weatherId >= 700 && weatherId < 800) return 'cloudy'; // Brouillard, etc.
      if (weatherId === 800) return 'sunny';                    // Ciel dégagé
      if (weatherId > 800) return 'cloudy';                     // Nuageux
      return 'sunny'; // Par défaut
    };
    
    // Formater les prévisions
    const forecast = [];
    const today = new Date();
    const processedDates = new Set();
    
    // Traiter uniquement les prévisions pour les 3 prochains jours
    forecastData.list.forEach(item => {
      const forecastDate = new Date(item.dt * 1000);
      const dateString = forecastDate.toLocaleDateString('fr-FR');
      
      // Ignorer les prévisions pour aujourd'hui et ne prendre qu'une prévision par jour
      if (forecastDate.getDate() !== today.getDate() && !processedDates.has(dateString)) {
        processedDates.add(dateString);
        
        const dayNames = ['Demain', 'Après-demain'];
        const dayIndex = Math.floor((forecastDate - today) / (1000 * 60 * 60 * 24));
        const dayName = dayIndex < 2 ? dayNames[dayIndex] : forecastDate.toLocaleDateString('fr-FR', { weekday: 'long' });
        
        forecast.push({
          day: dayName,
          date: dateString,
          temp: Math.round(item.main.temp),
          weather: mapWeatherCondition(item.weather[0].id),
          description: item.weather[0].description
        });
        
        // Limiter à 3 jours
        if (forecast.length >= 3) return;
      }
    });
    
    // Déterminer les alertes et l'impact
    const alerts = [];
    let productionImpact = '';
    let healthImpact = '';
    
    const currentTemp = currentData.main.temp;
    const currentCondition = mapWeatherCondition(currentData.weather[0].id);
    
    if (currentTemp > 25) {
      alerts.push({ 
        type: 'heat', 
        message: 'Températures élevées prévues',
        impact: 'Assurez une ventilation adéquate des bâtiments d\'élevage'
      });
    } else if (currentTemp < 15) {
      alerts.push({ 
        type: 'cold', 
        message: 'Températures fraîches prévues',
        impact: 'Vérifiez le chauffage des bâtiments d\'élevage'
      });
    }
    
    if (currentCondition === 'sunny' && currentTemp >= 20 && currentTemp <= 25) {
      productionImpact = 'Positif - Conditions idéales pour la croissance';
    } else if (currentCondition === 'sunny' && currentTemp > 25) {
      productionImpact = 'Négatif - Risque de stress thermique';
      healthImpact = 'Attention - Surveillez l\'hydratation des volailles';
    } else if (currentCondition === 'cloudy') {
      productionImpact = 'Neutre - Conditions normales';
      healthImpact = 'Normal - Aucune mesure particulière requise';
    } else if (currentCondition === 'rainy' || currentCondition === 'stormy') {
      productionImpact = 'Négatif - Risque d\'humidité excessive';
      healthImpact = 'Attention - Surveillez les conditions respiratoires';
    } else if (currentCondition === 'snowy' || currentTemp < 15) {
      productionImpact = 'Négatif - Températures basses';
      healthImpact = 'Attention - Risque de stress thermique froid';
    }
    
    return {
      current: {
        temp: Math.round(currentData.main.temp),
        humidity: currentData.main.humidity,
        wind_speed: Math.round(currentData.wind.speed * 3.6), // Convertir m/s en km/h
        weather: currentCondition,
        description: currentData.weather[0].description,
        location: currentData.name
      },
      forecast,
      alerts,
      impact: {
        production: productionImpact,
        health: healthImpact
      }
    };
  };
  
  // Fonction pour rafraîchir manuellement les données
  const refreshWeather = async () => {
    setLoading(true);
    try {
      const mockData = generateMockWeatherData(location);
      setWeatherData(mockData);
      setError(null);
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des données météo:', err);
      setError('Impossible de rafraîchir les données météo');
    } finally {
      setLoading(false);
    }
  };
  
  return { weatherData, loading, error, refreshWeather };
};

export default useWeatherData;
