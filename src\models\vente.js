// const { Pool } = require('pg'); // Sequelize gère la connexion
// const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`;
// const pool = new Pool({
//   connectionString,
// });

const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Vente extends Model {
    static associate(models) {
      Vente.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      Vente.belongsTo(models.User, { // En supposant que l'acheteur est un User
        foreignKey: 'acheteur_id',
        as: 'acheteur'
      });
      Vente.belongsTo(models.Volaille, { // O<PERSON>, ou un modèle Produit plus générique
        foreignKey: 'volaille_id', // Ce champ devra peut-être être polymorphique ou plus générique
        as: 'produitVendu'
      });
    }

    // Les méthodes statiques CRUD de base (create, findAll, findById, update, delete)
    // sont généralement fournies par Sequelize (Vente.create(), Vente.findAll(), etc.).
    // Les méthodes personnalisées comme findByEleveur, findByAcheteur peuvent être conservées.

    static async findByEleveur(eleveurId) {
      return await this.findAll({
        where: { eleveur_id: eleveurId },
        order: [['date_vente', 'DESC']]
      });
    }

    static async findByAcheteur(acheteurId) {
      return await this.findAll({
        where: { acheteur_id: acheteurId },
        order: [['date_vente', 'DESC']]
      });
    }
  }

  Vente.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false, // Rendre false si une vente doit toujours avoir un éleveur
      references: {
        model: 'eleveurs', // Nom de la table Eleveurs
        key: 'id'
      }
    },
    acheteur_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // Peut-être qu'une vente peut être anonyme ou l'acheteur non enregistré
      references: {
        model: 'users',    // Nom de la table Users
        key: 'id'
      }
    },
    volaille_id: { // Ce champ pourrait être renommé en `produit_id` et être associé à une table `produits` plus générique
      type: DataTypes.INTEGER,
      allowNull: true, // Si on vend autre chose que des volailles enregistrées
      references: {
        model: 'volailles', // Nom de la table Volailles
        key: 'id'
      }
    },
    quantite: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    prix_unitaire: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    montant_total: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    date_vente: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    statut: {
      type: DataTypes.STRING(20),
      defaultValue: 'completee' // ex: 'en_cours', 'completee', 'annulee'
    },
    notes: {
      type: DataTypes.TEXT
    }
  }, {
    sequelize,
    modelName: 'Vente',
    tableName: 'ventes',
    timestamps: true, // Sequelize gère createdAt et updatedAt
    // Si vous voulez que `date_vente` soit `createdAt`, configurez `createdAt: 'date_vente'`
    // et assurez-vous que `defaultValue` pour `date_vente` est géré correctement ou supprimé.
    hooks: {
        beforeValidate: (vente, options) => {
            if (vente.quantite && vente.prix_unitaire) {
                vente.montant_total = vente.quantite * vente.prix_unitaire;
            }
        }
    }
  });

  return Vente;
};

/*
// Code original utilisant pg directement, commenté.
class Vente {
  static async createTable() { ... }
  static async create(venteData) { ... }
  static async findAll() { ... }
  static async findById(id) { ... }
  static async findByEleveur(eleveurId) { ... }
  static async findByAcheteur(acheteurId) { ... }
  static async update(id, venteData) { ... }
  static async delete(id) { ... }
}
module.exports = Vente;
*/
