const express = require('express');
const router = express.Router();
const { auth, checkRole } = require('../middleware/auth');
const {
  getDisponibilites,
  ajouterDisponibilite,
  updateDisponibilite,
  supprimerDisponibilite
} = require('../controllers/disponibiliteController');

// Middleware pour vérifier si l'utilisateur est un vétérinaire
const isVeterinaire = async (req, res, next) => {
  try {
    if (req.user.role !== 'veterinaire') {
      return res.status(403).json({ message: 'Accès refusé. Rôle vétérinaire requis.' });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isVeterinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Routes pour la gestion des disponibilités
router.get('/:id', auth, getDisponibilites); // Accessible aux éleveurs pour consultation
router.get('/', auth, isVeterinaire, getDisponibilites); // Pour le vétérinaire connecté
router.post('/', auth, isVeterinaire, ajouterDisponibilite);
router.put('/:id', auth, isVeterinaire, updateDisponibilite);
router.delete('/:id', auth, isVeterinaire, supprimerDisponibilite);

module.exports = router;