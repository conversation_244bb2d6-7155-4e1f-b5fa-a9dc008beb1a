const sequelize = require('../src/config/database');
const { QueryTypes } = require('sequelize');

async function testAlertesStockTable() {
  try {
    console.log('Testing alertes_stock table...');

    // Check if table exists
    const tableCheck = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'alertes_stock'
      );
    `, { type: QueryTypes.SELECT });

    const tableExists = tableCheck[0].exists;
    console.log(`✅ Table 'alertes_stock' exists: ${tableExists}`);

    if (tableExists) {
      // Get table structure
      const tableStructure = await sequelize.query(`
        SELECT column_name, data_type, character_maximum_length, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'alertes_stock'
        ORDER BY ordinal_position;
      `, { type: QueryTypes.SELECT });

      console.log('✅ Table structure:');
      console.table(tableStructure);

      // Get sample data
      const sampleData = await sequelize.query(`
        SELECT * FROM alertes_stock LIMIT 1;
      `, { type: QueryTypes.SELECT });

      console.log('Sample data:');
      if (sampleData.length > 0) {
        console.log(JSON.stringify(sampleData[0], null, 2));
      } else {
        console.log('No data found in the table.');
      }
    } else {
      console.log('❌ Table does not exist. This will cause errors in the veterinaire dashboard and notifications endpoints.');
    }

  } catch (error) {
    console.error('Error testing alertes_stock table:', error);
  } finally {
    await sequelize.close();
  }
}

testAlertesStockTable();
