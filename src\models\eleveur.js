const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Eleveur extends Model {
    static associate(models) {
      // Définir les associations ici si Eleveur est lié à d'autres modèles
      // Par exemple, si un Eleveur peut avoir plusieurs Volailles:
      // Eleveur.hasMany(models.Volaille, { foreignKey: 'eleveur_id', as: 'volailles' });
      // Eleveur.hasMany(models.Poussin, { foreignKey: 'eleveur_id', as: 'poussins' });
      // Eleveur.hasMany(models.ProductionOeufs, { foreignKey: 'eleveur_id', as: 'productionsOeufs' });
      // Eleveur.hasMany(models.AlerteStock, { foreignKey: 'eleveur_id', as: 'alertesStock' });
      // Eleveur.hasMany(models.SuiviVeterinaire, { foreignKey: 'eleveur_id', as: 'suivisVeterinaires' });

      // Si Eleveur est un type de User (profil)
      // Eleveur.belongsTo(models.User, { foreignKey: 'user_id', as: 'userAccount' });
    }

    static async findByEmail(email) {
      return await this.findOne({ where: { email } });
    }

    static async updateProfile(id, updateData) {
      return await this.update(updateData, { where: { id } });
    }

    static async deleteEleveur(id) {
      return await this.destroy({ where: { id } });
    }
  }

  Eleveur.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    // user_id: { // Si Eleveur est un profil lié à User
    //   type: DataTypes.INTEGER,
    //   allowNull: false,
    //   references: {
    //     model: 'users',
    //     key: 'id'
    //   },
    //   unique: true
    // },
    nom: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    prenom: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    telephone: {
      type: DataTypes.STRING(20)
    },
    adresse: {
      type: DataTypes.TEXT
    },
    date_inscription: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    },
    // date_modification est géré par timestamps: true (updatedAt)
    statut: {
      type: DataTypes.STRING(20),
      defaultValue: 'actif'
    }
  }, {
    sequelize, // Passer l'instance de sequelize
    modelName: 'Eleveur',
    tableName: 'eleveurs',
    timestamps: true, // Sequelize gère createdAt et updatedAt
    underscored: true,
    // createdAt: 'date_inscription', // Si vous voulez mapper createdAt à date_inscription
    // updatedAt: 'date_modification', // Si vous voulez mapper updatedAt à date_modification
    indexes: [{
      unique: true,
      fields: ['email']
    }]
  });

  return Eleveur;
};
