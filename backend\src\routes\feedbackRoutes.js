/**
 * Routes pour la gestion des feedbacks et enquêtes
 */

const express = require('express');
const router = express.Router();
const { body, query, param } = require('express-validator');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const rateLimit = require('../middleware/rateLimit');
const feedbackService = require('../services/feedbackService');
const logger = require('../utils/logger');

// Rate limiting pour les feedbacks
const feedbackLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 feedbacks par fenêtre
  message: 'Trop de feedbacks soumis, veuillez réessayer plus tard'
});

/**
 * @swagger
 * /api/feedback/surveys/active:
 *   get:
 *     summary: Obtenir les enquêtes actives pour l'utilisateur
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: context
 *         schema:
 *           type: object
 *         description: Contexte de l'utilisateur (page actuelle, etc.)
 *     responses:
 *       200:
 *         description: Liste des enquêtes actives
 */
router.get('/surveys/active', 
  auth,
  validate([
    query('context').optional().isJSON()
  ]),
  async (req, res) => {
    try {
      const context = req.query.context ? JSON.parse(req.query.context) : {};
      
      const surveys = await feedbackService.getActiveSurveysForUser(
        req.user.id,
        req.user.role,
        context
      );

      res.json({
        success: true,
        data: surveys
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération des enquêtes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des enquêtes'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/surveys/{surveyId}/respond:
 *   post:
 *     summary: Soumettre une réponse à une enquête
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: surveyId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               answers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     questionId:
 *                       type: string
 *                     value:
 *                       type: any
 *                     text:
 *                       type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Réponse soumise avec succès
 */
router.post('/surveys/:surveyId/respond',
  auth,
  feedbackLimiter,
  validate([
    param('surveyId').isUUID(),
    body('answers').isArray().notEmpty(),
    body('answers.*.questionId').isUUID(),
    body('answers.*.value').exists(),
    body('metadata').optional().isObject()
  ]),
  async (req, res) => {
    try {
      const response = await feedbackService.submitSurveyResponse(
        req.user.id,
        req.params.surveyId,
        req.body
      );

      res.status(201).json({
        success: true,
        data: response,
        message: 'Réponse soumise avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la soumission de la réponse:', error);
      
      if (error.message.includes('déjà répondu')) {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Erreur lors de la soumission de la réponse'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/quick:
 *   post:
 *     summary: Soumettre un feedback rapide
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [bug, feature, improvement, complaint, praise]
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               comment:
 *                 type: string
 *               pageUrl:
 *                 type: string
 *               featureName:
 *                 type: string
 *               category:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Feedback créé avec succès
 */
router.post('/quick',
  auth,
  feedbackLimiter,
  validate([
    body('type').isIn(['bug', 'feature', 'improvement', 'complaint', 'praise']),
    body('rating').optional().isInt({ min: 1, max: 5 }),
    body('comment').optional().isString().isLength({ max: 2000 }),
    body('pageUrl').optional().isURL(),
    body('featureName').optional().isString().isLength({ max: 255 }),
    body('category').optional().isString().isLength({ max: 100 }),
    body('metadata').optional().isObject()
  ]),
  async (req, res) => {
    try {
      const feedback = await feedbackService.createQuickFeedback(
        req.user.id,
        req.body
      );

      res.status(201).json({
        success: true,
        data: feedback,
        message: 'Feedback soumis avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la création du feedback:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la soumission du feedback'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/analytics/surveys/{surveyId}:
 *   get:
 *     summary: Obtenir les analytics d'une enquête
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: surveyId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Analytics de l'enquête
 */
router.get('/analytics/surveys/:surveyId',
  auth,
  validate([
    param('surveyId').isUUID()
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions (admin ou créateur de l'enquête)
      if (req.user.role !== 'admin') {
        // TODO: Vérifier si l'utilisateur est le créateur de l'enquête
      }

      const analytics = await feedbackService.getSurveyAnalytics(req.params.surveyId);

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération des analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des analytics'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/analytics/categories:
 *   get:
 *     summary: Obtenir les feedbacks par catégorie
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Feedbacks par catégorie
 */
router.get('/analytics/categories',
  auth,
  validate([
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('category').optional().isString()
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'manager') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const analytics = await feedbackService.getFeedbackByCategory(req.query);

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération des analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des analytics'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/export:
 *   get:
 *     summary: Exporter les données de feedback
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, json]
 *           default: csv
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Données exportées
 */
router.get('/export',
  auth,
  validate([
    query('format').optional().isIn(['csv', 'json']),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const format = req.query.format || 'csv';
      const data = await feedbackService.exportFeedbackData(format, req.query);

      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=feedback_export.csv');
        res.send(data);
      } else {
        res.json({
          success: true,
          data: data
        });
      }
    } catch (error) {
      logger.error('Erreur lors de l\'export:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'export des données'
      });
    }
  }
);

/**
 * @swagger
 * /api/feedback/surveys:
 *   post:
 *     summary: Créer une nouvelle enquête (admin seulement)
 *     tags: [Feedback]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [feedback, nps, satisfaction, feature_request, bug_report]
 *               targetAudience:
 *                 type: object
 *               questions:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       201:
 *         description: Enquête créée avec succès
 */
router.post('/surveys',
  auth,
  validate([
    body('title').isString().isLength({ min: 1, max: 255 }),
    body('description').optional().isString(),
    body('type').isIn(['feedback', 'nps', 'satisfaction', 'feature_request', 'bug_report']),
    body('targetAudience').optional().isObject(),
    body('questions').isArray().notEmpty()
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const survey = await feedbackService.createSurvey({
        ...req.body,
        createdBy: req.user.id
      });

      res.status(201).json({
        success: true,
        data: survey,
        message: 'Enquête créée avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la création de l\'enquête:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de l\'enquête'
      });
    }
  }
);

module.exports = router;
