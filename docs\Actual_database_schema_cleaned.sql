-- Adminer 4.8.4 PostgreSQL 17.4 dump

DROP TABLE IF EXISTS "ApiConfigs";
DROP SEQUENCE IF EXISTS "ApiConfigs_id_seq";
CREATE SEQUENCE "ApiConfigs_id_seq" INCREMENT  MINVALUE  MAXVALUE  CACHE ;

CREATE TABLE "public"."ApiConfigs" (
    "id" integer DEFAULT nextval("ApiConfigs_id_seq") NOT NULL,
    "serviceName" character varying(255) NOT NULL,
    "apiKey" text NOT NULL,
    "apiSecret" text,
    "createdAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "description" text,
    "isEnabled" boolean DEFAULT true,
    CONSTRAINT "ApiConfigs_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "ApiConfigs_serviceName_key" UNIQUE ("serviceName")
) WITH (oids = false);


DROP TABLE IF EXISTS "SequelizeMeta";
CREATE TABLE "public"."SequelizeMeta" (
    "name" character varying(255) NOT NULL,
    CONSTRAINT "SequelizeMeta_pkey" PRIMARY KEY ("name")
) WITH (oids = false);


DROP TABLE IF EXISTS "Users";
DROP SEQUENCE IF EXISTS "Users_id_seq";
CREATE SEQUENCE "Users_id_seq" INCREMENT  MINVALUE  MAXVALUE  CACHE ;

CREATE TABLE "public"."Users" (
    "id" integer DEFAULT nextval("Users_id_seq") NOT NULL,
    "username" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "password" character varying(255) NOT NULL,
    "role" character varying(20) DEFAULT 'eleveur' NOT NULL,
    "first_name" character varying(100),
    "last_name" character varying(100),
    "profile_id" integer,
    "status" character varying(20) DEFAULT 'active',
    "preferences" jsonb DEFAULT '{}',
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    "role_id" integer,
    "subscription_plan_id" integer,
    "firebase_uid" character varying(255),
    "phone" character varying(50),
    "address" text,
    CONSTRAINT "Users_email_key" UNIQUE ("email"),
    CONSTRAINT "Users_email_key1" UNIQUE ("email"),
    CONSTRAINT "Users_email_key10" UNIQUE ("email"),
    CONSTRAINT "Users_email_key11" UNIQUE ("email"),
    CONSTRAINT "Users_email_key12" UNIQUE ("email"),
    CONSTRAINT "Users_email_key2" UNIQUE ("email"),
    CONSTRAINT "Users_email_key3" UNIQUE ("email"),
    CONSTRAINT "Users_email_key4" UNIQUE ("email"),
    CONSTRAINT "Users_email_key5" UNIQUE ("email"),
    CONSTRAINT "Users_email_key6" UNIQUE ("email"),
    CONSTRAINT "Users_email_key7" UNIQUE ("email"),
    CONSTRAINT "Users_email_key8" UNIQUE ("email"),
    CONSTRAINT "Users_email_key9" UNIQUE ("email"),
    CONSTRAINT "Users_firebase_uid_key" UNIQUE ("firebase_uid"),
    CONSTRAINT "Users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "Users_username_key" UNIQUE ("username"),
    CONSTRAINT "Users_username_key1" UNIQUE ("username"),
    CONSTRAINT "Users_username_key10" UNIQUE ("username"),
    CONSTRAINT "Users_username_key11" UNIQUE ("username"),
    CONSTRAINT "Users_username_key12" UNIQUE ("username"),
    CONSTRAINT "Users_username_key2" UNIQUE ("username"),
    CONSTRAINT "Users_username_key3" UNIQUE ("username"),
    CONSTRAINT "Users_username_key4" UNIQUE ("username"),
    CONSTRAINT "Users_username_key5" UNIQUE ("username"),
    CONSTRAINT "Users_username_key6" UNIQUE ("username"),
    CONSTRAINT "Users_username_key7" UNIQUE ("username"),
    CONSTRAINT "Users_username_key8" UNIQUE ("username"),
    CONSTRAINT "Users_username_key9" UNIQUE ("username")
) WITH (oids = false);


DROP TABLE IF EXISTS "alertes_stock";
DROP SEQUENCE IF EXISTS alertes_stock_id_seq;
CREATE SEQUENCE alertes_stock_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."alertes_stock" (
    "id" integer DEFAULT nextval('alertes_stock_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "type_alerte" enum_alertes_stock_type_alerte NOT NULL,
    "priorite" enum_alertes_stock_priorite DEFAULT 'normale',
    "statut" enum_alertes_stock_statut DEFAULT 'active',
    "titre" character varying(200) NOT NULL,
    "message" text NOT NULL,
    "message_ar" text,
    "donnees_contexte" jsonb DEFAULT '{}',
    "seuil_declenche" jsonb DEFAULT '{"unite": null, "operateur": null, "valeur_seuil": null, "valeur_actuelle": null}',
    "source_donnees" jsonb DEFAULT '{"table_source": null, "champ_surveille": null, "derniere_valeur": null, "id_enregistrement": null}',
    "actions_recommandees" jsonb DEFAULT '[]',
    "actions_entreprises" jsonb DEFAULT '[]',
    "date_declenchement" timestamptz NOT NULL,
    "date_vue" timestamptz,
    "date_traitee" timestamptz,
    "date_expiration" timestamptz,
    "frequence_rappel" integer,
    "nombre_rappels" integer DEFAULT '0',
    "dernier_rappel" timestamptz,
    "canaux_notification" jsonb DEFAULT '{"sms": false, "push": false, "email": false, "whatsapp": false, "dashboard": true}',
    "notifications_envoyees" jsonb DEFAULT '[]',
    "impact_estime" jsonb DEFAULT '{"financier": null, "production": null, "duree_estimee": null, "sante_animaux": null}',
    "cout_inaction" numeric(12,2),
    "cout_resolution" numeric(12,2),
    "automatique" boolean DEFAULT true,
    "recurrente" boolean DEFAULT false,
    "conditions_resolution" jsonb DEFAULT '{}',
    "liens_utiles" jsonb DEFAULT '[]',
    "contacts_urgence" jsonb DEFAULT '[]',
    "historique_similaires" jsonb DEFAULT '[]',
    "feedback_eleveur" jsonb DEFAULT '{"timing": null, "utilite": null, "precision": null, "suggestions": null}',
    "tags" jsonb DEFAULT '[]',
    "visible" boolean DEFAULT true,
    "archivee" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "alertes_stock_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "alertes_stock_archivee" ON "public"."alertes_stock" USING btree ("archivee");

CREATE INDEX "alertes_stock_automatique" ON "public"."alertes_stock" USING btree ("automatique");

CREATE INDEX "alertes_stock_date_declenchement" ON "public"."alertes_stock" USING btree ("date_declenchement");

CREATE INDEX "alertes_stock_eleveur_id" ON "public"."alertes_stock" USING btree ("eleveur_id");

CREATE INDEX "alertes_stock_eleveur_id_statut_visible" ON "public"."alertes_stock" USING btree ("eleveur_id", "statut", "visible");

CREATE INDEX "alertes_stock_priorite" ON "public"."alertes_stock" USING btree ("priorite");

CREATE INDEX "alertes_stock_statut" ON "public"."alertes_stock" USING btree ("statut");

CREATE INDEX "alertes_stock_type_alerte" ON "public"."alertes_stock" USING btree ("type_alerte");

CREATE INDEX "alertes_stock_visible" ON "public"."alertes_stock" USING btree ("visible");

COMMENT ON COLUMN "public"."alertes_stock"."eleveur_id" IS 'Référence vers l''éleveur';

COMMENT ON COLUMN "public"."alertes_stock"."type_alerte" IS 'Type d''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."priorite" IS 'Niveau de priorité';

COMMENT ON COLUMN "public"."alertes_stock"."statut" IS 'Statut de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."titre" IS 'Titre de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."message" IS 'Message détaillé de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."message_ar" IS 'Message en arabe';

COMMENT ON COLUMN "public"."alertes_stock"."donnees_contexte" IS 'Données contextuelles de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."seuil_declenche" IS 'Informations sur le seuil qui a déclenché l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."source_donnees" IS 'Source des données qui ont déclenché l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."actions_recommandees" IS 'Actions recommandées pour résoudre l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."actions_entreprises" IS 'Actions déjà entreprises par l''éleveur';

COMMENT ON COLUMN "public"."alertes_stock"."date_declenchement" IS 'Date de déclenchement de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."date_vue" IS 'Date à laquelle l''alerte a été vue';

COMMENT ON COLUMN "public"."alertes_stock"."date_traitee" IS 'Date de traitement de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."date_expiration" IS 'Date d''expiration de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."frequence_rappel" IS 'Fréquence de rappel en heures';

COMMENT ON COLUMN "public"."alertes_stock"."nombre_rappels" IS 'Nombre de rappels envoyés';

COMMENT ON COLUMN "public"."alertes_stock"."dernier_rappel" IS 'Date du dernier rappel';

COMMENT ON COLUMN "public"."alertes_stock"."canaux_notification" IS 'Canaux de notification activés';

COMMENT ON COLUMN "public"."alertes_stock"."notifications_envoyees" IS 'Historique des notifications envoyées';

COMMENT ON COLUMN "public"."alertes_stock"."impact_estime" IS 'Impact estimé de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."cout_inaction" IS 'Coût estimé de l''inaction en DA';

COMMENT ON COLUMN "public"."alertes_stock"."cout_resolution" IS 'Coût estimé de la résolution en DA';

COMMENT ON COLUMN "public"."alertes_stock"."automatique" IS 'Alerte générée automatiquement';

COMMENT ON COLUMN "public"."alertes_stock"."recurrente" IS 'Alerte récurrente';

COMMENT ON COLUMN "public"."alertes_stock"."conditions_resolution" IS 'Conditions pour considérer l''alerte comme résolue';

COMMENT ON COLUMN "public"."alertes_stock"."liens_utiles" IS 'Liens vers des ressources utiles';

COMMENT ON COLUMN "public"."alertes_stock"."contacts_urgence" IS 'Contacts d''urgence pour ce type d''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."historique_similaires" IS 'Références vers des alertes similaires passées';

COMMENT ON COLUMN "public"."alertes_stock"."feedback_eleveur" IS 'Feedback de l''éleveur sur l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."tags" IS 'Tags pour catégoriser l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."visible" IS 'Alerte visible dans le dashboard';

COMMENT ON COLUMN "public"."alertes_stock"."archivee" IS 'Alerte archivée';


DROP TABLE IF EXISTS "annonces";
DROP SEQUENCE IF EXISTS annonces_id_seq;
CREATE SEQUENCE annonces_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."annonces" (
    "id" integer DEFAULT nextval('annonces_id_seq') NOT NULL,
    "titre" character varying(255) NOT NULL,
    "description" text,
    "categorie" character varying(100) NOT NULL,
    "prix" numeric(10,2) NOT NULL,
    "localisation" character varying(255),
    "images" text[] DEFAULT ARRAY[],
    "est_active" boolean DEFAULT true,
    "utilisateur_id" integer NOT NULL,
    "date_creation" timestamptz,
    "date_mise_a_jour" timestamptz,
    CONSTRAINT "annonces_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "blog_posts";
DROP SEQUENCE IF EXISTS blog_posts_id_seq;
CREATE SEQUENCE blog_posts_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."blog_posts" (
    "id" integer DEFAULT nextval('blog_posts_id_seq') NOT NULL,
    "title" character varying(200) NOT NULL,
    "slug" character varying(200) NOT NULL,
    "content" text NOT NULL,
    "excerpt" text,
    "author_id" integer,
    "status" character varying(20) DEFAULT 'draft',
    "tags" jsonb DEFAULT '[]',
    "featured_image" character varying(255),
    "published_at" timestamptz,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "blog_posts_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "blog_posts_slug_key" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key1" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key10" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key11" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key12" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key2" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key3" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key4" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key5" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key6" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key7" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key8" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key9" UNIQUE ("slug")
) WITH (oids = false);


DROP TABLE IF EXISTS "clients";
DROP SEQUENCE IF EXISTS clients_id_seq;
CREATE SEQUENCE clients_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."clients" (
    "id" integer DEFAULT nextval('clients_id_seq') NOT NULL,
    "marchand_id" integer,
    "user_id" integer,
    "name" character varying(100) NOT NULL,
    "email" character varying(255),
    "phone" character varying(20),
    "address" text,
    "notes" text,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "clients_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_clients_marchand_id" ON "public"."clients" USING btree ("marchand_id");

COMMENT ON TABLE "public"."clients" IS 'Clients des marchands';


CREATE TRIGGER "update_clients_modtime" BEFORE UPDATE ON "public"."clients" FOR EACH ROW EXECUTE FUNCTION update_modified_column();

DROP TABLE IF EXISTS "consultations";
DROP SEQUENCE IF EXISTS consultations_id_seq;
CREATE SEQUENCE consultations_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."consultations" (
    "id" integer DEFAULT nextval('consultations_id_seq') NOT NULL,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "diagnostic" text,
    "notes" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "volaille_id" integer NOT NULL,
    "date" timestamptz,
    "symptomes" text,
    "traitement" text,
    "statut" character varying(20) DEFAULT 'en_cours',
    "cout" numeric(10,2),
    CONSTRAINT "consultations_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_consultations_eleveur_id" ON "public"."consultations" USING btree ("eleveur_id");

CREATE INDEX "idx_consultations_veterinaire_id" ON "public"."consultations" USING btree ("veterinaire_id");

COMMENT ON TABLE "public"."consultations" IS 'Consultations vétérinaires';


DROP TABLE IF EXISTS "eleveurs";
DROP SEQUENCE IF EXISTS eleveurs_id_seq;
CREATE SEQUENCE eleveurs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."eleveurs" (
    "id" integer DEFAULT nextval('eleveurs_id_seq') NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "telephone" character varying(20),
    "adresse" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "date_modification" timestamptz NOT NULL,
    "user_id" integer,
    CONSTRAINT "eleveurs_email" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key1" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key10" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key11" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key12" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key13" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key14" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key15" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key16" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key17" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key2" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key3" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key4" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key5" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key6" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key7" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key8" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key9" UNIQUE ("email"),
    CONSTRAINT "eleveurs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "favoris";
DROP SEQUENCE IF EXISTS favoris_id_seq;
CREATE SEQUENCE favoris_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."favoris" (
    "id" integer DEFAULT nextval('favoris_id_seq') NOT NULL,
    "utilisateur_id" integer NOT NULL,
    "annonce_id" integer NOT NULL,
    "date_ajout" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "favoris_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_favoris_annonce_id" ON "public"."favoris" USING btree ("annonce_id");

CREATE INDEX "idx_favoris_utilisateur_id" ON "public"."favoris" USING btree ("utilisateur_id");

COMMENT ON TABLE "public"."favoris" IS 'Annonces favorites des utilisateurs';


DROP TABLE IF EXISTS "feed_consumption_logs";
DROP SEQUENCE IF EXISTS feed_consumption_logs_id_seq;
CREATE SEQUENCE feed_consumption_logs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."feed_consumption_logs" (
    "id" integer DEFAULT nextval('feed_consumption_logs_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "feed_plan_id" integer,
    "date" date NOT NULL,
    "quantite_distribuee" numeric(10,2) NOT NULL,
    "quantite_consommee" numeric(10,2),
    "gaspillage" numeric(10,2),
    "observations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "feed_consumption_logs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_feed_consumption_logs_eleveur_id" ON "public"."feed_consumption_logs" USING btree ("eleveur_id");

CREATE INDEX "idx_feed_consumption_logs_feed_plan_id" ON "public"."feed_consumption_logs" USING btree ("feed_plan_id");

COMMENT ON TABLE "public"."feed_consumption_logs" IS 'Logs de consommation d''aliments';


DROP TABLE IF EXISTS "feed_plans";
DROP SEQUENCE IF EXISTS feed_plans_id_seq;
CREATE SEQUENCE feed_plans_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."feed_plans" (
    "id" integer DEFAULT nextval('feed_plans_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "nom_plan" character varying(200) NOT NULL,
    "type_aliment" character varying(100) NOT NULL,
    "quantite_journaliere" numeric(10,2) NOT NULL,
    "heure_distribution" time,
    "batiment_cible" character varying(100),
    "date_debut" date NOT NULL,
    "date_fin" date,
    "est_actif" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "feed_plans_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_feed_plans_eleveur_id" ON "public"."feed_plans" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."feed_plans" IS 'Plans d''alimentation pour les volailles';


DROP TABLE IF EXISTS "marchands";
DROP SEQUENCE IF EXISTS marchands_id_seq;
CREATE SEQUENCE marchands_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."marchands" (
    "id" integer DEFAULT nextval('marchands_id_seq') NOT NULL,
    "nom_entreprise" character varying(200) NOT NULL,
    "contact_nom" character varying(100),
    "contact_prenom" character varying(100),
    "email" character varying(255) NOT NULL,
    "telephone" character varying(20),
    "adresse" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "user_id" integer,
    CONSTRAINT "marchands_email" UNIQUE ("email"),
    CONSTRAINT "marchands_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "notifications";
DROP SEQUENCE IF EXISTS notifications_id_seq;
CREATE SEQUENCE notifications_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."notifications" (
    "id" integer DEFAULT nextval('notifications_id_seq') NOT NULL,
    "user_id" integer NOT NULL,
    "type" character varying(50) NOT NULL,
    "message" text NOT NULL,
    "is_read" boolean DEFAULT false,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_notifications_user_id" ON "public"."notifications" USING btree ("user_id");

COMMENT ON TABLE "public"."notifications" IS 'Notifications pour les utilisateurs';


DROP TABLE IF EXISTS "ouvriers";
DROP SEQUENCE IF EXISTS ouvriers_id_seq;
CREATE SEQUENCE ouvriers_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."ouvriers" (
    "id" integer DEFAULT nextval('ouvriers_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "role" character varying(100),
    "telephone" character varying(20),
    "date_embauche" date,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "ouvriers_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_ouvriers_eleveur_id" ON "public"."ouvriers" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."ouvriers" IS 'Ouvriers travaillant pour les éleveurs';


DROP TABLE IF EXISTS "prescriptions";
DROP SEQUENCE IF EXISTS prescriptions_id_seq;
CREATE SEQUENCE prescriptions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."prescriptions" (
    "id" integer DEFAULT nextval('prescriptions_id_seq') NOT NULL,
    "consultation_id" integer NOT NULL,
    "medicament" character varying(200) NOT NULL,
    "dosage" character varying(100),
    "frequence" character varying(100),
    "duree" character varying(100),
    "notes" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "prescriptions_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_prescriptions_consultation_id" ON "public"."prescriptions" USING btree ("consultation_id");

COMMENT ON TABLE "public"."prescriptions" IS 'Prescriptions vétérinaires';


DROP TABLE IF EXISTS "produits";
DROP SEQUENCE IF EXISTS produits_id_seq;
CREATE SEQUENCE produits_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."produits" (
    "id" integer DEFAULT nextval('produits_id_seq') NOT NULL,
    "marchand_id" integer NOT NULL,
    "nom" character varying(200) NOT NULL,
    "description" text,
    "categorie" character varying(100),
    "prix" numeric(10,2) NOT NULL,
    "quantite_stock" integer NOT NULL,
    "unite_mesure" character varying(50),
    "images" text[] DEFAULT ARRAY[],
    "est_disponible" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "produits_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_produits_marchand_id" ON "public"."produits" USING btree ("marchand_id");

COMMENT ON TABLE "public"."produits" IS 'Produits vendus par les marchands';


DROP TABLE IF EXISTS "production_oeufs";
DROP SEQUENCE IF EXISTS production_oeufs_id_seq;
CREATE SEQUENCE production_oeufs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."production_oeufs" (
    "id" integer DEFAULT nextval('production_oeufs_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "date" date NOT NULL,
    "nombre_oeufs" integer NOT NULL,
    "nombre_poules_pondeuses" integer,
    "taux_ponte" numeric(5,2),
    "observations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "production_oeufs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_production_oeufs_eleveur_id" ON "public"."production_oeufs" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."production_oeufs" IS 'Enregistrements de production d''œufs';


DROP TABLE IF EXISTS "poussins";
DROP SEQUENCE IF EXISTS poussins_id_seq;
CREATE SEQUENCE poussins_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."poussins" (
    "id" integer DEFAULT nextval('poussins_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "date_arrivee" date NOT NULL,
    "nombre_initial" integer NOT NULL,
    "race" character varying(100),
    "fournisseur" character varying(200),
    "mortalite_journaliere" integer DEFAULT 0,
    "poids_moyen_journalier" numeric(10,2),
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "poussins_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_poussins_eleveur_id" ON "public"."poussins" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."poussins" IS 'Suivi des poussins';


DROP TABLE IF EXISTS "roles";
DROP SEQUENCE IF EXISTS roles_id_seq;
CREATE SEQUENCE roles_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."roles" (
    "id" integer DEFAULT nextval('roles_id_seq') NOT NULL,
    "name" character varying(50) NOT NULL,
    "description" text,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "roles_name_key" UNIQUE ("name"),
    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "suivi_veterinaire";
DROP SEQUENCE IF EXISTS suivi_veterinaire_id_seq;
CREATE SEQUENCE suivi_veterinaire_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."suivi_veterinaire" (
    "id" integer DEFAULT nextval('suivi_veterinaire_id_seq') NOT NULL,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "date_visite" date NOT NULL,
    "observations" text,
    "recommandations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "suivi_veterinaire_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_suivi_veterinaire_eleveur_id" ON "public"."suivi_veterinaire" USING btree ("eleveur_id");

CREATE INDEX "idx_suivi_veterinaire_veterinaire_id" ON "public"."suivi_veterinaire" USING btree ("veterinaire_id");

COMMENT ON TABLE "public"."suivi_veterinaire" IS 'Suivi des visites vétérinaires';


DROP TABLE IF EXISTS "volailles";
DROP SEQUENCE IF EXISTS volailles_id_seq;
CREATE SEQUENCE volailles_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."volailles" (
    "id" integer DEFAULT nextval('volailles_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "type_volaille" character varying(100) NOT NULL,
    "race" character varying(100),
    "date_naissance" date,
    "sexe" character varying(10),
    "statut_sante" character varying(50) DEFAULT 'sain',
    "poids_actuel" numeric(10,2),
    "date_acquisition" date,
    "source_acquisition" character varying(200),
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "volailles_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "idx_volailles_eleveur_id" ON "public"."volailles" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."volailles" IS 'Informations sur les volailles';


DROP TABLE IF EXISTS "veterinaires";
DROP SEQUENCE IF EXISTS veterinaires_id_seq;
CREATE SEQUENCE veterinaires_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."veterinaires" (
    "id" integer DEFAULT nextval('veterinaires_id_seq') NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "telephone" character varying(20),
    "adresse_cabinet" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "user_id" integer,
    CONSTRAINT "veterinaires_email" UNIQUE ("email"),
    CONSTRAINT "veterinaires_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


-- 2024-06-15 18:17:55.413 +00


