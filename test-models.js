// Simple test script to check if models load correctly
console.log('Starting model test...');

try {
  console.log('Loading models...');
  const models = require('./src/models/index.js');
  console.log('Models loaded successfully!');
  console.log('Available models:', Object.keys(models));
} catch (error) {
  console.error('Error loading models:', error.message);
  console.error('Stack trace:', error.stack);
}
