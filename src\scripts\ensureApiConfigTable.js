/**
 * <PERSON>ript pour s'assurer que la table ApiConfig existe avec la structure correcte
 */
const db = require('../models');

async function ensureApiConfigTable() {
  try {
    console.log('Ensuring ApiConfig table exists with correct structure...');
    const ApiConfig = db.ApiConfig;
    const sequelize = db.sequelize;

    if (!ApiConfig) {
      console.error('ApiConfig model not found!');
      return;
    }

    // First check if the table exists
    const tableExists = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ApiConfigs'
      );
    `, { type: sequelize.QueryTypes.SELECT });

    const exists = tableExists[0].exists;

    if (!exists) {
      console.log('ApiConfig table does not exist. Creating...');
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS "ApiConfigs" (
          id SERIAL PRIMARY KEY,
          "serviceName" VARCHAR(255) NOT NULL UNIQUE,
          "apiKey" TEXT,
          "apiSecret" TEXT,
          "description" TEXT,
          "isEnabled" BOOLEAN DEFAULT true,
          "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('ApiConfig table created.');
    } else {
      // Check if columns need to be altered
      const hasApiKeyText = await sequelize.query(`
        SELECT data_type FROM information_schema.columns
        WHERE table_name = 'ApiConfigs' AND column_name = 'apiKey';
      `, { type: sequelize.QueryTypes.SELECT });

      if (hasApiKeyText.length > 0 && hasApiKeyText[0].data_type !== 'text') {
        console.log('Altering apiKey column to TEXT type...');
        await sequelize.query(`
          ALTER TABLE "ApiConfigs"
          ALTER COLUMN "apiKey" TYPE TEXT;
        `);
        console.log('apiKey column altered.');
      }

      const hasApiSecretText = await sequelize.query(`
        SELECT data_type FROM information_schema.columns
        WHERE table_name = 'ApiConfigs' AND column_name = 'apiSecret';
      `, { type: sequelize.QueryTypes.SELECT });

      if (hasApiSecretText.length > 0 && hasApiSecretText[0].data_type !== 'text') {
        console.log('Altering apiSecret column to TEXT type...');
        await sequelize.query(`
          ALTER TABLE "ApiConfigs"
          ALTER COLUMN "apiSecret" TYPE TEXT;
        `);
        console.log('apiSecret column altered.');
      }

      // Check if description column exists
      const hasDescription = await sequelize.query(`
        SELECT column_name FROM information_schema.columns
        WHERE table_name = 'ApiConfigs' AND column_name = 'description';
      `, { type: sequelize.QueryTypes.SELECT });

      if (hasDescription.length === 0) {
        console.log('Adding description column...');
        await sequelize.query(`
          ALTER TABLE "ApiConfigs"
          ADD COLUMN "description" TEXT;
        `);
        console.log('description column added.');
      }

      // Check if isEnabled column exists
      const hasIsEnabled = await sequelize.query(`
        SELECT column_name FROM information_schema.columns
        WHERE table_name = 'ApiConfigs' AND column_name = 'isEnabled';
      `, { type: sequelize.QueryTypes.SELECT });

      if (hasIsEnabled.length === 0) {
        console.log('Adding isEnabled column...');
        await sequelize.query(`
          ALTER TABLE "ApiConfigs"
          ADD COLUMN "isEnabled" BOOLEAN DEFAULT true;
        `);
        console.log('isEnabled column added.');
      }
    }

    console.log('ApiConfig table verified successfully');
  } catch (error) {
    console.error('Error ensuring ApiConfig table:', error);
  }
}

module.exports = ensureApiConfigTable;
