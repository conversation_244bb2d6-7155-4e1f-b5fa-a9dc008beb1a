import { useState, useEffect, useCallback } from 'react';
import errorHandler, { ERROR_TYPES, ERROR_CODES } from '../services/errorHandler';

/**
 * Hook pour gérer les erreurs dans les composants React
 */
export const useErrorHandler = (options = {}) => {
  const {
    showNotifications = true,
    autoRetry = false,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  const [errors, setErrors] = useState([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [retryCount, setRetryCount] = useState(0);

  // Gestionnaire d'erreurs
  const handleError = useCallback((error, context = {}) => {
    const standardError = errorHandler.handleAxiosError(error);
    
    // Ajouter le contexte à l'erreur
    standardError.context = context;
    
    // Ajouter l'erreur à la liste
    setErrors(prev => [...prev, standardError]);

    return standardError;
  }, []);

  // Nettoyer une erreur spécifique
  const clearError = useCallback((errorId) => {
    setErrors(prev => prev.filter(err => err.timestamp !== errorId));
  }, []);

  // Nettoyer toutes les erreurs
  const clearAllErrors = useCallback(() => {
    setErrors([]);
    setRetryCount(0);
  }, []);

  // Réessayer une action
  const retry = useCallback(async (action, context = {}) => {
    if (retryCount >= maxRetries) {
      console.warn('Nombre maximum de tentatives atteint');
      return null;
    }

    try {
      setRetryCount(prev => prev + 1);
      
      // Attendre avant de réessayer
      if (retryDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount));
      }

      const result = await action();
      
      // Réinitialiser le compteur en cas de succès
      setRetryCount(0);
      
      return result;
    } catch (error) {
      return handleError(error, { ...context, isRetry: true, retryAttempt: retryCount });
    }
  }, [retryCount, maxRetries, retryDelay, handleError]);

  // Wrapper pour les appels API avec gestion d'erreurs automatique
  const withErrorHandling = useCallback((apiCall, context = {}) => {
    return async (...args) => {
      try {
        const result = await apiCall(...args);
        return result;
      } catch (error) {
        const standardError = handleError(error, context);
        
        // Réessayer automatiquement si configuré et si l'erreur est récupérable
        if (autoRetry && errorHandler.isRecoverable(standardError) && retryCount < maxRetries) {
          console.log(`🔄 Tentative automatique ${retryCount + 1}/${maxRetries}`);
          return retry(() => apiCall(...args), context);
        }
        
        throw standardError;
      }
    };
  }, [handleError, autoRetry, maxRetries, retryCount, retry]);

  // Écouter les changements de connexion
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    const handleConnectionRestored = () => {
      console.log('🌐 Connexion restaurée');
      // Nettoyer les erreurs de connexion
      setErrors(prev => prev.filter(err => err.code !== ERROR_CODES.CONNECTION_LOST));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('connection:restored', handleConnectionRestored);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('connection:restored', handleConnectionRestored);
    };
  }, []);

  // Écouter les erreurs globales
  useEffect(() => {
    const errorListener = (error) => {
      if (showNotifications) {
        setErrors(prev => [...prev, error]);
      }
    };

    errorHandler.addErrorListener(errorListener);

    return () => {
      errorHandler.removeErrorListener(errorListener);
    };
  }, [showNotifications]);

  // Obtenir les erreurs par type
  const getErrorsByType = useCallback((type) => {
    return errors.filter(error => error.type === type);
  }, [errors]);

  // Vérifier s'il y a des erreurs d'un type spécifique
  const hasErrorOfType = useCallback((type) => {
    return errors.some(error => error.type === type);
  }, [errors]);

  // Obtenir la dernière erreur
  const getLastError = useCallback(() => {
    return errors.length > 0 ? errors[errors.length - 1] : null;
  }, [errors]);

  // Vérifier s'il y a des erreurs critiques
  const hasCriticalErrors = useCallback(() => {
    return errors.some(error => 
      error.type === ERROR_TYPES.AUTH || 
      error.type === ERROR_TYPES.FORBIDDEN ||
      (error.type === ERROR_TYPES.NETWORK && !isOnline)
    );
  }, [errors, isOnline]);

  // Obtenir un résumé des erreurs pour l'affichage
  const getErrorSummary = useCallback(() => {
    if (errors.length === 0) return null;

    const errorCounts = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {});

    return {
      total: errors.length,
      byType: errorCounts,
      lastError: getLastError(),
      hasCritical: hasCriticalErrors()
    };
  }, [errors, getLastError, hasCriticalErrors]);

  return {
    // État
    errors,
    isOnline,
    retryCount,
    
    // Actions
    handleError,
    clearError,
    clearAllErrors,
    retry,
    withErrorHandling,
    
    // Utilitaires
    getErrorsByType,
    hasErrorOfType,
    getLastError,
    hasCriticalErrors,
    getErrorSummary,
    
    // Helpers pour les types d'erreurs courants
    hasNetworkErrors: () => hasErrorOfType(ERROR_TYPES.NETWORK),
    hasAuthErrors: () => hasErrorOfType(ERROR_TYPES.AUTH),
    hasServerErrors: () => hasErrorOfType(ERROR_TYPES.SERVER),
    hasValidationErrors: () => hasErrorOfType(ERROR_TYPES.VALIDATION)
  };
};

/**
 * Hook simplifié pour la gestion d'erreurs dans les formulaires
 */
export const useFormErrorHandler = () => {
  const { handleError, clearAllErrors, hasValidationErrors, getErrorsByType } = useErrorHandler({
    showNotifications: false,
    autoRetry: false
  });

  const handleFormError = useCallback((error) => {
    const standardError = handleError(error);
    
    // Extraire les erreurs de validation pour les champs
    if (standardError.type === ERROR_TYPES.VALIDATION && standardError.details.validationErrors) {
      return standardError.details.validationErrors;
    }
    
    return { submit: standardError.userMessage };
  }, [handleError]);

  return {
    handleFormError,
    clearFormErrors: clearAllErrors,
    hasValidationErrors,
    getValidationErrors: () => getErrorsByType(ERROR_TYPES.VALIDATION)
  };
};

/**
 * Hook pour la gestion d'erreurs dans les appels API
 */
export const useApiErrorHandler = (options = {}) => {
  const {
    autoRetry = true,
    maxRetries = 3,
    retryDelay = 1000,
    showNotifications = true
  } = options;

  const errorHandler = useErrorHandler({
    showNotifications,
    autoRetry,
    maxRetries,
    retryDelay
  });

  const apiCall = useCallback((apiFunction, context = {}) => {
    return errorHandler.withErrorHandling(apiFunction, context);
  }, [errorHandler]);

  return {
    ...errorHandler,
    apiCall
  };
};

export default useErrorHandler;
