import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  CircularProgress,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Chip,
  Tabs,
  Tab,
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  ContentCopy as CopyIcon,
  Save as SaveIcon,
  BarChart as ChartIcon,
  InsertDriveFile as FileIcon,
  Download as DownloadIcon,
  Insights as InsightsIcon,
  TrendingUp as TrendingIcon,
  ShowChart as LineChartIcon,
  PieChart as PieChartIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

// Importation fictive de bibliothèques de graphiques
// Dans une implémentation réelle, vous utiliseriez des bibliothèques comme Chart.js, Recharts, etc.
const Chart = ({ type, data }) => {
  return (
    <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc' }}>
      {type === 'bar' && <ChartIcon sx={{ fontSize: 60, color: 'primary.main' }} />}
      {type === 'line' && <LineChartIcon sx={{ fontSize: 60, color: 'success.main' }} />}
      {type === 'pie' && <PieChartIcon sx={{ fontSize: 60, color: 'warning.main' }} />}
      <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
        {type === 'bar' ? 'Graphique à barres' : type === 'line' ? 'Graphique linéaire' : 'Graphique circulaire'} -
        {data || 'Données de démonstration'}
      </Typography>
    </Box>
  );
};

const AiDataAnalysis = () => {
  const [formData, setFormData] = useState({
    dataType: 'sales',
    timeRange: 'month',
    analysisType: 'trends',
    compareWithPrevious: true,
    language: 'french',
  });
  const [platformData, setPlatformData] = useState(null);
  const [generatedAnalysis, setGeneratedAnalysis] = useState('');
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [tabValue, setTabValue] = useState(0);

  // Fetch platform data on component mount and when data type or time range changes
  useEffect(() => {
    fetchPlatformData();
  }, [formData.dataType, formData.timeRange]);

  const fetchPlatformData = async () => {
    try {
      setDataLoading(true);
      const response = await axiosInstance.get(
        `/admin/data?type=${formData.dataType}&timeRange=${formData.timeRange}`
      );
      setPlatformData(response.data);
    } catch (err) {
      console.error('Error fetching platform data:', err);
      setError('Erreur lors de la récupération des données de la plateforme');
    } finally {
      setDataLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Include platform data in the request
      const requestData = {
        ...formData,
        platformData,
      };

      const response = await axiosInstance.post('/ai/analyze', requestData);
      setGeneratedAnalysis(response.data.analysis);
      setSuccess('Analyse générée avec succès!');
    } catch (err) {
      console.error('Error generating analysis:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération de l\'analyse');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedAnalysis);
    setSuccess('Analyse copiée dans le presse-papiers!');
  };

  const handleSaveReport = async () => {
    try {
      setLoading(true);
      const reportData = {
        title: `Analyse ${formData.analysisType} des ${formData.dataType} - ${new Date().toLocaleDateString()}`,
        content: generatedAnalysis,
        dataType: formData.dataType,
        timeRange: formData.timeRange,
        analysisType: formData.analysisType,
        rawData: platformData,
      };

      const response = await axiosInstance.post('/admin/reports', reportData);
      setSuccess('Rapport enregistré avec succès!');
    } catch (err) {
      console.error('Error saving report:', err);
      setError(err.response?.data?.message || 'Erreur lors de l\'enregistrement du rapport');
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    if (!platformData) return;

    // Convert data to CSV or JSON
    const dataStr = JSON.stringify(platformData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

    // Create download link and click it
    const exportFileDefaultName = `poultray-data-${formData.dataType}-${formData.timeRange}-${new Date().toISOString().split('T')[0]}.json`;
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    setSuccess('Données exportées avec succès!');
  };

  // Helper function to render the appropriate chart based on data type
  const renderCharts = () => {
    if (!platformData) return null;

    switch (formData.dataType) {
      case 'sales':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Ventes par période" />
                <CardContent>
                  <Chart type="bar" data="Ventes par période" />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Répartition des ventes par catégorie" />
                <CardContent>
                  <Chart type="pie" data="Répartition des ventes" />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );
      case 'users':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Nouveaux utilisateurs" />
                <CardContent>
                  <Chart type="line" data="Croissance des utilisateurs" />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Répartition par rôle" />
                <CardContent>
                  <Chart type="pie" data="Répartition des utilisateurs" />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );
      case 'products':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Produits les plus populaires" />
                <CardContent>
                  <Chart type="bar" data="Popularité des produits" />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Évolution des stocks" />
                <CardContent>
                  <Chart type="line" data="Évolution des stocks" />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );
      default:
        return null;
    }
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InsightsIcon color="primary" /> Analyse de Données IA
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<DownloadIcon />}
            onClick={handleExportData}
            disabled={!platformData}
          >
            Exporter les données
          </Button>
        </Box>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Analyse" icon={<AIIcon />} />
          <Tab label="Visualisation" icon={<ChartIcon />} />
          <Tab label="Données brutes" icon={<FileIcon />} />
        </Tabs>

        {tabValue === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={5}>
              <form onSubmit={handleSubmit}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Type de données</InputLabel>
                  <Select
                    name="dataType"
                    value={formData.dataType}
                    label="Type de données"
                    onChange={handleChange}
                  >
                    <MenuItem value="sales">Ventes</MenuItem>
                    <MenuItem value="users">Utilisateurs</MenuItem>
                    <MenuItem value="products">Produits</MenuItem>
                    <MenuItem value="engagement">Engagement</MenuItem>
                    <MenuItem value="performance">Performance</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel>Période</InputLabel>
                  <Select
                    name="timeRange"
                    value={formData.timeRange}
                    label="Période"
                    onChange={handleChange}
                  >
                    <MenuItem value="day">Aujourd'hui</MenuItem>
                    <MenuItem value="week">Cette semaine</MenuItem>
                    <MenuItem value="month">Ce mois</MenuItem>
                    <MenuItem value="quarter">Ce trimestre</MenuItem>
                    <MenuItem value="year">Cette année</MenuItem>
                    <MenuItem value="custom">Personnalisée</MenuItem>
                  </Select>
                </FormControl>

                {formData.timeRange === 'custom' && (
                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid item xs={6}>
                      <TextField
                        label="Date de début"
                        type="date"
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        label="Date de fin"
                        type="date"
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>
                )}

                <FormControl fullWidth margin="normal">
                  <InputLabel>Type d'analyse</InputLabel>
                  <Select
                    name="analysisType"
                    value={formData.analysisType}
                    label="Type d'analyse"
                    onChange={handleChange}
                  >
                    <MenuItem value="trends">Tendances</MenuItem>
                    <MenuItem value="forecast">Prévisions</MenuItem>
                    <MenuItem value="anomalies">Anomalies</MenuItem>
                    <MenuItem value="correlations">Corrélations</MenuItem>
                    <MenuItem value="summary">Résumé</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel>Langue</InputLabel>
                  <Select
                    name="language"
                    value={formData.language}
                    label="Langue"
                    onChange={handleChange}
                  >
                    <MenuItem value="french">Français</MenuItem>
                    <MenuItem value="arabic">Arabe</MenuItem>
                  </Select>
                </FormControl>

                <Box sx={{ mt: 2 }}>
                  <label>
                    <input
                      type="checkbox"
                      name="compareWithPrevious"
                      checked={formData.compareWithPrevious}
                      onChange={handleCheckboxChange}
                    />
                    Comparer avec la période précédente
                  </label>
                </Box>

                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{ mt: 3 }}
                  disabled={loading || dataLoading || !platformData}
                  startIcon={loading ? <CircularProgress size={20} /> : <AIIcon />}
                >
                  {loading ? 'Analyse en cours...' : 'Générer l\'analyse'}
                </Button>
              </form>
            </Grid>

            <Grid item xs={12} md={7}>
              <Box sx={{ position: 'relative' }}>
                <Typography variant="h6" gutterBottom>
                  Analyse générée
                </Typography>
                {generatedAnalysis && (
                  <Box sx={{ position: 'absolute', top: 0, right: 0 }}>
                    <Tooltip title="Copier l'analyse">
                      <IconButton onClick={handleCopyToClipboard}>
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Enregistrer le rapport">
                      <IconButton onClick={handleSaveReport}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}
                <TextField
                  multiline
                  rows={20}
                  fullWidth
                  variant="outlined"
                  value={generatedAnalysis}
                  onChange={(e) => setGeneratedAnalysis(e.target.value)}
                  placeholder="L'analyse générée apparaîtra ici..."
                />
                {generatedAnalysis && (
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SaveIcon />}
                      onClick={handleSaveReport}
                    >
                      Enregistrer le rapport
                    </Button>
                  </Box>
                )}
              </Box>
            </Grid>
          </Grid>
        )}

        {tabValue === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Visualisation des données
            </Typography>
            {dataLoading ? (
              <Box display="flex" justifyContent="center" my={4}>
                <CircularProgress />
              </Box>
            ) : platformData ? (
              renderCharts()
            ) : (
              <Alert severity="info">
                Sélectionnez un type de données et une période pour afficher les visualisations
              </Alert>
            )}
          </Box>
        )}

        {tabValue === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>Données brutes</span>
              <Button
                variant="outlined"
                size="small"
                startIcon={<DownloadIcon />}
                onClick={handleExportData}
                disabled={!platformData}
              >
                Exporter
              </Button>
            </Typography>
            {dataLoading ? (
              <Box display="flex" justifyContent="center" my={4}>
                <CircularProgress />
              </Box>
            ) : platformData ? (
              <TextField
                multiline
                rows={20}
                fullWidth
                variant="outlined"
                value={JSON.stringify(platformData, null, 2)}
                InputProps={{ readOnly: true }}
              />
            ) : (
              <Alert severity="info">
                Sélectionnez un type de données et une période pour afficher les données brutes
              </Alert>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default AiDataAnalysis;
