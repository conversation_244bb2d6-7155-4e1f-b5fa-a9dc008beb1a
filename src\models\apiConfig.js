const { DataTypes } = require('sequelize');
// const sequelize = require('../config/database'); // sequelize instance will be passed by index.js
const crypto = require('crypto');

// Ensure CRYPTO_SECRET_KEY is set in your .env file
const ENCRYPTION_KEY = process.env.CRYPTO_SECRET_KEY || 'default_secret_key_32_chars_long'; // Must be 32 bytes (256 bits) for aes-256-cbc
const IV_LENGTH = 16; // For AES, this is always 16

function encrypt(text) {
  if (!text) return null;
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return iv.toString('hex') + ':' + encrypted.toString('hex');
  } catch (error) {
    console.error('Encryption failed:', error);
    return null; // Or handle error appropriately
  }
}

function decrypt(text) {
  if (!text) return null;
  try {
    const textParts = text.split(':');
    if (textParts.length < 2) {
      console.warn('Invalid encrypted text format:', text);
      return text; // Return original if format is invalid
    }
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedText = Buffer.from(textParts.join(':'), 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  } catch (error) {
    console.error('Decryption failed:', error);
    return text; // Return original if decryption fails
  }
}

module.exports = (sequelize, DataTypes) => {
  const ApiConfig = sequelize.define('ApiConfig', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    serviceName: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true, // Each service should have a unique configuration
    },
    apiKey: {
      type: DataTypes.TEXT, // Changed from STRING to TEXT to handle longer API keys
      allowNull: true, // Allow null to support empty API keys
      get() {
        const rawValue = this.getDataValue('apiKey');
        try {
          return rawValue ? decrypt(rawValue) : '';
        } catch (error) {
          console.error('Error decrypting apiKey:', error);
          return '';
        }
      },
      set(value) {
        try {
          this.setDataValue('apiKey', value ? encrypt(value) : '');
        } catch (error) {
          console.error('Error encrypting apiKey:', error);
          this.setDataValue('apiKey', '');
        }
      },
    },
    apiSecret: {
      type: DataTypes.TEXT, // Changed from STRING to TEXT
      allowNull: true, // Some APIs might not have a secret
      get() {
        const rawValue = this.getDataValue('apiSecret');
        try {
          return rawValue ? decrypt(rawValue) : null;
        } catch (error) {
          console.error('Error decrypting apiSecret:', error);
          return null;
        }
      },
      set(value) {
        if (!value) {
          this.setDataValue('apiSecret', null);
          return;
        }

        try {
          this.setDataValue('apiSecret', encrypt(value));
        } catch (error) {
          console.error('Error encrypting apiSecret:', error);
          this.setDataValue('apiSecret', null);
        }
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  }, {
    sequelize,
    modelName: 'ApiConfig',
    tableName: 'ApiConfigs', // Match existing table name in database
    timestamps: true,
  });

  // If ApiConfig has associations, define them here
  // ApiConfig.associate = function(models) {
  //   // Example: ApiConfig.belongsTo(models.User, { foreignKey: 'userId' });
  // };

  return ApiConfig;
};

// module.exports = ApiConfig; // This line should be removed or commented out
