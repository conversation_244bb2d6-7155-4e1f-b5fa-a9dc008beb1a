const db = require('../models');
const BlogPost = db.BlogPost; // Get the initialized model
const slugify = require('slugify');

class BlogController {
  // Créer un nouveau blog post
  static async createPost(req, res) {
    try {
      const { title, content, excerpt, status, tags, featured_image } = req.body;

      // Générer un slug unique à partir du titre
      const slug = slugify(title, { lower: true, strict: true });

      const blogData = {
        title,
        slug,
        content,
        excerpt,
        author_id: req.user.id, // L'ID de l'utilisateur connecté
        status,
        tags: Array.isArray(tags) ? JSON.stringify(tags) : '[]',
        featured_image
      };

      const newPost = await BlogPost.create(blogData);
      res.status(201).json(newPost);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Récupérer tous les blog posts avec pagination et filtres
  static async getPosts(req, res) {
    try {
      const { page = 1, limit = 10, status } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Construire les options de requête
      const whereOptions = {};
      // Pour les requêtes non-admin, montrer seulement les articles publiés par défaut
      if (status) {
        whereOptions.status = status;
      } else if (!req.originalUrl.includes('/admin/')) {
        whereOptions.status = 'published';
      }

      // Utiliser findAndCountAll pour obtenir les posts et le total
      const result = await BlogPost.findAndCountAll({
        where: whereOptions,
        limit: parseInt(limit),
        offset: offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            association: 'author',
            attributes: ['id', 'username', 'first_name', 'last_name']
          }
        ]
      });

      // Formater la réponse
      const response = {
        posts: result.rows || [],
        total: result.count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((result.count || 0) / parseInt(limit))
      };

      res.json(response);
    } catch (error) {
      console.error('Erreur dans BlogController.getPosts:', error);
      res.status(500).json({
        error: error.message,
        posts: [],
        total: 0,
        page: parseInt(req.query.page || 1),
        limit: parseInt(req.query.limit || 10),
        totalPages: 0
      });
    }
  }

  // Récupérer les articles pour l'administration avec pagination
  static async getAdminPosts(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Pour l'admin, on récupère tous les articles sans filtrer par statut
      const result = await BlogPost.findAndCountAll({
        limit: parseInt(limit),
        offset: offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            association: 'author',
            attributes: ['id', 'username', 'first_name', 'last_name']
          }
        ]
      });

      // Formater la réponse
      const response = {
        posts: result.rows || [],
        total: result.count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((result.count || 0) / parseInt(limit))
      };

      res.json(response);
    } catch (error) {
      console.error('Erreur dans BlogController.getAdminPosts:', error);
      res.status(500).json({
        error: error.message,
        posts: [],
        total: 0,
        page: parseInt(req.query.page || 1),
        limit: parseInt(req.query.limit || 10),
        totalPages: 0
      });
    }
  }

  // Récupérer un blog post par son ID
  static async getPostById(req, res) {
    try {
      const post = await BlogPost.findById(req.params.id);
      if (!post) {
        return res.status(404).json({ error: 'Blog post non trouvé' });
      }
      res.json(post);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Mettre à jour un blog post
  static async updatePost(req, res) {
    try {
      const { title, content, excerpt, status, tags, featured_image } = req.body;
      const updateData = {};

      // Ne mettre à jour que les champs fournis
      if (title) {
        updateData.title = title;
        updateData.slug = slugify(title, { lower: true, strict: true });
      }
      if (content) updateData.content = content;
      if (excerpt) updateData.excerpt = excerpt;
      if (status) updateData.status = status;
      if (tags) updateData.tags = Array.isArray(tags) ? JSON.stringify(tags) : '[]';
      if (featured_image) updateData.featured_image = featured_image;

      const post = await BlogPost.findById(req.params.id);
      if (!post) {
        return res.status(404).json({ error: 'Blog post non trouvé' });
      }

      // Vérifier si l'utilisateur est l'auteur ou un admin
      if (post.author_id !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Non autorisé à modifier ce blog post' });
      }

      const updatedPost = await BlogPost.update(req.params.id, updateData);
      res.json(updatedPost);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Supprimer un blog post
  static async deletePost(req, res) {
    try {
      const post = await BlogPost.findById(req.params.id);
      if (!post) {
        return res.status(404).json({ error: 'Blog post non trouvé' });
      }

      // Vérifier si l'utilisateur est l'auteur ou un admin
      if (post.author_id !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Non autorisé à supprimer ce blog post' });
      }

      await BlogPost.delete(req.params.id);
      res.json({ message: 'Blog post supprimé avec succès' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = BlogController;
