/**
 * Service de notifications push et in-app
 */

import websocketService, { WS_EVENTS } from './websocketService';

// Types de notifications
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  ALERT: 'alert'
};

// Priorités des notifications
export const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
};

// Catégories de notifications
export const NOTIFICATION_CATEGORIES = {
  SYSTEM: 'system',
  VOLAILLE: 'volaille',
  PRODUCTION: 'production',
  CONSULTATION: 'consultation',
  COMMANDE: 'commande',
  MESSAGE: 'message',
  ALERT: 'alert'
};

class NotificationService {
  constructor() {
    this.notifications = [];
    this.listeners = new Map();
    this.settings = {
      enablePush: true,
      enableSound: true,
      enableVibration: true,
      maxNotifications: 50,
      autoRemoveDelay: 5000
    };
    this.permission = 'default';
    this.serviceWorkerRegistration = null;
    
    this.initializeService();
  }

  /**
   * Initialiser le service de notifications
   */
  async initializeService() {
    // Demander la permission pour les notifications push
    await this.requestPermission();
    
    // Enregistrer le service worker
    await this.registerServiceWorker();
    
    // Écouter les événements WebSocket
    this.setupWebSocketListeners();
    
    // Charger les paramètres utilisateur
    this.loadSettings();
  }

  /**
   * Demander la permission pour les notifications
   */
  async requestPermission() {
    if ('Notification' in window) {
      this.permission = await Notification.requestPermission();
      console.log('Permission notifications:', this.permission);
    }
  }

  /**
   * Enregistrer le service worker
   */
  async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker enregistré:', this.serviceWorkerRegistration);
      } catch (error) {
        console.error('Erreur enregistrement Service Worker:', error);
      }
    }
  }

  /**
   * Configurer les écouteurs WebSocket
   */
  setupWebSocketListeners() {
    websocketService.on(WS_EVENTS.NOTIFICATION, (data) => {
      this.handleNotification(data);
    });

    websocketService.on(WS_EVENTS.ALERT, (data) => {
      this.handleAlert(data);
    });

    websocketService.on(WS_EVENTS.VOLAILLE_CREATED, (data) => {
      this.showNotification({
        type: NOTIFICATION_TYPES.SUCCESS,
        category: NOTIFICATION_CATEGORIES.VOLAILLE,
        title: 'Nouvelle volaille ajoutée',
        message: `${data.nom} a été ajouté avec succès`,
        data: data
      });
    });

    websocketService.on(WS_EVENTS.CONSULTATION_SCHEDULED, (data) => {
      this.showNotification({
        type: NOTIFICATION_TYPES.INFO,
        category: NOTIFICATION_CATEGORIES.CONSULTATION,
        title: 'Consultation programmée',
        message: `Consultation avec Dr. ${data.veterinaire_nom} le ${new Date(data.date_consultation).toLocaleDateString()}`,
        priority: NOTIFICATION_PRIORITIES.HIGH,
        data: data
      });
    });

    websocketService.on(WS_EVENTS.ORDER_CREATED, (data) => {
      this.showNotification({
        type: NOTIFICATION_TYPES.SUCCESS,
        category: NOTIFICATION_CATEGORIES.COMMANDE,
        title: 'Nouvelle commande',
        message: `Commande #${data.id} créée pour ${data.montant_total} DA`,
        data: data
      });
    });

    websocketService.on(WS_EVENTS.MESSAGE_RECEIVED, (data) => {
      this.showNotification({
        type: NOTIFICATION_TYPES.INFO,
        category: NOTIFICATION_CATEGORIES.MESSAGE,
        title: `Message de ${data.sender_name}`,
        message: data.message,
        priority: NOTIFICATION_PRIORITIES.NORMAL,
        data: data
      });
    });
  }

  /**
   * Gérer une notification reçue
   */
  handleNotification(notificationData) {
    this.showNotification(notificationData);
  }

  /**
   * Gérer une alerte reçue
   */
  handleAlert(alertData) {
    this.showNotification({
      ...alertData,
      type: NOTIFICATION_TYPES.ALERT,
      priority: NOTIFICATION_PRIORITIES.URGENT,
      persistent: true
    });
  }

  /**
   * Afficher une notification
   */
  showNotification(notification) {
    const notif = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      read: false,
      persistent: false,
      ...notification
    };

    // Ajouter à la liste des notifications
    this.notifications.unshift(notif);
    
    // Limiter le nombre de notifications
    if (this.notifications.length > this.settings.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.settings.maxNotifications);
    }

    // Afficher la notification push si autorisée
    if (this.settings.enablePush && this.permission === 'granted') {
      this.showPushNotification(notif);
    }

    // Jouer un son si activé
    if (this.settings.enableSound) {
      this.playNotificationSound(notif.type);
    }

    // Vibrer si activé et supporté
    if (this.settings.enableVibration && 'vibrate' in navigator) {
      this.vibrateDevice(notif.priority);
    }

    // Notifier les écouteurs
    this.notifyListeners('notification', notif);

    // Supprimer automatiquement si pas persistante
    if (!notif.persistent && this.settings.autoRemoveDelay > 0) {
      setTimeout(() => {
        this.removeNotification(notif.id);
      }, this.settings.autoRemoveDelay);
    }

    return notif;
  }

  /**
   * Afficher une notification push native
   */
  showPushNotification(notification) {
    const options = {
      body: notification.message,
      icon: '/icons/notification-icon.png',
      badge: '/icons/badge-icon.png',
      tag: notification.category,
      data: notification.data,
      requireInteraction: notification.priority === NOTIFICATION_PRIORITIES.URGENT,
      silent: false,
      vibrate: this.getVibrationPattern(notification.priority)
    };

    if (this.serviceWorkerRegistration) {
      // Utiliser le service worker pour les notifications
      this.serviceWorkerRegistration.showNotification(notification.title, options);
    } else {
      // Notification directe
      new Notification(notification.title, options);
    }
  }

  /**
   * Jouer un son de notification
   */
  playNotificationSound(type) {
    const audio = new Audio();
    
    switch (type) {
      case NOTIFICATION_TYPES.ERROR:
      case NOTIFICATION_TYPES.ALERT:
        audio.src = '/sounds/error.mp3';
        break;
      case NOTIFICATION_TYPES.WARNING:
        audio.src = '/sounds/warning.mp3';
        break;
      case NOTIFICATION_TYPES.SUCCESS:
        audio.src = '/sounds/success.mp3';
        break;
      default:
        audio.src = '/sounds/notification.mp3';
    }
    
    audio.volume = 0.5;
    audio.play().catch(error => {
      console.log('Impossible de jouer le son:', error);
    });
  }

  /**
   * Faire vibrer l'appareil
   */
  vibrateDevice(priority) {
    const pattern = this.getVibrationPattern(priority);
    navigator.vibrate(pattern);
  }

  /**
   * Obtenir le pattern de vibration selon la priorité
   */
  getVibrationPattern(priority) {
    switch (priority) {
      case NOTIFICATION_PRIORITIES.URGENT:
        return [200, 100, 200, 100, 200];
      case NOTIFICATION_PRIORITIES.HIGH:
        return [200, 100, 200];
      case NOTIFICATION_PRIORITIES.NORMAL:
        return [200];
      default:
        return [100];
    }
  }

  /**
   * Marquer une notification comme lue
   */
  markAsRead(notificationId) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners('notificationRead', notification);
    }
  }

  /**
   * Marquer toutes les notifications comme lues
   */
  markAllAsRead() {
    this.notifications.forEach(notification => {
      notification.read = true;
    });
    this.notifyListeners('allNotificationsRead');
  }

  /**
   * Supprimer une notification
   */
  removeNotification(notificationId) {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      const removed = this.notifications.splice(index, 1)[0];
      this.notifyListeners('notificationRemoved', removed);
    }
  }

  /**
   * Supprimer toutes les notifications
   */
  clearAllNotifications() {
    this.notifications = [];
    this.notifyListeners('allNotificationsCleared');
  }

  /**
   * Obtenir toutes les notifications
   */
  getNotifications(filters = {}) {
    let filtered = [...this.notifications];

    if (filters.category) {
      filtered = filtered.filter(n => n.category === filters.category);
    }

    if (filters.type) {
      filtered = filtered.filter(n => n.type === filters.type);
    }

    if (filters.unreadOnly) {
      filtered = filtered.filter(n => !n.read);
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  /**
   * Obtenir le nombre de notifications non lues
   */
  getUnreadCount(category = null) {
    let notifications = this.notifications.filter(n => !n.read);
    
    if (category) {
      notifications = notifications.filter(n => n.category === category);
    }
    
    return notifications.length;
  }

  /**
   * Configurer les paramètres
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    this.notifyListeners('settingsUpdated', this.settings);
  }

  /**
   * Sauvegarder les paramètres
   */
  saveSettings() {
    localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
  }

  /**
   * Charger les paramètres
   */
  loadSettings() {
    const saved = localStorage.getItem('notificationSettings');
    if (saved) {
      try {
        this.settings = { ...this.settings, ...JSON.parse(saved) };
      } catch (error) {
        console.error('Erreur chargement paramètres notifications:', error);
      }
    }
  }

  /**
   * Ajouter un écouteur d'événements
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Supprimer un écouteur d'événements
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Notifier les écouteurs
   */
  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Erreur dans écouteur notification:', error);
        }
      });
    }
  }

  /**
   * Générer un ID unique
   */
  generateId() {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Instance singleton
const notificationService = new NotificationService();

export default notificationService;
