const { Sequelize } = require('sequelize');

// Configuration de la base de données PostgreSQL avec Sequelize
console.log('Database configuration:', {
  name: process.env.DB_NAME,
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT
});

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    pool: {
      max: 20,
      min: 5,
      idle: 30000,
      acquire: 60000,
      evict: 5000
    },
    dialectOptions: {
      connectTimeout: 60000,
      statement_timeout: 60000,
      idle_in_transaction_session_timeout: 60000
    },
    retry: {
      max: 3,
      match: [
        /ConnectionError/,
        /SequelizeConnectionError/,
        /SequelizeConnectionRefusedError/,
        /SequelizeHostNotFoundError/,
        /SequelizeHostNotReachableError/,
        /SequelizeInvalidConnectionError/,
        /SequelizeConnectionTimedOutError/,
        /TimeoutError/
      ]
    },
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    logging: (msg) => {
      if (msg.includes('Executing') || msg.includes('executing')) {
        console.log('SQL Query:', msg);
      }
    }
  }
);

// Test de connexion au démarrage avec retry
const testConnection = async (retries = 3, delay = 5000) => {
  for (let i = 0; i < retries; i++) {
    try {
      await sequelize.authenticate();
      console.log('Connexion à la base de données établie avec succès.');
      return;
    } catch (err) {
      console.error(`Tentative ${i + 1}/${retries} - Erreur de connexion:`, err.message);
      if (i < retries - 1) {
        console.log(`Nouvelle tentative dans ${delay/1000} secondes...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        console.error('Impossible de se connecter à la base de données après plusieurs tentatives.');
        process.exit(-1);
      }
    }
  }
};

testConnection();

module.exports = sequelize;
