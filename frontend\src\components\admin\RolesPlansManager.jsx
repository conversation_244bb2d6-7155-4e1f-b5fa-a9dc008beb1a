import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel,
  IconButton,
  Chip,
  Divider,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Composant de gestion des rôles et des plans d'abonnement
 * Permet aux administrateurs de gérer les différents rôles d'utilisateurs
 * et les plans d'abonnement disponibles sur la plateforme
 */
const RolesPlansManager = () => {
  // État pour les onglets
  const [tabValue, setTabValue] = useState(0);

  // États pour les rôles
  const [roles, setRoles] = useState([]);
  const [roleDialog, setRoleDialog] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [roleFormData, setRoleFormData] = useState({
    name: '',
    description: '',
    permissions: [],
    isActive: true
  });

  // États pour les plans d'abonnement
  const [plans, setPlans] = useState([]);
  const [planDialog, setPlanDialog] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [planFormData, setPlanFormData] = useState({
    name: '',
    description: '',
    price: 0,
    duration: 30, // en jours
    features: [],
    isActive: true,
    roleId: ''
  });

  // État pour les permissions disponibles
  const [availablePermissions, setAvailablePermissions] = useState([
    { id: 'read_volailles', name: 'Lecture des volailles' },
    { id: 'write_volailles', name: 'Écriture des volailles' },
    { id: 'read_users', name: 'Lecture des utilisateurs' },
    { id: 'write_users', name: 'Écriture des utilisateurs' },
    { id: 'read_sales', name: 'Lecture des ventes' },
    { id: 'write_sales', name: 'Écriture des ventes' },
    { id: 'read_stats', name: 'Lecture des statistiques' },
    { id: 'access_ai', name: 'Accès aux fonctionnalités IA' },
    { id: 'admin_access', name: 'Accès administrateur' }
  ]);

  // État pour les notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Champ de texte pour ajouter une fonctionnalité au plan
  const [newFeature, setNewFeature] = useState('');

  // Effet pour charger les données initiales
  useEffect(() => {
    fetchRoles();
    fetchPlans();
  }, []);

  // Fonction pour récupérer les rôles
  const fetchRoles = async () => {
    try {
      const response = await axiosInstance.get('/admin/roles');
      // Le backend retourne { roles: [...] }, on extrait le tableau
      setRoles(response.data.roles || []);
    } catch (error) {
      console.error('Erreur lors de la récupération des rôles:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la récupération des rôles',
        severity: 'error'
      });
    }
  };

  // Fonction pour récupérer les plans d'abonnement
  const fetchPlans = async () => {
    try {
      const response = await axiosInstance.get('/admin/subscription-plans');
      // Le backend retourne { plans: [...] }, on extrait le tableau
      setPlans(response.data.plans || []);
    } catch (error) {
      console.error('Erreur lors de la récupération des plans:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la récupération des plans d\'abonnement',
        severity: 'error'
      });
    }
  };

  // Gestionnaire de changement d'onglet
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Gestionnaires pour les dialogues de rôle
  const handleOpenRoleDialog = (role = null) => {
    if (role) {
      setEditingRole(role);
      setRoleFormData({
        name: role.name || '',
        description: role.description || '',
        permissions: role.permissions || [],
        isActive: Boolean(role.isActive)
      });
    } else {
      setEditingRole(null);
      setRoleFormData({
        name: '',
        description: '',
        permissions: [],
        isActive: true
      });
    }
    setRoleDialog(true);
  };

  const handleCloseRoleDialog = () => {
    setRoleDialog(false);
    setEditingRole(null);
  };

  // Gestionnaires pour les dialogues de plan
  const handleOpenPlanDialog = (plan = null) => {
    if (plan) {
      setEditingPlan(plan);
      setPlanFormData({
        name: plan.name || '',
        description: plan.description || '',
        price: plan.price || 0,
        duration: plan.duration || 30,
        features: plan.features || [],
        isActive: Boolean(plan.isActive),
        roleId: plan.roleId || ''
      });
    } else {
      setEditingPlan(null);
      setPlanFormData({
        name: '',
        description: '',
        price: 0,
        duration: 30,
        features: [],
        isActive: true,
        roleId: ''
      });
    }
    setPlanDialog(true);
  };

  const handleClosePlanDialog = () => {
    setPlanDialog(false);
    setEditingPlan(null);
  };

  // Gestionnaires de changement de formulaire
  const handleRoleFormChange = (e) => {
    const { name, value, checked } = e.target;
    setRoleFormData({
      ...roleFormData,
      [name]: name === 'isActive' ? checked : value
    });
  };

  const handlePlanFormChange = (e) => {
    const { name, value, checked } = e.target;
    setPlanFormData({
      ...planFormData,
      [name]: name === 'isActive' ? checked :
              (name === 'price' || name === 'duration') ? Number(value) : value
    });
  };

  // Gestion des permissions
  const handlePermissionToggle = (permissionId) => {
    const currentPermissions = [...roleFormData.permissions];
    const permissionIndex = currentPermissions.indexOf(permissionId);

    if (permissionIndex === -1) {
      currentPermissions.push(permissionId);
    } else {
      currentPermissions.splice(permissionIndex, 1);
    }

    setRoleFormData({
      ...roleFormData,
      permissions: currentPermissions
    });
  };

  // Gestion des fonctionnalités du plan
  const handleAddFeature = () => {
    if (newFeature.trim() !== '') {
      setPlanFormData({
        ...planFormData,
        features: [...planFormData.features, newFeature.trim()]
      });
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (index) => {
    const updatedFeatures = [...planFormData.features];
    updatedFeatures.splice(index, 1);
    setPlanFormData({
      ...planFormData,
      features: updatedFeatures
    });
  };

  // Soumission des formulaires
  const handleRoleSubmit = async () => {
    try {
      if (editingRole) {
        await axiosInstance.put(`/admin/roles/${editingRole.id}`, roleFormData);
        setSnackbar({
          open: true,
          message: 'Rôle mis à jour avec succès',
          severity: 'success'
        });
      } else {
        await axiosInstance.post('/admin/roles', roleFormData);
        setSnackbar({
          open: true,
          message: 'Rôle créé avec succès',
          severity: 'success'
        });
      }
      handleCloseRoleDialog();
      fetchRoles();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du rôle:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'enregistrement du rôle',
        severity: 'error'
      });
    }
  };

  const handlePlanSubmit = async () => {
    try {
      if (editingPlan) {
        await axiosInstance.put(`/admin/subscription-plans/${editingPlan.id}`, planFormData);
        setSnackbar({
          open: true,
          message: 'Plan d\'abonnement mis à jour avec succès',
          severity: 'success'
        });
      } else {
        await axiosInstance.post('/admin/subscription-plans', planFormData);
        setSnackbar({
          open: true,
          message: 'Plan d\'abonnement créé avec succès',
          severity: 'success'
        });
      }
      handleClosePlanDialog();
      fetchPlans();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du plan:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'enregistrement du plan d\'abonnement',
        severity: 'error'
      });
    }
  };

  // Suppression
  const handleDeleteRole = async (roleId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {
      try {
        await axiosInstance.delete(`/admin/roles/${roleId}`);
        fetchRoles();
        setSnackbar({
          open: true,
          message: 'Rôle supprimé avec succès',
          severity: 'success'
        });
      } catch (error) {
        console.error('Erreur lors de la suppression du rôle:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la suppression du rôle',
          severity: 'error'
        });
      }
    }
  };

  const handleDeletePlan = async (planId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce plan d\'abonnement ?')) {
      try {
        await axiosInstance.delete(`/admin/subscription-plans/${planId}`);
        fetchPlans();
        setSnackbar({
          open: true,
          message: 'Plan d\'abonnement supprimé avec succès',
          severity: 'success'
        });
      } catch (error) {
        console.error('Erreur lors de la suppression du plan:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la suppression du plan d\'abonnement',
          severity: 'error'
        });
      }
    }
  };

  // Fermeture de la notification
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Card>
        <CardContent>
          <Typography variant="h5" component="div" gutterBottom>
            Gestion des Rôles et Plans d'Abonnement
          </Typography>

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Rôles" />
              <Tab label="Plans d'Abonnement" />
            </Tabs>
          </Box>

          {/* Onglet des Rôles */}
          {tabValue === 0 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenRoleDialog()}
                >
                  Ajouter un Rôle
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nom</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Permissions</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Array.isArray(roles) && roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>{role.name}</TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>
                          {Array.isArray(role.permissions) && role.permissions.map((permission) => {
                            const permObj = availablePermissions.find(p => p.id === permission);
                            return (
                              <Chip
                                key={permission}
                                label={permObj ? permObj.name : permission}
                                size="small"
                              />
                            );
                          })}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={role.is_active ? 'Actif' : 'Inactif'}
                            color={role.is_active ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="primary"
                            onClick={() => handleOpenRoleDialog(role)}
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            color="error"
                            onClick={() => handleDeleteRole(role.id)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Onglet des Plans d'Abonnement */}
          {tabValue === 1 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenPlanDialog()}
                >
                  Ajouter un Plan
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nom</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Prix (DA)</TableCell>
                      <TableCell>Durée (jours)</TableCell>
                      <TableCell>Rôle associé</TableCell>
                      <TableCell>Statut</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {plans.map((plan) => (
                      <TableRow key={plan.id}>
                        <TableCell>{plan.name}</TableCell>
                        <TableCell>{plan.description}</TableCell>
                        <TableCell>{plan.price}</TableCell>
                        <TableCell>{plan.duration}</TableCell>
                        <TableCell>
                          {roles.find(r => r.id === plan.roleId)?.name || 'Non défini'}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={plan.isActive ? "Actif" : "Inactif"}
                            color={plan.isActive ? "success" : "error"}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="primary"
                            onClick={() => handleOpenPlanDialog(plan)}
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            color="error"
                            onClick={() => handleDeletePlan(plan.id)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Dialogue pour les rôles */}
      <Dialog open={roleDialog} onClose={handleCloseRoleDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRole ? 'Modifier le Rôle' : 'Ajouter un Rôle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Nom du rôle"
              name="name"
              value={roleFormData.name}
              onChange={handleRoleFormChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={roleFormData.description}
              onChange={handleRoleFormChange}
              margin="normal"
              multiline
              rows={3}
            />

            <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
              Permissions
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {availablePermissions.map((permission) => (
                <Chip
                  key={permission.id}
                  label={permission.name}
                  onClick={() => handlePermissionToggle(permission.id)}
                  color={roleFormData.permissions.includes(permission.id) ? "primary" : "default"}
                  deleteIcon={roleFormData.permissions.includes(permission.id) ? <CheckIcon /> : <CloseIcon />}
                  onDelete={() => handlePermissionToggle(permission.id)}
                />
              ))}
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={roleFormData.isActive}
                  onChange={handleRoleFormChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Actif"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRoleDialog}>Annuler</Button>
          <Button onClick={handleRoleSubmit} variant="contained" color="primary">
            {editingRole ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue pour les plans d'abonnement */}
      <Dialog open={planDialog} onClose={handleClosePlanDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingPlan ? 'Modifier le Plan d\'Abonnement' : 'Ajouter un Plan d\'Abonnement'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Nom du plan"
              name="name"
              value={planFormData.name}
              onChange={handlePlanFormChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={planFormData.description}
              onChange={handlePlanFormChange}
              margin="normal"
              multiline
              rows={3}
            />
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <TextField
                label="Prix (DA)"
                name="price"
                type="number"
                value={planFormData.price}
                onChange={handlePlanFormChange}
                margin="normal"
                required
                sx={{ flex: 1 }}
              />
              <TextField
                label="Durée (jours)"
                name="duration"
                type="number"
                value={planFormData.duration}
                onChange={handlePlanFormChange}
                margin="normal"
                required
                sx={{ flex: 1 }}
              />
            </Box>

            <FormControl fullWidth margin="normal">
              <InputLabel>Rôle associé</InputLabel>
              <Select
                name="roleId"
                value={planFormData.roleId}
                onChange={handlePlanFormChange}
                label="Rôle associé"
              >
                <MenuItem value="">Aucun</MenuItem>
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.id}>{role.name}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
              Fonctionnalités incluses
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <TextField
                label="Nouvelle fonctionnalité"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                sx={{ flex: 1 }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleAddFeature}
                sx={{ ml: 1 }}
              >
                Ajouter
              </Button>
            </Box>

            <Box sx={{ mb: 2 }}>
              {planFormData.features.map((feature, index) => (
                <Chip
                  key={index}
                  label={feature}
                  onDelete={() => handleRemoveFeature(index)}
                  sx={{ m: 0.5 }}
                />
              ))}
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={planFormData.isActive}
                  onChange={handlePlanFormChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Actif"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePlanDialog}>Annuler</Button>
          <Button onClick={handlePlanSubmit} variant="contained" color="primary">
            {editingPlan ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RolesPlansManager;
