require('dotenv').config({ path: 'K:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/.env' });
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Utiliser des valeurs par défaut si les variables d'environnement ne sont pas définies
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '5432';
const DB_NAME = process.env.DB_NAME || 'poultraydz';

console.log('Connexion à la base de données avec les paramètres suivants:');
console.log(`User: ${DB_USER}`);
console.log(`Host: ${DB_HOST}`);
console.log(`Database: ${DB_NAME}`);
console.log(`Port: ${DB_PORT}`);

// Utiliser une chaîne de connexion au lieu des paramètres individuels
const connectionString = `postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
console.log(`Connection string: ${connectionString}`);

const pool = new Pool({
  connectionString,
});

async function seedUsers() {
  try {
    // Vérifier si des utilisateurs existent déjà
    const existingUsers = await pool.query('SELECT COUNT(*) FROM users');
    if (parseInt(existingUsers.rows[0].count) > 0) {
      console.log('Des utilisateurs existent déjà, pas besoin d\'en créer de nouveaux');
      return true;
    }

    // Créer un utilisateur administrateur
    const salt = await bcrypt.genSalt(10);
    const adminPassword = await bcrypt.hash('admin123', salt);
    
    await pool.query(`
      INSERT INTO users (username, email, password, role, first_name, last_name)
      VALUES ('admin', '<EMAIL>', $1, 'admin', 'Admin', 'Poultray')
    `, [adminPassword]);
    
    // Créer un utilisateur éleveur
    const eleveurPassword = await bcrypt.hash('eleveur123', salt);
    
    await pool.query(`
      INSERT INTO users (username, email, password, role, first_name, last_name)
      VALUES ('eleveur', '<EMAIL>', $1, 'eleveur', 'Éleveur', 'Test')
    `, [eleveurPassword]);
    
    console.log('Utilisateurs créés avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création des utilisateurs:', error);
    return false;
  }
}

async function seedEleveurs() {
  try {
    // Vérifier si des éleveurs existent déjà
    const existingEleveurs = await pool.query('SELECT COUNT(*) FROM eleveurs');
    if (parseInt(existingEleveurs.rows[0].count) > 0) {
      console.log('Des éleveurs existent déjà, pas besoin d\'en créer de nouveaux');
      return true;
    }

    // Créer des éleveurs de test
    await pool.query(`
      INSERT INTO eleveurs (nom, prenom, email, telephone, adresse)
      VALUES 
        ('Benali', 'Mohamed', '<EMAIL>', '0555123456', 'Alger, Algérie'),
        ('Hadj', 'Karim', '<EMAIL>', '0555789012', 'Oran, Algérie'),
        ('Ziani', 'Fatima', '<EMAIL>', '0555345678', 'Constantine, Algérie')
    `);
    
    console.log('Éleveurs créés avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création des éleveurs:', error);
    return false;
  }
}

async function seedVolailles() {
  try {
    // Vérifier si des volailles existent déjà
    const existingVolailles = await pool.query('SELECT COUNT(*) FROM volailles');
    if (parseInt(existingVolailles.rows[0].count) > 0) {
      console.log('Des volailles existent déjà, pas besoin d\'en créer de nouvelles');
      return true;
    }

    // Récupérer les IDs des éleveurs
    const eleveursResult = await pool.query('SELECT id FROM eleveurs');
    if (eleveursResult.rows.length === 0) {
      console.log('Aucun éleveur trouvé, impossible de créer des volailles');
      return false;
    }

    const eleveurIds = eleveursResult.rows.map(row => row.id);

    // Créer des volailles de test pour chaque éleveur
    for (const eleveurId of eleveurIds) {
      await pool.query(`
        INSERT INTO volailles (eleveur_id, espece, race, age, poids, quantite, prix_unitaire, description)
        VALUES 
          ($1, 'Poulet', 'Poulet de chair', 45, 2.5, 100, 450.00, 'Poulets de chair prêts à la vente'),
          ($1, 'Dinde', 'Dinde blanche', 120, 8.0, 50, 1200.00, 'Dindes élevées en plein air'),
          ($1, 'Poule', 'Poule pondeuse', 180, 1.8, 200, 350.00, 'Poules pondeuses productives')
      `, [eleveurId]);
    }
    
    console.log('Volailles créées avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création des volailles:', error);
    return false;
  }
}

async function seedData() {
  try {
    console.log('Initialisation des données de test...');
    
    // Test de connexion à la base de données
    await pool.query('SELECT NOW()');
    console.log('Connexion à la base de données établie avec succès');
    
    // Création des données de test
    const usersCreated = await seedUsers();
    const eleveursCreated = await seedEleveurs();
    const volaillesCreated = await seedVolailles();
    
    if (usersCreated && eleveursCreated && volaillesCreated) {
      console.log('Toutes les données de test ont été créées avec succès');
    } else {
      console.log('Certaines données de test n\'ont pas pu être créées');
    }
    
    // Fermer la connexion à la base de données
    await pool.end();
    console.log('Connexion à la base de données fermée');
    
    process.exit(0);
  } catch (error) {
    console.error('Erreur lors de l\'initialisation des données de test:', error);
    process.exit(1);
  }
}

seedData();
