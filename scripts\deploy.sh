#!/bin/bash

# Script de déploiement automatisé pour Poultray DZ
# Supporte les environnements staging et production

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENVIRONMENT="${1:-staging}"
VERSION="${2:-latest}"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration par environnement
case $ENVIRONMENT in
    "staging")
        COMPOSE_FILE="docker-compose.staging.yml"
        ENV_FILE=".env.staging"
        DOMAIN="staging.poultraydz.com"
        ;;
    "production")
        COMPOSE_FILE="docker-compose.production.yml"
        ENV_FILE=".env.production"
        DOMAIN="app.poultraydz.com"
        ;;
    *)
        echo -e "${RED}❌ Environnement non supporté: $ENVIRONMENT${NC}"
        echo "Usage: $0 [staging|production] [version]"
        exit 1
        ;;
esac

# Fonctions de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌${NC} $1"
}

# Fonction de nettoyage en cas d'erreur
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Déploiement échoué (code: $exit_code)"
        
        # Rollback automatique en production
        if [ "$ENVIRONMENT" = "production" ]; then
            log_warning "Tentative de rollback automatique..."
            rollback_deployment || true
        fi
        
        send_notification "❌ Échec du déploiement $ENVIRONMENT" "Le déploiement de Poultray DZ en $ENVIRONMENT a échoué"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Fonction de notification
send_notification() {
    local title="$1"
    local message="$2"
    local webhook_url="${SLACK_WEBHOOK_URL:-}"
    
    if [ -n "$webhook_url" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"text\": \"$title\",
                \"attachments\": [{
                    \"color\": \"danger\",
                    \"text\": \"$message\",
                    \"footer\": \"Poultray DZ Deploy\",
                    \"ts\": $(date +%s)
                }]
            }" \
            "$webhook_url" > /dev/null 2>&1 || true
    fi
}

# Vérifier les prérequis
check_prerequisites() {
    log "Vérification des prérequis pour $ENVIRONMENT..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier le fichier de configuration
    if [ ! -f "$PROJECT_ROOT/$ENV_FILE" ]; then
        log_error "Fichier de configuration manquant: $ENV_FILE"
        exit 1
    fi
    
    # Vérifier le fichier docker-compose
    if [ ! -f "$PROJECT_ROOT/$COMPOSE_FILE" ]; then
        log_error "Fichier Docker Compose manquant: $COMPOSE_FILE"
        exit 1
    fi
    
    # Charger les variables d'environnement
    source "$PROJECT_ROOT/$ENV_FILE"
    
    log_success "Prérequis vérifiés"
}

# Sauvegarder l'état actuel
backup_current_state() {
    log "Sauvegarde de l'état actuel..."
    
    local backup_dir="/tmp/poultraydz_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Sauvegarder la configuration Docker Compose actuelle
    if [ -f "$PROJECT_ROOT/$COMPOSE_FILE" ]; then
        cp "$PROJECT_ROOT/$COMPOSE_FILE" "$backup_dir/"
    fi
    
    # Sauvegarder les variables d'environnement
    if [ -f "$PROJECT_ROOT/$ENV_FILE" ]; then
        cp "$PROJECT_ROOT/$ENV_FILE" "$backup_dir/"
    fi
    
    # Sauvegarder l'état des conteneurs
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps > "$backup_dir/containers_state.txt" 2>/dev/null || true
    
    echo "$backup_dir" > /tmp/poultraydz_last_backup
    log_success "État sauvegardé dans: $backup_dir"
}

# Effectuer une sauvegarde de la base de données
backup_database() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Sauvegarde de la base de données de production..."
        
        # Exécuter le script de sauvegarde
        if [ -f "$PROJECT_ROOT/scripts/backup/backup.sh" ]; then
            bash "$PROJECT_ROOT/scripts/backup/backup.sh" || {
                log_warning "Échec de la sauvegarde automatique"
                read -p "Continuer sans sauvegarde ? (y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    exit 1
                fi
            }
        else
            log_warning "Script de sauvegarde non trouvé"
        fi
    fi
}

# Mettre à jour le code source
update_source_code() {
    log "Mise à jour du code source..."
    
    # Sauvegarder les modifications locales
    git stash push -m "Déploiement $ENVIRONMENT $(date)" || true
    
    # Récupérer les dernières modifications
    git fetch origin
    
    # Checkout de la branche appropriée
    if [ "$ENVIRONMENT" = "production" ]; then
        git checkout main
        git pull origin main
    else
        git checkout develop
        git pull origin develop
    fi
    
    # Vérifier que nous avons la bonne version
    local current_commit=$(git rev-parse HEAD)
    log "Version déployée: $current_commit"
    
    log_success "Code source mis à jour"
}

# Construire les images Docker
build_images() {
    log "Construction des images Docker..."
    
    # Construire les images avec le tag de version
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" build \
        --build-arg VERSION="$VERSION" \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse HEAD)"
    
    log_success "Images construites"
}

# Effectuer les tests de pré-déploiement
run_pre_deployment_tests() {
    log "Exécution des tests de pré-déploiement..."
    
    # Tests de santé des images
    log "Test des images Docker..."
    
    # Démarrer les services en mode test
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d postgres redis
    
    # Attendre que les services soient prêts
    sleep 30
    
    # Tester la connexion à la base de données
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T postgres \
        pg_isready -h localhost -p 5432 -U "${DB_USER:-postgres}" || {
        log_error "Base de données non accessible"
        exit 1
    }
    
    # Tester Redis
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T redis \
        redis-cli ping | grep -q PONG || {
        log_error "Redis non accessible"
        exit 1
    }
    
    log_success "Tests de pré-déploiement réussis"
}

# Déployer les services
deploy_services() {
    log "Déploiement des services..."
    
    # Arrêter les anciens conteneurs
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" down --remove-orphans
    
    # Démarrer les nouveaux services
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d
    
    log_success "Services déployés"
}

# Exécuter les migrations de base de données
run_migrations() {
    log "Exécution des migrations de base de données..."
    
    # Attendre que le backend soit prêt
    sleep 30
    
    # Exécuter les migrations
    docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" exec -T backend \
        npm run migrate || {
        log_error "Échec des migrations"
        exit 1
    }
    
    log_success "Migrations exécutées"
}

# Effectuer les tests de post-déploiement
run_post_deployment_tests() {
    log "Tests de post-déploiement..."
    
    # Attendre que tous les services soient prêts
    sleep 60
    
    # Test de santé de l'application
    local health_url="https://$DOMAIN/health"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null; then
            log_success "Application accessible"
            break
        fi
        
        log "Tentative $attempt/$max_attempts - En attente de l'application..."
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "Application non accessible après $max_attempts tentatives"
        exit 1
    fi
    
    # Test de l'API
    local api_url="https://api.poultraydz.com/health"
    if curl -f -s "$api_url" > /dev/null; then
        log_success "API accessible"
    else
        log_error "API non accessible"
        exit 1
    fi
    
    # Tests de performance basiques
    log "Test de performance..."
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$health_url")
    if (( $(echo "$response_time < 2.0" | bc -l) )); then
        log_success "Temps de réponse acceptable: ${response_time}s"
    else
        log_warning "Temps de réponse élevé: ${response_time}s"
    fi
    
    log_success "Tests de post-déploiement réussis"
}

# Nettoyer les anciennes images
cleanup_old_images() {
    log "Nettoyage des anciennes images..."
    
    # Supprimer les images non utilisées
    docker image prune -f
    
    # Supprimer les volumes orphelins
    docker volume prune -f
    
    log_success "Nettoyage terminé"
}

# Rollback en cas d'échec
rollback_deployment() {
    log_warning "Rollback du déploiement..."
    
    local backup_file="/tmp/poultraydz_last_backup"
    if [ -f "$backup_file" ]; then
        local backup_dir=$(cat "$backup_file")
        if [ -d "$backup_dir" ]; then
            # Restaurer les fichiers de configuration
            cp "$backup_dir/$COMPOSE_FILE" "$PROJECT_ROOT/" 2>/dev/null || true
            cp "$backup_dir/$ENV_FILE" "$PROJECT_ROOT/" 2>/dev/null || true
            
            # Redémarrer avec l'ancienne configuration
            docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d
            
            log_success "Rollback effectué"
        fi
    else
        log_error "Aucune sauvegarde trouvée pour le rollback"
    fi
}

# Envoyer une notification de succès
send_success_notification() {
    local webhook_url="${SLACK_WEBHOOK_URL:-}"
    
    if [ -n "$webhook_url" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"text\": \"✅ Déploiement $ENVIRONMENT réussi\",
                \"attachments\": [{
                    \"color\": \"good\",
                    \"text\": \"Poultray DZ déployé avec succès en $ENVIRONMENT\nVersion: $(git rev-parse --short HEAD)\nURL: https://$DOMAIN\",
                    \"footer\": \"Poultray DZ Deploy\",
                    \"ts\": $(date +%s)
                }]
            }" \
            "$webhook_url" > /dev/null 2>&1 || true
    fi
}

# Fonction principale
main() {
    log "🚀 Début du déploiement Poultray DZ en $ENVIRONMENT"
    local start_time=$(date +%s)
    
    check_prerequisites
    backup_current_state
    backup_database
    update_source_code
    build_images
    run_pre_deployment_tests
    deploy_services
    run_migrations
    run_post_deployment_tests
    cleanup_old_images
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "✅ Déploiement terminé avec succès en ${duration}s"
    log_success "🌐 Application accessible sur: https://$DOMAIN"
    
    send_success_notification
}

# Vérifier les arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 [staging|production] [version]"
    echo "Exemple: $0 staging latest"
    echo "Exemple: $0 production v1.2.3"
    exit 1
fi

# Confirmation pour la production
if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${YELLOW}⚠️  Vous êtes sur le point de déployer en PRODUCTION${NC}"
    echo "Version: $VERSION"
    echo "Domaine: $DOMAIN"
    read -p "Êtes-vous sûr ? (yes/no): " -r
    if [[ ! $REPLY =~ ^yes$ ]]; then
        echo "Déploiement annulé"
        exit 0
    fi
fi

# Exécuter le déploiement
main "$@"
