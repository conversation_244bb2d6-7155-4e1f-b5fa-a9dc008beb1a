import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const useVetConsultations = () => {
  const [consultations, setConsultations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);

  const fetchConsultations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/vet-consultations');
      setConsultations(response.data);
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors de la récupération des consultations');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const response = await axios.get('/api/vet-consultations/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques:', err);
    }
  }, []);

  const addConsultation = useCallback(async (consultationData) => {
    try {
      const response = await axios.post('/api/vet-consultations', consultationData);
      await fetchConsultations();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de l\'ajout de la consultation');
    }
  }, [fetchConsultations, fetchStats]);

  const updateConsultation = useCallback(async (id, consultationData) => {
    try {
      const response = await axios.put(`/api/vet-consultations/${id}`, consultationData);
      await fetchConsultations();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la mise à jour de la consultation');
    }
  }, [fetchConsultations, fetchStats]);

  const deleteConsultation = useCallback(async (id) => {
    try {
      await axios.delete(`/api/vet-consultations/${id}`);
      await fetchConsultations();
      await fetchStats();
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la suppression de la consultation');
    }
  }, [fetchConsultations, fetchStats]);

  const addPrescription = useCallback(async (consultationId, prescriptionData) => {
    try {
      const response = await axios.post(`/api/vet-consultations/${consultationId}/prescriptions`, prescriptionData);
      await fetchConsultations();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de l\'ajout de la prescription');
    }
  }, [fetchConsultations]);

  const addSuiviTraitement = useCallback(async (consultationId, suiviData) => {
    try {
      const response = await axios.post(`/api/vet-consultations/${consultationId}/suivi`, suiviData);
      await fetchConsultations();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de l\'ajout du suivi de traitement');
    }
  }, [fetchConsultations]);

  const getConsultationStatus = useCallback((consultation) => {
    const now = new Date();
    const consultDate = new Date(consultation.date_consultation);

    if (consultation.statut === 'terminee') return 'terminée';
    if (consultDate > now) return 'planifiée';
    return 'en cours';
  }, []);

  const getPriorityLevel = useCallback((consultation) => {
    if (consultation.urgence) return 'haute';
    if (consultation.symptomes?.includes('grave')) return 'moyenne';
    return 'normale';
  }, []);

  useEffect(() => {
    fetchConsultations();
    fetchStats();
  }, [fetchConsultations, fetchStats]);

  return {
    consultations,
    loading,
    error,
    stats,
    fetchConsultations,
    fetchStats,
    addConsultation,
    updateConsultation,
    deleteConsultation,
    addPrescription,
    addSuiviTraitement,
    getConsultationStatus,
    getPriorityLevel
  };
};

export default useVetConsultations;
