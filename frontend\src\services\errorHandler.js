/**
 * Service centralisé de gestion des erreurs
 * Gère les erreurs réseau, de connectivité et d'API de manière cohérente
 */

// Types d'erreurs
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  TIMEOUT: 'TIMEOUT',
  SERVER: 'SERVER',
  AUTH: 'AUTH',
  VALIDATION: 'VALIDATION',
  NOT_FOUND: 'NOT_FOUND',
  FORBIDDEN: 'FORBIDDEN',
  RATE_LIMIT: 'RATE_LIMIT',
  UNKNOWN: 'UNKNOWN'
};

// Codes d'erreur personnalisés
export const ERROR_CODES = {
  CONNECTION_LOST: 'CONNECTION_LOST',
  SERVER_UNAVAILABLE: 'SERVER_UNAVAILABLE',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  RATE_LIMITED: 'RATE_LIMITED'
};

// Messages d'erreur par défaut
const DEFAULT_ERROR_MESSAGES = {
  [ERROR_CODES.CONNECTION_LOST]: 'Connexion internet perdue. Vérifiez votre connexion réseau.',
  [ERROR_CODES.SERVER_UNAVAILABLE]: 'Serveur temporairement indisponible. Veuillez réessayer dans quelques instants.',
  [ERROR_CODES.REQUEST_TIMEOUT]: 'La requête a pris trop de temps. Vérifiez votre connexion.',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Votre session a expiré. Veuillez vous reconnecter.',
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Identifiants incorrects. Vérifiez votre email et mot de passe.',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 'Vous n\'avez pas les permissions nécessaires pour cette action.',
  [ERROR_CODES.RESOURCE_NOT_FOUND]: 'La ressource demandée est introuvable.',
  [ERROR_CODES.VALIDATION_FAILED]: 'Les données saisies ne sont pas valides.',
  [ERROR_CODES.RATE_LIMITED]: 'Trop de requêtes. Veuillez patienter avant de réessayer.'
};

/**
 * Classe pour représenter une erreur standardisée
 */
export class StandardError extends Error {
  constructor(type, code, message, originalError = null, details = {}) {
    super(message);
    this.name = 'StandardError';
    this.type = type;
    this.code = code;
    this.originalError = originalError;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.userMessage = message;
    this.retryable = this.isRetryable();
  }

  isRetryable() {
    const retryableCodes = [
      ERROR_CODES.CONNECTION_LOST,
      ERROR_CODES.SERVER_UNAVAILABLE,
      ERROR_CODES.REQUEST_TIMEOUT
    ];
    return retryableCodes.includes(this.code);
  }

  toJSON() {
    return {
      type: this.type,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      timestamp: this.timestamp,
      retryable: this.retryable,
      details: this.details
    };
  }
}

/**
 * Service de gestion des erreurs
 */
class ErrorHandlerService {
  constructor() {
    this.errorListeners = [];
    this.connectionStatus = navigator.onLine;
    this.setupConnectionMonitoring();
  }

  /**
   * Surveiller l'état de la connexion
   */
  setupConnectionMonitoring() {
    window.addEventListener('online', () => {
      this.connectionStatus = true;
      this.notifyConnectionRestored();
    });

    window.addEventListener('offline', () => {
      this.connectionStatus = false;
      this.notifyConnectionLost();
    });
  }

  /**
   * Ajouter un listener pour les erreurs
   */
  addErrorListener(listener) {
    this.errorListeners.push(listener);
  }

  /**
   * Supprimer un listener
   */
  removeErrorListener(listener) {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  /**
   * Notifier tous les listeners d'une erreur
   */
  notifyError(error) {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('Erreur dans le listener d\'erreur:', err);
      }
    });
  }

  /**
   * Notifier la perte de connexion
   */
  notifyConnectionLost() {
    const error = new StandardError(
      ERROR_TYPES.NETWORK,
      ERROR_CODES.CONNECTION_LOST,
      DEFAULT_ERROR_MESSAGES[ERROR_CODES.CONNECTION_LOST]
    );
    this.notifyError(error);
  }

  /**
   * Notifier la restauration de connexion
   */
  notifyConnectionRestored() {
    // Émettre un événement personnalisé
    window.dispatchEvent(new CustomEvent('connection:restored', {
      detail: { timestamp: new Date().toISOString() }
    }));
  }

  /**
   * Analyser et standardiser une erreur Axios
   */
  handleAxiosError(error) {
    let standardError;

    // Vérifier l'état de la connexion
    if (!this.connectionStatus) {
      standardError = new StandardError(
        ERROR_TYPES.NETWORK,
        ERROR_CODES.CONNECTION_LOST,
        DEFAULT_ERROR_MESSAGES[ERROR_CODES.CONNECTION_LOST],
        error
      );
    }
    // Erreur de timeout
    else if (error.code === 'ECONNABORTED' || error.code === 'TIMEOUT') {
      standardError = new StandardError(
        ERROR_TYPES.TIMEOUT,
        ERROR_CODES.REQUEST_TIMEOUT,
        DEFAULT_ERROR_MESSAGES[ERROR_CODES.REQUEST_TIMEOUT],
        error
      );
    }
    // Erreur réseau (pas de réponse du serveur)
    else if (error.code === 'NETWORK_ERROR' || !error.response) {
      standardError = new StandardError(
        ERROR_TYPES.NETWORK,
        ERROR_CODES.SERVER_UNAVAILABLE,
        DEFAULT_ERROR_MESSAGES[ERROR_CODES.SERVER_UNAVAILABLE],
        error
      );
    }
    // Erreurs avec réponse du serveur
    else if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          standardError = new StandardError(
            ERROR_TYPES.AUTH,
            ERROR_CODES.TOKEN_EXPIRED,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.TOKEN_EXPIRED],
            error,
            { statusCode: status }
          );
          break;

        case 403:
          standardError = new StandardError(
            ERROR_TYPES.FORBIDDEN,
            ERROR_CODES.INSUFFICIENT_PERMISSIONS,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.INSUFFICIENT_PERMISSIONS],
            error,
            { statusCode: status }
          );
          break;

        case 404:
          standardError = new StandardError(
            ERROR_TYPES.NOT_FOUND,
            ERROR_CODES.RESOURCE_NOT_FOUND,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.RESOURCE_NOT_FOUND],
            error,
            { statusCode: status }
          );
          break;

        case 422:
          standardError = new StandardError(
            ERROR_TYPES.VALIDATION,
            ERROR_CODES.VALIDATION_FAILED,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.VALIDATION_FAILED],
            error,
            { statusCode: status, validationErrors: data?.errors }
          );
          break;

        case 429:
          standardError = new StandardError(
            ERROR_TYPES.RATE_LIMIT,
            ERROR_CODES.RATE_LIMITED,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.RATE_LIMITED],
            error,
            { statusCode: status, retryAfter: error.response.headers['retry-after'] }
          );
          break;

        case 500:
        case 502:
        case 503:
        case 504:
          standardError = new StandardError(
            ERROR_TYPES.SERVER,
            ERROR_CODES.SERVER_UNAVAILABLE,
            data?.message || DEFAULT_ERROR_MESSAGES[ERROR_CODES.SERVER_UNAVAILABLE],
            error,
            { statusCode: status }
          );
          break;

        default:
          standardError = new StandardError(
            ERROR_TYPES.UNKNOWN,
            'HTTP_' + status,
            data?.message || `Erreur HTTP ${status}`,
            error,
            { statusCode: status }
          );
      }
    }
    // Erreur inconnue
    else {
      standardError = new StandardError(
        ERROR_TYPES.UNKNOWN,
        'UNKNOWN_ERROR',
        error.message || 'Une erreur inattendue s\'est produite',
        error
      );
    }

    // Logger l'erreur
    this.logError(standardError);

    // Notifier les listeners
    this.notifyError(standardError);

    return standardError;
  }

  /**
   * Logger une erreur
   */
  logError(error) {
    const logData = {
      timestamp: error.timestamp,
      type: error.type,
      code: error.code,
      message: error.message,
      url: error.originalError?.config?.url,
      method: error.originalError?.config?.method,
      userAgent: navigator.userAgent,
      connectionStatus: this.connectionStatus
    };

    console.error('🚨 Erreur capturée:', logData);

    // En production, envoyer les erreurs à un service de monitoring
    if (process.env.NODE_ENV === 'production') {
      this.sendErrorToMonitoring(logData);
    }
  }

  /**
   * Envoyer l'erreur à un service de monitoring (à implémenter)
   */
  sendErrorToMonitoring(errorData) {
    // TODO: Intégrer avec un service comme Sentry, LogRocket, etc.
    console.log('📊 Envoi de l\'erreur au service de monitoring:', errorData);
  }

  /**
   * Vérifier si une erreur est récupérable
   */
  isRecoverable(error) {
    return error instanceof StandardError && error.retryable;
  }

  /**
   * Obtenir un message d'erreur convivial
   */
  getUserFriendlyMessage(error) {
    if (error instanceof StandardError) {
      return error.userMessage;
    }
    return 'Une erreur inattendue s\'est produite. Veuillez réessayer.';
  }
}

// Instance singleton
const errorHandler = new ErrorHandlerService();

export default errorHandler;
