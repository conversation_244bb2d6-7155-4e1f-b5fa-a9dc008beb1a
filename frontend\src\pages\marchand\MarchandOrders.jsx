import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot
} from '@mui/lab';
import { Bar } from 'react-chartjs-2';
import axios from 'axios';

const steps = ['En attente', 'Confirmée', 'En préparation', 'Expédiée', 'Livrée'];

const MarchandOrders = () => {
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [transporteur, setTransporteur] = useState('');
  const [trackingInfo, setTrackingInfo] = useState('');
  const [statsData, setStatsData] = useState({
    labels: [],
    datasets: [{
      label: 'Chi<PERSON>re d\'affaires par jour',
      data: [],
      backgroundColor: 'rgba(54, 162, 235, 0.5)'
    }]
  });

  useEffect(() => {
    fetchOrders();
    fetchStats();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await axios.get('/api/marchand/orders');
      setOrders(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des commandes:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/marchand/orders/stats');
      setStatsData({
        labels: response.data.dates,
        datasets: [{
          label: 'Chiffre d\'affaires par jour',
          data: response.data.revenues,
          backgroundColor: 'rgba(54, 162, 235, 0.5)'
        }]
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const handleStatusChange = async (orderId, newStatus) => {
    try {
      await axios.put(`/api/marchand/orders/${orderId}/status`, {
        status: newStatus
      });
      fetchOrders();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
    }
  };

  const handleTransporteurSelect = async () => {
    try {
      // Intégration avec l'API du transporteur
      let trackingResponse;
      if (transporteur === 'jumia') {
        trackingResponse = await axios.post('/api/marchand/shipping/jumia', {
          orderId: selectedOrder.id,
          // Autres détails nécessaires pour Jumia
        });
      } else if (transporteur === 'yassir') {
        trackingResponse = await axios.post('/api/marchand/shipping/yassir', {
          orderId: selectedOrder.id,
          // Autres détails nécessaires pour Yassir
        });
      }

      // Mise à jour du numéro de suivi
      await axios.put(`/api/marchand/orders/${selectedOrder.id}/tracking`, {
        tracking_number: trackingResponse.data.tracking_number,
        transporteur: transporteur
      });

      setTrackingInfo(trackingResponse.data.tracking_number);
      handleStatusChange(selectedOrder.id, 'expédiée');
      setOpenDialog(false);
    } catch (error) {
      console.error('Erreur lors de l\'intégration du transporteur:', error);
    }
  };

  const getStepColor = (orderStatus, step) => {
    const statusIndex = steps.indexOf(orderStatus);
    const stepIndex = steps.indexOf(step);
    
    if (stepIndex < statusIndex) return 'success';
    if (stepIndex === statusIndex) return 'primary';
    return 'grey';
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Grid container spacing={3}>
        {/* Statistiques */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Tableau de bord financier
            </Typography>
            <Box sx={{ height: 300 }}>
              <Bar
                data={statsData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true
                    }
                  }
                }}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Liste des commandes */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Commandes en cours
          </Typography>
          <Grid container spacing={2}>
            {orders.map((order) => (
              <Grid item xs={12} md={6} lg={4} key={order.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Commande #{order.numero_commande}
                    </Typography>
                    <Typography color="textSecondary">
                      Client: {order.client.nom} {order.client.prenom}
                    </Typography>
                    <Typography>
                      Montant: {order.montant_total} DA
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Stepper activeStep={steps.indexOf(order.statut)} alternativeLabel>
                        {steps.map((label) => (
                          <Step key={label}>
                            <StepLabel>{label}</StepLabel>
                          </Step>
                        ))}
                      </Stepper>
                    </Box>
                    {order.tracking_number && (
                      <Typography sx={{ mt: 2 }}>
                        Numéro de suivi: {order.tracking_number}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions>
                    {order.statut === 'confirmée' && (
                      <Button
                        color="primary"
                        onClick={() => {
                          setSelectedOrder(order);
                          setOpenDialog(true);
                        }}
                      >
                        Choisir transporteur
                      </Button>
                    )}
                    {order.statut === 'en_préparation' && (
                      <Button
                        color="primary"
                        onClick={() => handleStatusChange(order.id, 'expédiée')}
                      >
                        Marquer comme expédié
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>

      {/* Dialog pour le choix du transporteur */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Choisir un transporteur</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Transporteur</InputLabel>
            <Select
              value={transporteur}
              label="Transporteur"
              onChange={(e) => setTransporteur(e.target.value)}
            >
              <MenuItem value="jumia">Jumia Express</MenuItem>
              <MenuItem value="yassir">Yassir Express</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
          <Button onClick={handleTransporteurSelect} variant="contained">
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MarchandOrders;