'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // First check if the column already exists
      const tableInfo = await queryInterface.describeTable('Users');
      if (!tableInfo.firebase_uid) {
        await queryInterface.addColumn('Users', 'firebase_uid', {
          type: Sequelize.STRING(255),
          unique: true,
          allowNull: true
        });
        console.log('Successfully added firebase_uid column to Users table');
      } else {
        console.log('firebase_uid column already exists in Users table');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      await queryInterface.removeColumn('Users', 'firebase_uid');
      console.log('Successfully removed firebase_uid column from Users table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
