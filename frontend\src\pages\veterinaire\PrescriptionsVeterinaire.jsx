import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Fab,
  Tooltip,
  Pagination,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  Autocomplete,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  LocalHospital as HospitalIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  Print as PrintIcon,
  MedicalServices as MedicalIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  LocalPharmacy as PharmacyIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import axiosInstance from '../../utils/axiosConfig';

const PrescriptionsVeterinaire = () => {
  const [prescriptions, setPrescriptions] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [medicaments, setMedicaments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState({
    eleveur_id: '',
    animal_type: '',
    diagnostic: '',
    medicaments: [{
      nom: '',
      dosage: '',
      frequence: '',
      duree: '',
      instructions: ''
    }],
    date_prescription: new Date(),
    date_fin_traitement: null,
    notes: '',
    statut: 'active',
    suivi_requis: false,
    cout_estime: ''
  });
  const [stats, setStats] = useState({
    total_prescriptions: 0,
    prescriptions_actives: 0,
    prescriptions_mois: 0,
    eleveurs_traites: 0,
    prescriptions_expirees: 0,
    prescriptions_suivi_requis: 0
  });

  useEffect(() => {
    fetchPrescriptions();
    fetchEleveurs();
    fetchMedicaments();
    fetchStats();
  }, []);

  const fetchPrescriptions = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get('/api/veterinaire/prescriptions');
      setPrescriptions(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors de la récupération des prescriptions');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchEleveurs = async () => {
    try {
      const response = await axiosInstance.get('/api/veterinaire/eleveurs');
      setEleveurs(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des éleveurs:', err);
    }
  };

  const fetchMedicaments = async () => {
    try {
      const response = await axiosInstance.get('/api/veterinaire/medicaments');
      setMedicaments(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des médicaments:', err);
      // Données par défaut si l'API n'est pas disponible
      setMedicaments([
        { id: 1, nom: 'Amoxicilline', type: 'Antibiotique' },
        { id: 2, nom: 'Tylosine', type: 'Antibiotique' },
        { id: 3, nom: 'Enrofloxacine', type: 'Antibiotique' },
        { id: 4, nom: 'Vitamines A+D3+E', type: 'Vitamine' },
        { id: 5, nom: 'Coccidiostatique', type: 'Antiparasitaire' },
      ]);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axiosInstance.get('/api/veterinaire/prescriptions/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques:', err);
    }
  };

  const handleOpenDialog = (prescription = null) => {
    if (prescription) {
      setSelectedPrescription(prescription);
      setFormData({
        eleveur_id: prescription.eleveur_id,
        animal_type: prescription.animal_type,
        diagnostic: prescription.diagnostic,
        medicaments: prescription.medicaments || [{
          nom: '',
          dosage: '',
          frequence: '',
          duree: '',
          instructions: ''
        }],
        date_prescription: new Date(prescription.date_prescription),
        date_fin_traitement: prescription.date_fin_traitement ? new Date(prescription.date_fin_traitement) : null,
        notes: prescription.notes || '',
        statut: prescription.statut
      });
    } else {
      setSelectedPrescription(null);
      setFormData({
        eleveur_id: '',
        animal_type: '',
        diagnostic: '',
        medicaments: [{
          nom: '',
          dosage: '',
          frequence: '',
          duree: '',
          instructions: ''
        }],
        date_prescription: new Date(),
        date_fin_traitement: null,
        notes: '',
        statut: 'active'
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedPrescription(null);
  };

  const handleAddMedicament = () => {
    setFormData({
      ...formData,
      medicaments: [...formData.medicaments, {
        nom: '',
        dosage: '',
        frequence: '',
        duree: '',
        instructions: ''
      }]
    });
  };

  const handleRemoveMedicament = (index) => {
    const newMedicaments = formData.medicaments.filter((_, i) => i !== index);
    setFormData({ ...formData, medicaments: newMedicaments });
  };

  const handleMedicamentChange = (index, field, value) => {
    const newMedicaments = [...formData.medicaments];
    newMedicaments[index][field] = value;
    setFormData({ ...formData, medicaments: newMedicaments });
  };

  const handleSubmit = async () => {
    try {
      const prescriptionData = {
        ...formData,
        medicaments: JSON.stringify(formData.medicaments)
      };

      if (selectedPrescription) {
        await axiosInstance.put(`/api/veterinaire/prescriptions/${selectedPrescription.id}`, prescriptionData);
      } else {
        await axiosInstance.post('/api/veterinaire/prescriptions', prescriptionData);
      }

      fetchPrescriptions();
      fetchStats();
      handleCloseDialog();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la prescription');
      console.error('Erreur:', err);
    }
  };

  const handleMarkAsFinished = async (id) => {
    try {
      await axiosInstance.patch(`/api/veterinaire/prescriptions/${id}/finish`);
      fetchPrescriptions();
      fetchStats();
    } catch (err) {
      setError('Erreur lors de la finalisation de la prescription');
      console.error('Erreur:', err);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette prescription ?')) {
      try {
        await axiosInstance.delete(`/api/veterinaire/prescriptions/${id}`);
        fetchPrescriptions();
        fetchStats();
      } catch (err) {
        setError('Erreur lors de la suppression de la prescription');
        console.error('Erreur:', err);
      }
    }
  };

  const handlePrint = (prescription) => {
    // Logique d'impression de la prescription
    const printWindow = window.open('', '_blank');
    const medicamentsList = typeof prescription.medicaments === 'string' 
      ? JSON.parse(prescription.medicaments) 
      : prescription.medicaments || [];
    
    const printContent = `
      <html>
        <head>
          <title>Prescription Vétérinaire</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .section { margin: 20px 0; }
            .medicament { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>PRESCRIPTION VÉTÉRINAIRE</h1>
            <p>Dr. ${prescription.veterinaire_nom || 'Vétérinaire'}</p>
            <p>Date: ${new Date(prescription.date_prescription).toLocaleDateString('fr-FR')}</p>
          </div>
          <div class="section">
            <h3>Informations Patient</h3>
            <p><strong>Éleveur:</strong> ${prescription.eleveur_nom || 'N/A'}</p>
            <p><strong>Type d'animal:</strong> ${prescription.animal_type}</p>
            <p><strong>Diagnostic:</strong> ${prescription.diagnostic}</p>
          </div>
          <div class="section">
            <h3>Prescription</h3>
            ${medicamentsList.map(med => `
              <div class="medicament">
                <p><strong>Médicament:</strong> ${med.nom}</p>
                <p><strong>Dosage:</strong> ${med.dosage}</p>
                <p><strong>Fréquence:</strong> ${med.frequence}</p>
                <p><strong>Durée:</strong> ${med.duree}</p>
                <p><strong>Instructions:</strong> ${med.instructions}</p>
              </div>
            `).join('')}
          </div>
          <div class="section">
            <p><strong>Notes:</strong> ${prescription.notes || 'Aucune note'}</p>
          </div>
        </body>
      </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'active': return 'success';
      case 'terminee': return 'info';
      case 'annulee': return 'error';
      case 'expiree': return 'error';
      case 'en_attente': return 'info';
      default: return 'default';
    }
  };

  const getUrgenceColor = (urgence) => {
    switch (urgence) {
      case 'faible': return 'success';
      case 'normale': return 'default';
      case 'elevee': return 'warning';
      case 'critique': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Mes Prescriptions
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MedicalIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Prescriptions
                    </Typography>
                    <Typography variant="h5">
                      {stats.total_prescriptions}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PharmacyIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Actives
                    </Typography>
                    <Typography variant="h5">
                      {stats.prescriptions_actives}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WarningIcon color="error" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Expirées
                    </Typography>
                    <Typography variant="h5">
                      {stats.prescriptions_expirees}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MedicalIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Ce mois
                    </Typography>
                    <Typography variant="h5">
                      {stats.prescriptions_mois}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CheckIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Suivi Requis
                    </Typography>
                    <Typography variant="h5">
                      {stats.prescriptions_suivi_requis}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PersonIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Éleveurs traités
                    </Typography>
                    <Typography variant="h5">
                      {stats.eleveurs_traites}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Actions */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Prescription
          </Button>
        </Box>

        {/* Table des prescriptions */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Éleveur</TableCell>
                <TableCell>Animal</TableCell>
                <TableCell>Diagnostic</TableCell>
                <TableCell>Médicaments</TableCell>
                <TableCell>Statut</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {prescriptions.map((prescription) => {
                const medicamentsList = typeof prescription.medicaments === 'string' 
                  ? JSON.parse(prescription.medicaments) 
                  : prescription.medicaments || [];
                
                return (
                  <TableRow key={prescription.id}>
                    <TableCell>
                      {new Date(prescription.date_prescription).toLocaleDateString('fr-FR')}
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {prescription.eleveur_nom || 'N/A'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          ID: {prescription.eleveur_id}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={<PetsIcon />}
                        label={prescription.animal_type}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {prescription.diagnostic}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        {medicamentsList.slice(0, 2).map((med, index) => (
                          <Chip
                            key={index}
                            label={med.nom}
                            size="small"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                        {medicamentsList.length > 2 && (
                          <Typography variant="caption" color="textSecondary">
                            +{medicamentsList.length - 2} autres
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={prescription.statut}
                        color={getStatutColor(prescription.statut)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handlePrint(prescription)}
                        title="Imprimer"
                      >
                        <PrintIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(prescription)}
                        title="Modifier"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(prescription.id)}
                        title="Supprimer"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Dialog pour ajouter/modifier une prescription */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>
            {selectedPrescription ? 'Modifier la prescription' : 'Nouvelle prescription'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Éleveur</InputLabel>
                  <Select
                    value={formData.eleveur_id}
                    onChange={(e) => setFormData({ ...formData, eleveur_id: e.target.value })}
                  >
                    {eleveurs.map((eleveur) => (
                      <MenuItem key={eleveur.id} value={eleveur.id}>
                        {eleveur.first_name} {eleveur.last_name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Type d'animal</InputLabel>
                  <Select
                    value={formData.animal_type}
                    onChange={(e) => setFormData({ ...formData, animal_type: e.target.value })}
                  >
                    <MenuItem value="poulets">Poulets</MenuItem>
                    <MenuItem value="poules">Poules</MenuItem>
                    <MenuItem value="coqs">Coqs</MenuItem>
                    <MenuItem value="canards">Canards</MenuItem>
                    <MenuItem value="dindes">Dindes</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Diagnostic"
                  multiline
                  rows={2}
                  value={formData.diagnostic}
                  onChange={(e) => setFormData({ ...formData, diagnostic: e.target.value })}
                />
              </Grid>
              
              {/* Médicaments */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Médicaments prescrits
                </Typography>
                {formData.medicaments.map((medicament, index) => (
                  <Card key={index} sx={{ mb: 2, p: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Autocomplete
                          options={medicaments}
                          getOptionLabel={(option) => option.nom}
                          value={medicaments.find(m => m.nom === medicament.nom) || null}
                          onChange={(event, newValue) => {
                            handleMedicamentChange(index, 'nom', newValue ? newValue.nom : '');
                          }}
                          renderInput={(params) => (
                            <TextField {...params} label="Médicament" fullWidth />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Dosage"
                          value={medicament.dosage}
                          onChange={(e) => handleMedicamentChange(index, 'dosage', e.target.value)}
                          placeholder="ex: 1ml/kg"
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Fréquence"
                          value={medicament.frequence}
                          onChange={(e) => handleMedicamentChange(index, 'frequence', e.target.value)}
                          placeholder="ex: 2 fois/jour"
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Durée"
                          value={medicament.duree}
                          onChange={(e) => handleMedicamentChange(index, 'duree', e.target.value)}
                          placeholder="ex: 7 jours"
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Button
                          color="error"
                          onClick={() => handleRemoveMedicament(index)}
                          disabled={formData.medicaments.length === 1}
                        >
                          Supprimer
                        </Button>
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Instructions spéciales"
                          value={medicament.instructions}
                          onChange={(e) => handleMedicamentChange(index, 'instructions', e.target.value)}
                          placeholder="Instructions d'administration"
                        />
                      </Grid>
                    </Grid>
                  </Card>
                ))}
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddMedicament}
                  sx={{ mb: 2 }}
                >
                  Ajouter un médicament
                </Button>
              </Grid>

              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Date de prescription"
                  value={formData.date_prescription}
                  onChange={(date) => setFormData({ ...formData, date_prescription: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Date de fin de traitement"
                  value={formData.date_fin_traitement}
                  onChange={(date) => setFormData({ ...formData, date_fin_traitement: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={formData.statut}
                    onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="terminee">Terminée</MenuItem>
                    <MenuItem value="annulee">Annulée</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedPrescription ? 'Modifier' : 'Ajouter'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default PrescriptionsVeterinaire;