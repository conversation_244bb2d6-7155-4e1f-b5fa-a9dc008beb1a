/**
 * Service de base de données pour les tests
 * Ce service utilise la base de données PostgreSQL réelle au lieu des mocks
 */

import axios from 'axios';
import TEST_DB_CONFIG from '../config/testDatabase';
import { Pool } from 'pg';

// Créer une instance de Pool pour la connexion à la base de données
const pool = new Pool(TEST_DB_CONFIG);

// Service pour les tests des composants du Dashboard Éleveur
const testDbService = {
  // Méthode pour exécuter une requête SQL
  query: async (text, params) => {
    try {
      const result = await pool.query(text, params);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de l\'exécution de la requête SQL:', error);
      throw error;
    }
  },

  // Méthode pour nettoyer les données de test
  cleanTestData: async () => {
    try {
      // Supprimer les données de test des tables
      await pool.query('DELETE FROM alertes_stock WHERE titre LIKE \'%TEST%\'');
      await pool.query('DELETE FROM consultations WHERE notes LIKE \'%TEST%\'');
      await pool.query('DELETE FROM production_oeufs WHERE notes LIKE \'%TEST%\'');
      await pool.query('DELETE FROM poussins WHERE lot_numero LIKE \'%TEST%\'');
      return true;
    } catch (error) {
      console.error('Erreur lors du nettoyage des données de test:', error);
      throw error;
    }
  },

  // Méthode pour insérer des données de test pour les poussins
  insertTestPoussins: async (testData) => {
    try {
      const query = `
        INSERT INTO poussins (
          eleveur_id, lot_numero, race, souche, quantite_initiale, 
          quantite_actuelle, date_eclosion, date_arrivee, poids_moyen, 
          poids_objectif, gain_quotidien_moyen, taux_mortalite, 
          mortalite_cumulative, statut, type_elevage, batiment
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
        ) RETURNING *
      `;
      
      const result = await pool.query(query, [
        testData.eleveur_id,
        testData.lot_numero,
        testData.race,
        testData.souche,
        testData.quantite_initiale,
        testData.quantite_actuelle,
        testData.date_eclosion,
        testData.date_arrivee,
        testData.poids_moyen,
        testData.poids_objectif,
        testData.gain_quotidien_moyen,
        testData.taux_mortalite,
        testData.mortalite_cumulative,
        testData.statut,
        testData.type_elevage,
        testData.batiment
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Erreur lors de l\'insertion des données de test pour les poussins:', error);
      throw error;
    }
  },

  // Méthode pour insérer des données de test pour la production d'œufs
  insertTestProductionOeufs: async (testData) => {
    try {
      const query = `
        INSERT INTO production_oeufs (
          eleveur_id, volaille_id, date_production, nombre_poules, 
          oeufs_collectes, oeufs_vendables, oeufs_casses, oeufs_deformes, 
          oeufs_sales, oeufs_petits, poids_moyen_oeuf, notes
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
        ) RETURNING *
      `;
      
      const result = await pool.query(query, [
        testData.eleveur_id,
        testData.volaille_id,
        testData.date_production,
        testData.nombre_poules,
        testData.oeufs_collectes,
        testData.oeufs_vendables,
        testData.oeufs_casses,
        testData.oeufs_deformes,
        testData.oeufs_sales,
        testData.oeufs_petits,
        testData.poids_moyen_oeuf,
        testData.notes
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Erreur lors de l\'insertion des données de test pour la production d\'œufs:', error);
      throw error;
    }
  },

  // Méthode pour insérer des données de test pour les consultations vétérinaires
  insertTestConsultations: async (testData) => {
    try {
      const query = `
        INSERT INTO consultations (
          veterinaire_id, eleveur_id, diagnostic, notes, 
          created_at, updated_at, volaille_id, date, 
          symptomes, traitement, statut, cout
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
        ) RETURNING *
      `;
      
      const result = await pool.query(query, [
        testData.veterinaire_id,
        testData.eleveur_id,
        testData.diagnostic,
        testData.notes,
        testData.created_at,
        testData.updated_at,
        testData.volaille_id,
        testData.date,
        testData.symptomes,
        testData.traitement,
        testData.statut,
        testData.cout
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Erreur lors de l\'insertion des données de test pour les consultations:', error);
      throw error;
    }
  },

  // Méthode pour insérer des données de test pour les alertes de stock
  insertTestAlertesStock: async (testData) => {
    try {
      const query = `
        INSERT INTO alertes_stock (
          eleveur_id, type_alerte, priorite, statut, titre, 
          message, date_declenchement
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7
        ) RETURNING *
      `;
      
      const result = await pool.query(query, [
        testData.eleveur_id,
        testData.type_alerte,
        testData.priorite,
        testData.statut,
        testData.titre,
        testData.message,
        testData.date_declenchement
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Erreur lors de l\'insertion des données de test pour les alertes de stock:', error);
      throw error;
    }
  },

  // Méthode pour récupérer les poussins
  getPoussins: async (eleveurId) => {
    try {
      const query = 'SELECT * FROM poussins WHERE eleveur_id = $1';
      const result = await pool.query(query, [eleveurId]);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération des poussins:', error);
      throw error;
    }
  },

  // Méthode pour récupérer la production d'œufs
  getProductionOeufs: async (eleveurId) => {
    try {
      const query = 'SELECT * FROM production_oeufs WHERE eleveur_id = $1';
      const result = await pool.query(query, [eleveurId]);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération de la production d\'œufs:', error);
      throw error;
    }
  },

  // Méthode pour récupérer les consultations vétérinaires
  getConsultations: async (eleveurId) => {
    try {
      const query = 'SELECT * FROM consultations WHERE eleveur_id = $1';
      const result = await pool.query(query, [eleveurId]);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération des consultations:', error);
      throw error;
    }
  },

  // Méthode pour récupérer les alertes de stock
  getAlertesStock: async (eleveurId) => {
    try {
      const query = 'SELECT * FROM alertes_stock WHERE eleveur_id = $1';
      const result = await pool.query(query, [eleveurId]);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes de stock:', error);
      throw error;
    }
  },

  // Méthode pour fermer la connexion à la base de données
  close: async () => {
    try {
      await pool.end();
      return true;
    } catch (error) {
      console.error('Erreur lors de la fermeture de la connexion à la base de données:', error);
      throw error;
    }
  }
};

export default testDbService;

