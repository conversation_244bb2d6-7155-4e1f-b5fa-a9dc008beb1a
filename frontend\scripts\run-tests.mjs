import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Configuration des chemins
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.resolve(__dirname, '..');
const COVERAGE_DIR = path.join(ROOT_DIR, 'coverage');
const TEST_DIRS = [
  'src/components/dashboards/eleveur/__tests__',
  // Ajouter d'autres répertoires de tests si nécessaire
];

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
};

/**
 * Exécute une commande shell et affiche la sortie
 */
function runCommand(command, options = {}) {
  try {
    execSync(command, {
      stdio: 'inherit',
      cwd: ROOT_DIR,
      ...options,
    });
    return true;
  } catch (error) {
    console.error(`${colors.red}Erreur lors de l'exécution de la commande : ${command}${colors.reset}`);
    console.error(error.message);
    return false;
  }
}

/**
 * Nettoie les répertoires de couverture
 */
function cleanCoverage() {
  console.log(`\n${colors.bright}Nettoyage des rapports de couverture...${colors.reset}`);
  runCommand('npx rimraf coverage');
}

/**
 * Exécute les tests avec Jest
 */
function runTests() {
  console.log(`\n${colors.bright}Exécution des tests...${colors.reset}`);
  console.log('Répertoire de test:', TEST_DIRS[0]);

  const testCommand = 'node ./node_modules/jest/bin/jest.js';

  return runCommand(testCommand, {
    env: {
      ...process.env,
      NODE_ENV: 'test'
    }
  });
}

/**
 * Génère un rapport de couverture HTML
 */
function generateCoverageReport() {
  console.log(`\n${colors.bright}Génération du rapport de couverture...${colors.reset}`);
  runCommand('npx jest --coverage --coverageReporters="html" --coverageDirectory=coverage/html');
}

/**
 * Programme principal
 */
function main() {
  console.log(`${colors.bright}=== Exécution des tests du tableau de bord éleveur ===${colors.reset}\n`);

  // Nettoyage des anciens rapports
  cleanCoverage();

  // Exécution des tests
  const testsSucceeded = runTests();

  if (testsSucceeded) {
    // Génération du rapport de couverture
    generateCoverageReport();
    console.log(`\n${colors.green}✓ Tests terminés avec succès${colors.reset}`);
    console.log(`${colors.bright}Rapport de couverture disponible dans : ${colors.yellow}${COVERAGE_DIR}/html/index.html${colors.reset}`);
  } else {
    console.log(`\n${colors.red}✗ Certains tests ont échoué${colors.reset}`);
    process.exit(1);
  }
}

// Exécution
main();
