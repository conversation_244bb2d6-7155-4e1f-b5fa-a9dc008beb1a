import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Emergency as EmergencyIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useVetConsultations } from '../../../hooks/useVetConsultations';

const EMERGENCY_TYPES = {
  maladie_subite: 'Maladie subite',
  mortalite_elevee: 'Mortalité élevée',
  comportement_anormal: 'Comportement anormal',
  blessure: 'Blessure',
  autre: 'Autre urgence'
};

const PRIORITY_LEVELS = {
  critique: { color: 'error', label: 'Critique' },
  urgent: { color: 'warning', label: 'Urgent' },
  normal: { color: 'info', label: 'Normal' }
};

const EmergencyVet = () => {
  const { t } = useTranslation();
  const {
    emergencies,
    loading,
    error,
    addEmergency,
    updateEmergency,
    deleteEmergency,
    notifyVeterinarian
  } = useVetConsultations();

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedEmergency, setSelectedEmergency] = useState(null);
  const [formData, setFormData] = useState({
    type: '',
    description: '',
    lot_concerne: '',
    symptomes: '',
    priorite: 'normal',
    contact_immediat: false
  });
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', severity: 'success' });

  const handleOpenDialog = (emergency = null) => {
    if (emergency) {
      setSelectedEmergency(emergency);
      setFormData({
        type: emergency.type,
        description: emergency.description,
        lot_concerne: emergency.lot_concerne,
        symptomes: emergency.symptomes,
        priorite: emergency.priorite,
        contact_immediat: emergency.contact_immediat
      });
    } else {
      setSelectedEmergency(null);
      setFormData({
        type: '',
        description: '',
        lot_concerne: '',
        symptomes: '',
        priorite: 'normal',
        contact_immediat: false
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedEmergency(null);
    setFormData({
      type: '',
      description: '',
      lot_concerne: '',
      symptomes: '',
      priorite: 'normal',
      contact_immediat: false
    });
  };

  const handleChange = (event) => {
    const { name, value, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'contact_immediat' ? checked : value
    }));
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      const emergencyData = {
        ...formData,
        date_creation: new Date().toISOString(),
        statut: 'en_attente'
      };

      if (selectedEmergency) {
        await updateEmergency(selectedEmergency.id, emergencyData);
      } else {
        const newEmergency = await addEmergency(emergencyData);
        if (formData.contact_immediat) {
          await notifyVeterinarian(newEmergency.id);
        }
      }

      setSuccess(true);
      setNotification({
        show: true,
        message: selectedEmergency
          ? t('Urgence mise à jour avec succès')
          : t('Urgence signalée avec succès'),
        severity: 'success'
      });

      setTimeout(() => {
        handleCloseDialog();
        setSuccess(false);
        setNotification({ show: false, message: '', severity: 'success' });
      }, 2000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement de l\'urgence:', err);
      setNotification({
        show: true,
        message: t('Erreur lors de l\'enregistrement de l\'urgence'),
        severity: 'error'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (emergency) => {
    if (window.confirm(t('Êtes-vous sûr de vouloir supprimer cette urgence ?'))) {
      try {
        await deleteEmergency(emergency.id);
        setNotification({
          show: true,
          message: t('Urgence supprimée avec succès'),
          severity: 'success'
        });
      } catch (err) {
        console.error('Erreur lors de la suppression de l\'urgence:', err);
        setNotification({
          show: true,
          message: t('Erreur lors de la suppression de l\'urgence'),
          severity: 'error'
        });
      }
    }
  };

  const handleContactVet = async (emergency) => {
    try {
      await notifyVeterinarian(emergency.id);
      setNotification({
        show: true,
        message: t('Vétérinaire notifié avec succès'),
        severity: 'success'
      });
    } catch (err) {
      console.error('Erreur lors de la notification du vétérinaire:', err);
      setNotification({
        show: true,
        message: t('Erreur lors de la notification du vétérinaire'),
        severity: 'error'
      });
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h5">
                {t('Urgences Vétérinaires')}
              </Typography>
              <Button
                variant="contained"
                color="error"
                startIcon={<EmergencyIcon />}
                onClick={() => handleOpenDialog()}
              >
                {t('Signaler une Urgence')}
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {notification.show && (
              <Alert severity={notification.severity} sx={{ mb: 2 }}>
                {notification.message}
              </Alert>
            )}

            <List>
              {emergencies?.length === 0 ? (
                <Alert severity="info">
                  {t('Aucune urgence vétérinaire en cours')}
                </Alert>
              ) : (
                emergencies?.map((emergency) => (
                  <React.Fragment key={emergency.id}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle1">
                              {t(EMERGENCY_TYPES[emergency.type])}
                            </Typography>
                            <Chip
                              label={t(PRIORITY_LEVELS[emergency.priorite].label)}
                              color={PRIORITY_LEVELS[emergency.priorite].color}
                              size="small"
                            />
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" color="text.secondary">
                              {t('Lot concerné')}: {emergency.lot_concerne}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {t('Description')}: {emergency.description}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {t('Symptômes')}: {emergency.symptomes}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {t('Date de signalement')}: {new Date(emergency.date_creation).toLocaleString()}
                            </Typography>
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleContactVet(emergency)}
                          color="primary"
                          title={t('Contacter le vétérinaire')}
                        >
                          <PhoneIcon />
                        </IconButton>
                        <IconButton
                          edge="end"
                          onClick={() => handleOpenDialog(emergency)}
                          color="info"
                          title={t('Modifier l\'urgence')}
                        >
                          <MessageIcon />
                        </IconButton>
                        <IconButton
                          edge="end"
                          onClick={() => handleDelete(emergency)}
                          color="error"
                          title={t('Supprimer l\'urgence')}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))
              )}
            </List>
          </CardContent>
        </Card>
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedEmergency
            ? t('Modifier l\'urgence')
            : t('Signaler une nouvelle urgence')}
        </DialogTitle>
        <DialogContent>
          {success ? (
            <Alert severity="success">
              {selectedEmergency
                ? t('Urgence mise à jour avec succès')
                : t('Urgence signalée avec succès')}
            </Alert>
          ) : (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('Type d\'urgence')}
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                >
                  {Object.entries(EMERGENCY_TYPES).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {t(value)}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Lot concerné')}
                  name="lot_concerne"
                  value={formData.lot_concerne}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Description de l\'urgence')}
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  multiline
                  rows={3}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Symptômes observés')}
                  name="symptomes"
                  value={formData.symptomes}
                  onChange={handleChange}
                  multiline
                  rows={2}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('Niveau de priorité')}
                  name="priorite"
                  value={formData.priorite}
                  onChange={handleChange}
                  required
                >
                  {Object.entries(PRIORITY_LEVELS).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {t(value.label)}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('Contacter immédiatement le vétérinaire')}
                  name="contact_immediat"
                  value={formData.contact_immediat}
                  onChange={handleChange}
                >
                  <MenuItem value={true}>{t('Oui')}</MenuItem>
                  <MenuItem value={false}>{t('Non')}</MenuItem>
                </TextField>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('Annuler')}
          </Button>
          <Button
            onClick={handleSubmit}
            color="primary"
            disabled={submitting || !formData.type || !formData.lot_concerne || !formData.description || !formData.symptomes}
          >
            {submitting ? <CircularProgress size={24} /> : t('Enregistrer')}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default EmergencyVet;
