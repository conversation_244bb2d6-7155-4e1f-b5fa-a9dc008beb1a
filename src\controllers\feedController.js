const { 
  FeedItem, 
  FeedSupplier, 
  FeedStock, 
  FeedConsumptionLog, 
  FeedPlan, 
  FeedComposition, 
  FeedAlert,
  <PERSON>rm<PERSON>,
  <PERSON><PERSON><PERSON>,
  User
} = require('../models');
const { Op } = require('sequelize');
const { validationResult } = require('express-validator');

/**
 * Feed Management Controller
 * Handles all feed-related operations for the Poultry DZ platform
 */
class FeedController {
  
  // ==================== FEED ITEMS ====================
  
  /**
   * Get all feed items with filtering and pagination
   */
  async getFeedItems(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        category, 
        poultry_type, 
        status = 'active',
        search 
      } = req.query;
      
      const offset = (page - 1) * limit;
      const whereClause = { status };
      
      if (category) {
        whereClause.category = category;
      }
      
      if (poultry_type) {
        whereClause.poultry_types = {
          [Op.contains]: [poultry_type]
        };
      }
      
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { brand: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }
      
      const { count, rows } = await FeedItem.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: FeedComposition,
            as: 'compositions',
            where: { status: 'active' },
            required: false
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['name', 'ASC']]
      });
      
      res.json({
        success: true,
        data: {
          items: rows,
          pagination: {
            current_page: parseInt(page),
            total_pages: Math.ceil(count / limit),
            total_items: count,
            items_per_page: parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('Error fetching feed items:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des aliments',
        error: error.message
      });
    }
  }
  
  /**
   * Get a specific feed item by ID
   */
  async getFeedItem(req, res) {
    try {
      const { id } = req.params;
      
      const feedItem = await FeedItem.findByPk(id, {
        include: [
          {
            model: FeedComposition,
            as: 'compositions',
            where: { status: 'active' },
            required: false
          },
          {
            model: FeedStock,
            as: 'stock_entries',
            where: { status: 'active' },
            required: false,
            include: [{
              model: Ferme,
              as: 'farm',
              attributes: ['id', 'nom']
            }]
          }
        ]
      });
      
      if (!feedItem) {
        return res.status(404).json({
          success: false,
          message: 'Aliment non trouvé'
        });
      }
      
      res.json({
        success: true,
        data: feedItem
      });
    } catch (error) {
      console.error('Error fetching feed item:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'aliment',
        error: error.message
      });
    }
  }
  
  /**
   * Create a new feed item
   */
  async createFeedItem(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const feedItem = await FeedItem.create(req.body);
      
      res.status(201).json({
        success: true,
        message: 'Aliment créé avec succès',
        data: feedItem
      });
    } catch (error) {
      console.error('Error creating feed item:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de l\'aliment',
        error: error.message
      });
    }
  }
  
  /**
   * Update a feed item
   */
  async updateFeedItem(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const [updatedRows] = await FeedItem.update(req.body, {
        where: { id },
        returning: true
      });
      
      if (updatedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Aliment non trouvé'
        });
      }
      
      const updatedFeedItem = await FeedItem.findByPk(id);
      
      res.json({
        success: true,
        message: 'Aliment mis à jour avec succès',
        data: updatedFeedItem
      });
    } catch (error) {
      console.error('Error updating feed item:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour de l\'aliment',
        error: error.message
      });
    }
  }
  
  // ==================== FEED SUPPLIERS ====================
  
  /**
   * Get all feed suppliers
   */
  async getFeedSuppliers(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        status = 'active',
        search,
        delivery_zone 
      } = req.query;
      
      const offset = (page - 1) * limit;
      const whereClause = { status };
      
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { contact_person: { [Op.iLike]: `%${search}%` } }
        ];
      }
      
      if (delivery_zone) {
        whereClause.delivery_zones = {
          [Op.contains]: [delivery_zone]
        };
      }
      
      const { count, rows } = await FeedSupplier.findAndCountAll({
        where: whereClause,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['rating', 'DESC'], ['name', 'ASC']]
      });
      
      res.json({
        success: true,
        data: {
          suppliers: rows,
          pagination: {
            current_page: parseInt(page),
            total_pages: Math.ceil(count / limit),
            total_items: count,
            items_per_page: parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('Error fetching feed suppliers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des fournisseurs',
        error: error.message
      });
    }
  }
  
  /**
   * Create a new feed supplier
   */
  async createFeedSupplier(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const supplier = await FeedSupplier.create(req.body);
      
      res.status(201).json({
        success: true,
        message: 'Fournisseur créé avec succès',
        data: supplier
      });
    } catch (error) {
      console.error('Error creating feed supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du fournisseur',
        error: error.message
      });
    }
  }
  
  // ==================== FEED STOCK ====================
  
  /**
   * Get feed stock for a farm
   */
  async getFeedStock(req, res) {
    try {
      const { farm_id } = req.params;
      const { status = 'active', low_stock = false, expiring_soon = false } = req.query;
      
      // Verify farm access
      const farm = await Ferme.findByPk(farm_id);
      if (!farm) {
        return res.status(404).json({
          success: false,
          message: 'Ferme non trouvée'
        });
      }
      
      let stockEntries;
      
      if (low_stock === 'true') {
        stockEntries = await FeedStock.findLowStock(farm_id);
      } else if (expiring_soon === 'true') {
        const days = req.query.days || 30;
        stockEntries = await FeedStock.findExpiringSoon(days, farm_id);
      } else {
        stockEntries = await FeedStock.findByFarm(farm_id, {
          where: status ? { status } : {}
        });
      }
      
      res.json({
        success: true,
        data: stockEntries
      });
    } catch (error) {
      console.error('Error fetching feed stock:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du stock',
        error: error.message
      });
    }
  }
  
  /**
   * Add new feed stock entry
   */
  async addFeedStock(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const stockEntry = await FeedStock.create(req.body);
      
      const stockWithDetails = await FeedStock.findByPk(stockEntry.id, {
        include: [
          { model: FeedItem, as: 'feed_item' },
          { model: FeedSupplier, as: 'supplier' }
        ]
      });
      
      res.status(201).json({
        success: true,
        message: 'Stock ajouté avec succès',
        data: stockWithDetails
      });
    } catch (error) {
      console.error('Error adding feed stock:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'ajout du stock',
        error: error.message
      });
    }
  }
  
  /**
   * Update feed stock
   */
  async updateFeedStock(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const [updatedRows] = await FeedStock.update(req.body, {
        where: { id }
      });
      
      if (updatedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Stock non trouvé'
        });
      }
      
      const updatedStock = await FeedStock.findByPk(id, {
        include: [
          { model: FeedItem, as: 'feed_item' },
          { model: FeedSupplier, as: 'supplier' }
        ]
      });
      
      res.json({
        success: true,
        message: 'Stock mis à jour avec succès',
        data: updatedStock
      });
    } catch (error) {
      console.error('Error updating feed stock:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du stock',
        error: error.message
      });
    }
  }
  
  // ==================== FEED CONSUMPTION ====================
  
  /**
   * Get feed consumption logs
   */
  async getFeedConsumption(req, res) {
    try {
      const { farm_id } = req.params;
      const { start_date, end_date, feed_stock_id, poultry_batch_id } = req.query;
      
      let consumptionLogs;
      
      if (feed_stock_id) {
        consumptionLogs = await FeedConsumptionLog.findByFeedStock(feed_stock_id);
      } else if (poultry_batch_id) {
        consumptionLogs = await FeedConsumptionLog.findByPoultryBatch(poultry_batch_id);
      } else {
        consumptionLogs = await FeedConsumptionLog.findByFarm(
          farm_id, 
          start_date, 
          end_date
        );
      }
      
      res.json({
        success: true,
        data: consumptionLogs
      });
    } catch (error) {
      console.error('Error fetching feed consumption:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la consommation',
        error: error.message
      });
    }
  }
  
  /**
   * Record feed consumption
   */
  async recordFeedConsumption(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      // Add user who recorded this consumption
      const consumptionData = {
        ...req.body,
        recorded_by: req.user.id,
        recording_method: 'manual'
      };
      
      const consumptionLog = await FeedConsumptionLog.create(consumptionData);
      
      const logWithDetails = await FeedConsumptionLog.findByPk(consumptionLog.id, {
        include: [
          { 
            model: FeedStock, 
            as: 'feed_stock',
            include: [{ model: FeedItem, as: 'feed_item' }]
          },
          { model: Poussin, as: 'poultry_batch' },
          { model: User, as: 'recorder', attributes: ['id', 'nom', 'prenom'] }
        ]
      });
      
      res.status(201).json({
        success: true,
        message: 'Consommation enregistrée avec succès',
        data: logWithDetails
      });
    } catch (error) {
      console.error('Error recording feed consumption:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'enregistrement de la consommation',
        error: error.message
      });
    }
  }
  
  /**
   * Get feed consumption statistics
   */
  async getFeedConsumptionStats(req, res) {
    try {
      const { farm_id } = req.params;
      const { start_date, end_date } = req.query;
      
      const stats = await FeedConsumptionLog.getTotalConsumptionByFarm(
        farm_id, 
        start_date, 
        end_date
      );
      
      const averageFCR = await FeedConsumptionLog.getAverageFCR(
        farm_id, 
        start_date, 
        end_date
      );
      
      res.json({
        success: true,
        data: {
          ...stats,
          average_fcr: averageFCR
        }
      });
    } catch (error) {
      console.error('Error fetching consumption stats:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques',
        error: error.message
      });
    }
  }
  
  // ==================== FEED PLANS ====================
  
  /**
   * Get feed plans for a farm
   */
  async getFeedPlans(req, res) {
    try {
      const { farm_id } = req.params;
      const { status, is_template = false } = req.query;
      
      let feedPlans;
      
      if (is_template === 'true') {
        feedPlans = await FeedPlan.findTemplates();
      } else if (status === 'active') {
        feedPlans = await FeedPlan.findActiveByFarm(farm_id);
      } else {
        feedPlans = await FeedPlan.findByFarm(farm_id, {
          where: status ? { status } : {}
        });
      }
      
      res.json({
        success: true,
        data: feedPlans
      });
    } catch (error) {
      console.error('Error fetching feed plans:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des plans d\'alimentation',
        error: error.message
      });
    }
  }
  
  /**
   * Create a new feed plan
   */
  async createFeedPlan(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
      }
      
      const planData = {
        ...req.body,
        created_by: req.user.id
      };
      
      const feedPlan = await FeedPlan.create(planData);
      
      const planWithDetails = await FeedPlan.findByPk(feedPlan.id, {
        include: [
          { model: Poussin, as: 'poultry_batch' },
          { model: User, as: 'creator', attributes: ['id', 'nom', 'prenom'] }
        ]
      });
      
      res.status(201).json({
        success: true,
        message: 'Plan d\'alimentation créé avec succès',
        data: planWithDetails
      });
    } catch (error) {
      console.error('Error creating feed plan:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du plan d\'alimentation',
        error: error.message
      });
    }
  }
  
  /**
   * Activate a feed plan
   */
  async activateFeedPlan(req, res) {
    try {
      const { id } = req.params;
      
      const feedPlan = await FeedPlan.findByPk(id);
      if (!feedPlan) {
        return res.status(404).json({
          success: false,
          message: 'Plan d\'alimentation non trouvé'
        });
      }
      
      await feedPlan.activate();
      
      res.json({
        success: true,
        message: 'Plan d\'alimentation activé avec succès',
        data: feedPlan
      });
    } catch (error) {
      console.error('Error activating feed plan:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'activation du plan',
        error: error.message
      });
    }
  }
  
  // ==================== FEED ALERTS ====================
  
  /**
   * Get feed alerts for a farm
   */
  async getFeedAlerts(req, res) {
    try {
      const { farm_id } = req.params;
      const { severity, alert_type, status = 'active' } = req.query;
      
      let alerts;
      
      if (severity) {
        alerts = await FeedAlert.findBySeverity(severity, farm_id);
      } else if (alert_type) {
        alerts = await FeedAlert.findByType(alert_type, farm_id);
      } else {
        alerts = await FeedAlert.findByFarm(farm_id, {
          where: status ? { status } : {}
        });
      }
      
      res.json({
        success: true,
        data: alerts
      });
    } catch (error) {
      console.error('Error fetching feed alerts:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des alertes',
        error: error.message
      });
    }
  }
  
  /**
   * Resolve a feed alert
   */
  async resolveFeedAlert(req, res) {
    try {
      const { id } = req.params;
      const { resolution_notes } = req.body;
      
      const alert = await FeedAlert.findByPk(id);
      if (!alert) {
        return res.status(404).json({
          success: false,
          message: 'Alerte non trouvée'
        });
      }
      
      await alert.resolve(req.user.id, resolution_notes);
      
      res.json({
        success: true,
        message: 'Alerte résolue avec succès',
        data: alert
      });
    } catch (error) {
      console.error('Error resolving feed alert:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la résolution de l\'alerte',
        error: error.message
      });
    }
  }
  
  // ==================== ANALYTICS & REPORTS ====================
  
  /**
   * Get feed analytics dashboard data
   */
  async getFeedAnalytics(req, res) {
    try {
      const { farm_id } = req.params;
      const { period = '30' } = req.query;
      
      const days = parseInt(period);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      // Get consumption statistics
      const consumptionStats = await FeedConsumptionLog.getTotalConsumptionByFarm(
        farm_id, 
        startDate.toISOString().split('T')[0]
      );
      
      // Get stock value
      const stockValue = await FeedStock.getTotalValueByFarm(farm_id);
      
      // Get active alerts count
      const activeAlerts = await FeedAlert.count({
        where: {
          farm_id: farm_id,
          status: 'active'
        }
      });
      
      // Get low stock items count
      const lowStockItems = await FeedStock.findLowStock(farm_id);
      
      // Get average FCR
      const averageFCR = await FeedConsumptionLog.getAverageFCR(
        farm_id, 
        startDate.toISOString().split('T')[0]
      );
      
      res.json({
        success: true,
        data: {
          period_days: days,
          consumption: consumptionStats,
          stock_value: stockValue,
          active_alerts: activeAlerts,
          low_stock_items: lowStockItems.length,
          average_fcr: averageFCR,
          low_stock_details: lowStockItems
        }
      });
    } catch (error) {
      console.error('Error fetching feed analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des analyses',
        error: error.message
      });
    }
  }
  
  /**
   * Get feed recommendations
   */
  async getFeedRecommendations(req, res) {
    try {
      const { farm_id } = req.params;
      const { poultry_type, age_in_days, target_weight } = req.query;
      
      if (!poultry_type || !age_in_days) {
        return res.status(400).json({
          success: false,
          message: 'Type de volaille et âge requis'
        });
      }
      
      // Get recommended plan
      const recommendedPlan = await FeedPlan.getRecommendedPlan(
        poultry_type, 
        parseInt(age_in_days), 
        target_weight ? parseFloat(target_weight) : null
      );
      
      // Get suitable feed items
      const suitableFeedItems = await FeedItem.findByAgeRange(
        parseInt(age_in_days), 
        poultry_type
      );
      
      res.json({
        success: true,
        data: {
          recommended_plan: recommendedPlan,
          suitable_feed_items: suitableFeedItems
        }
      });
    } catch (error) {
      console.error('Error fetching feed recommendations:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des recommandations',
        error: error.message
      });
    }
  }
}

module.exports = new FeedController();