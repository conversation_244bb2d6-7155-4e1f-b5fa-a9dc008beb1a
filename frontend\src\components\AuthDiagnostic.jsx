import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Login as LoginIcon,
  AdminPanelSettings as AdminIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { decodeJWT, isTokenExpired, isUserAdmin, refreshTokenIfNeeded } from '../utils/authChecker';

const AuthDiagnostic = () => {
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [decodedToken, setDecodedToken] = useState(null);
  const [isExpired, setIsExpired] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(false);
  const [refreshed, setRefreshed] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (token) {
      const decoded = decodeJWT(token);
      setDecodedToken(decoded);
      setIsExpired(isTokenExpired(token));
      setIsAdmin(isUserAdmin(token));
    }
  }, [token]);

  const handleRefreshToken = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      if (data.token) {
        localStorage.setItem('token', data.token);
        setToken(data.token);
        setRefreshed(true);
        setTimeout(() => setRefreshed(false), 3000);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = () => {
    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto', my: 4 }}>
      <Typography variant="h5" gutterBottom>
        Diagnostic d'authentification
      </Typography>

      <Divider sx={{ my: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <AlertTitle>Erreur</AlertTitle>
          {error}
        </Alert>
      )}

      {refreshed && (
        <Alert severity="success" sx={{ mb: 2 }}>
          <AlertTitle>Succès</AlertTitle>
          Token rafraîchi avec succès!
        </Alert>
      )}

      <List>
        <ListItem>
          <ListItemIcon>
            {token ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
          </ListItemIcon>
          <ListItemText
            primary="Token JWT"
            secondary={token ? "Présent" : "Absent"}
          />
        </ListItem>

        <ListItem>
          <ListItemIcon>
            {!isExpired ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
          </ListItemIcon>
          <ListItemText
            primary="Validité du token"
            secondary={isExpired ? "Expiré" : "Valide"}
          />
        </ListItem>

        <ListItem>
          <ListItemIcon>
            {isAdmin ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
          </ListItemIcon>
          <ListItemText
            primary="Droits d'administrateur"
            secondary={isAdmin ? "Présents" : "Absents"}
          />
        </ListItem>

        {decodedToken && decodedToken.exp && (
          <ListItem>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary="Date d'expiration"
              secondary={new Date(decodedToken.exp * 1000).toLocaleString()}
            />
          </ListItem>
        )}
      </List>

      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'center' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={handleRefreshToken}
          disabled={loading || !token}
        >
          {loading ? <CircularProgress size={24} /> : "Rafraîchir le token"}
        </Button>

        <Button
          variant="outlined"
          color="secondary"
          startIcon={<LoginIcon />}
          onClick={handleLogin}
        >
          Se connecter
        </Button>
      </Box>

      {decodedToken && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Informations du token:
          </Typography>
          <pre style={{
            backgroundColor: '#f5f5f5',
            padding: '10px',
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {JSON.stringify(decodedToken, null, 2)}
          </pre>
        </Box>
      )}
    </Paper>
  );
};

export default AuthDiagnostic;
