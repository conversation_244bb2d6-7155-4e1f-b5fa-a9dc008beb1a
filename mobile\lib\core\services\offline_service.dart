import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/sync_models.dart';
import 'api_service.dart';
import 'storage_service.dart';

/// Service de gestion offline et synchronisation
class OfflineService extends ChangeNotifier {
  static final OfflineService _instance = OfflineService._internal();
  static OfflineService get instance => _instance;
  OfflineService._internal();

  Database? _database;
  bool _isOnline = true;
  Timer? _syncTimer;
  final List<PendingOperation> _pendingOperations = [];
  bool _isSyncing = false;

  // Getters
  bool get isOnline => _isOnline;
  bool get isSyncing => _isSyncing;
  List<PendingOperation> get pendingOperations => List.unmodifiable(_pendingOperations);

  /// Initialiser le service offline
  Future<void> initialize() async {
    await _initializeDatabase();
    await _loadPendingOperations();
    _startConnectivityMonitoring();
    _startPeriodicSync();
  }

  /// Initialiser la base de données locale
  Future<void> _initializeDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'poultray_offline.db');

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _upgradeTables,
    );
  }

  /// Créer les tables de la base de données
  Future<void> _createTables(Database db, int version) async {
    // Table des volailles
    await db.execute('''
      CREATE TABLE volailles (
        id TEXT PRIMARY KEY,
        nom TEXT NOT NULL,
        type_volaille TEXT NOT NULL,
        nombre_total INTEGER NOT NULL,
        nombre_actuel INTEGER NOT NULL,
        description TEXT,
        statut TEXT DEFAULT 'actif',
        eleveur_id TEXT NOT NULL,
        date_creation TEXT NOT NULL,
        date_modification TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        last_sync TEXT
      )
    ''');

    // Table de production
    await db.execute('''
      CREATE TABLE production (
        id TEXT PRIMARY KEY,
        volaille_id TEXT NOT NULL,
        type_production TEXT NOT NULL,
        quantite_produite INTEGER NOT NULL,
        unite TEXT NOT NULL,
        date_production TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        last_sync TEXT,
        FOREIGN KEY (volaille_id) REFERENCES volailles (id)
      )
    ''');

    // Table des consultations
    await db.execute('''
      CREATE TABLE consultations (
        id TEXT PRIMARY KEY,
        eleveur_id TEXT NOT NULL,
        veterinaire_id TEXT,
        volaille_id TEXT,
        date_consultation TEXT NOT NULL,
        diagnostic TEXT,
        traitement TEXT,
        notes TEXT,
        urgence INTEGER DEFAULT 0,
        statut TEXT DEFAULT 'programmee',
        created_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        last_sync TEXT
      )
    ''');

    // Table des opérations en attente
    await db.execute('''
      CREATE TABLE pending_operations (
        id TEXT PRIMARY KEY,
        operation_type TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0,
        last_retry TEXT
      )
    ''');

    // Table des fichiers en cache
    await db.execute('''
      CREATE TABLE cached_files (
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        local_path TEXT NOT NULL,
        file_type TEXT NOT NULL,
        size INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        last_accessed TEXT NOT NULL,
        expires_at TEXT
      )
    ''');

    // Index pour améliorer les performances
    await db.execute('CREATE INDEX idx_volailles_eleveur ON volailles(eleveur_id)');
    await db.execute('CREATE INDEX idx_production_volaille ON production(volaille_id)');
    await db.execute('CREATE INDEX idx_consultations_eleveur ON consultations(eleveur_id)');
    await db.execute('CREATE INDEX idx_pending_operations_type ON pending_operations(operation_type)');
  }

  /// Mettre à jour les tables lors des migrations
  Future<void> _upgradeTables(Database db, int oldVersion, int newVersion) async {
    // Gérer les migrations de base de données
    if (oldVersion < 2) {
      // Exemple de migration
      // await db.execute('ALTER TABLE volailles ADD COLUMN new_field TEXT');
    }
  }

  /// Sauvegarder une volaille localement
  Future<void> saveVolailleOffline(Map<String, dynamic> volailleData) async {
    if (_database == null) return;

    final id = volailleData['id'] ?? _generateId();
    volailleData['id'] = id;
    volailleData['date_modification'] = DateTime.now().toIso8601String();
    volailleData['is_synced'] = 0;

    await _database!.insert(
      'volailles',
      volailleData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Ajouter à la queue de synchronisation
    await _addPendingOperation(
      PendingOperation(
        id: _generateId(),
        operationType: _isOnline ? OperationType.update : OperationType.create,
        tableName: 'volailles',
        recordId: id,
        data: volailleData,
        createdAt: DateTime.now(),
      ),
    );

    notifyListeners();
  }

  /// Récupérer les volailles locales
  Future<List<Map<String, dynamic>>> getVolaillesOffline(String eleveurId) async {
    if (_database == null) return [];

    return await _database!.query(
      'volailles',
      where: 'eleveur_id = ?',
      whereArgs: [eleveurId],
      orderBy: 'date_creation DESC',
    );
  }

  /// Sauvegarder une production localement
  Future<void> saveProductionOffline(Map<String, dynamic> productionData) async {
    if (_database == null) return;

    final id = productionData['id'] ?? _generateId();
    productionData['id'] = id;
    productionData['created_at'] = DateTime.now().toIso8601String();
    productionData['is_synced'] = 0;

    await _database!.insert(
      'production',
      productionData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    await _addPendingOperation(
      PendingOperation(
        id: _generateId(),
        operationType: OperationType.create,
        tableName: 'production',
        recordId: id,
        data: productionData,
        createdAt: DateTime.now(),
      ),
    );

    notifyListeners();
  }

  /// Récupérer la production locale
  Future<List<Map<String, dynamic>>> getProductionOffline(String volailleId) async {
    if (_database == null) return [];

    return await _database!.query(
      'production',
      where: 'volaille_id = ?',
      whereArgs: [volailleId],
      orderBy: 'date_production DESC',
    );
  }

  /// Synchroniser les données avec le serveur
  Future<void> syncData() async {
    if (!_isOnline || _isSyncing) return;

    _isSyncing = true;
    notifyListeners();

    try {
      // Synchroniser les opérations en attente
      await _syncPendingOperations();
      
      // Synchroniser les données du serveur
      await _syncFromServer();
      
      // Nettoyer les anciennes données
      await _cleanupOldData();
      
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Synchroniser les opérations en attente
  Future<void> _syncPendingOperations() async {
    final operations = List<PendingOperation>.from(_pendingOperations);
    
    for (final operation in operations) {
      try {
        bool success = false;
        
        switch (operation.operationType) {
          case OperationType.create:
            success = await _syncCreate(operation);
            break;
          case OperationType.update:
            success = await _syncUpdate(operation);
            break;
          case OperationType.delete:
            success = await _syncDelete(operation);
            break;
        }
        
        if (success) {
          await _removePendingOperation(operation.id);
        } else {
          await _incrementRetryCount(operation.id);
        }
        
      } catch (e) {
        debugPrint('Erreur lors de la sync de l\'opération ${operation.id}: $e');
        await _incrementRetryCount(operation.id);
      }
    }
  }

  /// Synchroniser une création
  Future<bool> _syncCreate(PendingOperation operation) async {
    final endpoint = _getEndpointForTable(operation.tableName);
    final response = await ApiService.instance.post(endpoint, operation.data);
    
    if (response.success) {
      // Mettre à jour l'ID local avec l'ID du serveur
      await _updateLocalRecord(operation.tableName, operation.recordId, {
        'id': response.data['id'],
        'is_synced': 1,
        'last_sync': DateTime.now().toIso8601String(),
      });
      return true;
    }
    
    return false;
  }

  /// Synchroniser une mise à jour
  Future<bool> _syncUpdate(PendingOperation operation) async {
    final endpoint = '${_getEndpointForTable(operation.tableName)}/${operation.recordId}';
    final response = await ApiService.instance.put(endpoint, operation.data);
    
    if (response.success) {
      await _updateLocalRecord(operation.tableName, operation.recordId, {
        'is_synced': 1,
        'last_sync': DateTime.now().toIso8601String(),
      });
      return true;
    }
    
    return false;
  }

  /// Synchroniser une suppression
  Future<bool> _syncDelete(PendingOperation operation) async {
    final endpoint = '${_getEndpointForTable(operation.tableName)}/${operation.recordId}';
    final response = await ApiService.instance.delete(endpoint);
    
    if (response.success) {
      await _deleteLocalRecord(operation.tableName, operation.recordId);
      return true;
    }
    
    return false;
  }

  /// Synchroniser depuis le serveur
  Future<void> _syncFromServer() async {
    // Synchroniser les volailles
    await _syncTableFromServer('volailles', '/api/volailles');
    
    // Synchroniser la production
    await _syncTableFromServer('production', '/api/production');
    
    // Synchroniser les consultations
    await _syncTableFromServer('consultations', '/api/consultations');
  }

  /// Synchroniser une table depuis le serveur
  Future<void> _syncTableFromServer(String tableName, String endpoint) async {
    try {
      final lastSync = await StorageService.instance.getString('last_sync_$tableName');
      final params = lastSync != null ? {'since': lastSync} : <String, dynamic>{};
      
      final response = await ApiService.instance.get(endpoint, queryParameters: params);
      
      if (response.success && response.data is List) {
        final records = response.data as List;
        
        for (final record in records) {
          await _database!.insert(
            tableName,
            {
              ...record,
              'is_synced': 1,
              'last_sync': DateTime.now().toIso8601String(),
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
        
        // Mettre à jour le timestamp de dernière synchronisation
        await StorageService.instance.setString(
          'last_sync_$tableName',
          DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la sync de $tableName: $e');
    }
  }

  /// Ajouter une opération en attente
  Future<void> _addPendingOperation(PendingOperation operation) async {
    _pendingOperations.add(operation);
    
    if (_database != null) {
      await _database!.insert('pending_operations', {
        'id': operation.id,
        'operation_type': operation.operationType.name,
        'table_name': operation.tableName,
        'record_id': operation.recordId,
        'data': jsonEncode(operation.data),
        'created_at': operation.createdAt.toIso8601String(),
        'retry_count': operation.retryCount,
        'last_retry': operation.lastRetry?.toIso8601String(),
      });
    }
  }

  /// Supprimer une opération en attente
  Future<void> _removePendingOperation(String operationId) async {
    _pendingOperations.removeWhere((op) => op.id == operationId);
    
    if (_database != null) {
      await _database!.delete(
        'pending_operations',
        where: 'id = ?',
        whereArgs: [operationId],
      );
    }
  }

  /// Incrémenter le compteur de retry
  Future<void> _incrementRetryCount(String operationId) async {
    final index = _pendingOperations.indexWhere((op) => op.id == operationId);
    if (index != -1) {
      _pendingOperations[index] = _pendingOperations[index].copyWith(
        retryCount: _pendingOperations[index].retryCount + 1,
        lastRetry: DateTime.now(),
      );
      
      if (_database != null) {
        await _database!.update(
          'pending_operations',
          {
            'retry_count': _pendingOperations[index].retryCount,
            'last_retry': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [operationId],
        );
      }
    }
  }

  /// Charger les opérations en attente
  Future<void> _loadPendingOperations() async {
    if (_database == null) return;
    
    final results = await _database!.query('pending_operations');
    
    _pendingOperations.clear();
    for (final result in results) {
      _pendingOperations.add(PendingOperation(
        id: result['id'] as String,
        operationType: OperationType.values.firstWhere(
          (e) => e.name == result['operation_type'],
        ),
        tableName: result['table_name'] as String,
        recordId: result['record_id'] as String,
        data: jsonDecode(result['data'] as String),
        createdAt: DateTime.parse(result['created_at'] as String),
        retryCount: result['retry_count'] as int,
        lastRetry: result['last_retry'] != null 
          ? DateTime.parse(result['last_retry'] as String)
          : null,
      ));
    }
  }

  /// Surveiller la connectivité
  void _startConnectivityMonitoring() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (!wasOnline && _isOnline) {
        // Reconnecté - synchroniser automatiquement
        syncData();
      }
      
      notifyListeners();
    });
  }

  /// Démarrer la synchronisation périodique
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 15), (timer) {
      if (_isOnline) {
        syncData();
      }
    });
  }

  /// Méthodes utilitaires
  
  String _generateId() {
    return 'local_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  String _getEndpointForTable(String tableName) {
    switch (tableName) {
      case 'volailles':
        return '/api/volailles';
      case 'production':
        return '/api/production';
      case 'consultations':
        return '/api/consultations';
      default:
        return '/api/$tableName';
    }
  }

  Future<void> _updateLocalRecord(String tableName, String recordId, Map<String, dynamic> updates) async {
    if (_database != null) {
      await _database!.update(
        tableName,
        updates,
        where: 'id = ?',
        whereArgs: [recordId],
      );
    }
  }

  Future<void> _deleteLocalRecord(String tableName, String recordId) async {
    if (_database != null) {
      await _database!.delete(
        tableName,
        where: 'id = ?',
        whereArgs: [recordId],
      );
    }
  }

  Future<void> _cleanupOldData() async {
    // Supprimer les données anciennes (plus de 30 jours)
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    
    if (_database != null) {
      await _database!.delete(
        'cached_files',
        where: 'created_at < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );
    }
  }

  /// Nettoyer les ressources
  void dispose() {
    _syncTimer?.cancel();
    _database?.close();
    super.dispose();
  }
}
