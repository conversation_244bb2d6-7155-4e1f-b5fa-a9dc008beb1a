const request = require('supertest');
const express = require('express');
const { Pool } = require('pg');
require('dotenv').config();

// Import the enhanced routes
const veterinaireRoutes = require('./src/routes/veterinaireRoutes');
const marchandRoutes = require('./src/routes/marchandRoutes');
const eleveurRoutes = require('./src/routes/eleveurRoutes');

// Create test app
const app = express();
app.use(express.json());

// Mock authentication middleware for testing
const mockAuth = (req, res, next) => {
  req.user = {
    id: 1,
    role: 'admin',
    profile_id: 1
  };
  next();
};

app.use(mockAuth);

// Use the routes
app.use('/api/veterinaire', veterinaireRoutes);
app.use('/api/marchand', marchandRoutes);
app.use('/api/eleveurs', eleveurRoutes);

// Database connection for testing
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

describe('Enhanced Dashboard API Tests', () => {
  
  beforeAll(async () => {
    // Setup test data if needed
    console.log('Setting up test environment...');
  });

  afterAll(async () => {
    // Cleanup
    await pool.end();
  });

  describe('Veterinaire Dashboard Endpoints', () => {
    
    test('GET /api/veterinaire/dashboard should return dashboard data', async () => {
      const response = await request(app)
        .get('/api/veterinaire/dashboard')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('consultationsAVenir');
      expect(response.body.data).toHaveProperty('prescriptionsRecentes');
      expect(response.body.data).toHaveProperty('graphiques');
    });

    test('GET /api/veterinaire/notifications should return notifications', async () => {
      const response = await request(app)
        .get('/api/veterinaire/notifications')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('notifications');
      expect(Array.isArray(response.body.data.notifications)).toBe(true);
    });

    test('POST /api/veterinaire/consultations/quick should create consultation', async () => {
      const consultationData = {
        eleveur_id: 1,
        date_consultation: new Date().toISOString(),
        motif: 'Test consultation',
        urgence: false
      };

      const response = await request(app)
        .post('/api/veterinaire/consultations/quick')
        .send(consultationData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('POST /api/veterinaire/prescriptions/quick should create prescription', async () => {
      const prescriptionData = {
        eleveur_id: 1,
        volaille_id: 1,
        medicament: 'Test Medicine',
        dosage: '10mg',
        duree_traitement: '7 jours',
        instructions: 'Test instructions'
      };

      const response = await request(app)
        .post('/api/veterinaire/prescriptions/quick')
        .send(prescriptionData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });
  });

  describe('Marchand Dashboard Endpoints', () => {
    
    test('GET /api/marchand/dashboard should return dashboard data', async () => {
      const response = await request(app)
        .get('/api/marchand/dashboard')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('commandesRecentes');
      expect(response.body.data).toHaveProperty('produitsPopulaires');
      expect(response.body.data).toHaveProperty('alertesStock');
    });

    test('GET /api/marchand/dashboard/revenue should return revenue data', async () => {
      const response = await request(app)
        .get('/api/marchand/dashboard/revenue')
        .query({
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          groupBy: 'month'
        })
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('revenue');
      expect(Array.isArray(response.body.data.revenue)).toBe(true);
    });

    test('POST /api/marchand/products/quick should create product', async () => {
      const productData = {
        name: 'Test Product',
        description: 'Test Description',
        price: 99.99,
        stock_quantity: 100,
        category: 'Test Category',
        stock_alert_threshold: 10
      };

      const response = await request(app)
        .post('/api/marchand/products/quick')
        .send(productData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('PATCH /api/marchand/products/1/stock should update stock', async () => {
      const stockData = {
        stock_quantity: 50,
        operation: 'set'
      };

      const response = await request(app)
        .patch('/api/marchand/products/1/stock')
        .send(stockData)
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('GET /api/marchand/ai/recommendations should return AI recommendations', async () => {
      const response = await request(app)
        .get('/api/marchand/ai/recommendations')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('recommendations');
    });
  });

  describe('Eleveur Dashboard Endpoints', () => {
    
    test('GET /api/eleveurs/1/dashboard should return dashboard data', async () => {
      const response = await request(app)
        .get('/api/eleveurs/1/dashboard')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('alertes');
      expect(response.body.data).toHaveProperty('ventesRecentes');
      expect(response.body.data).toHaveProperty('saisiesQuotidiennes');
    });

    test('GET /api/eleveurs/1/ouvriers should return farm workers', async () => {
      const response = await request(app)
        .get('/api/eleveurs/1/ouvriers')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('ouvriers');
      expect(Array.isArray(response.body.data.ouvriers)).toBe(true);
    });

    test('POST /api/eleveurs/1/ouvriers should create farm worker', async () => {
      const ouvrierData = {
        username: 'test_worker',
        email: '<EMAIL>',
        password: 'testpassword',
        first_name: 'Test',
        last_name: 'Worker',
        fermes: [1]
      };

      const response = await request(app)
        .post('/api/eleveurs/1/ouvriers')
        .send(ouvrierData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('POST /api/eleveurs/saisies-quotidiennes should create daily entry', async () => {
      const saisieData = {
        eleveur_id: 1,
        ferme_id: 1,
        volaille_id: 1,
        date_saisie: new Date().toISOString().split('T')[0],
        nombre_morts: 0,
        nombre_malades: 0,
        temperature_moyenne: 25.5,
        humidite_moyenne: 60.0,
        observations: 'Test observation'
      };

      const response = await request(app)
        .post('/api/eleveurs/saisies-quotidiennes')
        .send(saisieData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('POST /api/eleveurs/1/ventes/quick should create quick sale', async () => {
      const venteData = {
        volaille_id: 1,
        quantite: 10,
        prix_unitaire: 50.00,
        acheteur: 'Test Buyer',
        date_vente: new Date().toISOString()
      };

      const response = await request(app)
        .post('/api/eleveurs/1/ventes/quick')
        .send(venteData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
    });

    test('GET /api/eleveurs/1/activites should return multi-activity view', async () => {
      const response = await request(app)
        .get('/api/eleveurs/1/activites')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('activites');
      expect(Array.isArray(response.body.data.activites)).toBe(true);
    });

    test('GET /api/eleveurs/1/activites with filter should return filtered activities', async () => {
      const response = await request(app)
        .get('/api/eleveurs/1/activites')
        .query({ type_activite: 'pondeuses' })
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body.data).toHaveProperty('activites');
    });
  });

  describe('Error Handling Tests', () => {
    
    test('Should handle invalid eleveur ID', async () => {
      const response = await request(app)
        .get('/api/eleveurs/999999/dashboard')
        .expect(404);

      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('message');
    });

    test('Should handle missing required fields in consultation creation', async () => {
      const invalidData = {
        eleveur_id: 1
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/veterinaire/consultations/quick')
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });

    test('Should handle invalid product data', async () => {
      const invalidProductData = {
        name: '', // Empty name
        price: -10 // Negative price
      };

      const response = await request(app)
        .post('/api/marchand/products/quick')
        .send(invalidProductData)
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });
  });

  describe('Performance Tests', () => {
    
    test('Dashboard endpoints should respond within acceptable time', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/api/veterinaire/dashboard')
        .expect(200);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(5000); // Should respond within 5 seconds
    });

    test('Large dataset queries should be optimized', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/api/eleveurs/1/activites')
        .expect(200);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(3000); // Should respond within 3 seconds
    });
  });

  describe('Security Tests', () => {
    
    test('Should prevent SQL injection in dashboard queries', async () => {
      const maliciousInput = "1'; DROP TABLE users; --";
      
      const response = await request(app)
        .get(`/api/eleveurs/${maliciousInput}/dashboard`)
        .expect(400); // Should return bad request, not execute malicious SQL

      expect(response.body).toHaveProperty('status', 'error');
    });

    test('Should validate input data types', async () => {
      const invalidData = {
        eleveur_id: 'not_a_number',
        date_consultation: 'invalid_date',
        motif: null
      };

      const response = await request(app)
        .post('/api/veterinaire/consultations/quick')
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });
  });
});

// Helper function to run all tests
const runTests = async () => {
  console.log('🧪 Starting Enhanced Dashboard API Tests...');
  
  try {
    // Test database connection first
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    console.log('✅ Database connection successful');
    
    // Run the test suite
    console.log('🚀 Running test suite...');
    
  } catch (error) {
    console.error('❌ Test setup failed:', error);
    process.exit(1);
  }
};

// Export for use in other test files
module.exports = {
  app,
  pool,
  runTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

