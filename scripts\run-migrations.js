const path = require('path');
const { Sequelize } = require('sequelize');
const { Umzug, SequelizeStorage } = require('umzug');
const fs = require('fs');
require('dotenv').config();

console.log('Starting migration process...');
console.log('Database config from environment variables:', {
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  dialect: 'postgres'
});

// Create a Sequelize instance using environment variables
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log,
  }
);

// Create an Umzug instance for migrations
const umzug = new Umzug({
  migrations: {
    glob: ['../src/migrations/*.js', { cwd: __dirname }],
    resolve: ({ name, path, context }) => {
      const migration = require(path);
      return {
        name,
        up: async () => migration.up(sequelize.getQueryInterface(), Sequelize),
        down: async () => migration.down(sequelize.getQueryInterface(), Sequelize),
      };
    }
  },
  context: {
    queryInterface: sequelize.getQueryInterface(),
    sequelize: Sequelize
  },
  storage: new SequelizeStorage({ sequelize }),
  logger: console,
});

// Function to run the migrations
async function runMigrations() {
  try {
    console.log('Running migrations...');
    await umzug.up();
    console.log('Migrations completed successfully.');
    process.exit(0);
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  }
}

// Execute the migrations
runMigrations();
