import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Ressources de traduction pour les tests
const resources = {
  fr: {
    translation: {
      'poussin.addLot': 'Ajouter un lot',
      'poussin.lotNumber': 'Numéro de lot',
      'poussin.race': 'Race',
      'poussin.souche': 'Souche',
      'poussin.quantite': 'Quantité',
      'poussin.dateEclosion': 'Date d\'éclosion',
      'poussin.poidsObjectif': 'Poids objectif',
      'common.save': 'Enregistrer',
      'common.cancel': 'Annuler',
      'common.error_loading': 'Erreur lors du chargement',
      'common.success': 'Succès',
      'common.error': 'Erreur'
    }
  },
  ar: {
    translation: {
      'poussin.addLot': 'إضافة مجموعة',
      'poussin.lotNumber': 'رقم المجموعة',
      'poussin.race': 'السلالة',
      'poussin.souche': 'النوع',
      'poussin.quantite': 'الكمية',
      'poussin.dateEclosion': 'تاريخ الفقس',
      'poussin.poidsObjectif': 'الوزن المستهدف',
      'common.save': 'حفظ',
      'common.cancel': 'إلغاء',
      'common.error_loading': 'خطأ في التحميل',
      'common.success': 'نجاح',
      'common.error': 'خطأ'
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'fr',
    fallbackLng: 'fr',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
