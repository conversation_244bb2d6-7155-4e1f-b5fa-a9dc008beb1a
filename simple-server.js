const http = require('http');

const PORT = 3003;

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ 
    message: 'Serveur Poultray DZ fonctionnel',
    port: PORT,
    timestamp: new Date().toISOString()
  }));
});

server.listen(PORT, () => {
  console.log(`✅ Serveur simple démarré sur le port ${PORT}`);
});

server.on('error', (error) => {
  console.error('❌ Erreur serveur:', error);
});
