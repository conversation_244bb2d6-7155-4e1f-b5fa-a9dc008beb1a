import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Paper,
  Divider,
  Chip,
  Avatar,
  useTheme
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  TrendingFlat,
  Egg,
  Pets,
  MonetizationOn,
  HealthAndSafety
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import useEleveurData from '../../../hooks/useEleveurData';
import useProductionData from '../../../hooks/useProductionData';

/**
 * Composant Vue d'Ensemble (Overview) pour le dashboard éleveur
 * Affiche les statistiques clés, les tendances et les graphiques de production
 */
const Overview = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState('month'); // 'week', 'month', 'year'
  const [healthStatus, setHealthStatus] = useState('good'); // 'good', 'warning', 'critical'
  
  // Récupérer les données de l'éleveur
  const { data: eleveurData, loading: eleveurLoading, error: eleveurError, fetchEleveurStats } = useEleveurData();
  
  // Récupérer les données de production
  const { data: productionData, loading: productionLoading, error: productionError, stats } = useProductionData({
    startDate: '2024-01-01',
    endDate: '2024-06-01'
  });

  // État pour stocker les statistiques
  const [statsData, setStatsData] = useState({
    totalVolailles: 0,
    productionJournaliere: 0,
    tauxMortalite: 0,
    revenuMensuel: 0,
    tendanceProduction: 'stable',
    santeTroupeau: 'good'
  });

  // Charger les statistiques
  useEffect(() => {
    const loadStats = async () => {
      try {
        const stats = await fetchEleveurStats();
        setStatsData({
          totalVolailles: stats.totalVolailles || 0,
          productionJournaliere: stats.productionJournaliere || 0,
          tauxMortalite: stats.tauxMortalite || 0,
          revenuMensuel: stats.revenuMensuel || 0,
          tendanceProduction: stats.tendanceProduction || 'stable',
          santeTroupeau: stats.santeTroupeau || 'good'
        });
        setHealthStatus(stats.santeTroupeau || 'good');
      } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
        // Utiliser des données de démonstration en cas d'erreur
        setStatsData({
          totalVolailles: 1250,
          productionJournaliere: 850,
          tauxMortalite: 2.5,
          revenuMensuel: 15000,
          tendanceProduction: 'up',
          santeTroupeau: 'good'
        });
      }
    };

    if (!eleveurLoading && !eleveurError) {
      loadStats();
    }
  }, [eleveurLoading, eleveurError, fetchEleveurStats]);

  // Données de démonstration pour les graphiques
  const productionChartData = [
    { date: '01/01', oeufs: 750, poulets: 0 },
    { date: '01/02', oeufs: 780, poulets: 0 },
    { date: '01/03', oeufs: 810, poulets: 0 },
    { date: '01/04', oeufs: 790, poulets: 0 },
    { date: '01/05', oeufs: 830, poulets: 0 },
    { date: '01/06', oeufs: 850, poulets: 0 },
    { date: '01/07', oeufs: 820, poulets: 0 },
    { date: '01/08', oeufs: 800, poulets: 0 },
    { date: '01/09', oeufs: 840, poulets: 0 },
    { date: '01/10', oeufs: 860, poulets: 0 },
    { date: '01/11', oeufs: 880, poulets: 0 },
    { date: '01/12', oeufs: 900, poulets: 0 },
    { date: '01/13', oeufs: 920, poulets: 0 },
    { date: '01/14', oeufs: 910, poulets: 0 }
  ];

  const healthChartData = [
    { date: '01/01', mortalite: 0.5, maladies: 1 },
    { date: '01/02', mortalite: 0.7, maladies: 1 },
    { date: '01/03', mortalite: 0.3, maladies: 0 },
    { date: '01/04', mortalite: 0.2, maladies: 0 },
    { date: '01/05', mortalite: 0.4, maladies: 0 },
    { date: '01/06', mortalite: 0.6, maladies: 1 },
    { date: '01/07', mortalite: 0.8, maladies: 2 },
    { date: '01/08', mortalite: 0.5, maladies: 1 },
    { date: '01/09', mortalite: 0.3, maladies: 0 },
    { date: '01/10', mortalite: 0.2, maladies: 0 },
    { date: '01/11', mortalite: 0.1, maladies: 0 },
    { date: '01/12', mortalite: 0.3, maladies: 0 },
    { date: '01/13', mortalite: 0.4, maladies: 0 },
    { date: '01/14', mortalite: 0.2, maladies: 0 }
  ];

  // Obtenir l'icône de tendance
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <TrendingUp color="success" />;
      case 'down':
        return <TrendingDown color="error" />;
      default:
        return <TrendingFlat color="info" />;
    }
  };

  // Obtenir la couleur de statut de santé
  const getHealthColor = (status) => {
    switch (status) {
      case 'good':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'critical':
        return theme.palette.error.main;
      default:
        return theme.palette.info.main;
    }
  };

  // Afficher un indicateur de chargement
  if (eleveurLoading || productionLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 4 }}>
      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Pets sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography color="textSecondary" variant="subtitle2">
                  {t('overview.totalPoultry')}
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {statsData.totalVolailles.toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {getTrendIcon(statsData.tendanceProduction)}
                <Typography variant="body2" sx={{ ml: 0.5 }}>
                  {statsData.tendanceProduction === 'up' 
                    ? t('overview.increasing') 
                    : statsData.tendanceProduction === 'down' 
                      ? t('overview.decreasing') 
                      : t('overview.stable')}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Egg sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography color="textSecondary" variant="subtitle2">
                  {t('overview.dailyProduction')}
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {statsData.productionJournaliere.toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {getTrendIcon(stats.trend)}
                <Typography variant="body2" sx={{ ml: 0.5 }}>
                  {stats.trend === 'up' 
                    ? `+${((stats.average / stats.total) * 100).toFixed(1)}%` 
                    : stats.trend === 'down' 
                      ? `-${((stats.average / stats.total) * 100).toFixed(1)}%` 
                      : t('overview.stable')}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <HealthAndSafety sx={{ mr: 1, color: getHealthColor(healthStatus) }} />
                <Typography color="textSecondary" variant="subtitle2">
                  {t('overview.mortalityRate')}
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {statsData.tauxMortalite.toFixed(1)}%
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  size="small" 
                  label={t(`overview.health.${healthStatus}`)} 
                  sx={{ 
                    bgcolor: getHealthColor(healthStatus),
                    color: 'white'
                  }} 
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MonetizationOn sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography color="textSecondary" variant="subtitle2">
                  {t('overview.monthlyRevenue')}
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {statsData.revenuMensuel.toLocaleString()} DA
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {getTrendIcon('up')}
                <Typography variant="body2" sx={{ ml: 0.5 }}>
                  +5.2% {t('overview.fromLastMonth')}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Graphiques */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {t('overview.productionTrend')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={productionChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="oeufs" name={t('overview.eggs')} fill={theme.palette.primary.main} />
                <Bar dataKey="poulets" name={t('overview.chickens')} fill={theme.palette.secondary.main} />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {t('overview.healthMetrics')}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={healthChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="mortalite" 
                  name={t('overview.mortality')} 
                  stroke={theme.palette.error.main} 
                  activeDot={{ r: 8 }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="maladies" 
                  name={t('overview.diseases')} 
                  stroke={theme.palette.warning.main} 
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Overview;

