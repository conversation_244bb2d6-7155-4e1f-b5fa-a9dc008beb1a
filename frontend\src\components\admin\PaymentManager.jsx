import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { paymentService } from '../../services/paymentService';

const PaymentManager = () => {
  const [payments, setPayments] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [paymentConfig, setPaymentConfig] = useState({
    baridi: { enabled: true, accountNumber: '' },
    ccp: { enabled: true, accountNumber: '' },
    bankTransfer: { enabled: true, accountInfo: '' }
  });

  useEffect(() => {
    loadPayments();
  }, []);

  const loadPayments = async () => {
    try {
      const response = await paymentService.getPaymentHistory('admin');
      setPayments(response.payments);
    } catch (error) {
      console.error('Erreur lors du chargement des paiements:', error);
    }
  };

  const handleConfigUpdate = async () => {
    try {
      // Implémenter la mise à jour de la configuration des paiements
      console.log('Configuration mise à jour:', paymentConfig);
      setOpenDialog(false);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la configuration:', error);
    }
  };

  const handlePaymentAction = async (paymentId, action) => {
    try {
      if (action === 'confirm') {
        await paymentService.confirmPayment(paymentId, { status: 'confirmed' });
      }
      await loadPayments();
    } catch (error) {
      console.error('Erreur lors du traitement du paiement:', error);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Gestion des Paiements
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setOpenDialog(true)}
        >
          Configurer les Méthodes de Paiement
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Utilisateur</TableCell>
              <TableCell>Méthode</TableCell>
              <TableCell>Montant</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell>{payment.id}</TableCell>
                <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                <TableCell>{payment.userName}</TableCell>
                <TableCell>{payment.method}</TableCell>
                <TableCell>{payment.amount} DZD</TableCell>
                <TableCell>{payment.status}</TableCell>
                <TableCell>
                  <Button
                    size="small"
                    color="primary"
                    onClick={() => handlePaymentAction(payment.id, 'confirm')}
                    disabled={payment.status === 'confirmed'}
                  >
                    Confirmer
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Configuration des Méthodes de Paiement</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Baridi Mob
            </Typography>
            <FormControl fullWidth margin="normal">
              <TextField
                label="Numéro de compte Baridi"
                value={paymentConfig.baridi.accountNumber}
                onChange={(e) =>
                  setPaymentConfig({
                    ...paymentConfig,
                    baridi: { ...paymentConfig.baridi, accountNumber: e.target.value }
                  })
                }
              />
            </FormControl>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              CCP
            </Typography>
            <FormControl fullWidth margin="normal">
              <TextField
                label="Numéro de compte CCP"
                value={paymentConfig.ccp.accountNumber}
                onChange={(e) =>
                  setPaymentConfig({
                    ...paymentConfig,
                    ccp: { ...paymentConfig.ccp, accountNumber: e.target.value }
                  })
                }
              />
            </FormControl>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Virement Bancaire
            </Typography>
            <FormControl fullWidth margin="normal">
              <TextField
                label="Informations bancaires"
                multiline
                rows={3}
                value={paymentConfig.bankTransfer.accountInfo}
                onChange={(e) =>
                  setPaymentConfig({
                    ...paymentConfig,
                    bankTransfer: { ...paymentConfig.bankTransfer, accountInfo: e.target.value }
                  })
                }
              />
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
          <Button onClick={handleConfigUpdate} variant="contained" color="primary">
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentManager;
