'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // ==================== FEED ITEMS ====================
      
      const feedItems = [
        {
          name: 'Aliment Poussin Démarrage Premium',
          brand: 'ONAB',
          category: 'starter',
          description: 'Aliment complet pour poussins de 0 à 21 jours, riche en protéines et vitamines',
          poultry_types: JSON.stringify(['poulet_chair', 'pondeuse']),
          recommended_age_min: 0,
          recommended_age_max: 21,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 25,
          nutritional_info: JSON.stringify({
            protein: 22,
            fat: 4.5,
            fiber: 3.5,
            ash: 6.5,
            calcium: 1.0,
            phosphorus: 0.7,
            energy: 2950
          }),
          storage_instructions: JSON.stringify({
            temperature: '15-25°C',
            humidity: '<65%',
            ventilation: 'required',
            protection: 'rodents, insects'
          }),
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: '<PERSON><PERSON> Croissance Chair',
          brand: 'ONAB',
          category: 'grower',
          description: 'Aliment de croissance pour poulets de chair de 22 à 35 jours',
          poultry_types: JSON.stringify(['poulet_chair']),
          recommended_age_min: 22,
          recommended_age_max: 35,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 80,
          nutritional_info: JSON.stringify({
            protein: 20,
            fat: 5.0,
            fiber: 4.0,
            ash: 6.0,
            calcium: 0.9,
            phosphorus: 0.65,
            energy: 3100
          }),
          storage_instructions: JSON.stringify({
            temperature: '15-25°C',
            humidity: '<65%',
            ventilation: 'required',
            protection: 'rodents, insects'
          }),
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Aliment Finition Chair Premium',
          brand: 'ONAB',
          category: 'finisher',
          description: 'Aliment de finition pour poulets de chair de 36 jours à abattage',
          poultry_types: JSON.stringify(['poulet_chair']),
          recommended_age_min: 36,
          recommended_age_max: 56,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 120,
          nutritional_info: JSON.stringify({
            protein: 18,
            fat: 5.5,
            fiber: 4.5,
            ash: 5.5,
            calcium: 0.85,
            phosphorus: 0.6,
            energy: 3200
          }),
          storage_instructions: JSON.stringify({
            temperature: '15-25°C',
            humidity: '<65%',
            ventilation: 'required',
            protection: 'rodents, insects'
          }),

          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Aliment Pondeuse Production',
          brand: 'ONAB',
          category: 'layer',
          description: 'Aliment complet pour poules pondeuses en production',
          poultry_types: JSON.stringify(['pondeuse']),
          recommended_age_min: 140,
          recommended_age_max: 500,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 110,
          nutritional_info: JSON.stringify({
            protein: 16.5,
            fat: 3.5,
            fiber: 5.0,
            ash: 12.0,
            calcium: 3.8,
            phosphorus: 0.65,
            energy: 2750
          }),
          storage_instructions: JSON.stringify({
            temperature: '15-25°C',
            humidity: '<65%',
            ventilation: 'required',
            protection: 'rodents, insects'
          }),
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Aliment Dinde Démarrage',
          brand: 'ONAB',
          category: 'starter',
          description: 'Aliment spécialisé pour dindonneaux de 0 à 28 jours',
          poultry_types: JSON.stringify(['dinde']),
          recommended_age_min: 0,
          recommended_age_max: 28,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 35,
          nutritional_info: JSON.stringify({
            protein: 28,
            fat: 4.0,
            fiber: 3.0,
            ash: 7.0,
            calcium: 1.2,
            phosphorus: 0.8,
            energy: 2900
          }),
          storage_instructions: JSON.stringify({
            temperature: '15-25°C',
            humidity: '<65%',
            ventilation: 'required',
            protection: 'rodents, insects'
          }),
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Complément Vitaminé Premium',
          brand: 'VitaPoultry',
          category: 'supplement',
          description: 'Complément vitaminé et minéral pour toutes volailles',
          poultry_types: JSON.stringify(['poulet_chair', 'pondeuse', 'dinde', 'canard']),
          recommended_age_min: 0,
          recommended_age_max: 365,
          unit_of_measure: 'kg',
          daily_consumption_per_bird: 2,
          nutritional_info: JSON.stringify({
            vitamin_a: 12000,
            vitamin_d3: 2500,
            vitamin_e: 30,
            vitamin_k3: 3,
            vitamin_b1: 2,
            vitamin_b2: 6,
            vitamin_b6: 4,
            vitamin_b12: 0.02,
            niacin: 40,
            calcium: 25,
            phosphorus: 12
          }),
          storage_instructions: JSON.stringify({
            temperature: '10-25°C',
            humidity: '<60%',
            light: 'protected',
            ventilation: 'required'
          }),
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      ];
      
      await queryInterface.bulkInsert('feed_items', feedItems, { transaction });
      
      // ==================== FEED SUPPLIERS ====================
      
      const feedSuppliers = [
        {
          name: 'ONAB - Office National des Aliments du Bétail',
          contact_person: 'Ahmed Benali',
          phone: '+213 21 45 67 89',
          email: '<EMAIL>',
          address: 'Zone Industrielle, Rouiba, Alger',
          delivery_zones: JSON.stringify(['Alger', 'Blida', 'Boumerdes', 'Tipaza', 'Bouira']),
          payment_terms: 'credit_30',
          credit_limit: 500000.00,
          rating: 4.5,
          notes: 'Fournisseur principal, livraisons régulières, bonne qualité',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Société Algérienne des Aliments Composés (SAAC)',
          contact_person: 'Fatima Khelifi',
          phone: '+213 25 78 90 12',
          email: '<EMAIL>',
          address: 'Route Nationale 5, Sétif',
          delivery_zones: JSON.stringify(['Sétif', 'Batna', 'Biskra', 'Khenchela', 'Oum El Bouaghi']),
          payment_terms: 'credit_15',
          credit_limit: 300000.00,
          rating: 4.2,
          notes: 'Spécialisé dans les aliments biologiques, prix compétitifs',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Groupe Avicole de l\'Ouest (GAO)',
          contact_person: 'Mohamed Benaissa',
          phone: '+213 41 23 45 67',
          email: '<EMAIL>',
          address: 'Zone Industrielle Es-Sénia, Oran',
          delivery_zones: JSON.stringify(['Oran', 'Mostaganem', 'Relizane', 'Mascara', 'Saida']),
          payment_terms: 'credit_30',
          credit_limit: 400000.00,
          rating: 4.3,
          notes: 'Couverture excellente de l\'ouest, service client réactif',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Nutrition Animale du Sud (NAS)',
          contact_person: 'Karim Ouali',
          phone: '+213 29 87 65 43',
          email: '<EMAIL>',
          address: 'Route de Hassi Messaoud, Ouargla',
          delivery_zones: JSON.stringify(['Ouargla', 'Ghardaïa', 'El Oued', 'Illizi', 'Tamanrasset']),
          payment_terms: 'credit_15',
          credit_limit: 200000.00,
          rating: 4.0,
          notes: 'Seul fournisseur fiable du sud, délais de livraison parfois longs',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'VitaPoultry Algeria',
          contact_person: 'Amina Cherif',
          phone: '+213 21 98 76 54',
          email: '<EMAIL>',
          address: 'Bab Ezzouar, Alger',
          delivery_zones: JSON.stringify(['Alger', 'Blida', 'Boumerdes', 'Tipaza', 'Médéa']),
          payment_terms: 'credit_7',
          credit_limit: 150000.00,
          rating: 4.7,
          notes: 'Spécialisé en compléments et vitamines, qualité premium',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      ];
      
      // Create basic users and eleveurs first if they don't exist
      const existingUsers = await queryInterface.sequelize.query(
        'SELECT id FROM "Users" WHERE id IN (1, 2)',
        { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
      );
      
      if (existingUsers.length === 0) {
        const basicUsers = [
          {
            username: 'eleveur1',
            email: '<EMAIL>',
            password: '$2b$10$example.hash.for.demo.purposes.only',
            role: 'eleveur',
            isActive: true,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            username: 'eleveur2',
            email: '<EMAIL>',
            password: '$2b$10$example.hash.for.demo.purposes.only',
            role: 'eleveur',
            isActive: true,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
        
        await queryInterface.bulkInsert('Users', basicUsers, { transaction });
        
        const basicEleveurs = [
          {
            user_id: 1,
            nom: 'Ahmed',
            prenom: 'Benali',
            telephone: '+213 25 43 21 09',
            adresse: 'Route Nationale 1, Blida',
            experience_annees: 10,
            specialite: 'poulets_chairs',
            certifications: JSON.stringify(['bio', 'halal']),
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            user_id: 2,
            nom: 'Fatima',
            prenom: 'Khelifi',
            telephone: '+213 36 87 65 43',
            adresse: 'Zone Industrielle, Sétif',
            experience_annees: 15,
            specialite: 'pondeuses',
            certifications: JSON.stringify(['bio']),
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
        
        await queryInterface.bulkInsert('eleveurs', basicEleveurs, { transaction });
      }
      
      // Create basic farms first if they don't exist
      const existingFarms = await queryInterface.sequelize.query(
        'SELECT id FROM fermes WHERE id IN (1, 2)',
        { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
      );
      
      if (existingFarms.length === 0) {
        // Get the eleveur IDs that were just created
        const eleveurIds = await queryInterface.sequelize.query(
          'SELECT id FROM eleveurs WHERE user_id IN (1, 2) ORDER BY user_id',
          { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
        );
        
        const basicFarms = [
            {
              eleveur_id: eleveurIds[0]?.id || 1,
              nom: 'Ferme Avicole El Bahdja',
              adresse: 'Route Nationale 1, Blida',
              superficie: 5.5,
              capacite_maximale: 10000,
              type_elevage: 'mixte',
              coordonnees_gps: JSON.stringify({ latitude: 36.4203, longitude: 2.8277 }),
              equipements: JSON.stringify(['incubateurs', 'ventilation', 'chauffage']),
              status: 'active',
              created_at: new Date(),
              updated_at: new Date()
            },
            {
              eleveur_id: eleveurIds[1]?.id || 2,
              nom: 'Exploitation Avicole Moderne',
              adresse: 'Zone Industrielle, Sétif',
              superficie: 8.2,
              capacite_maximale: 15000,
              type_elevage: 'chair',
              coordonnees_gps: JSON.stringify({ latitude: 36.1906, longitude: 5.4137 }),
              equipements: JSON.stringify(['automatisation', 'ventilation', 'alimentation_automatique']),
              status: 'active',
              created_at: new Date(),
              updated_at: new Date()
            }
          ];
        
        await queryInterface.bulkInsert('fermes', basicFarms, { transaction });
      }
      
      // Get the farm IDs that were just created or already exist
        const farmIds = await queryInterface.sequelize.query(
          'SELECT id FROM fermes ORDER BY id LIMIT 2',
          { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
        );
        
        // Get the user IDs that were just created or already exist
        const userIds = await queryInterface.sequelize.query(
          'SELECT id FROM "Users" WHERE username IN (\'eleveur1\', \'eleveur2\') ORDER BY id',
          { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
        );
      
      await queryInterface.bulkInsert('feed_suppliers', feedSuppliers, { transaction });
      
      // ==================== FEED COMPOSITIONS ====================
      // Note: Feed composition data will be added through the feed plan management interface
      
      // ==================== FEED PLANS ====================
      
      const feedPlans = [
        {
          farm_id: farmIds[0]?.id || 1,
          name: 'Plan Standard Poulet Chair 42 jours',
          description: 'Plan standard optimisé pour poulets de chair',
          poultry_type: 'poulet_chair',
          start_date: new Date(),
          end_date: null,
          bird_count: 1000,
          feeding_schedule: JSON.stringify({
            feeding_times: ['07:00', '13:00', '19:00'],
            feed_composition: [
              { feed_item_id: 1, age_start: 0, age_end: 21, percentage: 100 },
              { feed_item_id: 2, age_start: 22, age_end: 35, percentage: 100 },
              { feed_item_id: 3, age_start: 36, age_end: 42, percentage: 100 }
            ],
            nutritional_requirements: {
              protein_min: 18,
              energy_min: 3000,
              calcium_max: 1.0,
              phosphorus_min: 0.6
            }
          }),
          total_estimated_cost: 45500.00,
          actual_cost: null,
          status: 'active',
          created_by: userIds[0]?.id || 1,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          farm_id: farmIds[0]?.id || 1,
          name: 'Plan Pondeuse Production Intensive',
          description: 'Plan pour poules pondeuses en production',
          poultry_type: 'pondeuse',
          start_date: new Date(),
          end_date: null,
          bird_count: 500,
          feeding_schedule: JSON.stringify({
            feeding_times: ['07:00', '17:00'],
            feed_composition: [
              { feed_item_id: 4, age_start: 140, age_end: 500, percentage: 95 },
              { feed_item_id: 6, age_start: 140, age_end: 500, percentage: 5 }
            ],
            nutritional_requirements: {
              protein_min: 16,
              energy_min: 2700,
              calcium_min: 3.5,
              phosphorus_min: 0.6
            }
          }),
          total_estimated_cost: 90000.00,
          actual_cost: null,
          status: 'completed',
            created_by: 1,
            created_at: new Date(),
          updated_at: new Date()
        },
        {
          farm_id: farmIds[1]?.id || 2,
          name: 'Plan Dinde Croissance Rapide',
          description: 'Plan personnalisé pour dindes de Noël',
          poultry_type: 'dinde',
          start_date: new Date(),
          end_date: null,
          bird_count: 200,
          feeding_schedule: JSON.stringify({
            feeding_times: ['06:00', '11:00', '16:00', '20:00'],
            feed_composition: [
              { feed_item_id: 5, age_start: 0, age_end: 28, percentage: 100 },
              { feed_item_id: 2, age_start: 29, age_end: 56, percentage: 100 },
              { feed_item_id: 3, age_start: 57, age_end: 84, percentage: 100 }
            ],
            nutritional_requirements: {
              protein_min: 20,
              energy_min: 2900,
              calcium_max: 1.2,
              phosphorus_min: 0.7
            }
          }),
          total_estimated_cost: 25000.00,
          actual_cost: null,
          status: 'draft',
          created_by: 1,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];
      
      await queryInterface.bulkInsert('feed_plans', feedPlans, { transaction });
      
      console.log('✅ Feed management seed data inserted successfully');
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error inserting feed management seed data:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Delete in reverse order to respect foreign key constraints
        await queryInterface.bulkDelete('feed_plans', null, { transaction });
        await queryInterface.bulkDelete('feed_suppliers', null, { transaction });
        await queryInterface.bulkDelete('feed_items', null, { transaction });
        
        // Remove the farms we created (only if they were created by this seeder)
        await queryInterface.bulkDelete('fermes', {
          nom: ['Ferme Avicole El Bahdja', 'Exploitation Avicole Moderne']
        }, { transaction });
        
        // Remove eleveurs and users we created
        await queryInterface.bulkDelete('eleveurs', {
          nom: ['Ahmed', 'Fatima']
        }, { transaction });
        
        await queryInterface.bulkDelete('Users', {
          username: ['eleveur1', 'eleveur2']
        }, { transaction });
      
      console.log('✅ Feed management seed data removed successfully');
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing feed management seed data:', error);
      throw error;
    }
  }
};