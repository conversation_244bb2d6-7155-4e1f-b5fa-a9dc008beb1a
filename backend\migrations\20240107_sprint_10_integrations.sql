-- Migration SPRINT 10: Third-Party Integrations & External APIs

-- Table des logs météorologiques
CREATE TABLE weather_logs (
    id SERIAL PRIMARY KEY,
    farm_id INTEGER REFERENCES fermes(id),
    temperature DECIMAL(5,2),
    humidity DECIMAL(5,2),
    wind_speed DECIMAL(5,2),
    weather_data JSONB,
    log_type VARCHAR(20) DEFAULT 'current', -- current, forecast, alert
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des prix de marché
CREATE TABLE market_prices (
    id SERIAL PRIMARY KEY,
    commodity VARCHAR(100) NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20),
    category VARCHAR(50),
    price_data JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des dispositifs IoT
CREATE TABLE iot_devices (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(100) UNIQUE NOT NULL,
    device_name VARCHAR(255),
    device_type VARCHAR(100), -- temperature_sensor, humidity_sensor, feed_monitor, etc.
    farm_id INTEGER REFERENCES fermes(id),
    location VARCHAR(255),
    configuration JSONB,
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, maintenance
    last_seen TIMESTAMP WITH TIME ZONE,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des logs de données IoT
CREATE TABLE iot_device_logs (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    sensor_type VARCHAR(100), -- temperature, humidity, feed_level, water_level, etc.
    value TEXT NOT NULL,
    unit VARCHAR(20),
    farm_id INTEGER REFERENCES fermes(id),
    metadata JSONB,
    received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des alertes IoT
CREATE TABLE iot_alerts (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    farm_id INTEGER REFERENCES fermes(id),
    sensor_type VARCHAR(100),
    alert_type VARCHAR(100), -- threshold_high, threshold_low, device_offline, etc.
    severity VARCHAR(20), -- info, warning, critical
    message TEXT,
    trigger_value TEXT,
    threshold_value TEXT,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by INTEGER REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des commandes IoT
CREATE TABLE iot_device_commands (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    command VARCHAR(100) NOT NULL,
    parameters JSONB,
    status VARCHAR(20) DEFAULT 'sent', -- sent, acknowledged, executed, failed
    response JSONB,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des seuils d'alerte IoT
CREATE TABLE iot_device_thresholds (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(100),
    sensor_type VARCHAR(100),
    farm_id INTEGER REFERENCES fermes(id),
    thresholds JSONB, -- {min: 15, max: 30, critical_min: 5, critical_max: 40}
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(device_id, sensor_type)
);

-- Table des intégrations externes
CREATE TABLE external_integrations (
    id SERIAL PRIMARY KEY,
    integration_type VARCHAR(100) NOT NULL, -- weather, market_prices, iot, veterinary, supplier
    provider VARCHAR(100), -- openweather, commodities_api, mqtt_broker, etc.
    farm_id INTEGER REFERENCES fermes(id),
    configuration JSONB,
    api_key_encrypted TEXT,
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, error
    last_sync TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des webhooks
CREATE TABLE webhooks (
    id SERIAL PRIMARY KEY,
    webhook_url VARCHAR(500) NOT NULL,
    webhook_secret VARCHAR(255),
    event_types TEXT[], -- array of event types to listen for
    farm_id INTEGER REFERENCES fermes(id),
    integration_type VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    last_triggered TIMESTAMP WITH TIME ZONE,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des logs de webhooks
CREATE TABLE webhook_logs (
    id SERIAL PRIMARY KEY,
    webhook_id INTEGER REFERENCES webhooks(id),
    event_type VARCHAR(100),
    payload JSONB,
    response_status INTEGER,
    response_body TEXT,
    processing_time INTEGER, -- en millisecondes
    success BOOLEAN,
    error_message TEXT,
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des fournisseurs/vendeurs
CREATE TABLE suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100), -- feed, equipment, veterinary, etc.
    contact_info JSONB,
    address TEXT,
    wilaya VARCHAR(100),
    commune VARCHAR(100),
    api_endpoint VARCHAR(500),
    api_credentials JSONB,
    rating DECIMAL(3,2),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des commandes fournisseurs
CREATE TABLE supplier_orders (
    id SERIAL PRIMARY KEY,
    supplier_id INTEGER REFERENCES suppliers(id),
    farm_id INTEGER REFERENCES fermes(id),
    order_reference VARCHAR(100),
    order_data JSONB,
    total_amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'DZD',
    status VARCHAR(50) DEFAULT 'pending', -- pending, confirmed, shipped, delivered, cancelled
    ordered_by INTEGER REFERENCES users(id),
    order_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivery_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des cliniques vétérinaires
CREATE TABLE veterinary_clinics (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    wilaya VARCHAR(100),
    commune VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    api_endpoint VARCHAR(500),
    api_credentials JSONB,
    services TEXT[], -- array of services offered
    working_hours JSONB,
    emergency_contact VARCHAR(20),
    rating DECIMAL(3,2),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des rendez-vous externes
CREATE TABLE external_appointments (
    id SERIAL PRIMARY KEY,
    clinic_id INTEGER REFERENCES veterinary_clinics(id),
    farm_id INTEGER REFERENCES fermes(id),
    appointment_reference VARCHAR(100),
    appointment_data JSONB,
    scheduled_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, confirmed, completed, cancelled
    sync_status VARCHAR(20) DEFAULT 'pending', -- pending, synced, failed
    external_id VARCHAR(100), -- ID from external system
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ajouter des colonnes aux tables existantes pour les coordonnées GPS
ALTER TABLE fermes ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8);
ALTER TABLE fermes ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8);
ALTER TABLE fermes ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'Africa/Algiers';
ALTER TABLE fermes ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;

-- Index pour améliorer les performances
CREATE INDEX idx_weather_logs_farm_date ON weather_logs(farm_id, recorded_at);
CREATE INDEX idx_weather_logs_type ON weather_logs(log_type);
CREATE INDEX idx_market_prices_commodity ON market_prices(commodity);
CREATE INDEX idx_market_prices_date ON market_prices(recorded_at);
CREATE INDEX idx_iot_devices_farm ON iot_devices(farm_id);
CREATE INDEX idx_iot_devices_type ON iot_devices(device_type);
CREATE INDEX idx_iot_device_logs_device ON iot_device_logs(device_id);
CREATE INDEX idx_iot_device_logs_farm_date ON iot_device_logs(farm_id, received_at);
CREATE INDEX idx_iot_device_logs_sensor_type ON iot_device_logs(sensor_type);
CREATE INDEX idx_iot_alerts_farm ON iot_alerts(farm_id);
CREATE INDEX idx_iot_alerts_severity ON iot_alerts(severity);
CREATE INDEX idx_iot_alerts_resolved ON iot_alerts(resolved);
CREATE INDEX idx_iot_commands_device ON iot_device_commands(device_id);
CREATE INDEX idx_iot_commands_status ON iot_device_commands(status);
CREATE INDEX idx_external_integrations_farm ON external_integrations(farm_id);
CREATE INDEX idx_external_integrations_type ON external_integrations(integration_type);
CREATE INDEX idx_webhooks_farm ON webhooks(farm_id);
CREATE INDEX idx_webhook_logs_webhook ON webhook_logs(webhook_id);
CREATE INDEX idx_suppliers_type ON suppliers(type);
CREATE INDEX idx_suppliers_wilaya ON suppliers(wilaya);
CREATE INDEX idx_supplier_orders_farm ON supplier_orders(farm_id);
CREATE INDEX idx_supplier_orders_status ON supplier_orders(status);
CREATE INDEX idx_veterinary_clinics_wilaya ON veterinary_clinics(wilaya);
CREATE INDEX idx_external_appointments_farm ON external_appointments(farm_id);
CREATE INDEX idx_external_appointments_clinic ON external_appointments(clinic_id);

-- Triggers pour mettre à jour les timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_iot_device_thresholds_updated_at BEFORE UPDATE ON iot_device_thresholds
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_external_integrations_updated_at BEFORE UPDATE ON external_integrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_supplier_orders_updated_at BEFORE UPDATE ON supplier_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_veterinary_clinics_updated_at BEFORE UPDATE ON veterinary_clinics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_external_appointments_updated_at BEFORE UPDATE ON external_appointments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insérer des données de test pour les fournisseurs
INSERT INTO suppliers (name, type, contact_info, address, wilaya, commune, rating) VALUES
('Aliments Avicoles Algérie', 'feed', '{"phone": "+213 21 123456", "email": "<EMAIL>"}', 'Zone Industrielle, Rouiba', 'Alger', 'Rouiba', 4.5),
('Équipements Fermiers DZ', 'equipment', '{"phone": "+213 25 987654", "email": "<EMAIL>"}', 'Route Nationale 1, Blida', 'Blida', 'Blida', 4.2),
('Vétérinaire Services Plus', 'veterinary', '{"phone": "+213 31 456789", "email": "<EMAIL>"}', 'Centre Ville, Oran', 'Oran', 'Oran', 4.8),
('Céréales du Maghreb', 'feed', '{"phone": "+213 38 321654", "email": "<EMAIL>"}', 'Zone Agricole, Sétif', 'Sétif', 'Sétif', 4.3);

-- Insérer des données de test pour les cliniques vétérinaires
INSERT INTO veterinary_clinics (name, address, wilaya, commune, phone, email, services, working_hours, emergency_contact, rating) VALUES
('Clinique Vétérinaire Centrale', 'Rue Didouche Mourad, Alger', 'Alger', 'Alger Centre', '+213 21 654321', '<EMAIL>', 
 ARRAY['consultation', 'vaccination', 'chirurgie', 'urgence'], 
 '{"lundi": "08:00-18:00", "mardi": "08:00-18:00", "mercredi": "08:00-18:00", "jeudi": "08:00-18:00", "vendredi": "08:00-18:00", "samedi": "08:00-14:00"}',
 '+213 21 654322', 4.7),
('Cabinet Vétérinaire Tizi-Ouzou', 'Boulevard Stiti Ali, Tizi-Ouzou', 'Tizi-Ouzou', 'Tizi-Ouzou', '+213 26 789123', '<EMAIL>',
 ARRAY['consultation', 'vaccination', 'analyses'], 
 '{"lundi": "09:00-17:00", "mardi": "09:00-17:00", "mercredi": "09:00-17:00", "jeudi": "09:00-17:00", "vendredi": "09:00-17:00"}',
 '+213 26 789124', 4.4);

-- Insérer des seuils par défaut pour les capteurs IoT
INSERT INTO iot_device_thresholds (sensor_type, thresholds) VALUES
('temperature', '{"min": 15, "max": 30, "critical_min": 5, "critical_max": 40}'),
('humidity', '{"min": 40, "max": 80, "critical_min": 20, "critical_max": 95}'),
('feed_level', '{"min": 20, "critical_min": 5}'),
('water_level', '{"min": 30, "critical_min": 10}'),
('air_quality', '{"max": 500, "critical_max": 1000}'),
('sound_level', '{"max": 70, "critical_max": 90}'),
('light_level', '{"min": 100, "max": 1000}');

-- Fonction pour nettoyer les anciens logs
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS void AS $$
BEGIN
    -- Supprimer les logs météo de plus de 6 mois
    DELETE FROM weather_logs WHERE recorded_at < NOW() - INTERVAL '6 months';
    
    -- Supprimer les logs IoT de plus de 3 mois
    DELETE FROM iot_device_logs WHERE received_at < NOW() - INTERVAL '3 months';
    
    -- Supprimer les logs de webhook de plus de 1 mois
    DELETE FROM webhook_logs WHERE triggered_at < NOW() - INTERVAL '1 month';
    
    -- Supprimer les alertes IoT résolues de plus de 1 mois
    DELETE FROM iot_alerts WHERE resolved = TRUE AND resolved_at < NOW() - INTERVAL '1 month';
END;
$$ LANGUAGE plpgsql;

-- Créer une tâche de nettoyage (à exécuter manuellement ou via cron)
-- SELECT cleanup_old_logs();

-- Vues pour faciliter les requêtes

-- Vue des dispositifs IoT avec leur dernier statut
CREATE VIEW iot_devices_status AS
SELECT 
    d.id,
    d.device_id,
    d.device_name,
    d.device_type,
    d.farm_id,
    d.location,
    d.status,
    d.last_seen,
    CASE 
        WHEN d.last_seen > NOW() - INTERVAL '5 minutes' THEN 'online'
        WHEN d.last_seen > NOW() - INTERVAL '1 hour' THEN 'warning'
        ELSE 'offline'
    END as connection_status,
    (SELECT COUNT(*) FROM iot_alerts a WHERE a.device_id = d.device_id AND a.resolved = FALSE) as active_alerts
FROM iot_devices d;

-- Vue des alertes IoT non résolues avec informations du dispositif
CREATE VIEW active_iot_alerts AS
SELECT 
    a.*,
    d.device_name,
    d.device_type,
    d.location,
    f.nom as farm_name
FROM iot_alerts a
JOIN iot_devices d ON a.device_id = d.device_id
LEFT JOIN fermes f ON a.farm_id = f.id
WHERE a.resolved = FALSE
ORDER BY a.created_at DESC;

-- Vue des prix de marché les plus récents
CREATE VIEW latest_market_prices AS
SELECT DISTINCT ON (commodity) 
    commodity,
    price,
    unit,
    category,
    price_data,
    recorded_at
FROM market_prices 
ORDER BY commodity, recorded_at DESC;
