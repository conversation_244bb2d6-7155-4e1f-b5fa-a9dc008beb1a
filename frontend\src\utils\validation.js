/**
 * Service de validation pour les formulaires
 * Fournit des règles de validation réutilisables et des fonctions d'aide
 */

// Expressions régulières pour la validation
const REGEX_PATTERNS = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^(\+213|0)[5-7][0-9]{8}$/, // Format téléphone algérien
  phoneInternational: /^\+?[1-9]\d{1,14}$/, // Format international
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, // Au moins 1 minuscule, 1 majuscule, 1 chiffre, 1 caractère spécial
  username: /^[a-zA-Z0-9_-]{3,20}$/, // Lettres, chiffres, tirets et underscores, 3-20 caractères
  name: /^[a-zA-ZÀ-ÿ\s'-]{2,50}$/, // Lettres avec accents, espaces, apostrophes et tirets
  postalCode: /^[0-9]{5}$/, // Code postal algérien (5 chiffres)
  number: /^\d+(\.\d+)?$/, // Nombres décimaux
  positiveInteger: /^[1-9]\d*$/, // Entiers positifs
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
};

// Messages d'erreur par défaut
const DEFAULT_MESSAGES = {
  required: 'Ce champ est obligatoire',
  email: 'Veuillez entrer une adresse email valide',
  phone: 'Veuillez entrer un numéro de téléphone valide (ex: +213 555 123 456)',
  password: 'Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial',
  passwordMin: 'Le mot de passe doit contenir au moins {min} caractères',
  passwordMatch: 'Les mots de passe ne correspondent pas',
  username: 'Le nom d\'utilisateur doit contenir 3-20 caractères (lettres, chiffres, - et _)',
  name: 'Le nom doit contenir 2-50 caractères (lettres uniquement)',
  minLength: 'Ce champ doit contenir au moins {min} caractères',
  maxLength: 'Ce champ ne peut pas dépasser {max} caractères',
  min: 'La valeur doit être supérieure ou égale à {min}',
  max: 'La valeur doit être inférieure ou égale à {max}',
  number: 'Veuillez entrer un nombre valide',
  positiveInteger: 'Veuillez entrer un nombre entier positif',
  url: 'Veuillez entrer une URL valide',
  postalCode: 'Veuillez entrer un code postal valide (5 chiffres)'
};

/**
 * Classe pour définir des règles de validation
 */
class ValidationRule {
  constructor(validator, message, options = {}) {
    this.validator = validator;
    this.message = message;
    this.options = options;
  }

  validate(value, allValues = {}) {
    if (typeof this.validator === 'function') {
      return this.validator(value, allValues, this.options);
    }
    return this.validator.test(value);
  }

  getMessage(options = {}) {
    let message = this.message;
    Object.keys(options).forEach(key => {
      message = message.replace(`{${key}}`, options[key]);
    });
    return message;
  }
}

/**
 * Règles de validation prédéfinies
 */
export const ValidationRules = {
  required: () => new ValidationRule(
    (value) => value !== null && value !== undefined && value !== '',
    DEFAULT_MESSAGES.required
  ),

  email: () => new ValidationRule(
    REGEX_PATTERNS.email,
    DEFAULT_MESSAGES.email
  ),

  phone: () => new ValidationRule(
    REGEX_PATTERNS.phone,
    DEFAULT_MESSAGES.phone
  ),

  phoneInternational: () => new ValidationRule(
    REGEX_PATTERNS.phoneInternational,
    DEFAULT_MESSAGES.phone
  ),

  password: (minLength = 8) => new ValidationRule(
    (value) => value && value.length >= minLength && REGEX_PATTERNS.password.test(value),
    minLength === 8 ? DEFAULT_MESSAGES.password : DEFAULT_MESSAGES.passwordMin,
    { min: minLength }
  ),

  passwordSimple: (minLength = 6) => new ValidationRule(
    (value) => value && value.length >= minLength,
    DEFAULT_MESSAGES.passwordMin,
    { min: minLength }
  ),

  passwordMatch: (fieldName) => new ValidationRule(
    (value, allValues) => value === allValues[fieldName],
    DEFAULT_MESSAGES.passwordMatch
  ),

  username: () => new ValidationRule(
    REGEX_PATTERNS.username,
    DEFAULT_MESSAGES.username
  ),

  name: () => new ValidationRule(
    REGEX_PATTERNS.name,
    DEFAULT_MESSAGES.name
  ),

  minLength: (min) => new ValidationRule(
    (value) => !value || value.length >= min,
    DEFAULT_MESSAGES.minLength,
    { min }
  ),

  maxLength: (max) => new ValidationRule(
    (value) => !value || value.length <= max,
    DEFAULT_MESSAGES.maxLength,
    { max }
  ),

  min: (min) => new ValidationRule(
    (value) => !value || parseFloat(value) >= min,
    DEFAULT_MESSAGES.min,
    { min }
  ),

  max: (max) => new ValidationRule(
    (value) => !value || parseFloat(value) <= max,
    DEFAULT_MESSAGES.max,
    { max }
  ),

  number: () => new ValidationRule(
    REGEX_PATTERNS.number,
    DEFAULT_MESSAGES.number
  ),

  positiveInteger: () => new ValidationRule(
    REGEX_PATTERNS.positiveInteger,
    DEFAULT_MESSAGES.positiveInteger
  ),

  url: () => new ValidationRule(
    REGEX_PATTERNS.url,
    DEFAULT_MESSAGES.url
  ),

  postalCode: () => new ValidationRule(
    REGEX_PATTERNS.postalCode,
    DEFAULT_MESSAGES.postalCode
  ),

  custom: (validator, message) => new ValidationRule(validator, message)
};

/**
 * Schémas de validation prédéfinis pour les formulaires courants
 */
export const ValidationSchemas = {
  login: {
    email: [ValidationRules.required(), ValidationRules.email()],
    password: [ValidationRules.required()]
  },

  register: {
    username: [ValidationRules.required(), ValidationRules.username()],
    email: [ValidationRules.required(), ValidationRules.email()],
    password: [ValidationRules.required(), ValidationRules.passwordSimple(6)],
    confirmPassword: [ValidationRules.required(), ValidationRules.passwordMatch('password')],
    first_name: [ValidationRules.required(), ValidationRules.name()],
    last_name: [ValidationRules.required(), ValidationRules.name()],
    role: [ValidationRules.required()]
  },

  user: {
    username: [ValidationRules.required(), ValidationRules.username()],
    email: [ValidationRules.required(), ValidationRules.email()],
    password: [ValidationRules.required(), ValidationRules.passwordSimple(6)],
    first_name: [ValidationRules.name()],
    last_name: [ValidationRules.name()],
    role: [ValidationRules.required()]
  },

  eleveur: {
    nom: [ValidationRules.required(), ValidationRules.name()],
    prenom: [ValidationRules.required(), ValidationRules.name()],
    email: [ValidationRules.required(), ValidationRules.email()],
    telephone: [ValidationRules.phone()],
    adresse: [ValidationRules.minLength(5), ValidationRules.maxLength(200)]
  },

  veterinaire: {
    nom: [ValidationRules.required(), ValidationRules.name()],
    prenom: [ValidationRules.required(), ValidationRules.name()],
    email: [ValidationRules.required(), ValidationRules.email()],
    telephone: [ValidationRules.phone()],
    specialite: [ValidationRules.required(), ValidationRules.minLength(2)],
    numero_ordre: [ValidationRules.required()]
  },

  marchand: {
    nom: [ValidationRules.required(), ValidationRules.name()],
    prenom: [ValidationRules.required(), ValidationRules.name()],
    email: [ValidationRules.required(), ValidationRules.email()],
    telephone: [ValidationRules.phone()],
    entreprise: [ValidationRules.required(), ValidationRules.minLength(2)],
    siret: [ValidationRules.minLength(14), ValidationRules.maxLength(14)]
  },

  volaille: {
    espece: [ValidationRules.required()],
    race: [ValidationRules.required()],
    quantite: [ValidationRules.required(), ValidationRules.positiveInteger()],
    age: [ValidationRules.required(), ValidationRules.positiveInteger()],
    poids: [ValidationRules.number(), ValidationRules.min(0)]
  },

  production: {
    date: [ValidationRules.required()],
    quantite: [ValidationRules.required(), ValidationRules.positiveInteger()],
    poids_moyen: [ValidationRules.number(), ValidationRules.min(0)]
  }
};

/**
 * Fonction principale de validation
 */
export const validateForm = (values, schema) => {
  const errors = {};

  Object.keys(schema).forEach(fieldName => {
    const rules = schema[fieldName];
    const value = values[fieldName];

    for (const rule of rules) {
      if (!rule.validate(value, values)) {
        errors[fieldName] = rule.getMessage(rule.options);
        break; // Arrêter à la première erreur pour ce champ
      }
    }
  });

  return errors;
};

/**
 * Validation d'un champ unique
 */
export const validateField = (value, rules, allValues = {}) => {
  for (const rule of rules) {
    if (!rule.validate(value, allValues)) {
      return rule.getMessage(rule.options);
    }
  }
  return null;
};

/**
 * Utilitaires de validation
 */
export const ValidationUtils = {
  // Nettoyer et formater un numéro de téléphone
  formatPhone: (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('213')) {
      return '+' + cleaned;
    }
    if (cleaned.startsWith('0')) {
      return '+213' + cleaned.substring(1);
    }
    return phone;
  },

  // Nettoyer et formater un email
  formatEmail: (email) => {
    return email ? email.toLowerCase().trim() : '';
  },

  // Vérifier si une valeur est vide
  isEmpty: (value) => {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0) ||
           (typeof value === 'object' && Object.keys(value).length === 0);
  },

  // Générer un mot de passe sécurisé
  generateSecurePassword: (length = 12) => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$!%*?&';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }
};

export default {
  ValidationRules,
  ValidationSchemas,
  validateForm,
  validateField,
  ValidationUtils
};
