global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'poultraydz-monitor'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus lui-même
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend Node.js
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Frontend Nginx
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Base de données PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter pour les métriques système
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor pour les métriques Docker
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Blackbox exporter pour les tests de connectivité
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://app.poultraydz.com
        - https://api.poultraydz.com/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Métriques personnalisées de l'application
  - job_name: 'app-metrics'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'secure_password'

# Configuration des métriques de recording
recording_rules:
  - name: poultraydz.rules
    rules:
    # Taux de requêtes par seconde
    - record: poultraydz:http_requests:rate5m
      expr: rate(http_requests_total[5m])
    
    # Latence moyenne des requêtes
    - record: poultraydz:http_request_duration:mean5m
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])
    
    # Taux d'erreur
    - record: poultraydz:http_requests:error_rate5m
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
    
    # Utilisation CPU moyenne
    - record: poultraydz:cpu_usage:mean5m
      expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
    
    # Utilisation mémoire
    - record: poultraydz:memory_usage:percent
      expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100
    
    # Espace disque utilisé
    - record: poultraydz:disk_usage:percent
      expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100
    
    # Connexions base de données actives
    - record: poultraydz:db_connections:active
      expr: pg_stat_activity_count{state="active"}
    
    # Taille de la base de données
    - record: poultraydz:db_size:bytes
      expr: pg_database_size_bytes{datname="poultraydz"}
    
    # Métriques métier
    - record: poultraydz:users:total
      expr: poultraydz_users_total
    
    - record: poultraydz:volailles:total
      expr: poultraydz_volailles_total
    
    - record: poultraydz:consultations:daily
      expr: increase(poultraydz_consultations_total[1d])
    
    - record: poultraydz:production:daily
      expr: increase(poultraydz_production_total[1d])

# Configuration des alertes
alert_rules:
  - name: poultraydz.alerts
    rules:
    # Alertes infrastructure
    - alert: HighCPUUsage
      expr: poultraydz:cpu_usage:mean5m > 80
      for: 5m
      labels:
        severity: warning
        service: infrastructure
      annotations:
        summary: "Utilisation CPU élevée sur {{ $labels.instance }}"
        description: "L'utilisation CPU est de {{ $value }}% depuis plus de 5 minutes"

    - alert: HighMemoryUsage
      expr: poultraydz:memory_usage:percent > 85
      for: 5m
      labels:
        severity: warning
        service: infrastructure
      annotations:
        summary: "Utilisation mémoire élevée sur {{ $labels.instance }}"
        description: "L'utilisation mémoire est de {{ $value }}% depuis plus de 5 minutes"

    - alert: DiskSpaceLow
      expr: poultraydz:disk_usage:percent > 90
      for: 2m
      labels:
        severity: critical
        service: infrastructure
      annotations:
        summary: "Espace disque faible sur {{ $labels.instance }}"
        description: "L'espace disque utilisé est de {{ $value }}%"

    # Alertes application
    - alert: HighErrorRate
      expr: poultraydz:http_requests:error_rate5m > 0.05
      for: 2m
      labels:
        severity: critical
        service: backend
      annotations:
        summary: "Taux d'erreur élevé"
        description: "Le taux d'erreur est de {{ $value | humanizePercentage }} depuis 2 minutes"

    - alert: HighResponseTime
      expr: poultraydz:http_request_duration:mean5m > 2
      for: 5m
      labels:
        severity: warning
        service: backend
      annotations:
        summary: "Temps de réponse élevé"
        description: "Le temps de réponse moyen est de {{ $value }}s depuis 5 minutes"

    - alert: ServiceDown
      expr: up == 0
      for: 1m
      labels:
        severity: critical
        service: "{{ $labels.job }}"
      annotations:
        summary: "Service {{ $labels.job }} indisponible"
        description: "Le service {{ $labels.job }} sur {{ $labels.instance }} est indisponible depuis 1 minute"

    # Alertes base de données
    - alert: DatabaseConnectionsHigh
      expr: poultraydz:db_connections:active > 80
      for: 5m
      labels:
        severity: warning
        service: database
      annotations:
        summary: "Nombre élevé de connexions à la base de données"
        description: "{{ $value }} connexions actives à la base de données"

    - alert: DatabaseSizeGrowth
      expr: increase(poultraydz:db_size:bytes[1h]) > 1073741824  # 1GB
      for: 0m
      labels:
        severity: warning
        service: database
      annotations:
        summary: "Croissance rapide de la base de données"
        description: "La base de données a augmenté de {{ $value | humanizeBytes }} en 1 heure"

    # Alertes métier
    - alert: NoNewUsers
      expr: increase(poultraydz:users:total[24h]) == 0
      for: 0m
      labels:
        severity: info
        service: business
      annotations:
        summary: "Aucun nouvel utilisateur en 24h"
        description: "Aucun nouvel utilisateur ne s'est inscrit dans les dernières 24 heures"

    - alert: LowProductionActivity
      expr: poultraydz:production:daily < 10
      for: 0m
      labels:
        severity: info
        service: business
      annotations:
        summary: "Faible activité de production"
        description: "Seulement {{ $value }} enregistrements de production aujourd'hui"

    # Alertes sécurité
    - alert: TooManyFailedLogins
      expr: increase(poultraydz_failed_logins_total[5m]) > 10
      for: 0m
      labels:
        severity: warning
        service: security
      annotations:
        summary: "Tentatives de connexion échouées élevées"
        description: "{{ $value }} tentatives de connexion échouées en 5 minutes"

    - alert: SuspiciousActivity
      expr: increase(poultraydz_suspicious_requests_total[1m]) > 5
      for: 0m
      labels:
        severity: critical
        service: security
      annotations:
        summary: "Activité suspecte détectée"
        description: "{{ $value }} requêtes suspectes détectées en 1 minute"
