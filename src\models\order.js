const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize) => {
  class Order extends Model {
    static associate(models) {
      Order.belongsTo(models.User, {
        foreignKey: 'client_id',
        as: 'client'
      });
      Order.belongsTo(models.User, {
        foreignKey: 'marchand_id',
        as: 'marchand'
      });
      Order.hasMany(models.OrderItem, {
        foreignKey: 'order_id',
        as: 'items'
      });
      Order.hasMany(models.OrderStatusHistory, {
        foreignKey: 'order_id',
        as: 'statusHistory'
      });
      Order.hasMany(models.OrderReturn, {
        foreignKey: 'order_id',
        as: 'returns'
      });
    }

    // Find orders by client
    static async findByClient(clientId) {
      return await this.findAll({
        where: { client_id: clientId },
        include: [
          { model: sequelize.models.OrderItem, as: 'items' },
          { model: sequelize.models.OrderStatusHistory, as: 'statusHistory' }
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // Find orders by merchant
    static async findByMarchand(marchandId) {
      return await this.findAll({
        where: { marchand_id: marchandId },
        include: [
          { model: sequelize.models.OrderItem, as: 'items' },
          { model: sequelize.models.OrderStatusHistory, as: 'statusHistory' }
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // Update order status with history tracking
    static async updateStatus(id, status, userId, notes = null) {
      const transaction = await sequelize.transaction();
      try {
        // Update order status
        await this.update(
          { status },
          { where: { id }, transaction }
        );

        // Add status history entry
        await sequelize.models.OrderStatusHistory.create({
          order_id: id,
          status,
          notes,
          created_by: userId
        }, { transaction });

        await transaction.commit();
        return true;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    // Calculate order totals
    async calculateTotals() {
      const items = await this.getItems();
      let subtotal = 0;

      items.forEach(item => {
        subtotal += parseFloat(item.total_price);
      });

      const shipping = parseFloat(this.shipping_cost || 0);
      const tax = parseFloat(this.tax_amount || 0);
      const total = subtotal + shipping + tax;

      await this.update({
        subtotal_amount: subtotal,
        total_amount: total
      });

      return {
        subtotal,
        shipping,
        tax,
        total
      };
    }

    // Generate unique order number
    static async generateOrderNumber() {
      const date = new Date();
      const prefix = 'CMD';
      const timestamp = date.getTime().toString().slice(-6);
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      return `${prefix}${timestamp}${random}`;
    }
  }

  Order.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    client_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    marchand_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    order_number: {
      type: DataTypes.STRING(50),
      unique: true,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'pending',
      validate: {
        isIn: [['pending', 'processing', 'confirmed', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'refunded']]
      }
    },
    payment_status: {
      type: DataTypes.STRING(20),
      defaultValue: 'pending',
      validate: {
        isIn: [['pending', 'authorized', 'paid', 'failed', 'refunded', 'partially_refunded']]
      }
    },
    payment_method: {
      type: DataTypes.STRING(50)
    },
    subtotal_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    },
    shipping_cost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    tax_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    tracking_number: {
      type: DataTypes.STRING(50)
    },
    shipping_method: {
      type: DataTypes.STRING(50)
    },
    shipping_address: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    billing_address: {
      type: DataTypes.TEXT
    },
    notes: {
      type: DataTypes.TEXT
    },
    estimated_delivery_date: {
      type: DataTypes.DATE
    },
    actual_delivery_date: {
      type: DataTypes.DATE
    }
  }, {
    sequelize,
    modelName: 'Order',
    tableName: 'orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: async (order) => {
        if (!order.order_number) {
          order.order_number = await Order.generateOrderNumber();
        }
      },
      afterCreate: async (order) => {
        // Add initial status history entry
        await sequelize.models.OrderStatusHistory.create({
          order_id: order.id,
          status: order.status,
          notes: 'Order created'
        });
      }
    },
    indexes: [
      { fields: ['client_id'] },
      { fields: ['marchand_id'] },
      { fields: ['status'] },
      { fields: ['payment_status'] },
      { fields: ['created_at'] }
    ]
  });

  return Order;
};
