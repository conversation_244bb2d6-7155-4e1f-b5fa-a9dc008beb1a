// Script pour ajouter un utilisateur vétérinaire et des données de test
const { Sequelize, DataTypes, QueryTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// Charger la configuration depuis config.json
const configPath = path.join(__dirname, '../config/config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8')).development;

// Connexion à la base de données
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    dialect: config.dialect,
    logging: console.log
  }
);

async function main() {
  try {
    // Tester la connexion
    await sequelize.authenticate();
    console.log('✅ Connexion à la base de données établie avec succès.');

    // Hasher le mot de passe
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);

    // Vérifier si l'utilisateur existe déjà
    const userExistsQuery = `SELECT id FROM "users" WHERE email = '<EMAIL>'`;
    const existingUser = await sequelize.query(userExistsQuery, { type: QueryTypes.SELECT });

    let userId;
    if (existingUser.length > 0) {
      userId = existingUser[0].id;
      console.log(`✅ L'utilisateur vétérinaire existe déjà avec l'ID: ${userId}`);
    } else {
      // Insérer un utilisateur vétérinaire
      const insertUserQuery = `
        INSERT INTO "users" (nom, prenom, email, password, role, created_at, updated_at)
        VALUES ('Dupont', 'Jean', '<EMAIL>', :hashedPassword, 'veterinaire', NOW(), NOW())
        RETURNING id
      `;
      const userResult = await sequelize.query(insertUserQuery, {
        replacements: { hashedPassword },
        type: QueryTypes.INSERT
      });
      userId = userResult[0][0].id;
      console.log(`✅ Utilisateur vétérinaire créé avec l'ID: ${userId}`);
    }

    // Vérifier si le profil vétérinaire existe déjà
    const vetExistsQuery = `SELECT id FROM "veterinaires" WHERE user_id = :userId`;
    const existingVet = await sequelize.query(vetExistsQuery, {
      replacements: { userId },
      type: QueryTypes.SELECT
    });

    let vetId;
    if (existingVet.length > 0) {
      vetId = existingVet[0].id;
      console.log(`✅ Le profil vétérinaire existe déjà avec l'ID: ${vetId}`);
    } else {
      // Insérer un profil vétérinaire
      const insertVetQuery = `
        INSERT INTO "veterinaires" (
          user_id, numero_ordre, specialites, disponibilites,
          telephone, email_professionnel, adresse_cabinet,
          description, tarifs, note_moyenne, nombre_avis, statut,
          created_at, updated_at
        )
        VALUES (
          :userId, 'VET-12345', ARRAY['aviculture', 'volailles'],
          '{"lundi": "09:00-17:00", "mardi": "09:00-17:00", "mercredi": "09:00-17:00", "jeudi": "09:00-17:00", "vendredi": "09:00-12:00"}',
          '0555123789', '<EMAIL>', 'Cabinet Vétérinaire, Alger',
          'Vétérinaire spécialisé en aviculture avec 10 ans d''expérience',
          '{"consultation": 2000, "visite": 3500}',
          4.5, 10, 'actif',
          NOW(), NOW()
        )
        RETURNING id
      `;
      const vetResult = await sequelize.query(insertVetQuery, {
        replacements: { userId },
        type: QueryTypes.INSERT
      });
      vetId = vetResult[0][0].id;
      console.log(`✅ Profil vétérinaire créé avec l'ID: ${vetId}`);
    }

    // Vérifier si l'éleveur existe déjà
    const eleveurExistsQuery = `SELECT id FROM "eleveurs" WHERE email = '<EMAIL>'`;
    const existingEleveur = await sequelize.query(eleveurExistsQuery, { type: QueryTypes.SELECT });

    let eleveurId;
    if (existingEleveur.length > 0) {
      eleveurId = existingEleveur[0].id;
      console.log(`✅ L'éleveur existe déjà avec l'ID: ${eleveurId}`);
    } else {
      // Insérer un éleveur
      const insertEleveurQuery = `
        INSERT INTO "eleveurs" (
          nom, prenom, email, telephone, adresse,
          date_inscription, statut, created_at, updated_at, date_modification
        )
        VALUES (
          'Martin', 'Pierre', '<EMAIL>', '0555987654', 'Ferme avicole, Blida',
          NOW(), 'actif', NOW(), NOW(), NOW()
        )
        RETURNING id
      `;
      const eleveurResult = await sequelize.query(insertEleveurQuery, {
        type: QueryTypes.INSERT
      });
      eleveurId = eleveurResult[0][0].id;
      console.log(`✅ Éleveur créé avec l'ID: ${eleveurId}`);
    }

    // Insérer une consultation
    const insertConsultationQuery = `
      INSERT INTO "consultations" (
        veterinaire_id, eleveur_id, date, symptomes,
        diagnostic, traitement, notes, statut,
        urgence, cout, note_satisfaction, created_at, updated_at
      )
      VALUES (
        :userId, :eleveurId, NOW() + INTERVAL '2 days', 'Problèmes respiratoires chez les poulets',
        NULL, NULL, 'Consultation planifiée suite à un appel téléphonique', 'programmee',
        false, 2500, NULL, NOW(), NOW()
      )
      ON CONFLICT DO NOTHING
      RETURNING id
    `;

    const consultationResult = await sequelize.query(insertConsultationQuery, {
      replacements: { userId, eleveurId },
      type: QueryTypes.INSERT
    });

    if (consultationResult[0].length > 0) {
      console.log(`✅ Consultation créée avec l'ID: ${consultationResult[0][0].id}`);
    } else {
      console.log('⚠️ La consultation existe déjà ou n\'a pas pu être créée');
    }

    // Insérer une alerte de stock
    const insertAlerteQuery = `
      INSERT INTO "alertes_stock" (
        eleveur_id, type_alerte, priorite, statut, titre,
        message, date_declenchement, visible, created_at, updated_at, date_modification
      )
      VALUES (
        :eleveurId, 'mortalite_elevee', 'haute', 'active', 'Mortalité élevée détectée',
        'Une hausse de mortalité a été détectée dans votre élevage. Contactez votre vétérinaire.',
        NOW() - INTERVAL '1 day', true, NOW(), NOW(), NOW()
      )
      ON CONFLICT DO NOTHING
      RETURNING id
    `;

    const alerteResult = await sequelize.query(insertAlerteQuery, {
      replacements: { eleveurId },
      type: QueryTypes.INSERT
    });

    if (alerteResult[0].length > 0) {
      console.log(`✅ Alerte créée avec l'ID: ${alerteResult[0][0].id}`);
    } else {
      console.log('⚠️ L\'alerte existe déjà ou n\'a pas pu être créée');
    }

    console.log('✅ Données de test ajoutées avec succès.');

  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout des données de test:', error);
  } finally {
    await sequelize.close();
  }
}

main();
