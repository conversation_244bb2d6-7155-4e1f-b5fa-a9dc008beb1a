const { 
  FeedItem, 
  FeedSupplier, 
  FeedStock, 
  FeedConsumptionLog, 
  FeedPlan, 
  FeedComposition, 
  FeedAlert,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  User,
  sequelize
} = require('../models');
const { Op } = require('sequelize');

/**
 * Feed Management Service
 * Business logic layer for feed management operations
 */
class FeedService {
  
  // ==================== FEED ANALYTICS ====================
  
  /**
   * Calculate comprehensive feed analytics for a farm
   */
  async calculateFeedAnalytics(farmId, startDate, endDate) {
    try {
      const analytics = {
        consumption: await this.getConsumptionAnalytics(farmId, startDate, endDate),
        stock: await this.getStockAnalytics(farmId),
        efficiency: await this.getEfficiencyAnalytics(farmId, startDate, endDate),
        costs: await this.getCostAnalytics(farmId, startDate, endDate),
        alerts: await this.getAlertAnalytics(farmId),
        trends: await this.getTrendAnalytics(farmId, startDate, endDate)
      };
      
      return analytics;
    } catch (error) {
      console.error('Error calculating feed analytics:', error);
      throw error;
    }
  }
  
  /**
   * Get consumption analytics
   */
  async getConsumptionAnalytics(farmId, startDate, endDate) {
    const whereClause = {
      farm_id: farmId,
      verification_status: 'verified'
    };
    
    if (startDate && endDate) {
      whereClause.consumption_date = {
        [Op.between]: [startDate, endDate]
      };
    }
    
    const consumptionData = await FeedConsumptionLog.findAll({
      where: whereClause,
      include: [
        {
          model: FeedStock,
          as: 'feed_stock',
          include: [{
            model: FeedItem,
            as: 'feed_item',
            attributes: ['name', 'category', 'unit_of_measure']
          }]
        }
      ],
      order: [['consumption_date', 'ASC']]
    });
    
    // Calculate totals and averages
    const totalConsumption = consumptionData.reduce((sum, log) => sum + log.quantity_consumed, 0);
    const totalCost = consumptionData.reduce((sum, log) => sum + (log.feed_cost || 0), 0);
    const averageFCR = consumptionData.length > 0 
      ? consumptionData.reduce((sum, log) => sum + (log.fcr || 0), 0) / consumptionData.length 
      : 0;
    
    // Group by category
    const byCategory = {};
    consumptionData.forEach(log => {
      const category = log.feed_stock?.feed_item?.category || 'unknown';
      if (!byCategory[category]) {
        byCategory[category] = {
          quantity: 0,
          cost: 0,
          count: 0
        };
      }
      byCategory[category].quantity += log.quantity_consumed;
      byCategory[category].cost += log.feed_cost || 0;
      byCategory[category].count += 1;
    });
    
    // Daily consumption trend
    const dailyConsumption = {};
    consumptionData.forEach(log => {
      const date = log.consumption_date.toISOString().split('T')[0];
      if (!dailyConsumption[date]) {
        dailyConsumption[date] = 0;
      }
      dailyConsumption[date] += log.quantity_consumed;
    });
    
    return {
      total_consumption: totalConsumption,
      total_cost: totalCost,
      average_fcr: averageFCR,
      by_category: byCategory,
      daily_consumption: dailyConsumption,
      consumption_logs_count: consumptionData.length
    };
  }
  
  /**
   * Get stock analytics
   */
  async getStockAnalytics(farmId) {
    const stockEntries = await FeedStock.findAll({
      where: {
        farm_id: farmId,
        status: 'active'
      },
      include: [
        {
          model: FeedItem,
          as: 'feed_item',
          attributes: ['name', 'category', 'unit_of_measure']
        },
        {
          model: FeedSupplier,
          as: 'supplier',
          attributes: ['name']
        }
      ]
    });
    
    const totalValue = stockEntries.reduce((sum, stock) => {
      return sum + (stock.quantity_current * (stock.unit_cost || 0));
    }, 0);
    
    const totalQuantity = stockEntries.reduce((sum, stock) => sum + stock.quantity_current, 0);
    
    // Low stock items
    const lowStockItems = stockEntries.filter(stock => {
      return stock.quantity_current <= (stock.low_stock_threshold || 0);
    });
    
    // Expiring items (within 30 days)
    const expiringItems = stockEntries.filter(stock => {
      if (!stock.expiry_date) return false;
      const daysUntilExpiry = Math.ceil((new Date(stock.expiry_date) - new Date()) / (1000 * 60 * 60 * 24));
      return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    });
    
    // Stock by category
    const byCategory = {};
    stockEntries.forEach(stock => {
      const category = stock.feed_item?.category || 'unknown';
      if (!byCategory[category]) {
        byCategory[category] = {
          quantity: 0,
          value: 0,
          items_count: 0
        };
      }
      byCategory[category].quantity += stock.quantity_current;
      byCategory[category].value += stock.quantity_current * (stock.unit_cost || 0);
      byCategory[category].items_count += 1;
    });
    
    return {
      total_value: totalValue,
      total_quantity: totalQuantity,
      total_items: stockEntries.length,
      low_stock_items: lowStockItems.length,
      expiring_items: expiringItems.length,
      by_category: byCategory,
      low_stock_details: lowStockItems.map(stock => ({
        id: stock.id,
        feed_item: stock.feed_item?.name,
        current_quantity: stock.quantity_current,
        threshold: stock.low_stock_threshold
      })),
      expiring_details: expiringItems.map(stock => ({
        id: stock.id,
        feed_item: stock.feed_item?.name,
        expiry_date: stock.expiry_date,
        days_until_expiry: Math.ceil((new Date(stock.expiry_date) - new Date()) / (1000 * 60 * 60 * 24))
      }))
    };
  }
  
  /**
   * Get efficiency analytics (FCR, consumption patterns)
   */
  async getEfficiencyAnalytics(farmId, startDate, endDate) {
    const whereClause = {
      farm_id: farmId,
      fcr: { [Op.not]: null },
      verification_status: 'verified'
    };
    
    if (startDate && endDate) {
      whereClause.consumption_date = {
        [Op.between]: [startDate, endDate]
      };
    }
    
    const fcrData = await FeedConsumptionLog.findAll({
      where: whereClause,
      attributes: [
        'fcr',
        'consumption_date',
        'quantity_consumed',
        'number_of_birds',
        'average_bird_weight'
      ],
      order: [['consumption_date', 'ASC']]
    });
    
    if (fcrData.length === 0) {
      return {
        average_fcr: 0,
        fcr_trend: [],
        efficiency_score: 0,
        recommendations: []
      };
    }
    
    const averageFCR = fcrData.reduce((sum, log) => sum + log.fcr, 0) / fcrData.length;
    const targetFCR = 1.8; // Standard target for broilers
    const efficiencyScore = Math.max(0, Math.min(100, (targetFCR / averageFCR) * 100));
    
    // FCR trend over time
    const fcrTrend = fcrData.map(log => ({
      date: log.consumption_date,
      fcr: log.fcr,
      consumption: log.quantity_consumed
    }));
    
    // Generate recommendations
    const recommendations = [];
    if (averageFCR > 2.0) {
      recommendations.push({
        type: 'efficiency',
        priority: 'high',
        message: 'FCR élevé détecté. Vérifiez la qualité de l\'aliment et les conditions d\'élevage.'
      });
    }
    
    if (averageFCR > targetFCR) {
      recommendations.push({
        type: 'feed_quality',
        priority: 'medium',
        message: 'Considérez l\'utilisation d\'un aliment de meilleure qualité pour améliorer l\'efficacité.'
      });
    }
    
    return {
      average_fcr: averageFCR,
      target_fcr: targetFCR,
      efficiency_score: efficiencyScore,
      fcr_trend: fcrTrend,
      recommendations: recommendations
    };
  }
  
  /**
   * Get cost analytics
   */
  async getCostAnalytics(farmId, startDate, endDate) {
    const whereClause = {
      farm_id: farmId
    };
    
    if (startDate && endDate) {
      whereClause.consumption_date = {
        [Op.between]: [startDate, endDate]
      };
    }
    
    // Consumption costs
    const consumptionCosts = await FeedConsumptionLog.sum('feed_cost', {
      where: whereClause
    }) || 0;
    
    // Stock purchases
    const stockPurchases = await FeedStock.findAll({
      where: {
        farm_id: farmId,
        purchase_date: startDate && endDate ? {
          [Op.between]: [startDate, endDate]
        } : undefined
      },
      attributes: ['quantity_received', 'unit_cost', 'purchase_date']
    });
    
    const totalPurchaseCost = stockPurchases.reduce((sum, stock) => {
      return sum + (stock.quantity_received * (stock.unit_cost || 0));
    }, 0);
    
    // Cost per bird calculation
    const totalBirds = await FeedConsumptionLog.sum('number_of_birds', {
      where: whereClause
    }) || 1;
    
    const costPerBird = consumptionCosts / totalBirds;
    
    // Monthly cost trend
    const monthlyCosts = {};
    stockPurchases.forEach(stock => {
      const month = stock.purchase_date ? stock.purchase_date.toISOString().substring(0, 7) : 'unknown';
      if (!monthlyCosts[month]) {
        monthlyCosts[month] = 0;
      }
      monthlyCosts[month] += stock.quantity_received * (stock.unit_cost || 0);
    });
    
    return {
      total_consumption_cost: consumptionCosts,
      total_purchase_cost: totalPurchaseCost,
      cost_per_bird: costPerBird,
      monthly_costs: monthlyCosts,
      average_unit_cost: stockPurchases.length > 0 
        ? stockPurchases.reduce((sum, stock) => sum + (stock.unit_cost || 0), 0) / stockPurchases.length 
        : 0
    };
  }
  
  /**
   * Get alert analytics
   */
  async getAlertAnalytics(farmId) {
    const alerts = await FeedAlert.findAll({
      where: {
        farm_id: farmId
      },
      attributes: ['alert_type', 'severity', 'status', 'created_at']
    });
    
    const activeAlerts = alerts.filter(alert => alert.status === 'active');
    
    // Group by type
    const byType = {};
    alerts.forEach(alert => {
      if (!byType[alert.alert_type]) {
        byType[alert.alert_type] = {
          total: 0,
          active: 0,
          resolved: 0
        };
      }
      byType[alert.alert_type].total += 1;
      if (alert.status === 'active') {
        byType[alert.alert_type].active += 1;
      } else if (alert.status === 'resolved') {
        byType[alert.alert_type].resolved += 1;
      }
    });
    
    // Group by severity
    const bySeverity = {};
    activeAlerts.forEach(alert => {
      if (!bySeverity[alert.severity]) {
        bySeverity[alert.severity] = 0;
      }
      bySeverity[alert.severity] += 1;
    });
    
    return {
      total_alerts: alerts.length,
      active_alerts: activeAlerts.length,
      by_type: byType,
      by_severity: bySeverity,
      critical_alerts: activeAlerts.filter(alert => alert.severity === 'critical').length
    };
  }
  
  /**
   * Get trend analytics
   */
  async getTrendAnalytics(farmId, startDate, endDate) {
    // Get consumption trend over time
    const consumptionTrend = await sequelize.query(`
      SELECT 
        DATE(consumption_date) as date,
        SUM(quantity_consumed) as total_consumption,
        AVG(fcr) as average_fcr,
        COUNT(*) as feeding_sessions
      FROM feed_consumption_logs 
      WHERE farm_id = :farmId 
        AND consumption_date BETWEEN :startDate AND :endDate
        AND verification_status = 'verified'
      GROUP BY DATE(consumption_date)
      ORDER BY date ASC
    `, {
      replacements: { farmId, startDate, endDate },
      type: sequelize.QueryTypes.SELECT
    });
    
    // Calculate growth trends
    const trends = {
      consumption_trend: consumptionTrend,
      consumption_growth: this.calculateGrowthRate(consumptionTrend, 'total_consumption'),
      fcr_trend: this.calculateGrowthRate(consumptionTrend, 'average_fcr'),
      feeding_frequency_trend: this.calculateGrowthRate(consumptionTrend, 'feeding_sessions')
    };
    
    return trends;
  }
  
  /**
   * Calculate growth rate for a metric
   */
  calculateGrowthRate(data, metric) {
    if (data.length < 2) return 0;
    
    const firstValue = parseFloat(data[0][metric]) || 0;
    const lastValue = parseFloat(data[data.length - 1][metric]) || 0;
    
    if (firstValue === 0) return 0;
    
    return ((lastValue - firstValue) / firstValue) * 100;
  }
  
  // ==================== FEED RECOMMENDATIONS ====================
  
  /**
   * Generate feed recommendations based on poultry type, age, and performance
   */
  async generateFeedRecommendations(farmId, poultryType, ageInDays, targetWeight = null, currentPerformance = null) {
    try {
      const recommendations = {
        feed_items: await this.recommendFeedItems(poultryType, ageInDays),
        feeding_plan: await this.recommendFeedingPlan(poultryType, ageInDays, targetWeight),
        optimization: await this.recommendOptimizations(farmId, currentPerformance),
        suppliers: await this.recommendSuppliers(farmId)
      };
      
      return recommendations;
    } catch (error) {
      console.error('Error generating feed recommendations:', error);
      throw error;
    }
  }
  
  /**
   * Recommend suitable feed items
   */
  async recommendFeedItems(poultryType, ageInDays) {
    const suitableFeedItems = await FeedItem.findAll({
      where: {
        poultry_types: {
          [Op.contains]: [poultryType]
        },
        min_age_days: {
          [Op.lte]: ageInDays
        },
        max_age_days: {
          [Op.gte]: ageInDays
        },
        status: 'active'
      },
      include: [
        {
          model: FeedComposition,
          as: 'compositions',
          where: { status: 'active' },
          required: false
        }
      ],
      order: [['daily_consumption_per_bird', 'ASC']]
    });
    
    return suitableFeedItems.map(item => ({
      id: item.id,
      name: item.name,
      brand: item.brand,
      category: item.category,
      daily_consumption: item.daily_consumption_per_bird,
      nutritional_info: item.nutritional_info,
      suitability_score: this.calculateSuitabilityScore(item, poultryType, ageInDays)
    }));
  }
  
  /**
   * Calculate suitability score for a feed item
   */
  calculateSuitabilityScore(feedItem, poultryType, ageInDays) {
    let score = 100;
    
    // Age range match
    const ageRange = feedItem.max_age_days - feedItem.min_age_days;
    const agePosition = (ageInDays - feedItem.min_age_days) / ageRange;
    if (agePosition < 0.2 || agePosition > 0.8) {
      score -= 20; // Penalize if age is at the edges of the range
    }
    
    // Poultry type specificity
    if (feedItem.poultry_types.length === 1 && feedItem.poultry_types[0] === poultryType) {
      score += 10; // Bonus for specific poultry type
    }
    
    // Nutritional completeness
    if (feedItem.nutritional_info && Object.keys(feedItem.nutritional_info).length >= 4) {
      score += 5; // Bonus for complete nutritional info
    }
    
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Recommend feeding plan
   */
  async recommendFeedingPlan(poultryType, ageInDays, targetWeight) {
    // Standard feeding plans based on poultry type and age
    const feedingPlans = {
      poulet_chair: {
        '0-21': {
          daily_amount: 50,
          frequency: 4,
          times: ['06:00', '11:00', '16:00', '20:00'],
          feed_type: 'starter'
        },
        '22-35': {
          daily_amount: 120,
          frequency: 3,
          times: ['07:00', '13:00', '19:00'],
          feed_type: 'grower'
        },
        '36-42': {
          daily_amount: 150,
          frequency: 3,
          times: ['07:00', '13:00', '19:00'],
          feed_type: 'finisher'
        }
      },
      pondeuse: {
        '0-18': {
          daily_amount: 80,
          frequency: 3,
          times: ['07:00', '13:00', '18:00'],
          feed_type: 'starter'
        },
        '19-140': {
          daily_amount: 110,
          frequency: 2,
          times: ['07:00', '17:00'],
          feed_type: 'grower'
        },
        '141+': {
          daily_amount: 120,
          frequency: 2,
          times: ['07:00', '17:00'],
          feed_type: 'layer'
        }
      }
    };
    
    const plans = feedingPlans[poultryType];
    if (!plans) {
      return null;
    }
    
    // Find appropriate age range
    let selectedPlan = null;
    for (const [ageRange, plan] of Object.entries(plans)) {
      const [minAge, maxAge] = ageRange.split('-').map(age => 
        age === '+' ? Infinity : parseInt(age)
      );
      
      if (ageInDays >= minAge && ageInDays <= maxAge) {
        selectedPlan = plan;
        break;
      }
    }
    
    if (selectedPlan && targetWeight) {
      // Adjust daily amount based on target weight
      const weightFactor = targetWeight / 2.0; // Assuming 2kg as standard
      selectedPlan.daily_amount = Math.round(selectedPlan.daily_amount * weightFactor);
    }
    
    return selectedPlan;
  }
  
  /**
   * Recommend optimizations based on current performance
   */
  async recommendOptimizations(farmId, currentPerformance) {
    const optimizations = [];
    
    if (!currentPerformance) {
      return optimizations;
    }
    
    // FCR optimization
    if (currentPerformance.fcr > 2.0) {
      optimizations.push({
        type: 'fcr_improvement',
        priority: 'high',
        title: 'Amélioration du FCR',
        description: 'Votre FCR actuel est élevé. Considérez l\'amélioration de la qualité de l\'aliment.',
        actions: [
          'Vérifier la fraîcheur de l\'aliment',
          'Améliorer les conditions de stockage',
          'Considérer un aliment de meilleure qualité',
          'Vérifier l\'état de santé des volailles'
        ]
      });
    }
    
    // Consumption pattern optimization
    if (currentPerformance.consumption_variance > 20) {
      optimizations.push({
        type: 'consumption_consistency',
        priority: 'medium',
        title: 'Régularité de la consommation',
        description: 'La consommation varie beaucoup. Une alimentation plus régulière améliorerait les performances.',
        actions: [
          'Établir des horaires d\'alimentation fixes',
          'Vérifier la disponibilité constante de l\'eau',
          'Surveiller les conditions environnementales'
        ]
      });
    }
    
    return optimizations;
  }
  
  /**
   * Recommend suppliers based on location and performance
   */
  async recommendSuppliers(farmId) {
    const farm = await Ferme.findByPk(farmId, {
      attributes: ['adresse']
    });
    
    if (!farm) {
      return [];
    }
    
    // Extract city from address (simplified)
    const farmCity = farm.adresse ? farm.adresse.split(',')[0].trim() : '';
    
    const suppliers = await FeedSupplier.findAll({
      where: {
        status: 'active',
        delivery_zones: {
          [Op.contains]: [farmCity]
        }
      },
      order: [['rating', 'DESC'], ['name', 'ASC']],
      limit: 5
    });
    
    return suppliers.map(supplier => ({
      id: supplier.id,
      name: supplier.name,
      rating: supplier.rating,
      contact_info: {
        phone: supplier.phone,
        email: supplier.email
      },
      delivery_zones: supplier.delivery_zones,
      payment_terms: supplier.payment_terms
    }));
  }
  
  // ==================== STOCK MANAGEMENT ====================
  
  /**
   * Auto-generate stock alerts based on thresholds and expiry dates
   */
  async generateStockAlerts(farmId) {
    try {
      const stockEntries = await FeedStock.findAll({
        where: {
          farm_id: farmId,
          status: 'active'
        },
        include: [
          {
            model: FeedItem,
            as: 'feed_item',
            attributes: ['name', 'category']
          }
        ]
      });
      
      const alerts = [];
      
      for (const stock of stockEntries) {
        // Low stock alert
        if (stock.quantity_current <= (stock.low_stock_threshold || 0)) {
          const existingAlert = await FeedAlert.findOne({
            where: {
              farm_id: farmId,
              feed_stock_id: stock.id,
              alert_type: 'low_stock',
              status: 'active'
            }
          });
          
          if (!existingAlert) {
            const alert = await FeedAlert.create({
              farm_id: farmId,
              feed_stock_id: stock.id,
              alert_type: 'low_stock',
              severity: stock.quantity_current === 0 ? 'critical' : 'medium',
              title: `Stock bas: ${stock.feed_item?.name || 'Aliment'}`,
              message: `Le stock de ${stock.feed_item?.name || 'cet aliment'} est en dessous du seuil (${stock.quantity_current}/${stock.low_stock_threshold})`,
              threshold_value: stock.low_stock_threshold,
              current_value: stock.quantity_current,
              recommended_action: 'Réapprovisionner le stock',
              status: 'active'
            });
            alerts.push(alert);
          }
        }
        
        // Expiry alert
        if (stock.expiry_date) {
          const daysUntilExpiry = Math.ceil((new Date(stock.expiry_date) - new Date()) / (1000 * 60 * 60 * 24));
          
          if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
            const existingAlert = await FeedAlert.findOne({
              where: {
                farm_id: farmId,
                feed_stock_id: stock.id,
                alert_type: 'expiry_warning',
                status: 'active'
              }
            });
            
            if (!existingAlert) {
              const severity = daysUntilExpiry <= 7 ? 'high' : daysUntilExpiry <= 15 ? 'medium' : 'low';
              
              const alert = await FeedAlert.create({
                farm_id: farmId,
                feed_stock_id: stock.id,
                alert_type: 'expiry_warning',
                severity: severity,
                title: `Expiration proche: ${stock.feed_item?.name || 'Aliment'}`,
                message: `${stock.feed_item?.name || 'Cet aliment'} expire dans ${daysUntilExpiry} jour(s)`,
                threshold_value: 30,
                current_value: daysUntilExpiry,
                recommended_action: 'Utiliser en priorité ou retourner au fournisseur',
                expiry_date: new Date(Date.now() + daysUntilExpiry * 24 * 60 * 60 * 1000),
                status: 'active'
              });
              alerts.push(alert);
            }
          }
        }
      }
      
      return alerts;
    } catch (error) {
      console.error('Error generating stock alerts:', error);
      throw error;
    }
  }
  
  /**
   * Optimize stock levels based on consumption patterns
   */
  async optimizeStockLevels(farmId) {
    try {
      // Get consumption data for the last 90 days
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 90);
      
      const consumptionData = await FeedConsumptionLog.findAll({
        where: {
          farm_id: farmId,
          consumption_date: {
            [Op.gte]: startDate
          },
          verification_status: 'verified'
        },
        include: [
          {
            model: FeedStock,
            as: 'feed_stock',
            include: [{
              model: FeedItem,
              as: 'feed_item',
              attributes: ['id', 'name', 'category']
            }]
          }
        ]
      });
      
      // Group consumption by feed item
      const consumptionByFeedItem = {};
      consumptionData.forEach(log => {
        const feedItemId = log.feed_stock?.feed_item?.id;
        if (feedItemId) {
          if (!consumptionByFeedItem[feedItemId]) {
            consumptionByFeedItem[feedItemId] = {
              feed_item: log.feed_stock.feed_item,
              total_consumption: 0,
              consumption_days: new Set()
            };
          }
          consumptionByFeedItem[feedItemId].total_consumption += log.quantity_consumed;
          consumptionByFeedItem[feedItemId].consumption_days.add(
            log.consumption_date.toISOString().split('T')[0]
          );
        }
      });
      
      const recommendations = [];
      
      for (const [feedItemId, data] of Object.entries(consumptionByFeedItem)) {
        const dailyAverage = data.total_consumption / data.consumption_days.size;
        const recommendedStock = dailyAverage * 30; // 30 days supply
        const safetyStock = dailyAverage * 7; // 7 days safety stock
        const reorderPoint = dailyAverage * 14; // Reorder when 14 days left
        
        // Get current stock
        const currentStock = await FeedStock.findAll({
          where: {
            farm_id: farmId,
            feed_item_id: feedItemId,
            status: 'active'
          }
        });
        
        const totalCurrentStock = currentStock.reduce((sum, stock) => sum + stock.quantity_current, 0);
        
        recommendations.push({
          feed_item: data.feed_item,
          current_stock: totalCurrentStock,
          daily_average_consumption: dailyAverage,
          recommended_stock_level: recommendedStock,
          safety_stock: safetyStock,
          reorder_point: reorderPoint,
          days_of_supply: totalCurrentStock / dailyAverage,
          action: totalCurrentStock < reorderPoint ? 'reorder' : 'maintain'
        });
      }
      
      return recommendations;
    } catch (error) {
      console.error('Error optimizing stock levels:', error);
      throw error;
    }
  }
}

module.exports = new FeedService();