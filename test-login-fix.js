const axios = require('axios');

async function testLoginFix() {
  console.log('=== Test de la correction du rôle de connexion ===');

  // Test avec le serveur minimal (simulé)
  try {
    console.log('1. 🔐 Test de connexion...');

    // Simuler la réponse du serveur avec les données de test
    const loginResponse = {
      token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************.test',
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        first_name: 'Admin',
        last_name: '<PERSON><PERSON><PERSON>'
      }
    };

    console.log('✅ Simulation de connexion réussie');
    console.log('📊 Données utilisateur reçues:', JSON.stringify(loginResponse.user, null, 2));
    console.log('🎯 Rôle extrait:', loginResponse.user.role);

    // Test de la logique de redirection
    const getRedirectPath = (role) => {
      const rolePathMap = {
        'admin': '/admin/dashboard',
        'eleveur': '/eleveur/dashboard',
        'veterinaire': '/veterinaire/dashboard',
        'marchand': '/marchand/dashboard'
      };
      return rolePathMap[role] || '/dashboard';
    };

    const redirectPath = getRedirectPath(loginResponse.user.role);
    console.log('🗺️ Chemin de redirection calculé:', redirectPath);

    // Test du formatage du rôle
    const formattedUser = {
      ...loginResponse.user,
      role: loginResponse.user.role || (loginResponse.user.role?.name ? loginResponse.user.role.name : null)
    };

    console.log('✨ Utilisateur formaté:', JSON.stringify(formattedUser, null, 2));

    if (formattedUser.role === 'admin') {
      console.log('✅ SUCCESS: Le rôle admin est correctement formaté');
      console.log('✅ SUCCESS: La redirection devrait fonctionner vers:', redirectPath);
    } else {
      console.log('❌ FAILURE: Le rôle n\'est pas correctement formaté');
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }

  console.log('🏁 Test terminé');
}

testLoginFix().catch(console.error);
