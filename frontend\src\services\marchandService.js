import axiosInstance from '../utils/axiosConfig';

/**
 * Récupère le résumé du tableau de bord du marchand
 * @returns {Promise<Object>} Données du tableau de bord
 */
export const fetchDashboardSummary = async () => {
  try {
    const response = await axiosInstance.get('/marchand/dashboard/summary');
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération du résumé du tableau de bord:', error);
    throw error;
  }
};

/**
 * Récupère les données de revenus pour une période donnée
 * @param {Date} startDate - Date de début
 * @param {Date} endDate - Date de fin
 * @returns {Promise<Array>} Données de revenus
 */
export const fetchRevenueData = async (startDate, endDate) => {
  try {
    const response = await axiosInstance.get('/marchand/dashboard/revenue', {
      params: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      },
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des données de revenus:', error);
    throw error;
  }
};

/**
 * Récupère les produits du marchand
 * @returns {Promise<Array>} Liste des produits
 */
export const fetchProducts = async () => {
  try {
    const response = await axiosInstance.get('/marchand/products');
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des produits:', error);
    throw error;
  }
};

/**
 * Crée un nouveau produit
 * @param {Object} productData - Données du produit
 * @returns {Promise<Object>} Produit créé
 */
export const createProduct = async (productData) => {
  try {
    const response = await axiosInstance.post('/marchand/products', productData);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création du produit:', error);
    throw error;
  }
};

/**
 * Met à jour le stock d'un produit
 * @param {number} productId - ID du produit
 * @param {number} stockQuantity - Nouvelle quantité en stock
 * @returns {Promise<Object>} Produit mis à jour
 */
export const updateProductStock = async (productId, stockQuantity) => {
  try {
    const response = await axiosInstance.patch(`/marchand/products/${productId}/stock`, {
      stock_quantity: stockQuantity,
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour du stock:', error);
    throw error;
  }
};

/**
 * Récupère les commandes du marchand
 * @param {Object} options - Options de filtrage
 * @param {string} options.status - Statut des commandes à récupérer
 * @returns {Promise<Array>} Liste des commandes
 */
export const fetchOrders = async (options = {}) => {
  try {
    const response = await axiosInstance.get('/marchand/orders', {
      params: options,
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des commandes:', error);
    throw error;
  }
};

/**
 * Crée une nouvelle commande
 * @param {Object} orderData - Données de la commande
 * @returns {Promise<Object>} Commande créée
 */
export const createOrder = async (orderData) => {
  try {
    const response = await axiosInstance.post('/marchand/orders', orderData);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création de la commande:', error);
    throw error;
  }
};

/**
 * Met à jour le statut d'une commande
 * @param {number} orderId - ID de la commande
 * @param {string} status - Nouveau statut
 * @returns {Promise<Object>} Commande mise à jour
 */
export const updateOrderStatus = async (orderId, status) => {
  try {
    const response = await axiosInstance.patch(`/marchand/orders/${orderId}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut de la commande:', error);
    throw error;
  }
};

/**
 * Récupère les recommandations IA pour le marchand
 * @returns {Promise<Object>} Recommandations IA
 */
export const fetchAIRecommendations = async () => {
  try {
    const response = await axiosInstance.get('/marchand/ai/recommendations');
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des recommandations IA:', error);
    throw error;
  }
};
