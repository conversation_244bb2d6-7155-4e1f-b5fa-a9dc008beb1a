/**
 * Service API pour les fonctionnalités marchand
 */

import axiosInstance from '../utils/axiosConfig';

const marchandAPI = {
  // === GESTION DES PRODUITS ===

  // Récupérer tous les produits du marchand
  getProduits: () => {
    return axiosInstance.get('/marchand/produits');
  },

  // Créer un nouveau produit
  createProduit: (produitData) => {
    return axiosInstance.post('/marchand/produits', produitData);
  },

  // Mettre à jour un produit
  updateProduit: (produitId, produitData) => {
    return axiosInstance.put(`/marchand/produits/${produitId}`, produitData);
  },

  // Supprimer un produit
  deleteProduit: (produitId) => {
    return axiosInstance.delete(`/marchand/produits/${produitId}`);
  },

  // Récupérer les statistiques des produits
  getProduitsStats: () => {
    return axiosInstance.get('/marchand/produits/stats');
  },

  // === GESTION DES COMMANDES ===

  // Récupérer toutes les commandes
  getCommandes: () => {
    return axiosInstance.get('/marchand/commandes');
  },

  // Créer une nouvelle commande
  createCommande: (commandeData) => {
    return axiosInstance.post('/marchand/commandes', commandeData);
  },

  // Mettre à jour une commande
  updateCommande: (commandeId, commandeData) => {
    return axiosInstance.put(`/marchand/commandes/${commandeId}`, commandeData);
  },

  // Supprimer une commande
  deleteCommande: (commandeId) => {
    return axiosInstance.delete(`/marchand/commandes/${commandeId}`);
  },

  // Changer le statut d'une commande
  updateCommandeStatus: (commandeId, statut) => {
    return axiosInstance.put(`/marchand/commandes/${commandeId}/status`, { statut });
  },

  // Récupérer les statistiques des commandes
  getCommandesStats: () => {
    return axiosInstance.get('/marchand/commandes/stats');
  },

  // === GESTION DES VENTES ===

  // Récupérer toutes les ventes
  getVentes: () => {
    return axiosInstance.get('/marchand/ventes');
  },

  // Créer une nouvelle vente
  createVente: (venteData) => {
    return axiosInstance.post('/marchand/ventes', venteData);
  },

  // Mettre à jour une vente
  updateVente: (venteId, venteData) => {
    return axiosInstance.put(`/marchand/ventes/${venteId}`, venteData);
  },

  // Supprimer une vente
  deleteVente: (venteId) => {
    return axiosInstance.delete(`/marchand/ventes/${venteId}`);
  },

  // Récupérer les statistiques des ventes
  getVentesStats: () => {
    return axiosInstance.get('/marchand/ventes/stats');
  },

  // === GESTION DE L'INVENTAIRE ===

  // Récupérer l'inventaire complet
  getInventaire: () => {
    return axiosInstance.get('/marchand/inventaire');
  },

  // Mettre à jour le stock d'un produit
  updateStock: (produitId, stockData) => {
    return axiosInstance.put(`/marchand/inventaire/${produitId}/stock`, stockData);
  },

  // Récupérer les alertes de stock
  getStockAlerts: () => {
    return axiosInstance.get('/marchand/inventaire/alerts');
  },

  // === GESTION DES CLIENTS ===

  // Récupérer tous les clients
  getClients: () => {
    return axiosInstance.get('/marchand/clients');
  },

  // Créer un nouveau client
  createClient: (clientData) => {
    return axiosInstance.post('/marchand/clients', clientData);
  },

  // === RAPPORTS ET ANALYTICS ===

  // Récupérer les données du dashboard
  getDashboardData: () => {
    return axiosInstance.get('/marchand/dashboard');
  },

  // === PARAMÈTRES ===

  // Récupérer les paramètres du marchand
  getSettings: () => {
    return axiosInstance.get('/marchand/settings');
  },

  // Mettre à jour les paramètres
  updateSettings: (settings) => {
    return axiosInstance.put('/marchand/settings', settings);
  }
};

export { marchandAPI };
export default marchandAPI;
