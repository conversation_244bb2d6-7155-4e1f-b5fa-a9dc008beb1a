/**
 * Service API pour les fonctionnalités marchand
 */

import axiosInstance from '../utils/axiosConfig';

const marchandAPI = {
  // === GESTION DES PRODUITS ===

  // Récupérer tous les produits du marchand
  getProduits: () => {
    return axiosInstance.get('/api/marchand/produits');
  },

  // Créer un nouveau produit
  createProduit: (produitData) => {
    return axiosInstance.post('/api/marchand/produits', produitData);
  },

  // Mettre à jour un produit
  updateProduit: (produitId, produitData) => {
    return axiosInstance.put(`/api/marchand/produits/${produitId}`, produitData);
  },

  // Supprimer un produit
  deleteProduit: (produitId) => {
    return axiosInstance.delete(`/api/marchand/produits/${produitId}`);
  },

  // Récupérer les statistiques des produits
  getProduitsStats: () => {
    return axiosInstance.get('/api/marchand/produits/stats');
  },

  // === GESTION DES COMMANDES ===

  // Récupérer toutes les commandes
  getCommandes: () => {
    return axiosInstance.get('/api/marchand/commandes');
  },

  // Créer une nouvelle commande
  createCommande: (commandeData) => {
    return axiosInstance.post('/api/marchand/commandes', commandeData);
  },

  // Mettre à jour une commande
  updateCommande: (commandeId, commandeData) => {
    return axiosInstance.put(`/api/marchand/commandes/${commandeId}`, commandeData);
  },

  // Supprimer une commande
  deleteCommande: (commandeId) => {
    return axiosInstance.delete(`/api/marchand/commandes/${commandeId}`);
  },

  // Changer le statut d'une commande
  updateCommandeStatus: (commandeId, statut) => {
    return axiosInstance.put(`/api/marchand/commandes/${commandeId}/status`, { statut });
  },

  // Récupérer les statistiques des commandes
  getCommandesStats: () => {
    return axiosInstance.get('/api/marchand/commandes/stats');
  },

  // === GESTION DES VENTES ===

  // Récupérer toutes les ventes
  getVentes: () => {
    return axiosInstance.get('/api/marchand/ventes');
  },

  // Créer une nouvelle vente
  createVente: (venteData) => {
    return axiosInstance.post('/api/marchand/ventes', venteData);
  },

  // Mettre à jour une vente
  updateVente: (venteId, venteData) => {
    return axiosInstance.put(`/api/marchand/ventes/${venteId}`, venteData);
  },

  // Supprimer une vente
  deleteVente: (venteId) => {
    return axiosInstance.delete(`/api/marchand/ventes/${venteId}`);
  },

  // Récupérer les statistiques des ventes
  getVentesStats: () => {
    return axiosInstance.get('/api/marchand/ventes/stats');
  },

  // === GESTION DE L'INVENTAIRE ===

  // Récupérer l'inventaire complet
  getInventaire: () => {
    return axiosInstance.get('/api/marchand/inventaire');
  },

  // Mettre à jour le stock d'un produit
  updateStock: (produitId, stockData) => {
    return axiosInstance.put(`/api/marchand/inventaire/${produitId}/stock`, stockData);
  },

  // Récupérer les alertes de stock
  getStockAlerts: () => {
    return axiosInstance.get('/api/marchand/inventaire/alerts');
  },

  // === GESTION DES CLIENTS ===

  // Récupérer tous les clients
  getClients: () => {
    return axiosInstance.get('/api/marchand/clients');
  },

  // Créer un nouveau client
  createClient: (clientData) => {
    return axiosInstance.post('/api/marchand/clients', clientData);
  },

  // === RAPPORTS ET ANALYTICS ===

  // Récupérer les données du dashboard
  getDashboardData: () => {
    return axiosInstance.get('/api/marchand/dashboard');
  },

  // === PARAMÈTRES ===

  // Récupérer les paramètres du marchand
  getSettings: () => {
    return axiosInstance.get('/api/marchand/settings');
  },

  // Mettre à jour les paramètres
  updateSettings: (settings) => {
    return axiosInstance.put('/api/marchand/settings', settings);
  }
};

export { marchandAPI };
export default marchandAPI;
