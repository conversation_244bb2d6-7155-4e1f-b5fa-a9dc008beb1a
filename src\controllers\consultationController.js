const { Consultation, User, Volaille, Veterinaire } = require('../models');
const { Op } = require('sequelize');

// Créer une nouvelle consultation
const createConsultation = async (req, res) => {
  try {
    const { eleveur_id, volaille_id, date, heure, type, description } = req.body;
    const veterinaire_id = req.user.id;

    // Vérifier les disponibilités du vétérinaire
    const existingConsultation = await Consultation.findOne({
      where: {
        veterinaire_id,
        date,
        heure,
        statut: { [Op.ne]: 'annulée' }
      }
    });

    if (existingConsultation) {
      return res.status(400).json({
        error: 'Vous avez déjà une consultation prévue à cette date et heure'
      });
    }

    const consultation = await Consultation.create({
      eleveur_id,
      veterinaire_id,
      volaille_id,
      date,
      heure,
      type,
      description,
      statut: 'en_attente'
    });

    res.status(201).json({
      message: 'Consultation créée avec succès',
      consultation
    });

  } catch (error) {
    console.error('Erreur lors de la création de la consultation:', error);
    res.status(500).json({
      error: 'Erreur lors de la création de la consultation'
    });
  }
};

// Récupérer les consultations à venir
const getUpcomingConsultations = async (req, res) => {
  try {
    const veterinaire_id = req.user.id;
    const today = new Date();

    const consultations = await Consultation.findAll({
      where: {
        veterinaire_id,
        date: { [Op.gte]: today },
        statut: { [Op.ne]: 'annulée' }
      },
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'prenom', 'email', 'telephone']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ],
      order: [['date', 'ASC'], ['heure', 'ASC']]
    });

    res.json(consultations);

  } catch (error) {
    console.error('Erreur lors de la récupération des consultations à venir:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des consultations à venir'
    });
  }
};

// Mettre à jour une consultation
const updateConsultation = async (req, res) => {
  try {
    const { id } = req.params;
    const { diagnostic, traitement, notes, ordonnance } = req.body;
    const veterinaire_id = req.user.id;

    const consultation = await Consultation.findOne({
      where: { id, veterinaire_id }
    });

    if (!consultation) {
      return res.status(404).json({
        error: 'Consultation non trouvée'
      });
    }

    await consultation.update({
      diagnostic,
      traitement,
      notes,
      ordonnance,
      statut: 'terminée',
      date_modification: new Date()
    });

    res.json({
      message: 'Consultation mise à jour avec succès',
      consultation
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour de la consultation:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour de la consultation'
    });
  }
};

// Annuler une consultation
const cancelConsultation = async (req, res) => {
  try {
    const { id } = req.params;
    const { raison_annulation } = req.body;
    const veterinaire_id = req.user.id;

    const consultation = await Consultation.findOne({
      where: { id, veterinaire_id }
    });

    if (!consultation) {
      return res.status(404).json({
        error: 'Consultation non trouvée'
      });
    }

    await consultation.update({
      statut: 'annulée',
      raison_annulation,
      date_modification: new Date()
    });

    res.json({
      message: 'Consultation annulée avec succès',
      consultation
    });

  } catch (error) {
    console.error('Erreur lors de l\'annulation de la consultation:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'annulation de la consultation'
    });
  }
};

// Récupérer les statistiques des consultations
const getConsultationStats = async (req, res) => {
  try {
    const veterinaire_id = req.user.id;
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Statistiques du mois en cours
const monthlyStats = await Consultation.findAll({
      where: {
        veterinaire_id,
        date: { [Op.gte]: startOfMonth },
        statut: { [Op.ne]: 'annulée' }
      },
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['type']
    });

    // Total des consultations
    const totalConsultations = await Consultation.count({
      where: {
        veterinaire_id,
        statut: { [Op.ne]: 'annulée' }
      }
    });

    // Consultations par statut
    const consultationsByStatus = await Consultation.findAll({
      where: { veterinaire_id },
      attributes: [
        'statut',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['statut']
    });

    res.json({
      monthlyStats,
      totalConsultations,
      consultationsByStatus
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques'
    });
  }
};

module.exports = {
  createConsultation,
  getUpcomingConsultations,
  updateConsultation,
  cancelConsultation,
  getConsultationStats
};