'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('veterinaires', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      numero_ordre: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      specialites: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: false,
        defaultValue: []
      },
      disponibilites: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      telephone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      email_professionnel: {
        type: Sequelize.STRING,
        allowNull: true
      },
      adresse_cabinet: {
        type: Sequelize.STRING,
        allowNull: true
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      tarifs: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      note_moyenne: {
        type: Sequelize.FLOAT,
        allowNull: true
      },
      nombre_avis: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      statut: {
        type: Sequelize.ENUM('actif', 'inactif', 'en_conge'),
        allowNull: false,
        defaultValue: 'actif'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Ajouter les index
    await queryInterface.addIndex('veterinaires', ['user_id']);
    await queryInterface.addIndex('veterinaires', ['numero_ordre'], { unique: true });
    await queryInterface.addIndex('veterinaires', ['statut']);
    await queryInterface.addIndex('veterinaires', ['specialites']);
  },

  down: async (queryInterface, Sequelize) => {
    // Supprimer les index
    await queryInterface.removeIndex('veterinaires', ['user_id']);
    await queryInterface.removeIndex('veterinaires', ['numero_ordre']);
    await queryInterface.removeIndex('veterinaires', ['statut']);
    await queryInterface.removeIndex('veterinaires', ['specialites']);

    // Supprimer la table
    await queryInterface.dropTable('veterinaires');
  }
};
