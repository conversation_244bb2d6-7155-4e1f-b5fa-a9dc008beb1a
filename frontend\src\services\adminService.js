import axiosInstance from '../utils/axiosConfig';

/**
 * Service pour les fonctionnalités d'administration
 * Gère les appels API pour les statistiques, les rôles, les plans d'abonnement, etc.
 */
const adminService = {
  /**
   * Récupère les statistiques générales pour le tableau de bord admin
   * @returns {Promise} Promesse contenant les statistiques
   */
  getStats: async () => {
    try {
      const response = await axiosInstance.get('/admin/stats');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  },

  /**
   * Récupère la liste des utilisateurs avec pagination
   * @param {Object} params Paramètres de pagination et filtres
   * @returns {Promise} Promesse contenant la liste des utilisateurs
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axiosInstance.get('/admin/users', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      throw error;
    }
  },

  /**
   * Récupère la liste des rôles
   * @returns {Promise} Promesse contenant la liste des rôles
   */
  getRoles: async () => {
    try {
      const response = await axiosInstance.get('/admin/roles');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des rôles:', error);
      throw error;
    }
  },

  /**
   * Crée un nouvel utilisateur
   * @param {Object} userData Données de l'utilisateur à créer
   * @returns {Promise} Promesse contenant les données de l'utilisateur créé
   */
  createUser: async (userData) => {
    try {
      const response = await axiosInstance.post('/admin/users', userData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de l\'utilisateur:', error);
      throw error;
    }
  },

  /**
   * Met à jour un utilisateur existant
   * @param {number} userId ID de l'utilisateur à mettre à jour
   * @param {Object} userData Données de l'utilisateur à mettre à jour
   * @returns {Promise} Promesse contenant les données de l'utilisateur mis à jour
   */
  updateUser: async (userId, userData) => {
    try {
      const response = await axiosInstance.put(`/admin/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
      throw error;
    }
  }
};

export default adminService;
