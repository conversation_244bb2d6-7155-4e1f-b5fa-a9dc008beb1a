const admin = require('firebase-admin');

let firebaseInitialized = false;

try {
  const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT
    ? JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT)
    : require('../firebase-service-account.json');

  if (serviceAccount && process.env.FIREBASE_PROJECT_ID) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}.firebaseio.com`
    });
    firebaseInitialized = true;
    console.log('Firebase Admin SDK initialized successfully');
  } else {
    console.log('Firebase credentials not provided, Firebase features will be disabled');
  }
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  console.log('Firebase features will be disabled');
}

module.exports = {
  admin,
  isInitialized: () => firebaseInitialized
};
