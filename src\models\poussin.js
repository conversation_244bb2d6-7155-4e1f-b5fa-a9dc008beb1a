const { DataTypes, Model } = require('sequelize');

/**
 * <PERSON><PERSON><PERSON><PERSON> pour la gestion spécialisée des poussins
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 *
 * Fonctionnalités:
 * - Gestion des lots de poussins
 * - Suivi de croissance
 * - Monitoring de la mortalité
 * - Traçabilité complète
 */
module.exports = (sequelize) => {
  class Poussin extends Model {
    static associate(models) {
      Poussin.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      // Si Poussin peut avoir plusieurs SuiviVeterinaire
      Poussin.hasMany(models.SuiviVeterinaire, {
        foreignKey: 'poussin_id',
        as: 'suivisVeterinaires'
      });
    }

    calculerPerformances() { // Changé en méthode d'instance
      const ageJours = this.age_jours;
      const poidsActuel = this.poids_moyen;
      const poidsObjectif = this.poids_objectif;

      return {
        age_jours: ageJours,
        poids_actuel: poidsActuel,
        poids_objectif: poidsObjectif,
        ecart_objectif: poidsObjectif ? ((poidsActuel - poidsObjectif) / poidsObjectif * 100) : null,
        gain_quotidien: this.gain_quotidien_moyen,
        taux_mortalite: this.taux_mortalite,
        quantite_restante: this.quantite_actuelle,
        taux_survie: ((this.quantite_actuelle / this.quantite_initiale) * 100)
      };
    }

    async ajouterVaccination(vaccination) { // Changé en méthode d'instance et async
      const vaccinations = this.vaccinations || [];
      vaccinations.push({
        ...vaccination,
        date_ajout: new Date()
      });
      this.vaccinations = vaccinations;
      return await this.save(); // Ajout de await
    }

    async ajouterTraitement(traitement) { // Changé en méthode d'instance et async
      const traitements = this.traitements || [];
      traitements.push({
        ...traitement,
        date_ajout: new Date()
      });
      this.traitements = traitements;
      return await this.save(); // Ajout de await
    }

    static async findByEleveur(eleveurId, options = {}) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          ...options.where
        },
        order: [['date_arrivee', 'DESC']],
        ...options
      });
    }

    static async findActifs(eleveurId) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          statut: 'actif'
        },
        order: [['date_arrivee', 'DESC']]
      });
    }

    static async getStatistiques(eleveurId) {
      const poussins = await this.findByEleveur(eleveurId);

      const stats = {
        total_lots: poussins.length,
        lots_actifs: poussins.filter(p => p.statut === 'actif').length,
        total_poussins: poussins.reduce((sum, p) => sum + (p.quantite_initiale || 0), 0),
        poussins_vivants: poussins.reduce((sum, p) => sum + (p.quantite_actuelle || 0), 0),
        taux_mortalite_global: 0,
        poids_moyen_global: 0,
        lots_avec_alerte: poussins.filter(p => p.alerte_active).length
      };

      if (stats.total_poussins > 0) {
        const totalMorts = stats.total_poussins - stats.poussins_vivants;
        stats.taux_mortalite_global = (totalMorts / stats.total_poussins) * 100;
      }

      const poussinsAvecPoids = poussins.filter(p => p.poids_moyen);
      if (poussinsAvecPoids.length > 0) {
        stats.poids_moyen_global = poussinsAvecPoids.reduce((sum, p) => sum + (parseFloat(p.poids_moyen) || 0), 0) / poussinsAvecPoids.length;
      }

      return stats;
    }
  }

  Poussin.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'eleveurs', // Nom de la table Eleveurs
        key: 'id'
      },
      comment: 'Référence vers l\'éleveur propriétaire'
    },
    lot_numero: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Numéro unique du lot de poussins'
    },
    race: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Race des poussins (ex: Cobb 500, Ross 308, ISA Brown)'
    },
    souche: {
      type: DataTypes.STRING(50),
      comment: 'Souche spécifique de la race'
    },
    quantite_initiale: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      },
      comment: 'Nombre de poussins à l\'arrivée'
    },
    quantite_actuelle: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Nombre de poussins vivants actuellement'
    },
    date_eclosion: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Date d\'éclosion des poussins'
    },
    date_arrivee: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Date d\'arrivée dans l\'élevage'
    },
    age_jours: {
      type: DataTypes.VIRTUAL,
      get() {
        const dateEclosion = this.getDataValue('date_eclosion');
        if (!dateEclosion) return null;
        const maintenant = new Date();
        const diffTime = Math.abs(maintenant - new Date(dateEclosion)); // Assurer que dateEclosion est un objet Date
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      },
      comment: 'Âge calculé en jours depuis l\'éclosion'
    },
    poids_moyen: {
      type: DataTypes.DECIMAL(6, 2),
      comment: 'Poids moyen du lot en grammes'
    },
    poids_objectif: {
      type: DataTypes.DECIMAL(6, 2),
      comment: 'Poids objectif selon l\'âge et la race'
    },
    gain_quotidien_moyen: {
      type: DataTypes.DECIMAL(5, 2),
      comment: 'Gain de poids quotidien moyen en grammes'
    },
    taux_mortalite: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'Taux de mortalité en pourcentage'
    },
    mortalite_cumulative: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre total de morts depuis l\'arrivée'
    },
    statut: {
      type: DataTypes.ENUM('actif', 'vendu', 'transfere', 'abattu', 'archive'),
      defaultValue: 'actif',
      comment: 'Statut actuel du lot'
    },
    type_elevage: {
      type: DataTypes.ENUM('chair', 'pondeuse', 'reproducteur', 'mixte'),
      allowNull: false,
      comment: 'Destination finale des poussins'
    },
    batiment: {
      type: DataTypes.STRING(50),
      comment: 'Bâtiment ou zone d\'élevage'
    },
    conditions_elevage: {
      type: DataTypes.JSONB,
      defaultValue: {
        temperature: null,
        humidite: null,
        ventilation: null,
        eclairage: null,
        densite: null
      },
      comment: 'Conditions d\'élevage actuelles'
    },
    alimentation: {
      type: DataTypes.JSONB,
      defaultValue: {
        type_aliment: null,
        consommation_quotidienne: null,
        indice_consommation: null
      },
      comment: 'Données d\'alimentation'
    },
    vaccinations: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Historique des vaccinations'
    },
    traitements: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Historique des traitements médicaux'
    },
    fournisseur: {
      type: DataTypes.STRING(100),
      comment: 'Fournisseur des poussins'
    },
    prix_achat_unitaire: {
      type: DataTypes.DECIMAL(8, 2),
      comment: 'Prix d\'achat par poussin en DA'
    },
    cout_total_actuel: {
      type: DataTypes.DECIMAL(12, 2),
      comment: 'Coût total investi (achat + alimentation + soins)'
    },
    notes: {
      type: DataTypes.TEXT,
      comment: 'Notes et observations de l\'éleveur'
    },
    alerte_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si une alerte est active pour ce lot'
    },
    // date_modification est géré par timestamps: true (updatedAt)
  }, {
    sequelize,
    modelName: 'Poussin',
    tableName: 'poussins',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at', // Sequelize gère updated_at, donc date_modification peut être redondant
    indexes: [
      { fields: ['eleveur_id'] },
      { fields: ['lot_numero'] },
      { fields: ['statut'] },
      { fields: ['date_eclosion'] },
      { fields: ['type_elevage'] },
      { unique: true, fields: ['eleveur_id', 'lot_numero'] }
    ],
    hooks: {
      beforeUpdate: (poussin, options) => {
        if (poussin.quantite_initiale && (poussin.changed('mortalite_cumulative') || poussin.changed('quantite_initiale'))) {
          const tauxMortalite = (poussin.mortalite_cumulative / poussin.quantite_initiale) * 100;
          poussin.taux_mortalite = Math.round(tauxMortalite * 100) / 100;
        }
        if (poussin.changed('mortalite_cumulative') || poussin.changed('quantite_initiale')) {
            poussin.quantite_actuelle = (poussin.quantite_initiale || 0) - (poussin.mortalite_cumulative || 0);
        }
        if (poussin.taux_mortalite > 5) {
          poussin.alerte_active = true;
        } else {
          // Optionnel: désactiver l'alerte si le taux redescend en dessous du seuil
          // et que l'alerte n'est pas maintenue pour une autre raison.
          // poussin.alerte_active = false;
        }
        // La colonne date_modification est gérée par `updatedAt: 'updated_at'` ou `timestamps: true`
      }
    }
  });

  return Poussin;
};
