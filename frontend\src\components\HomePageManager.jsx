import React, { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  IconButton,
  Chip
} from '@mui/material';
import { DragIndicator, Visibility, VisibilityOff } from '@mui/icons-material';

// Composant SortableItem pour les sections
const SortableSection = ({ id, children, isActive }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Card
        sx={{
          mb: 2,
          border: isDragging ? '2px dashed #1976d2' : '1px solid #e0e0e0',
          backgroundColor: isActive ? '#f3f4f6' : 'white',
          '&:hover': {
            backgroundColor: '#f9f9f9',
            boxShadow: 2,
          },
        }}
      >
        <CardContent sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
          <IconButton
            {...attributes}
            {...listeners}
            sx={{
              cursor: 'grab',
              mr: 2,
              '&:active': { cursor: 'grabbing' }
            }}
          >
            <DragIndicator />
          </IconButton>
          {children}
        </CardContent>
      </Card>
    </div>
  );
};

const HomePageManager = () => {
  const [activeId, setActiveId] = useState(null);
  const [sections, setSections] = useState([
    {
      id: 'hero',
      name: 'Section Hero',
      description: 'Bannière principale avec titre et call-to-action',
      visible: true,
      order: 1
    },
    {
      id: 'features',
      name: 'Section Fonctionnalités',
      description: 'Présentation des principales fonctionnalités',
      visible: true,
      order: 2
    },
    {
      id: 'about',
      name: 'Section À Propos',
      description: 'Informations sur l\'entreprise',
      visible: true,
      order: 3
    },
    {
      id: 'services',
      name: 'Section Services',
      description: 'Liste des services proposés',
      visible: true,
      order: 4
    },
    {
      id: 'testimonials',
      name: 'Section Témoignages',
      description: 'Avis et retours clients',
      visible: false,
      order: 5
    },
    {
      id: 'contact',
      name: 'Section Contact',
      description: 'Formulaire de contact et informations',
      visible: true,
      order: 6
    }
  ]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setSections((sections) => {
        const oldIndex = sections.findIndex(section => section.id === active.id);
        const newIndex = sections.findIndex(section => section.id === over.id);

        const newSections = arrayMove(sections, oldIndex, newIndex);

        // Mettre à jour l'ordre
        return newSections.map((section, index) => ({
          ...section,
          order: index + 1
        }));
      });
    }

    setActiveId(null);
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  const toggleVisibility = (sectionId) => {
    setSections(sections.map(section =>
      section.id === sectionId
        ? { ...section, visible: !section.visible }
        : section
    ));
  };

  const activeSection = sections.find(section => section.id === activeId);

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        🏠 Gestionnaire de Page d'Accueil
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
          Réorganisez les sections de votre page d'accueil
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Glissez-déposez les sections pour changer leur ordre d'affichage
        </Typography>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragCancel={handleDragCancel}
          modifiers={[restrictToVerticalAxis]}
        >
          <SortableContext
            items={sections.map(section => section.id)}
            strategy={verticalListSortingStrategy}
          >
            {sections.map((section, index) => (
              <SortableSection
                key={section.id}
                id={section.id}
                isActive={section.id === activeId}
              >
                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" sx={{ mr: 2 }}>
                      {section.name}
                    </Typography>
                    <Chip
                      label={`Position ${index + 1}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mr: 1 }}
                    />
                    <Chip
                      label={section.visible ? 'Visible' : 'Masquée'}
                      size="small"
                      color={section.visible ? 'success' : 'default'}
                      variant={section.visible ? 'filled' : 'outlined'}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {section.description}
                  </Typography>
                </Box>

                <IconButton
                  onClick={() => toggleVisibility(section.id)}
                  color={section.visible ? 'primary' : 'default'}
                  sx={{ ml: 1 }}
                >
                  {section.visible ? <Visibility /> : <VisibilityOff />}
                </IconButton>
              </SortableSection>
            ))}
          </SortableContext>

          <DragOverlay>
            {activeSection ? (
              <Card sx={{
                opacity: 0.9,
                boxShadow: 4,
                transform: 'rotate(5deg)',
              }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
                  <DragIndicator sx={{ mr: 2, color: 'primary.main' }} />
                  <Box>
                    <Typography variant="h6">
                      {activeSection.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {activeSection.description}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            ) : null}
          </DragOverlay>
        </DndContext>

        <Box sx={{ mt: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            💡 Conseils : Utilisez les icônes d'œil pour masquer/afficher les sections.
            L'ordre d'affichage correspond à l'ordre dans cette liste.
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default HomePageManager;
