// Configuration de l'environnement de test pour Jest avec base de données réelle
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import testDbService from './services/testDbService';

// Configuration de react-testing-library
configure({
  testIdAttribute: 'data-testid',
});

// Configuration des variables d'environnement
process.env.REACT_APP_API_URL = 'http://localhost:3000';
process.env.TEST_DB_HOST = 'localhost';
process.env.TEST_DB_PORT = '5432';
process.env.TEST_DB_NAME = 'poultraydz_db';
process.env.TEST_DB_USER = 'poultraydz_user';
process.env.TEST_DB_PASSWORD = 'poultraydz_password';

// Mock pour IntersectionObserver (utilisé par Material-UI)
class MockIntersectionObserver {
  constructor() {
    this.observe = jest.fn();
    this.unobserve = jest.fn();
    this.disconnect = jest.fn();
  }
}
global.IntersectionObserver = MockIntersectionObserver;

// Mock pour ResizeObserver
class ResizeObserverMock {
  observe() {}
  unobserve() {}
  disconnect() {}
}
global.ResizeObserver = ResizeObserverMock;

// Suppression des avertissements de console pendant les tests
global.console = {
  ...console,
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Mock pour window.matchMedia (utilisé par Material-UI)
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock pour les animations du navigateur
global.requestAnimationFrame = callback => setTimeout(callback, 0);
global.cancelAnimationFrame = id => clearTimeout(id);

// Mock pour localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock pour les traductions i18n
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: {
      changeLanguage: jest.fn(),
    },
  }),
  initReactI18next: {
    type: '3rdParty',
    init: jest.fn(),
  },
}));

// Remplacer les mocks des hooks personnalisés par des hooks qui utilisent la base de données réelle
jest.mock('./hooks/usePoussins', () => ({
  usePoussins: () => {
    const [lots, setLots] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
      const fetchLots = async () => {
        try {
          setLoading(true);
          const data = await testDbService.getPoussins(1); // Utiliser l'ID de l'éleveur 1 pour les tests
          setLots(data);
          setLoading(false);
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      };

      fetchLots();
    }, []);

    const addLot = async (lotData) => {
      try {
        const newLot = await testDbService.insertTestPoussins({
          ...lotData,
          eleveur_id: 1, // Utiliser l'ID de l'éleveur 1 pour les tests
        });
        setLots([...lots, newLot]);
        return newLot;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const updateLot = async (id, lotData) => {
      try {
        // Implémenter la mise à jour si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const deleteLot = async (id) => {
      try {
        // Implémenter la suppression si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    return { lots, loading, error, addLot, updateLot, deleteLot };
  },
}));

jest.mock('./hooks/useEggProduction', () => ({
  useEggProduction: () => {
    const [productions, setProductions] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
      const fetchProductions = async () => {
        try {
          setLoading(true);
          const data = await testDbService.getProductionOeufs(1); // Utiliser l'ID de l'éleveur 1 pour les tests
          setProductions(data);
          setLoading(false);
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      };

      fetchProductions();
    }, []);

    const addProduction = async (productionData) => {
      try {
        const newProduction = await testDbService.insertTestProductionOeufs({
          ...productionData,
          eleveur_id: 1, // Utiliser l'ID de l'éleveur 1 pour les tests
        });
        setProductions([...productions, newProduction]);
        return newProduction;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const updateProduction = async (id, productionData) => {
      try {
        // Implémenter la mise à jour si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const deleteProduction = async (id) => {
      try {
        // Implémenter la suppression si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    return { productions, loading, error, addProduction, updateProduction, deleteProduction };
  },
}));

jest.mock('./hooks/useVetConsultations', () => ({
  useVetConsultations: () => {
    const [consultations, setConsultations] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
      const fetchConsultations = async () => {
        try {
          setLoading(true);
          const data = await testDbService.getConsultations(1); // Utiliser l'ID de l'éleveur 1 pour les tests
          setConsultations(data);
          setLoading(false);
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      };

      fetchConsultations();
    }, []);

    const addConsultation = async (consultationData) => {
      try {
        const newConsultation = await testDbService.insertTestConsultations({
          ...consultationData,
          eleveur_id: 1, // Utiliser l'ID de l'éleveur 1 pour les tests
        });
        setConsultations([...consultations, newConsultation]);
        return newConsultation;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const updateConsultation = async (id, consultationData) => {
      try {
        // Implémenter la mise à jour si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const deleteConsultation = async (id) => {
      try {
        // Implémenter la suppression si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    return { consultations, loading, error, addConsultation, updateConsultation, deleteConsultation };
  },
}));

jest.mock('./hooks/useStockAlerts', () => ({
  useStockAlerts: () => {
    const [alerts, setAlerts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
      const fetchAlerts = async () => {
        try {
          setLoading(true);
          const data = await testDbService.getAlertesStock(1); // Utiliser l'ID de l'éleveur 1 pour les tests
          setAlerts(data);
          setLoading(false);
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      };

      fetchAlerts();
    }, []);

    const addAlert = async (alertData) => {
      try {
        const newAlert = await testDbService.insertTestAlertesStock({
          ...alertData,
          eleveur_id: 1, // Utiliser l'ID de l'éleveur 1 pour les tests
        });
        setAlerts([...alerts, newAlert]);
        return newAlert;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const updateAlert = async (id, alertData) => {
      try {
        // Implémenter la mise à jour si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const resolveAlert = async (id) => {
      try {
        // Implémenter la résolution si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    const updateNotificationSettings = async (settings) => {
      try {
        // Implémenter la mise à jour des paramètres de notification si nécessaire
        return true;
      } catch (err) {
        setError(err);
        throw err;
      }
    };

    return { alerts, loading, error, addAlert, updateAlert, resolveAlert, updateNotificationSettings };
  },
}));

// Nettoyage des données de test avant chaque test
beforeEach(async () => {
  await testDbService.cleanTestData();
});

// Fermeture de la connexion à la base de données après tous les tests
afterAll(async () => {
  await testDbService.close();
});

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks();
});

