<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation - Poultray DZ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .user-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .role-badge {
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
        }
        .admin { background-color: #dc3545; }
        .eleveur { background-color: #007bff; }
        .veterinaire { background-color: #28a745; }
        .marchand { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Navigation Frontend - Poultray DZ</h1>

        <div class="test-section info">
            <h3>🔍 État Actuel</h3>
            <div id="currentState">
                <p><strong>Token:</strong> <span id="tokenStatus">Vérification...</span></p>
                <p><strong>Utilisateur:</strong> <span id="userStatus">Vérification...</span></p>
                <p><strong>URL actuelle:</strong> <span id="currentUrl"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Simuler la Connexion</h3>
            <div class="user-info">
                <select id="roleSelect">
                    <option value="admin">Administrateur</option>
                    <option value="eleveur">Éleveur</option>
                    <option value="veterinaire">Vétérinaire</option>
                    <option value="marchand">Marchand</option>
                </select>
                <button onclick="simulateLogin()">Simuler Connexion</button>
                <button onclick="clearAuth()">Effacer Auth</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🧭 Test Navigation</h3>
            <button onclick="testNavigation('/admin/dashboard')">Admin Dashboard</button>
            <button onclick="testNavigation('/eleveur/dashboard')">Éleveur Dashboard</button>
            <button onclick="testNavigation('/veterinaire/dashboard')">Vétérinaire Dashboard</button>
            <button onclick="testNavigation('/marchand/dashboard')">Marchand Dashboard</button>
        </div>

        <div class="test-section">
            <h3>📊 Test API Backend</h3>
            <button onclick="testAPI('/api/auth/user')">Test Auth User</button>
            <button onclick="testAPI('/api/admin/stats')">Test Admin Stats</button>
            <button onclick="testLogin()">Test Login API</button>
        </div>

        <div class="test-section">
            <h3>📋 Journal des Tests</h3>
            <button onclick="clearLog()">Effacer Journal</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        let testLog = document.getElementById('testLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            testLog.textContent = '';
        }

        function updateCurrentState() {
            const token = localStorage.getItem('token');
            const userStr = localStorage.getItem('user');

            document.getElementById('tokenStatus').textContent = token ? 'Présent' : 'Absent';
            document.getElementById('tokenStatus').className = token ? 'success' : 'error';

            if (userStr) {
                try {
                    const user = JSON.parse(userStr);
                    document.getElementById('userStatus').innerHTML =
                        `<span class="role-badge ${user.role}">${user.role}</span> ${user.email}`;
                } catch (e) {
                    document.getElementById('userStatus').textContent = 'Erreur parsing';
                }
            } else {
                document.getElementById('userStatus').textContent = 'Aucun';
            }

            document.getElementById('currentUrl').textContent = window.location.href;
        }

        function simulateLogin() {
            const role = document.getElementById('roleSelect').value;
            log(`🔐 Simulation connexion pour rôle: ${role}`);

            // Créer des données utilisateur de test
            const mockUser = {
                id: 1,
                email: `${role}@poultray.dz`,
                role: role,
                first_name: role.charAt(0).toUpperCase() + role.slice(1),
                last_name: 'Test'
            };

            const mockToken = 'mock-jwt-token-' + Date.now();

            // Stocker dans localStorage
            localStorage.setItem('token', mockToken);
            localStorage.setItem('user', JSON.stringify(mockUser));

            log(`✅ Utilisateur simulé créé: ${mockUser.email} (${role})`);
            updateCurrentState();

            // Simuler la logique de navigation
            const targetPath = getRedirectPath(role);
            log(`🎯 Navigation simulée vers: ${targetPath}`);
        }

        function getRedirectPath(role) {
            const rolePathMap = {
                'admin': '/admin/dashboard',
                'eleveur': '/eleveur/dashboard',
                'veterinaire': '/veterinaire/dashboard',
                'marchand': '/marchand/dashboard'
            };

            return rolePathMap[role] || '/dashboard';
        }

        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('🗑️ Authentification effacée');
            updateCurrentState();
        }

        function testNavigation(path) {
            log(`🧭 Test navigation vers: ${path}`);

            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ Pas de token, navigation vers /login');
                return;
            }

            const userStr = localStorage.getItem('user');
            if (!userStr) {
                log('❌ Pas d\'utilisateur, navigation vers /login');
                return;
            }

            let user;
            try {
                user = JSON.parse(userStr);
            } catch (e) {
                log('❌ Erreur parsing utilisateur');
                return;
            }

            // Simuler la logique DashboardLayout
            const requiredRole = path.split('/')[1]; // Extract role from path
            if (requiredRole && user.role !== requiredRole) {
                const correctPath = `/${user.role}/dashboard`;
                log(`🚫 Rôle incorrect. Requis: ${requiredRole}, Actuel: ${user.role}`);
                log(`📍 Redirection vers: ${correctPath}`);
            } else {
                log(`✅ Accès autorisé à ${path}`);
            }
        }

        async function testAPI(endpoint) {
            log(`🔌 Test API: ${endpoint}`);

            const token = localStorage.getItem('token');
            const headers = {
                'Content-Type': 'application/json'
            };

            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            try {
                const response = await fetch(`http://localhost:3003${endpoint}`, {
                    method: 'GET',
                    headers: headers
                });

                log(`📡 Statut réponse: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Réponse API: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log(`❌ Erreur API: ${errorText}`);
                }
            } catch (error) {
                log(`❌ Erreur réseau: ${error.message}`);
            }
        }

        async function testLogin() {
            log('🔐 Test Login API');

            const testCredentials = {
                email: '<EMAIL>',
                password: 'admin123'
            };

            try {
                const response = await fetch('http://localhost:3003/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testCredentials)
                });

                log(`📡 Statut login: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Login réussi: ${JSON.stringify(data.user, null, 2)}`);

                    // Stocker les données
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    updateCurrentState();
                } else {
                    const errorText = await response.text();
                    log(`❌ Erreur login: ${errorText}`);
                }
            } catch (error) {
                log(`❌ Erreur réseau login: ${error.message}`);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentState();
            log('🚀 Test page chargée');
            log('📝 Instructions:');
            log('1. Simuler une connexion avec un rôle');
            log('2. Tester la navigation vers différents dashboards');
            log('3. Vérifier les redirections automatiques');
        });
    </script>
</body>
</html>
