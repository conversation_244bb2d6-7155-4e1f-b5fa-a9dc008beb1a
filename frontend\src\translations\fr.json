{"common": {"appName": "Poultray DZ", "loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "add": "Ajouter", "submit": "So<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "back": "Retour", "next": "Suivant", "previous": "Précédent", "yes": "O<PERSON>", "no": "Non", "confirm": "Confirmer", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "info": "Information"}, "auth": {"login": "Connexion", "logout": "Déconnexion", "register": "Inscription", "forgotPassword": "Mot de passe oublié", "resetPassword": "Réinitialiser le mot de passe", "changePassword": "Changer le mot de passe", "email": "<PERSON><PERSON><PERSON> email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "username": "Nom d'utilisateur", "firstName": "Prénom", "lastName": "Nom", "role": "R<PERSON><PERSON>", "rememberMe": "Se souvenir de moi", "loginButton": "Se connecter", "registerButton": "S'inscrire", "alreadyHaveAccount": "Vous avez déjà un compte ?", "dontHaveAccount": "Vous n'avez pas de compte ?", "loginSuccess": "Connexion réussie", "loginError": "Erreur de connexion", "registerSuccess": "Inscription réussie", "registerError": "Erreur d'inscription", "logoutSuccess": "Déconnexion réussie", "passwordMismatch": "Les mots de passe ne correspondent pas", "passwordChanged": "Mot de passe modifié avec succès", "passwordResetSent": "Instructions de réinitialisation envoyées à votre email", "invalidCredentials": "Email ou mot de passe incorrect"}, "dashboard": {"dashboard": "Tableau de bord", "overview": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statistiques", "reports": "Rapports", "analytics": "Analyses", "welcome": "Bienvenue", "quickActions": "Actions rapides", "recentActivity": "Activité récente", "notifications": "Notifications", "settings": "Paramètres", "profile": "Profil", "help": "Aide"}, "admin": {"adminPanel": "Panneau d'administration", "users": "Utilisateurs", "roles": "<PERSON><PERSON><PERSON>", "permissions": "Permissions", "settings": "Paramètres", "logs": "<PERSON><PERSON><PERSON>", "system": "Système", "maintenance": "Maintenance", "backups": "<PERSON><PERSON><PERSON><PERSON>", "translations": "Traductions", "blog": "Blog", "ai": "Intelligence Artificielle", "aiTools": "Outils IA", "blogGenerator": "Générateur de blog", "dataAnalysis": "<PERSON><PERSON><PERSON>", "pageContent": "Contenu de page"}, "eleveur": {"dashboard": "Tableau de bord éleveur", "myVolailles": "<PERSON><PERSON> vol<PERSON>", "mySales": "<PERSON><PERSON> ventes", "myStatistics": "Mes statistiques", "addVolaille": "Ajouter des volailles", "editVolaille": "Modifier des volailles", "deleteVolaille": "Supprimer des volailles", "volailleDetails": "Détails des volailles", "volailleStats": "Statistiques des volailles", "volailleHealth": "Santé des volailles", "volailleFeeding": "Alimentation des volailles", "volailleGrowth": "Croissance des volailles", "volailleSales": "Ventes de volailles"}, "veterinaire": {"dashboard": "Tableau de bord vétérinaire", "consultations": "Consultations", "prescriptions": "Prescriptions", "appointments": "<PERSON><PERSON><PERSON>vous", "patients": "Patients", "medicalRecords": "Do<PERSON><PERSON> m<PERSON>", "treatments": "Traitements", "vaccines": "Vaccins", "diseases": "Mal<PERSON><PERSON>", "addConsultation": "Ajouter une consultation", "editConsultation": "Modifier une consultation", "deleteConsultation": "Supprimer une consultation", "addPrescription": "Ajouter une prescription", "editPrescription": "Modifier une prescription", "deletePrescription": "Supprimer une prescription"}, "marchand": {"dashboard": "Tableau de bord marchand", "products": "Produits", "orders": "Commandes", "sales": "<PERSON><PERSON><PERSON>", "customers": "Clients", "inventory": "Inventaire", "shipping": "Expédition", "returns": "Retours", "addProduct": "Ajouter un produit", "editProduct": "Modifier un produit", "deleteProduct": "Supprimer un produit", "productDetails": "Détails du produit", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "processOrder": "Trai<PERSON> la commande", "cancelOrder": "Annuler la commande", "refundOrder": "<PERSON><PERSON><PERSON><PERSON> la commande"}, "volailles": {"volailles": "<PERSON><PERSON><PERSON>", "espece": "Espèce", "race": "Race", "age": "Âge", "poids": "Poids", "quantite": "Quantité", "prix": "Prix", "valeur": "<PERSON><PERSON>", "statut": "Statut", "description": "Description", "dateAjout": "Date d'ajout", "eleveur": "<PERSON><PERSON><PERSON>", "poulets": "Poulets", "dindes": "<PERSON><PERSON>", "canards": "Canards", "cailles": "<PERSON><PERSON><PERSON>", "oies": "Oies", "pintades": "<PERSON>nta<PERSON>", "disponible": "Disponible", "vendu": "Vendu", "reserve": "Réservé", "malade": "Malade", "traitement": "En traitement", "quarantaine": "En quarantaine"}, "notifications": {"newMessage": "Nouveau message", "newOrder": "Nouvelle commande", "orderStatus": "Statut de commande mis à jour", "paymentReceived": "Pa<PERSON><PERSON> reçu", "appointmentReminder": "Rappel de rendez-vous", "systemUpdate": "Mise à jour système", "lowStock": "Stock faible", "criticalAlert": "Alerte critique", "markAllAsRead": "<PERSON><PERSON> tout comme lu", "clearAll": "Tout effacer", "viewAll": "Voir tout"}, "errors": {"required": "Ce champ est requis", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "minLength": "Doit contenir au moins {min} caractères", "maxLength": "Ne doit pas dépasser {max} caractères", "passwordMismatch": "Les mots de passe ne correspondent pas", "serverError": "<PERSON><PERSON><PERSON> serveur, ve<PERSON><PERSON><PERSON> réessayer plus tard", "notFound": "Non trouvé", "unauthorized": "Non autorisé", "forbidden": "<PERSON><PERSON>ès refusé", "badRequest": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide", "conflict": "Conf<PERSON> de donn<PERSON>", "networkError": "<PERSON><PERSON><PERSON> r<PERSON>, veuillez vérifier votre connexion", "fetchingSmtpSettings": "Erreur lors de la récupération des paramètres SMTP", "updatingSmtpSettings": "Erreur lors de la mise à jour des paramètres SMTP", "fetchingSecuritySettings": "Erreur lors de la récupération des paramètres de sécurité", "updatingSecuritySettings": "Erreur lors de la mise à jour des paramètres de sécurité", "fetchingGeneralSettings": "Erreur lors de la récupération des paramètres généraux", "updatingGeneralSettings": "Erreur lors de la mise à jour des paramètres généraux"}, "success": {"smtpSettingsSaved": "Paramètres SMTP enregistrés avec succès", "securitySettingsSaved": "Paramètres de sécurité enregistrés avec succès", "generalSettingsSaved": "Paramètres généraux enregistrés avec succès"}, "settings": {"title": "Paramètres", "general": {"title": "Paramètres généraux", "siteName": "Nom du site", "siteDescription": "Description du site", "contactEmail": "Email de contact", "contactPhone": "Téléphone de contact", "address": "<PERSON><PERSON><PERSON>", "logo": "Logo", "favicon": "Favicon", "primaryColor": "Couleur principale", "secondaryColor": "<PERSON>uleur secondaire", "defaultLanguage": "Langue par défaut", "availableLanguages": "Langues disponibles", "dateFormat": "Format de date", "timeFormat": "Format d'heure", "timezone": "<PERSON><PERSON> ho<PERSON>", "appearance": "Apparence", "localization": "Localisation", "maintenance": "Maintenance", "registration": "Inscription", "social": "<PERSON><PERSON><PERSON><PERSON>", "maintenanceMode": "Mode maintenance", "maintenanceMessage": "Message de maintenance", "allowUserRegistration": "Autoriser l'inscription des utilisateurs", "defaultUserRole": "Rôle par défaut des utilisateurs", "footerText": "Texte de pied de page", "maxUploadSize": "Taille maximale d'upload (MB)", "socialLinks": "<PERSON>ns sociaux", "facebook": "Facebook", "twitter": "Twitter", "instagram": "Instagram", "linkedin": "LinkedIn", "youtube": "YouTube"}, "smtp": {"title": "Configuration SMTP", "host": "Hôte SMTP", "port": "Port SMTP", "secure": "Connexion sécurisée", "user": "Nom d'utilisateur", "pass": "Mot de passe", "fromName": "Nom d'expéditeur", "fromEmail": "<PERSON><PERSON> d'expéditeur", "replyTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testEmailRecipient": "Destinataire de l'email de test", "isEnabled": "Activer SMTP", "testEmailButton": "Tester la configuration", "testEmailSuccess": "Email de test envoy<PERSON> avec succès", "testEmailError": "Erreur lors de l'envoi de l'email de test"}, "security": {"title": "Paramètres de sécurité", "fetchError": "Erreur lors de la récupération des paramètres de sécurité", "saveError": "Erreur lors de la mise à jour des paramètres de sécurité", "saveSuccess": "Paramètres de sécurité enregistrés avec succès", "authentication": {"title": "Authentification", "enable2FA": "Activer l'authentification à deux facteurs", "sessionTimeout": "<PERSON><PERSON><PERSON> d'expiration de la session (minutes)", "maxLoginAttempts": "Nombre maximum de tentatives de connexion", "lockoutDuration": "<PERSON><PERSON><PERSON> verrouillage (minutes)"}, "passwordPolicy": {"title": "Politique de mot de passe", "complexityRegex": "Expression régulière pour la complexité du mot de passe", "complexityRegexHelp": "Expression régulière pour la validation du mot de passe (ex: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$)", "historyCount": "Nombre d'historique de mots de passe", "historyCountHelp": "Nombre de mots de passe précédents à mémoriser pour éviter la réutilisation", "expiryDays": "Expiration du mot de passe (jours)", "expiryDaysHelp": "Nombre de jours après lesquels les mots de passe expirent (0 pour désactiver)"}, "apiSecurity": {"title": "Sécurité de l'API", "corsAllowedOrigins": "Origines CORS autorisées", "corsAllowedOriginsHelp": "Liste d'origines autorisées pour CORS (ex: http://localhost:3000,https://votredomaine.com)", "logLevel": "Niveau de journalisation", "rateLimitingEnabled": "Activer la limitation du taux d'API", "rateLimitRequests": "Nombre de requêtes API autorisées", "rateLimitRequestsHelp": "Nombre maximum de requêtes dans la fenêtre de temps", "rateLimitWindowMs": "Fenêtre de limitation (ms)", "rateLimitWindowMsHelp": "Fenêtre de temps pour la limitation en millisecondes (ex: 900000 pour 15 minutes)"}}}}