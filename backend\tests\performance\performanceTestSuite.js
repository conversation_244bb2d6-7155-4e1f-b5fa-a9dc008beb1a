/**
 * Suite de tests de performance automatisés
 */

const { performance } = require('perf_hooks');
const autocannon = require('autocannon');
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../../src/utils/logger');

class PerformanceTestSuite {
  constructor() {
    this.testResults = new Map();
    this.benchmarks = {
      api: {
        responseTime: 200, // ms
        throughput: 1000, // req/s
        errorRate: 0.01 // 1%
      },
      database: {
        queryTime: 50, // ms
        connectionTime: 10, // ms
        poolUtilization: 0.8 // 80%
      },
      frontend: {
        fcp: 1500, // ms
        lcp: 2500, // ms
        cls: 0.1,
        fid: 100 // ms
      }
    };
  }

  /**
   * Exécuter tous les tests de performance
   */
  async runAllTests() {
    console.log('🚀 Démarrage de la suite de tests de performance...');
    
    const results = {
      timestamp: new Date().toISOString(),
      api: await this.runAPIPerformanceTests(),
      database: await this.runDatabasePerformanceTests(),
      frontend: await this.runFrontendPerformanceTests(),
      loadTesting: await this.runLoadTests(),
      stressTesting: await this.runStressTests()
    };

    // Générer le rapport
    const report = await this.generatePerformanceReport(results);
    
    // Sauvegarder les résultats
    await this.saveTestResults(results);

    console.log('✅ Suite de tests de performance terminée');
    return report;
  }

  /**
   * Tests de performance API
   */
  async runAPIPerformanceTests() {
    console.log('🔧 Tests de performance API...');
    
    const apiTests = [
      { name: 'Login', endpoint: '/api/auth/login', method: 'POST' },
      { name: 'Get User Profile', endpoint: '/api/user/profile', method: 'GET' },
      { name: 'List Volailles', endpoint: '/api/volailles', method: 'GET' },
      { name: 'Create Volaille', endpoint: '/api/volailles', method: 'POST' },
      { name: 'Get Production Data', endpoint: '/api/production', method: 'GET' },
      { name: 'List Consultations', endpoint: '/api/consultations', method: 'GET' }
    ];

    const results = [];

    for (const test of apiTests) {
      const result = await this.testAPIEndpoint(test);
      results.push(result);
    }

    return {
      summary: this.calculateAPISummary(results),
      details: results
    };
  }

  /**
   * Tester un endpoint API
   */
  async testAPIEndpoint(test) {
    const startTime = performance.now();
    
    try {
      // Configuration du test autocannon
      const config = {
        url: `http://localhost:3000${test.endpoint}`,
        method: test.method,
        duration: 30, // 30 secondes
        connections: 10,
        pipelining: 1,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      };

      if (test.method === 'POST') {
        config.body = JSON.stringify(this.getTestPayload(test.endpoint));
      }

      const result = await autocannon(config);
      const endTime = performance.now();

      return {
        name: test.name,
        endpoint: test.endpoint,
        method: test.method,
        duration: endTime - startTime,
        throughput: result.requests.average,
        latency: {
          average: result.latency.average,
          p50: result.latency.p50,
          p95: result.latency.p95,
          p99: result.latency.p99
        },
        errors: result.errors,
        timeouts: result.timeouts,
        success: result.errors === 0,
        benchmark: this.compareWithBenchmark('api', result)
      };

    } catch (error) {
      logger.error(`Erreur test API ${test.name}:`, error);
      return {
        name: test.name,
        endpoint: test.endpoint,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Tests de performance base de données
   */
  async runDatabasePerformanceTests() {
    console.log('🗄️ Tests de performance base de données...');
    
    const dbTests = [
      { name: 'Simple Select', query: 'SELECT * FROM users LIMIT 100' },
      { name: 'Complex Join', query: `
        SELECT u.*, v.nom, COUNT(p.id) as production_count
        FROM users u
        LEFT JOIN volailles v ON u.id = v.eleveur_id
        LEFT JOIN production p ON v.id = p.volaille_id
        WHERE u.role = 'eleveur'
        GROUP BY u.id, v.id
        LIMIT 50
      ` },
      { name: 'Aggregation Query', query: `
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as count,
          AVG(quantite_produite) as avg_production
        FROM production
        WHERE created_at >= NOW() - INTERVAL '1 year'
        GROUP BY month
        ORDER BY month
      ` },
      { name: 'Full Text Search', query: `
        SELECT * FROM volailles
        WHERE to_tsvector('french', nom || ' ' || description) @@ to_tsquery('french', 'poule')
        LIMIT 20
      ` }
    ];

    const results = [];

    for (const test of dbTests) {
      const result = await this.testDatabaseQuery(test);
      results.push(result);
    }

    // Tests de connexion
    const connectionTest = await this.testDatabaseConnections();
    results.push(connectionTest);

    return {
      summary: this.calculateDBSummary(results),
      details: results
    };
  }

  /**
   * Tester une requête de base de données
   */
  async testDatabaseQuery(test) {
    const { Pool } = require('pg');
    const pool = new Pool();
    
    const iterations = 100;
    const times = [];

    try {
      // Échauffement
      await pool.query(test.query);

      // Tests de performance
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        await pool.query(test.query);
        const endTime = performance.now();
        times.push(endTime - startTime);
      }

      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      const p95Time = times.sort((a, b) => a - b)[Math.floor(times.length * 0.95)];

      return {
        name: test.name,
        query: test.query,
        iterations: iterations,
        averageTime: avgTime,
        minTime: minTime,
        maxTime: maxTime,
        p95Time: p95Time,
        success: true,
        benchmark: this.compareWithBenchmark('database', { averageTime: avgTime })
      };

    } catch (error) {
      logger.error(`Erreur test DB ${test.name}:`, error);
      return {
        name: test.name,
        error: error.message,
        success: false
      };
    } finally {
      await pool.end();
    }
  }

  /**
   * Tester les connexions à la base de données
   */
  async testDatabaseConnections() {
    const { Pool } = require('pg');
    const connectionTimes = [];
    const poolSize = 20;

    try {
      for (let i = 0; i < poolSize; i++) {
        const startTime = performance.now();
        const pool = new Pool({ max: 1 });
        const client = await pool.connect();
        const endTime = performance.now();
        
        connectionTimes.push(endTime - startTime);
        client.release();
        await pool.end();
      }

      const avgConnectionTime = connectionTimes.reduce((sum, time) => sum + time, 0) / connectionTimes.length;

      return {
        name: 'Database Connections',
        poolSize: poolSize,
        averageConnectionTime: avgConnectionTime,
        minConnectionTime: Math.min(...connectionTimes),
        maxConnectionTime: Math.max(...connectionTimes),
        success: true,
        benchmark: this.compareWithBenchmark('database', { connectionTime: avgConnectionTime })
      };

    } catch (error) {
      logger.error('Erreur test connexions DB:', error);
      return {
        name: 'Database Connections',
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Tests de performance frontend
   */
  async runFrontendPerformanceTests() {
    console.log('🌐 Tests de performance frontend...');
    
    const pages = [
      { name: 'Home Page', url: 'http://localhost:3000/' },
      { name: 'Dashboard', url: 'http://localhost:3000/dashboard' },
      { name: 'Volailles List', url: 'http://localhost:3000/volailles' },
      { name: 'Production Dashboard', url: 'http://localhost:3000/production' }
    ];

    const results = [];

    for (const page of pages) {
      const result = await this.testPagePerformance(page);
      results.push(result);
    }

    return {
      summary: this.calculateFrontendSummary(results),
      details: results
    };
  }

  /**
   * Tester la performance d'une page
   */
  async testPagePerformance(page) {
    let chrome;
    
    try {
      // Lancer Chrome
      chrome = await chromeLauncher.launch({
        chromeFlags: ['--headless', '--no-sandbox', '--disable-dev-shm-usage']
      });

      // Exécuter Lighthouse
      const options = {
        logLevel: 'info',
        output: 'json',
        onlyCategories: ['performance'],
        port: chrome.port
      };

      const runnerResult = await lighthouse(page.url, options);
      const report = runnerResult.report;
      const results = JSON.parse(report);

      // Extraire les métriques importantes
      const metrics = results.audits;
      
      return {
        name: page.name,
        url: page.url,
        score: results.categories.performance.score * 100,
        metrics: {
          fcp: metrics['first-contentful-paint'].numericValue,
          lcp: metrics['largest-contentful-paint'].numericValue,
          cls: metrics['cumulative-layout-shift'].numericValue,
          fid: metrics['max-potential-fid'].numericValue,
          speedIndex: metrics['speed-index'].numericValue,
          totalBlockingTime: metrics['total-blocking-time'].numericValue
        },
        opportunities: this.extractOptimizationOpportunities(metrics),
        success: true,
        benchmark: this.compareWithBenchmark('frontend', {
          fcp: metrics['first-contentful-paint'].numericValue,
          lcp: metrics['largest-contentful-paint'].numericValue,
          cls: metrics['cumulative-layout-shift'].numericValue
        })
      };

    } catch (error) {
      logger.error(`Erreur test page ${page.name}:`, error);
      return {
        name: page.name,
        url: page.url,
        error: error.message,
        success: false
      };
    } finally {
      if (chrome) {
        await chrome.kill();
      }
    }
  }

  /**
   * Tests de charge
   */
  async runLoadTests() {
    console.log('⚡ Tests de charge...');
    
    const loadScenarios = [
      { name: 'Normal Load', connections: 50, duration: 60 },
      { name: 'High Load', connections: 100, duration: 60 },
      { name: 'Peak Load', connections: 200, duration: 30 }
    ];

    const results = [];

    for (const scenario of loadScenarios) {
      const result = await this.runLoadTest(scenario);
      results.push(result);
    }

    return {
      summary: this.calculateLoadTestSummary(results),
      details: results
    };
  }

  /**
   * Exécuter un test de charge
   */
  async runLoadTest(scenario) {
    try {
      const config = {
        url: 'http://localhost:3000/api/health',
        connections: scenario.connections,
        duration: scenario.duration,
        pipelining: 1
      };

      const result = await autocannon(config);

      return {
        name: scenario.name,
        connections: scenario.connections,
        duration: scenario.duration,
        throughput: result.requests.average,
        latency: result.latency.average,
        errors: result.errors,
        success: result.errors === 0,
        cpuUsage: await this.measureCPUUsage(),
        memoryUsage: await this.measureMemoryUsage()
      };

    } catch (error) {
      logger.error(`Erreur test de charge ${scenario.name}:`, error);
      return {
        name: scenario.name,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Tests de stress
   */
  async runStressTests() {
    console.log('💪 Tests de stress...');
    
    // Test de montée en charge progressive
    const stressResult = await this.runProgressiveStressTest();
    
    // Test de pic de trafic
    const spikeResult = await this.runSpikeTest();

    return {
      progressive: stressResult,
      spike: spikeResult
    };
  }

  /**
   * Test de stress progressif
   */
  async runProgressiveStressTest() {
    const stages = [
      { connections: 10, duration: 30 },
      { connections: 50, duration: 30 },
      { connections: 100, duration: 30 },
      { connections: 200, duration: 30 },
      { connections: 500, duration: 30 }
    ];

    const results = [];

    for (const stage of stages) {
      const result = await this.runLoadTest({
        name: `Stress ${stage.connections} connections`,
        ...stage
      });
      results.push(result);

      // Arrêter si le système commence à échouer
      if (result.errors > 100 || result.latency > 5000) {
        break;
      }
    }

    return {
      maxConnections: results[results.length - 1].connections,
      breakingPoint: results.find(r => r.errors > 100 || r.latency > 5000),
      results: results
    };
  }

  /**
   * Test de pic de trafic
   */
  async runSpikeTest() {
    // Trafic normal puis pic soudain
    const normalLoad = await this.runLoadTest({
      name: 'Normal before spike',
      connections: 50,
      duration: 30
    });

    const spike = await this.runLoadTest({
      name: 'Traffic spike',
      connections: 500,
      duration: 10
    });

    const recovery = await this.runLoadTest({
      name: 'Recovery after spike',
      connections: 50,
      duration: 30
    });

    return {
      normal: normalLoad,
      spike: spike,
      recovery: recovery,
      recoveryTime: this.calculateRecoveryTime(normalLoad, spike, recovery)
    };
  }

  /**
   * Méthodes utilitaires
   */

  getTestPayload(endpoint) {
    const payloads = {
      '/api/auth/login': {
        email: '<EMAIL>',
        password: 'password123'
      },
      '/api/volailles': {
        nom: 'Test Volaille',
        type_volaille: 'poule',
        nombre_total: 100,
        description: 'Test description'
      }
    };

    return payloads[endpoint] || {};
  }

  compareWithBenchmark(category, result) {
    const benchmark = this.benchmarks[category];
    const comparison = {};

    Object.keys(benchmark).forEach(metric => {
      const value = this.extractMetricValue(result, metric);
      const benchmarkValue = benchmark[metric];
      
      comparison[metric] = {
        value: value,
        benchmark: benchmarkValue,
        ratio: value / benchmarkValue,
        status: value <= benchmarkValue ? 'pass' : 'fail'
      };
    });

    return comparison;
  }

  extractMetricValue(result, metric) {
    const mappings = {
      responseTime: result.latency?.average || result.averageTime,
      throughput: result.throughput,
      errorRate: result.errors / (result.requests || 1),
      queryTime: result.averageTime,
      connectionTime: result.averageConnectionTime,
      fcp: result.fcp,
      lcp: result.lcp,
      cls: result.cls,
      fid: result.fid
    };

    return mappings[metric] || 0;
  }

  async measureCPUUsage() {
    // Mesurer l'utilisation CPU (simplifié)
    const usage = process.cpuUsage();
    return (usage.user + usage.system) / 1000000; // Convertir en secondes
  }

  async measureMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      rss: usage.rss / 1024 / 1024, // MB
      heapTotal: usage.heapTotal / 1024 / 1024,
      heapUsed: usage.heapUsed / 1024 / 1024,
      external: usage.external / 1024 / 1024
    };
  }

  extractOptimizationOpportunities(metrics) {
    const opportunities = [];

    Object.entries(metrics).forEach(([key, metric]) => {
      if (metric.details && metric.details.overallSavingsMs > 100) {
        opportunities.push({
          audit: key,
          title: metric.title,
          savings: metric.details.overallSavingsMs,
          description: metric.description
        });
      }
    });

    return opportunities.sort((a, b) => b.savings - a.savings);
  }

  calculateAPISummary(results) {
    const successful = results.filter(r => r.success);
    return {
      totalTests: results.length,
      successful: successful.length,
      failed: results.length - successful.length,
      averageLatency: successful.reduce((sum, r) => sum + r.latency.average, 0) / successful.length,
      averageThroughput: successful.reduce((sum, r) => sum + r.throughput, 0) / successful.length
    };
  }

  calculateDBSummary(results) {
    const successful = results.filter(r => r.success);
    return {
      totalTests: results.length,
      successful: successful.length,
      failed: results.length - successful.length,
      averageQueryTime: successful.reduce((sum, r) => sum + (r.averageTime || 0), 0) / successful.length
    };
  }

  calculateFrontendSummary(results) {
    const successful = results.filter(r => r.success);
    return {
      totalTests: results.length,
      successful: successful.length,
      failed: results.length - successful.length,
      averageScore: successful.reduce((sum, r) => sum + r.score, 0) / successful.length,
      averageFCP: successful.reduce((sum, r) => sum + r.metrics.fcp, 0) / successful.length,
      averageLCP: successful.reduce((sum, r) => sum + r.metrics.lcp, 0) / successful.length
    };
  }

  calculateLoadTestSummary(results) {
    return {
      totalTests: results.length,
      successful: results.filter(r => r.success).length,
      maxThroughput: Math.max(...results.map(r => r.throughput || 0)),
      maxConnections: Math.max(...results.map(r => r.connections || 0))
    };
  }

  calculateRecoveryTime(normal, spike, recovery) {
    // Calculer le temps de récupération basé sur la latence
    const normalLatency = normal.latency;
    const recoveryLatency = recovery.latency;
    
    return Math.abs(recoveryLatency - normalLatency) < normalLatency * 0.1 ? 'fast' : 'slow';
  }

  async generatePerformanceReport(results) {
    const report = {
      timestamp: results.timestamp,
      summary: {
        overall: this.calculateOverallScore(results),
        api: results.api.summary,
        database: results.database.summary,
        frontend: results.frontend.summary,
        loadTesting: results.loadTesting.summary
      },
      recommendations: this.generateRecommendations(results),
      benchmarkComparison: this.generateBenchmarkComparison(results)
    };

    return report;
  }

  calculateOverallScore(results) {
    // Calculer un score global basé sur tous les tests
    const scores = [];
    
    if (results.api.summary.successful > 0) {
      scores.push(results.api.summary.successful / results.api.summary.totalTests * 100);
    }
    
    if (results.database.summary.successful > 0) {
      scores.push(results.database.summary.successful / results.database.summary.totalTests * 100);
    }
    
    if (results.frontend.summary.successful > 0) {
      scores.push(results.frontend.summary.averageScore);
    }

    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  generateRecommendations(results) {
    const recommendations = [];

    // Recommandations API
    if (results.api.summary.averageLatency > this.benchmarks.api.responseTime) {
      recommendations.push({
        category: 'API',
        priority: 'high',
        issue: 'Latence API élevée',
        recommendation: 'Optimiser les requêtes de base de données et implémenter du cache'
      });
    }

    // Recommandations Frontend
    if (results.frontend.summary.averageFCP > this.benchmarks.frontend.fcp) {
      recommendations.push({
        category: 'Frontend',
        priority: 'medium',
        issue: 'First Contentful Paint lent',
        recommendation: 'Optimiser les ressources critiques et implémenter le preloading'
      });
    }

    return recommendations;
  }

  generateBenchmarkComparison(results) {
    // Comparer avec les benchmarks définis
    return {
      api: this.compareCategoryWithBenchmark('api', results.api),
      database: this.compareCategoryWithBenchmark('database', results.database),
      frontend: this.compareCategoryWithBenchmark('frontend', results.frontend)
    };
  }

  compareCategoryWithBenchmark(category, results) {
    const benchmark = this.benchmarks[category];
    const comparison = {};

    // Logique de comparaison spécifique à chaque catégorie
    // À implémenter selon les besoins

    return comparison;
  }

  async saveTestResults(results) {
    const filename = `performance-test-${Date.now()}.json`;
    const filepath = path.join(__dirname, '../../reports', filename);
    
    try {
      await fs.writeFile(filepath, JSON.stringify(results, null, 2));
      console.log(`📊 Résultats sauvegardés: ${filepath}`);
    } catch (error) {
      logger.error('Erreur sauvegarde résultats:', error);
    }
  }
}

module.exports = PerformanceTestSuite;
