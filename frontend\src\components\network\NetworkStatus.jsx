import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Tooltip,
  IconButton,
  Collapse,
  Typography,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  Wifi,
  WifiOff,
  SignalWifi4Bar,
  SignalWifi3Bar,
  SignalWifi2Bar,
  SignalWifi1Bar,
  Refresh,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';

/**
 * Hook pour surveiller la qualité de la connexion réseau
 */
const useNetworkQuality = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionQuality, setConnectionQuality] = useState('unknown');
  const [latency, setLatency] = useState(null);
  const [lastCheck, setLastCheck] = useState(null);

  const checkConnectionQuality = async () => {
    if (!navigator.onLine) {
      setConnectionQuality('offline');
      setLatency(null);
      return;
    }

    try {
      const startTime = performance.now();
      
      // Test de ping vers notre API
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      setLatency(Math.round(responseTime));
      setLastCheck(new Date());

      if (response.ok) {
        if (responseTime < 100) {
          setConnectionQuality('excellent');
        } else if (responseTime < 300) {
          setConnectionQuality('good');
        } else if (responseTime < 1000) {
          setConnectionQuality('fair');
        } else {
          setConnectionQuality('poor');
        }
      } else {
        setConnectionQuality('poor');
      }
    } catch (error) {
      setConnectionQuality('poor');
      setLatency(null);
    }
  };

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      checkConnectionQuality();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionQuality('offline');
      setLatency(null);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Vérification initiale
    checkConnectionQuality();

    // Vérification périodique (toutes les 30 secondes)
    const interval = setInterval(checkConnectionQuality, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  return {
    isOnline,
    connectionQuality,
    latency,
    lastCheck,
    checkConnectionQuality
  };
};

/**
 * Composant pour afficher l'état du réseau
 */
const NetworkStatus = ({ 
  variant = 'chip', // 'chip' | 'detailed' | 'minimal'
  showLatency = true,
  autoRefresh = true,
  position = 'relative' // 'fixed' | 'relative'
}) => {
  const { isOnline, connectionQuality, latency, lastCheck, checkConnectionQuality } = useNetworkQuality();
  const [expanded, setExpanded] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const getConnectionIcon = () => {
    if (!isOnline) return <WifiOff />;
    
    switch (connectionQuality) {
      case 'excellent':
        return <SignalWifi4Bar />;
      case 'good':
        return <SignalWifi3Bar />;
      case 'fair':
        return <SignalWifi2Bar />;
      case 'poor':
        return <SignalWifi1Bar />;
      default:
        return <Wifi />;
    }
  };

  const getConnectionColor = () => {
    if (!isOnline) return 'error';
    
    switch (connectionQuality) {
      case 'excellent':
        return 'success';
      case 'good':
        return 'success';
      case 'fair':
        return 'warning';
      case 'poor':
        return 'error';
      default:
        return 'default';
    }
  };

  const getConnectionLabel = () => {
    if (!isOnline) return 'Hors ligne';
    
    switch (connectionQuality) {
      case 'excellent':
        return 'Excellente';
      case 'good':
        return 'Bonne';
      case 'fair':
        return 'Correcte';
      case 'poor':
        return 'Faible';
      default:
        return 'Inconnue';
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await checkConnectionQuality();
    setTimeout(() => setIsRefreshing(false), 500);
  };

  const containerProps = position === 'fixed' ? {
    position: 'fixed',
    top: 16,
    left: 16,
    zIndex: 1000
  } : {};

  if (variant === 'minimal') {
    return (
      <Box {...containerProps}>
        <Tooltip title={`Connexion: ${getConnectionLabel()}${latency ? ` (${latency}ms)` : ''}`}>
          <IconButton size="small" color={getConnectionColor()}>
            {getConnectionIcon()}
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  if (variant === 'chip') {
    return (
      <Box {...containerProps}>
        <Tooltip title={`Dernière vérification: ${lastCheck?.toLocaleTimeString() || 'Jamais'}`}>
          <Chip
            icon={getConnectionIcon()}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                {getConnectionLabel()}
                {showLatency && latency && (
                  <Typography variant="caption">
                    ({latency}ms)
                  </Typography>
                )}
              </Box>
            }
            color={getConnectionColor()}
            variant="outlined"
            size="small"
            onClick={() => setExpanded(!expanded)}
            onDelete={autoRefresh ? handleRefresh : undefined}
            deleteIcon={
              <Refresh 
                sx={{ 
                  animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }} 
              />
            }
          />
        </Tooltip>

        <Collapse in={expanded}>
          <Box mt={1} p={2} bgcolor="background.paper" borderRadius={1} boxShadow={1}>
            <Typography variant="subtitle2" gutterBottom>
              État de la connexion
            </Typography>
            
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Statut:</Typography>
              <Typography variant="body2" color={getConnectionColor()}>
                {isOnline ? 'En ligne' : 'Hors ligne'}
              </Typography>
            </Box>

            {isOnline && (
              <>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Qualité:</Typography>
                  <Typography variant="body2" color={getConnectionColor()}>
                    {getConnectionLabel()}
                  </Typography>
                </Box>

                {latency && (
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">Latence:</Typography>
                    <Typography variant="body2">
                      {latency}ms
                    </Typography>
                  </Box>
                )}

                <Box mb={1}>
                  <Typography variant="body2" gutterBottom>
                    Performance:
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={
                      connectionQuality === 'excellent' ? 100 :
                      connectionQuality === 'good' ? 75 :
                      connectionQuality === 'fair' ? 50 :
                      connectionQuality === 'poor' ? 25 : 0
                    }
                    color={getConnectionColor()}
                  />
                </Box>
              </>
            )}

            {lastCheck && (
              <Typography variant="caption" color="text.secondary">
                Dernière vérification: {lastCheck.toLocaleTimeString()}
              </Typography>
            )}
          </Box>
        </Collapse>
      </Box>
    );
  }

  // Variant 'detailed'
  return (
    <Box {...containerProps}>
      <Alert
        severity={getConnectionColor()}
        icon={getConnectionIcon()}
        action={
          <IconButton
            size="small"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <Refresh 
              sx={{ 
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} 
            />
          </IconButton>
        }
      >
        <Box>
          <Typography variant="subtitle2">
            Connexion: {getConnectionLabel()}
          </Typography>
          
          {isOnline && latency && (
            <Typography variant="body2">
              Latence: {latency}ms
            </Typography>
          )}
          
          {lastCheck && (
            <Typography variant="caption" display="block">
              Dernière vérification: {lastCheck.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
      </Alert>
    </Box>
  );
};

export default NetworkStatus;
