import React, { memo, useState, useCallback, useEffect } from 'react';
import {
  TextField,
  Autocomplete,
  Chip,
  Box,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Switch,
  Slider,
  Rating,
  Button,
  IconButton,
  InputAdornment,
  Tooltip,
  Alert,
  LinearProgress,
  Card,
  CardContent,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  CloudUpload,
  Delete,
  Add,
  Remove,
  Info,
  CheckCircle,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useTranslation, useLocalizedValidation } from '../../hooks/useAdvancedLanguage';
import { useOptimizedMemo } from '../../hooks/usePerformance';

/**
 * Champ de texte avec validation en temps réel et suggestions
 */
const SmartTextField = memo(({
  name,
  label,
  value,
  onChange,
  onBlur,
  validation = {},
  suggestions = [],
  showSuggestions = false,
  helperText,
  error,
  required = false,
  type = 'text',
  multiline = false,
  rows = 4,
  placeholder,
  disabled = false,
  autoComplete = 'off',
  ...props
}) => {
  const { t } = useTranslation();
  const { validateRequired, validateEmail, validateMinLength } = useLocalizedValidation();
  const [showPassword, setShowPassword] = useState(false);
  const [validationState, setValidationState] = useState({ isValid: true, message: '' });

  // Validation en temps réel
  const validateField = useCallback((fieldValue) => {
    if (required) {
      const requiredError = validateRequired(fieldValue, name);
      if (requiredError) {
        return { isValid: false, message: requiredError };
      }
    }

    if (type === 'email' && fieldValue) {
      const emailError = validateEmail(fieldValue, name);
      if (emailError) {
        return { isValid: false, message: emailError };
      }
    }

    if (validation.minLength && fieldValue) {
      const minLengthError = validateMinLength(fieldValue, validation.minLength, name);
      if (minLengthError) {
        return { isValid: false, message: minLengthError };
      }
    }

    if (validation.pattern && fieldValue) {
      if (!validation.pattern.test(fieldValue)) {
        return { isValid: false, message: validation.patternMessage || t('validation.pattern') };
      }
    }

    if (validation.custom && fieldValue) {
      const customError = validation.custom(fieldValue);
      if (customError) {
        return { isValid: false, message: customError };
      }
    }

    return { isValid: true, message: '' };
  }, [required, type, validation, validateRequired, validateEmail, validateMinLength, name, t]);

  // Valider lors du changement de valeur
  useEffect(() => {
    const result = validateField(value);
    setValidationState(result);
  }, [value, validateField]);

  const handleChange = (event) => {
    const newValue = event.target.value;
    onChange(event);
    
    // Validation immédiate pour certains types
    if (type === 'email' || validation.immediate) {
      const result = validateField(newValue);
      setValidationState(result);
    }
  };

  const handleBlur = (event) => {
    const result = validateField(event.target.value);
    setValidationState(result);
    onBlur?.(event);
  };

  const isError = error || !validationState.isValid;
  const displayHelperText = helperText || validationState.message;

  // Configuration pour les mots de passe
  const passwordProps = type === 'password' ? {
    type: showPassword ? 'text' : 'password',
    InputProps: {
      endAdornment: (
        <InputAdornment position="end">
          <IconButton
            onClick={() => setShowPassword(!showPassword)}
            edge="end"
            size="small"
          >
            {showPassword ? <VisibilityOff /> : <Visibility />}
          </IconButton>
        </InputAdornment>
      )
    }
  } : {};

  if (showSuggestions && suggestions.length > 0) {
    return (
      <Autocomplete
        freeSolo
        options={suggestions}
        value={value}
        onChange={(event, newValue) => {
          onChange({ target: { name, value: newValue || '' } });
        }}
        onInputChange={(event, newInputValue) => {
          onChange({ target: { name, value: newInputValue } });
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            name={name}
            label={label}
            error={isError}
            helperText={displayHelperText}
            required={required}
            multiline={multiline}
            rows={multiline ? rows : undefined}
            placeholder={placeholder}
            disabled={disabled}
            autoComplete={autoComplete}
            onBlur={handleBlur}
            {...passwordProps}
            {...props}
          />
        )}
      />
    );
  }

  return (
    <TextField
      name={name}
      label={label}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      error={isError}
      helperText={displayHelperText}
      required={required}
      multiline={multiline}
      rows={multiline ? rows : undefined}
      placeholder={placeholder}
      disabled={disabled}
      autoComplete={autoComplete}
      {...passwordProps}
      {...props}
    />
  );
});

/**
 * Sélecteur multiple avec chips
 */
const MultiSelectChips = memo(({
  name,
  label,
  value = [],
  onChange,
  options = [],
  placeholder,
  disabled = false,
  error = false,
  helperText,
  required = false,
  maxSelections = null,
  ...props
}) => {
  const { t } = useTranslation();

  const handleChange = (event, newValue) => {
    if (maxSelections && newValue.length > maxSelections) {
      return; // Ne pas dépasser le maximum
    }
    
    onChange({
      target: {
        name,
        value: newValue
      }
    });
  };

  const displayValue = useOptimizedMemo(() => {
    return value.map(val => {
      const option = options.find(opt => opt.value === val);
      return option ? option.label : val;
    });
  }, [value, options], 'MultiSelectChips.displayValue');

  return (
    <Autocomplete
      multiple
      options={options}
      getOptionLabel={(option) => option.label || option}
      value={options.filter(option => value.includes(option.value))}
      onChange={handleChange}
      disabled={disabled}
      renderTags={(tagValue, getTagProps) =>
        tagValue.map((option, index) => (
          <Chip
            variant="outlined"
            label={option.label}
            {...getTagProps({ index })}
            key={option.value}
          />
        ))
      }
      renderInput={(params) => (
        <TextField
          {...params}
          name={name}
          label={label}
          placeholder={placeholder}
          error={error}
          helperText={helperText}
          required={required}
          {...props}
        />
      )}
    />
  );
});

/**
 * Composant de téléchargement de fichiers avec prévisualisation
 */
const FileUpload = memo(({
  name,
  label,
  value = [],
  onChange,
  accept = "image/*",
  multiple = false,
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 5,
  showPreview = true,
  disabled = false,
  error = false,
  helperText
}) => {
  const { t } = useTranslation();
  const [dragOver, setDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  const handleFileSelect = useCallback((files) => {
    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = [];

    fileArray.forEach(file => {
      if (file.size > maxSize) {
        errors.push(`${file.name}: ${t('validation.fileSize', { max: Math.round(maxSize / 1024 / 1024) })}`);
        return;
      }

      if (!multiple && validFiles.length >= 1) {
        return;
      }

      if (multiple && value.length + validFiles.length >= maxFiles) {
        errors.push(t('validation.maxFiles', { max: maxFiles }));
        return;
      }

      validFiles.push(file);
    });

    if (validFiles.length > 0) {
      const newFiles = multiple ? [...value, ...validFiles] : validFiles;
      onChange({
        target: {
          name,
          value: newFiles
        }
      });
    }

    if (errors.length > 0) {
      console.warn('Erreurs de téléchargement:', errors);
    }
  }, [value, onChange, name, multiple, maxSize, maxFiles, t]);

  const handleDrop = useCallback((event) => {
    event.preventDefault();
    setDragOver(false);
    
    const files = event.dataTransfer.files;
    handleFileSelect(files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOver(false);
  }, []);

  const removeFile = useCallback((index) => {
    const newFiles = value.filter((_, i) => i !== index);
    onChange({
      target: {
        name,
        value: newFiles
      }
    });
  }, [value, onChange, name]);

  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        {label}
      </Typography>
      
      <Card
        variant="outlined"
        sx={{
          border: dragOver ? '2px dashed #2196F3' : '2px dashed #ccc',
          backgroundColor: dragOver ? 'rgba(33, 150, 243, 0.1)' : 'transparent',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.6 : 1
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <CardContent sx={{ textAlign: 'center', py: 3 }}>
          <input
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={(e) => handleFileSelect(e.target.files)}
            style={{ display: 'none' }}
            id={`file-upload-${name}`}
            disabled={disabled}
          />
          <label htmlFor={`file-upload-${name}`}>
            <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body1" gutterBottom>
              {t('form.dragDropFiles')}
            </Typography>
            <Button variant="outlined" component="span" disabled={disabled}>
              {t('form.selectFiles')}
            </Button>
          </label>
        </CardContent>
      </Card>

      {helperText && (
        <Typography variant="caption" color={error ? 'error' : 'text.secondary'} sx={{ mt: 1, display: 'block' }}>
          {helperText}
        </Typography>
      )}

      {/* Prévisualisation des fichiers */}
      {showPreview && value.length > 0 && (
        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            {t('form.selectedFiles')}
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {value.map((file, index) => (
              <Chip
                key={index}
                label={file.name}
                onDelete={() => removeFile(index)}
                deleteIcon={<Delete />}
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
});

/**
 * Formulaire en étapes (Stepper)
 */
const StepperForm = memo(({
  steps = [],
  activeStep = 0,
  onStepChange,
  onSubmit,
  onCancel,
  orientation = 'vertical',
  showProgress = true,
  allowSkip = false,
  children
}) => {
  const { t } = useTranslation();

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      onStepChange(activeStep + 1);
    } else {
      onSubmit?.();
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      onStepChange(activeStep - 1);
    }
  };

  const handleSkip = () => {
    if (allowSkip && activeStep < steps.length - 1) {
      onStepChange(activeStep + 1);
    }
  };

  const progress = ((activeStep + 1) / steps.length) * 100;

  return (
    <Box>
      {showProgress && (
        <Box mb={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="body2" color="text.secondary">
              {t('form.step')} {activeStep + 1} {t('form.of')} {steps.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {Math.round(progress)}%
            </Typography>
          </Box>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
      )}

      <Stepper activeStep={activeStep} orientation={orientation}>
        {steps.map((step, index) => (
          <Step key={step.label}>
            <StepLabel
              optional={step.optional && (
                <Typography variant="caption">{t('form.optional')}</Typography>
              )}
            >
              {step.label}
            </StepLabel>
            {orientation === 'vertical' && (
              <StepContent>
                {step.content}
                <Box sx={{ mb: 2 }}>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    sx={{ mt: 1, mr: 1 }}
                  >
                    {activeStep === steps.length - 1 ? t('form.finish') : t('form.continue')}
                  </Button>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    sx={{ mt: 1, mr: 1 }}
                  >
                    {t('form.back')}
                  </Button>
                  {allowSkip && !step.required && activeStep !== steps.length - 1 && (
                    <Button onClick={handleSkip} sx={{ mt: 1, mr: 1 }}>
                      {t('form.skip')}
                    </Button>
                  )}
                </Box>
              </StepContent>
            )}
          </Step>
        ))}
      </Stepper>

      {orientation === 'horizontal' && (
        <Box mt={3}>
          {steps[activeStep]?.content}
          
          <Box display="flex" justifyContent="space-between" mt={3}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
            >
              {t('form.back')}
            </Button>
            
            <Box>
              {allowSkip && !steps[activeStep]?.required && activeStep !== steps.length - 1 && (
                <Button onClick={handleSkip} sx={{ mr: 1 }}>
                  {t('form.skip')}
                </Button>
              )}
              <Button variant="contained" onClick={handleNext}>
                {activeStep === steps.length - 1 ? t('form.finish') : t('form.continue')}
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
});

SmartTextField.displayName = 'SmartTextField';
MultiSelectChips.displayName = 'MultiSelectChips';
FileUpload.displayName = 'FileUpload';
StepperForm.displayName = 'StepperForm';

export {
  SmartTextField,
  MultiSelectChips,
  FileUpload,
  StepperForm
};
