/**
 * Service d'intégration météorologique avec OpenWeatherMap
 */

const axios = require('axios');
const { Pool } = require('pg');
const logger = require('../utils/logger');

class WeatherService {
  constructor() {
    this.pool = new Pool();
    this.apiKey = process.env.OPENWEATHER_API_KEY;
    this.baseUrl = 'https://api.openweathermap.org/data/2.5';
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
  }

  /**
   * Obtenir les données météo actuelles pour une ferme
   */
  async getCurrentWeather(farmId, lat, lon) {
    try {
      const cacheKey = `current_${farmId}`;
      
      // Vérifier le cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      // Appel API OpenWeatherMap
      const response = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          lat: lat,
          lon: lon,
          appid: this.apiKey,
          units: 'metric',
          lang: 'fr'
        }
      });

      const weatherData = this.formatCurrentWeather(response.data);
      
      // Sauvegarder en base
      await this.saveWeatherLog(farmId, weatherData, 'current');
      
      // Mettre en cache
      this.cache.set(cacheKey, {
        data: weatherData,
        timestamp: Date.now()
      });

      return weatherData;

    } catch (error) {
      logger.error('Erreur lors de la récupération des données météo:', error);
      throw new Error('Impossible de récupérer les données météo');
    }
  }

  /**
   * Obtenir les prévisions météo pour 5 jours
   */
  async getWeatherForecast(farmId, lat, lon) {
    try {
      const cacheKey = `forecast_${farmId}`;
      
      // Vérifier le cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      // Appel API pour les prévisions
      const response = await axios.get(`${this.baseUrl}/forecast`, {
        params: {
          lat: lat,
          lon: lon,
          appid: this.apiKey,
          units: 'metric',
          lang: 'fr'
        }
      });

      const forecastData = this.formatForecastWeather(response.data);
      
      // Sauvegarder en base
      await this.saveWeatherLog(farmId, forecastData, 'forecast');
      
      // Mettre en cache
      this.cache.set(cacheKey, {
        data: forecastData,
        timestamp: Date.now()
      });

      return forecastData;

    } catch (error) {
      logger.error('Erreur lors de la récupération des prévisions météo:', error);
      throw new Error('Impossible de récupérer les prévisions météo');
    }
  }

  /**
   * Obtenir les alertes météo
   */
  async getWeatherAlerts(farmId, lat, lon) {
    try {
      // Utiliser l'API One Call pour les alertes
      const response = await axios.get(`${this.baseUrl}/onecall`, {
        params: {
          lat: lat,
          lon: lon,
          appid: this.apiKey,
          units: 'metric',
          lang: 'fr',
          exclude: 'minutely,hourly'
        }
      });

      const alerts = response.data.alerts || [];
      const formattedAlerts = alerts.map(alert => ({
        event: alert.event,
        description: alert.description,
        start: new Date(alert.start * 1000),
        end: new Date(alert.end * 1000),
        severity: this.mapAlertSeverity(alert.tags)
      }));

      // Analyser l'impact sur l'aviculture
      const impact = this.analyzeWeatherImpact(response.data.current, formattedAlerts);

      return {
        alerts: formattedAlerts,
        impact: impact,
        recommendations: this.generateWeatherRecommendations(impact)
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des alertes météo:', error);
      return { alerts: [], impact: null, recommendations: [] };
    }
  }

  /**
   * Formater les données météo actuelles
   */
  formatCurrentWeather(data) {
    return {
      temperature: Math.round(data.main.temp),
      feelsLike: Math.round(data.main.feels_like),
      humidity: data.main.humidity,
      pressure: data.main.pressure,
      windSpeed: data.wind.speed,
      windDirection: data.wind.deg,
      visibility: data.visibility / 1000, // en km
      cloudiness: data.clouds.all,
      weather: {
        main: data.weather[0].main,
        description: data.weather[0].description,
        icon: data.weather[0].icon
      },
      sunrise: new Date(data.sys.sunrise * 1000),
      sunset: new Date(data.sys.sunset * 1000),
      timestamp: new Date(data.dt * 1000)
    };
  }

  /**
   * Formater les prévisions météo
   */
  formatForecastWeather(data) {
    const dailyForecasts = {};
    
    data.list.forEach(item => {
      const date = new Date(item.dt * 1000).toDateString();
      
      if (!dailyForecasts[date]) {
        dailyForecasts[date] = {
          date: new Date(item.dt * 1000),
          temperatures: [],
          humidity: [],
          weather: [],
          windSpeed: [],
          precipitation: 0
        };
      }

      dailyForecasts[date].temperatures.push(item.main.temp);
      dailyForecasts[date].humidity.push(item.main.humidity);
      dailyForecasts[date].weather.push({
        main: item.weather[0].main,
        description: item.weather[0].description,
        icon: item.weather[0].icon
      });
      dailyForecasts[date].windSpeed.push(item.wind.speed);
      
      if (item.rain) {
        dailyForecasts[date].precipitation += item.rain['3h'] || 0;
      }
      if (item.snow) {
        dailyForecasts[date].precipitation += item.snow['3h'] || 0;
      }
    });

    // Calculer les moyennes et extremes pour chaque jour
    const forecasts = Object.values(dailyForecasts).map(day => ({
      date: day.date,
      tempMin: Math.round(Math.min(...day.temperatures)),
      tempMax: Math.round(Math.max(...day.temperatures)),
      tempAvg: Math.round(day.temperatures.reduce((sum, temp) => sum + temp, 0) / day.temperatures.length),
      humidity: Math.round(day.humidity.reduce((sum, h) => sum + h, 0) / day.humidity.length),
      windSpeed: Math.round(day.windSpeed.reduce((sum, w) => sum + w, 0) / day.windSpeed.length),
      precipitation: Math.round(day.precipitation * 10) / 10,
      weather: day.weather[0] // Prendre la première prévision de la journée
    }));

    return {
      city: data.city.name,
      country: data.city.country,
      forecasts: forecasts.slice(0, 5) // 5 jours
    };
  }

  /**
   * Analyser l'impact météo sur l'aviculture
   */
  analyzeWeatherImpact(currentWeather, alerts) {
    const impact = {
      overall: 'neutral',
      factors: [],
      risks: [],
      opportunities: []
    };

    const temp = currentWeather.temp;
    const humidity = currentWeather.humidity;
    const windSpeed = currentWeather.wind_speed;

    // Analyse de la température
    if (temp < 5) {
      impact.factors.push('Température très basse');
      impact.risks.push('Risque de gel - protection des volailles nécessaire');
      impact.overall = 'negative';
    } else if (temp < 15) {
      impact.factors.push('Température basse');
      impact.risks.push('Augmentation de la consommation d\'aliment');
    } else if (temp > 35) {
      impact.factors.push('Température très élevée');
      impact.risks.push('Stress thermique - risque de mortalité');
      impact.overall = 'negative';
    } else if (temp > 28) {
      impact.factors.push('Température élevée');
      impact.risks.push('Réduction de la ponte et de l\'appétit');
    } else {
      impact.opportunities.push('Température optimale pour la production');
    }

    // Analyse de l'humidité
    if (humidity > 80) {
      impact.factors.push('Humidité très élevée');
      impact.risks.push('Risque de maladies respiratoires');
      if (impact.overall !== 'negative') impact.overall = 'warning';
    } else if (humidity < 40) {
      impact.factors.push('Humidité faible');
      impact.risks.push('Stress hydrique et problèmes respiratoires');
    }

    // Analyse du vent
    if (windSpeed > 15) {
      impact.factors.push('Vent fort');
      impact.risks.push('Refroidissement éolien - protection nécessaire');
    }

    // Analyse des alertes
    alerts.forEach(alert => {
      if (alert.severity === 'severe' || alert.severity === 'extreme') {
        impact.overall = 'negative';
        impact.risks.push(`Alerte météo: ${alert.event}`);
      }
    });

    return impact;
  }

  /**
   * Générer des recommandations basées sur la météo
   */
  generateWeatherRecommendations(impact) {
    const recommendations = [];

    impact.risks.forEach(risk => {
      if (risk.includes('gel')) {
        recommendations.push({
          priority: 'high',
          action: 'Activer le chauffage d\'appoint',
          description: 'Protéger les volailles du gel avec un système de chauffage'
        });
      }
      
      if (risk.includes('stress thermique')) {
        recommendations.push({
          priority: 'high',
          action: 'Améliorer la ventilation',
          description: 'Augmenter la circulation d\'air et fournir de l\'ombre'
        });
      }
      
      if (risk.includes('maladies respiratoires')) {
        recommendations.push({
          priority: 'medium',
          action: 'Surveiller la santé respiratoire',
          description: 'Contrôler régulièrement les signes de maladies respiratoires'
        });
      }
      
      if (risk.includes('consommation d\'aliment')) {
        recommendations.push({
          priority: 'medium',
          action: 'Ajuster l\'alimentation',
          description: 'Augmenter les rations énergétiques par temps froid'
        });
      }
    });

    return recommendations;
  }

  /**
   * Mapper la sévérité des alertes
   */
  mapAlertSeverity(tags) {
    if (!tags || tags.length === 0) return 'minor';
    
    if (tags.includes('Extreme')) return 'extreme';
    if (tags.includes('Severe')) return 'severe';
    if (tags.includes('Moderate')) return 'moderate';
    return 'minor';
  }

  /**
   * Sauvegarder les données météo en base
   */
  async saveWeatherLog(farmId, weatherData, type) {
    try {
      await this.pool.query(`
        INSERT INTO weather_logs (farm_id, temperature, humidity, wind_speed, weather_data, log_type, recorded_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW())
      `, [
        farmId,
        weatherData.temperature || weatherData.forecasts?.[0]?.tempAvg,
        weatherData.humidity || weatherData.forecasts?.[0]?.humidity,
        weatherData.windSpeed || weatherData.forecasts?.[0]?.windSpeed,
        JSON.stringify(weatherData),
        type
      ]);
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde des données météo:', error);
    }
  }

  /**
   * Obtenir l'historique météo d'une ferme
   */
  async getWeatherHistory(farmId, days = 7) {
    try {
      const result = await this.pool.query(`
        SELECT * FROM weather_logs 
        WHERE farm_id = $1 
          AND recorded_at >= NOW() - INTERVAL '${days} days'
          AND log_type = 'current'
        ORDER BY recorded_at DESC
      `, [farmId]);

      return result.rows.map(row => ({
        ...row,
        weather_data: typeof row.weather_data === 'string' ? 
          JSON.parse(row.weather_data) : row.weather_data
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique météo:', error);
      return [];
    }
  }

  /**
   * Obtenir les coordonnées d'une ferme
   */
  async getFarmCoordinates(farmId) {
    try {
      const result = await this.pool.query(`
        SELECT latitude, longitude, nom, adresse 
        FROM fermes 
        WHERE id = $1
      `, [farmId]);

      if (result.rows.length === 0) {
        throw new Error('Ferme non trouvée');
      }

      const farm = result.rows[0];
      
      // Si pas de coordonnées, essayer de les obtenir via géocodage
      if (!farm.latitude || !farm.longitude) {
        const coordinates = await this.geocodeAddress(farm.adresse);
        if (coordinates) {
          await this.updateFarmCoordinates(farmId, coordinates.lat, coordinates.lon);
          return coordinates;
        }
      }

      return {
        lat: parseFloat(farm.latitude),
        lon: parseFloat(farm.longitude)
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération des coordonnées:', error);
      throw error;
    }
  }

  /**
   * Géocoder une adresse pour obtenir les coordonnées
   */
  async geocodeAddress(address) {
    try {
      const response = await axios.get(`${this.baseUrl}/geocoding/direct`, {
        params: {
          q: address,
          limit: 1,
          appid: this.apiKey
        }
      });

      if (response.data.length > 0) {
        const location = response.data[0];
        return {
          lat: location.lat,
          lon: location.lon
        };
      }

      return null;
    } catch (error) {
      logger.error('Erreur lors du géocodage:', error);
      return null;
    }
  }

  /**
   * Mettre à jour les coordonnées d'une ferme
   */
  async updateFarmCoordinates(farmId, lat, lon) {
    try {
      await this.pool.query(`
        UPDATE fermes 
        SET latitude = $1, longitude = $2 
        WHERE id = $3
      `, [lat, lon, farmId]);
    } catch (error) {
      logger.error('Erreur lors de la mise à jour des coordonnées:', error);
    }
  }

  /**
   * Analyser les tendances météo pour recommandations
   */
  async analyzeWeatherTrends(farmId) {
    try {
      const history = await this.getWeatherHistory(farmId, 30);
      
      if (history.length < 7) {
        return { trends: [], recommendations: [] };
      }

      const temperatures = history.map(h => h.temperature);
      const humidities = history.map(h => h.humidity);
      
      const tempTrend = this.calculateTrend(temperatures);
      const humidityTrend = this.calculateTrend(humidities);

      const trends = [];
      const recommendations = [];

      if (tempTrend > 0.5) {
        trends.push('Tendance au réchauffement');
        recommendations.push({
          action: 'Préparer les systèmes de refroidissement',
          priority: 'medium'
        });
      } else if (tempTrend < -0.5) {
        trends.push('Tendance au refroidissement');
        recommendations.push({
          action: 'Vérifier les systèmes de chauffage',
          priority: 'medium'
        });
      }

      if (humidityTrend > 2) {
        trends.push('Augmentation de l\'humidité');
        recommendations.push({
          action: 'Améliorer la ventilation',
          priority: 'medium'
        });
      }

      return { trends, recommendations };
    } catch (error) {
      logger.error('Erreur lors de l\'analyse des tendances:', error);
      return { trends: [], recommendations: [] };
    }
  }

  /**
   * Calculer la tendance d'une série de données
   */
  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  /**
   * Démarrer la collecte automatique des données météo
   */
  startAutomaticWeatherCollection() {
    // Collecter les données météo toutes les heures
    setInterval(async () => {
      try {
        const farms = await this.getAllActiveFarms();
        
        for (const farm of farms) {
          if (farm.latitude && farm.longitude) {
            await this.getCurrentWeather(farm.id, farm.latitude, farm.longitude);
          }
        }
      } catch (error) {
        logger.error('Erreur lors de la collecte automatique météo:', error);
      }
    }, 60 * 60 * 1000); // 1 heure
  }

  /**
   * Obtenir toutes les fermes actives
   */
  async getAllActiveFarms() {
    try {
      const result = await this.pool.query(`
        SELECT id, latitude, longitude, nom 
        FROM fermes 
        WHERE active = true
      `);
      
      return result.rows;
    } catch (error) {
      logger.error('Erreur lors de la récupération des fermes:', error);
      return [];
    }
  }
}

module.exports = new WeatherService();
