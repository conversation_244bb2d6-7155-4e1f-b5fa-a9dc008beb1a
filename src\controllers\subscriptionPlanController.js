const SubscriptionPlan = require('../models/subscriptionPlan');

/**
 * Contrôleur pour la gestion des plans d'abonnement
 */
const subscriptionPlanController = {
  /**
   * Récupère tous les plans d'abonnement
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  getAllPlans: async (req, res) => {
    try {
      const plans = await SubscriptionPlan.findAll();
      res.json(plans);
    } catch (error) {
      console.error('Erreur lors de la récupération des plans d\'abonnement:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des plans d\'abonnement',
        error: error.message
      });
    }
  },

  /**
   * Récupère un plan d'abonnement par son ID
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  getPlanById: async (req, res) => {
    try {
      const plan = await SubscriptionPlan.findById(req.params.id);
      if (!plan) {
        return res.status(404).json({ message: 'Plan d\'abonnement non trouvé' });
      }
      res.json(plan);
    } catch (error) {
      console.error('Erreur lors de la récupération du plan d\'abonnement:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération du plan d\'abonnement',
        error: error.message
      });
    }
  },

  /**
   * Crée un nouveau plan d'abonnement
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  createPlan: async (req, res) => {
    try {
      const { name, description, price, duration, features, isActive } = req.body;

      // Validation des données
      if (!name) {
        return res.status(400).json({ message: 'Le nom du plan est requis' });
      }

      if (price === undefined || price < 0) {
        return res.status(400).json({ message: 'Le prix doit être un nombre positif' });
      }

      if (!duration || duration <= 0) {
        return res.status(400).json({ message: 'La durée doit être un nombre positif' });
      }

      const planData = {
        name,
        description,
        price,
        duration_days: duration,
        features,
        is_active: isActive
      };

      const newPlan = await SubscriptionPlan.create(planData);
      res.status(201).json(newPlan);
    } catch (error) {
      console.error('Erreur lors de la création du plan d\'abonnement:', error);
      res.status(500).json({
        message: 'Erreur lors de la création du plan d\'abonnement',
        error: error.message
      });
    }
  },

  /**
   * Met à jour un plan d'abonnement existant
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  updatePlan: async (req, res) => {
    try {
      const { name, description, price, duration, features, isActive } = req.body;

      // Validation des données
      if (!name) {
        return res.status(400).json({ message: 'Le nom du plan est requis' });
      }

      if (price === undefined || price < 0) {
        return res.status(400).json({ message: 'Le prix doit être un nombre positif' });
      }

      if (!duration || duration <= 0) {
        return res.status(400).json({ message: 'La durée doit être un nombre positif' });
      }

      const planData = {
        name,
        description,
        price,
        duration_days: duration,
        features,
        is_active: isActive
      };

      const updatedPlan = await SubscriptionPlan.update(req.params.id, planData);
      if (!updatedPlan) {
        return res.status(404).json({ message: 'Plan d\'abonnement non trouvé' });
      }

      res.json(updatedPlan);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du plan d\'abonnement:', error);
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du plan d\'abonnement',
        error: error.message
      });
    }
  },

  /**
   * Supprime un plan d'abonnement
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  deletePlan: async (req, res) => {
    try {
      const deletedPlan = await SubscriptionPlan.delete(req.params.id);
      if (!deletedPlan) {
        return res.status(404).json({ message: 'Plan d\'abonnement non trouvé' });
      }

      res.json({ message: 'Plan d\'abonnement supprimé avec succès', plan: deletedPlan });
    } catch (error) {
      console.error('Erreur lors de la suppression du plan d\'abonnement:', error);
      res.status(500).json({
        message: 'Erreur lors de la suppression du plan d\'abonnement',
        error: error.message
      });
    }
  }
};

module.exports = subscriptionPlanController;
