import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Box, Menu, MenuItem, IconButton, Avatar, Tooltip } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  AccountCircle,
} from '@mui/icons-material';
import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const StyledToolbar = styled(Toolbar)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.spacing(0, 3),
}));

const NavButton = styled(Button)(({ theme }) => ({
  color: 'white',
  marginLeft: theme.spacing(2),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
}));

function Navbar({ children }) {
  const { user, isA<PERSON><PERSON><PERSON><PERSON>, logout } = useAuth();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    logout();
    navigate('/login');
  };

  const handleDashboardClick = () => {
    if (user) {
      // Handle both string roles (legacy) and object roles (new API format)
      const userRole = typeof user.role === 'object' && user.role !== null ? user.role.name : user.role;
      navigate(`/${userRole}/dashboard`);
    } else {
      navigate('/login');
    }
  };

  return (
    <>
      <AppBar position="static">
        <StyledToolbar>
          <Typography
            variant="h6"
            component={RouterLink}
            to="/"
            sx={{
              color: 'white',
              textDecoration: 'none',
              fontWeight: 'bold',
            }}
          >
            Poultray DZ
          </Typography>
          <Box>
            <NavButton component={RouterLink} to="/eleveurs">
              Éleveurs
            </NavButton>
            <NavButton component={RouterLink} to="/volailles">
              Volailles
            </NavButton>

            {isAuthenticated() ? (
              <>
                <NavButton
                  onClick={handleDashboardClick}
                  startIcon={<DashboardIcon />}
                  sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                >
                  Tableau de bord
                </NavButton>

                <Tooltip title="Profil">
                  <IconButton
                    onClick={handleProfileMenuOpen}
                    sx={{ ml: 2, color: 'white' }}
                  >
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                      {user?.first_name ? user.first_name[0] : user?.username ? user.username[0] : 'U'}
                    </Avatar>
                  </IconButton>
                </Tooltip>

                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  PaperProps={{
                    elevation: 0,
                    sx: {
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                      mt: 1.5,
                    },
                  }}
                >
                  <MenuItem onClick={() => { handleMenuClose(); navigate(`/${user.role}/profile`); }}>
                    <PersonIcon sx={{ mr: 1 }} /> Profil
                  </MenuItem>
                  <MenuItem onClick={handleLogout}>
                    <LogoutIcon sx={{ mr: 1 }} /> Déconnexion
                  </MenuItem>
                </Menu>
              </>
            ) : (
              <NavButton
                component={RouterLink}
                to="/login"
                startIcon={<LoginIcon />}
              >
                Connexion
              </NavButton>
            )}
          </Box>
        </StyledToolbar>
      </AppBar>

      {/* Render children below the navbar */}
      {children}
    </>
  );
}

export default Navbar;
