const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Annonce extends Model {
    static associate(models) {
      Annonce.belongsTo(models.User, {
        foreignKey: 'utilisateur_id',
        as: 'utilisateur'
      });
    }
  }

  Annonce.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  titre: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  categorie: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  prix: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  localisation: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  images: {
    type: DataTypes.ARRAY(DataTypes.TEXT),
    defaultValue: []
  },
  est_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  utilisateur_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  date_creation: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  date_mise_a_jour: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Annonce',
  tableName: 'annonces',
  timestamps: false,
  hooks: {
    beforeUpdate: (annonce) => {
      annonce.date_mise_a_jour = new Date();
    }
  }
});

  return Annonce;
};
