import { useState, useEffect } from 'react';
import testDbService from '../services/testDbService';

/**
 * Hook pour gérer les données des poussins
 * @returns {Object} - Données des poussins, état de chargement et erreurs
 */
export const usePoussins = () => {
  const [lots, setLots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPoussins = async () => {
      try {
        const data = await testDbService.getPoussins();
        setLots(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchPoussins();
  }, []);

  const addLot = async (lotData) => {
    try {
      const newLot = await testDbService.insertTestPoussins(lotData);
      setLots([...lots, newLot]);
      return newLot;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateLot = async (id, lotData) => {
    try {
      const updatedLot = await testDbService.updatePoussins(id, lotData);
      setLots(lots.map(lot => lot.id === id ? updatedLot : lot));
      return updatedLot;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteLot = async (id) => {
    try {
      await testDbService.deletePoussins(id);
      setLots(lots.filter(lot => lot.id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    lots,
    loading,
    error,
    addLot,
    updateLot,
    deleteLot
  };
};

export default usePoussins;

