const http = require('http');

async function getAuthToken() {
  const makeRequest = (path, method = 'GET', data = null) => {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3003,
        path: path,
        method: method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => responseData += chunk);
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(responseData);
            resolve({ status: res.statusCode, data: jsonData });
          } catch (err) {
            resolve({ status: res.statusCode, data: responseData });
          }
        });
      });

      req.on('error', (err) => reject(err));

      if (data) {
        req.write(JSON.stringify(data));
      }
      req.end();
    });
  };

  try {
    console.log('Attempting to login...');
    const loginData = {
      email: '<EMAIL>',
      firebase_token: 'dummy_token',
      firebase_uid: 'dummy_uid',
      display_name: null
    };

    const response = await makeRequest('/api/auth/login', 'POST', loginData);

    if (response.status === 200 && response.data.token) {
      console.log('✅ Login successful! Token:', response.data.token);
      return response.data.token;
    } else {
      console.log('❌ Login failed:', response.status, response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function testWithToken(token) {
  if (!token) {
    console.log('No token available for testing');
    return;
  }

  const makeRequest = (path) => {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3003,
        path: path,
        method: 'GET',
        headers: {
          'x-auth-token': token,
          'Authorization': `Bearer ${token}`
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            resolve({ status: res.statusCode, data: jsonData });
          } catch (err) {
            resolve({ status: res.statusCode, data: data });
          }
        });
      });

      req.on('error', (err) => reject(err));
      req.end();
    });
  };

  try {
    console.log('\nTesting admin users endpoint...');
    const response = await makeRequest('/api/admin/users?role=eleveur&page=1&limit=10');

    if (response.status === 200) {
      console.log('✅ Users endpoint working! Total users:', response.data.total || 0);
      console.log('Users returned:', response.data.users?.length || 0);
    } else {
      console.log('❌ Users endpoint error:', response.status, response.data);
    }
  } catch (error) {
    console.log('❌ Users request error:', error.message);
  }

  try {
    console.log('\nTesting stats endpoint...');
    const statsResponse = await makeRequest('/api/admin/stats');

    if (statsResponse.status === 200) {
      console.log('✅ Stats endpoint working!');
      console.log('Total users:', statsResponse.data.totalUsers);
      console.log('Total eleveurs:', statsResponse.data.totalEleveurs);
    } else {
      console.log('❌ Stats endpoint error:', statsResponse.status, statsResponse.data);
    }
  } catch (error) {
    console.log('❌ Stats request error:', error.message);
  }
}

async function main() {
  const token = await getAuthToken();
  await testWithToken(token);
}

main();
