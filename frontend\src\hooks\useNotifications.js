import { useState, useEffect, useCallback, useRef } from 'react';
import notificationService, { 
  NOTIFICATION_TYPES, 
  NOTIFICATION_PRIORITIES, 
  NOTIFICATION_CATEGORIES 
} from '../services/notificationService';

/**
 * Hook pour gérer les notifications dans les composants React
 */
export const useNotifications = (options = {}) => {
  const {
    category = null,
    autoMarkAsRead = false,
    maxNotifications = 10
  } = options;

  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const mountedRef = useRef(true);

  // Charger les notifications initiales
  useEffect(() => {
    const loadNotifications = () => {
      const filters = { limit: maxNotifications };
      if (category) filters.category = category;
      
      const notifs = notificationService.getNotifications(filters);
      const unread = notificationService.getUnreadCount(category);
      
      if (mountedRef.current) {
        setNotifications(notifs);
        setUnreadCount(unread);
        setLoading(false);
      }
    };

    loadNotifications();
  }, [category, maxNotifications]);

  // Écouter les nouvelles notifications
  useEffect(() => {
    const handleNewNotification = (notification) => {
      if (!category || notification.category === category) {
        setNotifications(prev => {
          const updated = [notification, ...prev];
          return updated.slice(0, maxNotifications);
        });
        setUnreadCount(prev => prev + 1);

        // Marquer automatiquement comme lu si demandé
        if (autoMarkAsRead) {
          setTimeout(() => {
            notificationService.markAsRead(notification.id);
          }, 1000);
        }
      }
    };

    const handleNotificationRead = (notification) => {
      if (!category || notification.category === category) {
        setNotifications(prev => 
          prev.map(n => n.id === notification.id ? { ...n, read: true } : n)
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    };

    const handleNotificationRemoved = (notification) => {
      if (!category || notification.category === category) {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
        if (!notification.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      }
    };

    const handleAllNotificationsRead = () => {
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);
    };

    const handleAllNotificationsCleared = () => {
      setNotifications([]);
      setUnreadCount(0);
    };

    // Ajouter les écouteurs
    notificationService.addEventListener('notification', handleNewNotification);
    notificationService.addEventListener('notificationRead', handleNotificationRead);
    notificationService.addEventListener('notificationRemoved', handleNotificationRemoved);
    notificationService.addEventListener('allNotificationsRead', handleAllNotificationsRead);
    notificationService.addEventListener('allNotificationsCleared', handleAllNotificationsCleared);

    // Cleanup
    return () => {
      mountedRef.current = false;
      notificationService.removeEventListener('notification', handleNewNotification);
      notificationService.removeEventListener('notificationRead', handleNotificationRead);
      notificationService.removeEventListener('notificationRemoved', handleNotificationRemoved);
      notificationService.removeEventListener('allNotificationsRead', handleAllNotificationsRead);
      notificationService.removeEventListener('allNotificationsCleared', handleAllNotificationsCleared);
    };
  }, [category, maxNotifications, autoMarkAsRead]);

  // Fonctions utilitaires
  const markAsRead = useCallback((notificationId) => {
    notificationService.markAsRead(notificationId);
  }, []);

  const markAllAsRead = useCallback(() => {
    notificationService.markAllAsRead();
  }, []);

  const removeNotification = useCallback((notificationId) => {
    notificationService.removeNotification(notificationId);
  }, []);

  const clearAll = useCallback(() => {
    notificationService.clearAllNotifications();
  }, []);

  const showNotification = useCallback((notification) => {
    return notificationService.showNotification(notification);
  }, []);

  // Fonctions de raccourci pour différents types
  const showSuccess = useCallback((title, message, options = {}) => {
    return showNotification({
      type: NOTIFICATION_TYPES.SUCCESS,
      title,
      message,
      ...options
    });
  }, [showNotification]);

  const showError = useCallback((title, message, options = {}) => {
    return showNotification({
      type: NOTIFICATION_TYPES.ERROR,
      title,
      message,
      priority: NOTIFICATION_PRIORITIES.HIGH,
      ...options
    });
  }, [showNotification]);

  const showWarning = useCallback((title, message, options = {}) => {
    return showNotification({
      type: NOTIFICATION_TYPES.WARNING,
      title,
      message,
      priority: NOTIFICATION_PRIORITIES.NORMAL,
      ...options
    });
  }, [showNotification]);

  const showInfo = useCallback((title, message, options = {}) => {
    return showNotification({
      type: NOTIFICATION_TYPES.INFO,
      title,
      message,
      ...options
    });
  }, [showNotification]);

  const showAlert = useCallback((title, message, options = {}) => {
    return showNotification({
      type: NOTIFICATION_TYPES.ALERT,
      title,
      message,
      priority: NOTIFICATION_PRIORITIES.URGENT,
      persistent: true,
      ...options
    });
  }, [showNotification]);

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showAlert
  };
};

/**
 * Hook pour les paramètres de notifications
 */
export const useNotificationSettings = () => {
  const [settings, setSettings] = useState(notificationService.settings);

  useEffect(() => {
    const handleSettingsUpdate = (newSettings) => {
      setSettings(newSettings);
    };

    notificationService.addEventListener('settingsUpdated', handleSettingsUpdate);

    return () => {
      notificationService.removeEventListener('settingsUpdated', handleSettingsUpdate);
    };
  }, []);

  const updateSettings = useCallback((newSettings) => {
    notificationService.updateSettings(newSettings);
  }, []);

  const requestPermission = useCallback(async () => {
    return await notificationService.requestPermission();
  }, []);

  return {
    settings,
    updateSettings,
    requestPermission,
    permission: notificationService.permission
  };
};

/**
 * Hook pour les notifications temps réel spécifiques
 */
export const useRealTimeNotifications = (userId, userRole) => {
  const { showNotification } = useNotifications();

  useEffect(() => {
    // S'abonner aux notifications spécifiques à l'utilisateur
    const subscriptions = [];

    switch (userRole) {
      case 'eleveur':
        subscriptions.push(
          `user_${userId}`,
          `eleveur_${userId}`,
          'eleveur_general'
        );
        break;
      case 'veterinaire':
        subscriptions.push(
          `user_${userId}`,
          `veterinaire_${userId}`,
          'veterinaire_general'
        );
        break;
      case 'marchand':
        subscriptions.push(
          `user_${userId}`,
          `marchand_${userId}`,
          'marchand_general'
        );
        break;
      case 'admin':
        subscriptions.push(
          `user_${userId}`,
          'admin_general',
          'system_alerts'
        );
        break;
    }

    // S'abonner via WebSocket
    subscriptions.forEach(channel => {
      websocketService.subscribe(channel, { userId, userRole });
    });

    return () => {
      // Se désabonner lors du démontage
      subscriptions.forEach(channel => {
        websocketService.unsubscribe(channel);
      });
    };
  }, [userId, userRole, showNotification]);
};

/**
 * Hook pour les notifications de chat/messages
 */
export const useChatNotifications = (chatId = null) => {
  const { showNotification } = useNotifications({ category: NOTIFICATION_CATEGORIES.MESSAGE });

  useEffect(() => {
    if (chatId) {
      websocketService.subscribe(`chat_${chatId}`);
      
      return () => {
        websocketService.unsubscribe(`chat_${chatId}`);
      };
    }
  }, [chatId]);

  const notifyNewMessage = useCallback((message) => {
    showNotification({
      type: NOTIFICATION_TYPES.INFO,
      category: NOTIFICATION_CATEGORIES.MESSAGE,
      title: `Message de ${message.sender_name}`,
      message: message.content,
      data: message
    });
  }, [showNotification]);

  const notifyUserTyping = useCallback((user) => {
    // Notification discrète pour l'indicateur de frappe
    showNotification({
      type: NOTIFICATION_TYPES.INFO,
      category: NOTIFICATION_CATEGORIES.MESSAGE,
      title: `${user.name} est en train d'écrire...`,
      message: '',
      priority: NOTIFICATION_PRIORITIES.LOW,
      persistent: false
    });
  }, [showNotification]);

  return {
    notifyNewMessage,
    notifyUserTyping
  };
};

export default useNotifications;
