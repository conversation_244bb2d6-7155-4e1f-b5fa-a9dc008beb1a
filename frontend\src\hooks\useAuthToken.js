import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

/**
 * Hook personnalisé pour gérer l'authentification et les tokens JWT
 * @returns {Object} Fonctions et données pour gérer l'authentification
 */
export const useAuthToken = () => {
  const [token, setToken] = useState(localStorage.getItem('auth_token'));
  const [refreshToken, setRefreshToken] = useState(localStorage.getItem('refresh_token'));
  const [isAuthenticated, setIsAuthenticated] = useState(!!token);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Décoder le token JWT pour obtenir les informations utilisateur
  const decodeToken = useCallback((token) => {
    try {
      if (!token) return null;
      return jwtDecode(token);
    } catch (err) {
      console.error('Erreur lors du décodage du token:', err);
      return null;
    }
  }, []);

  // Vérifier si le token est expiré
  const isTokenExpired = useCallback((token) => {
    try {
      const decoded = decodeToken(token);
      if (!decoded) return true;
      
      // Vérifier si le token est expiré (avec une marge de 60 secondes)
      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime - 60;
    } catch (err) {
      return true;
    }
  }, [decodeToken]);

  // Rafraîchir le token
  const refreshAuthToken = useCallback(async () => {
    if (!refreshToken) {
      setError(new Error('Pas de refresh token disponible'));
      setIsAuthenticated(false);
      return null;
    }

    try {
      const response = await axios.post('/api/auth/refresh', { refreshToken });
      const { token: newToken, refreshToken: newRefreshToken } = response.data;
      
      localStorage.setItem('auth_token', newToken);
      localStorage.setItem('refresh_token', newRefreshToken);
      
      setToken(newToken);
      setRefreshToken(newRefreshToken);
      setIsAuthenticated(true);
      
      const decodedUser = decodeToken(newToken);
      setUser(decodedUser);
      
      return newToken;
    } catch (err) {
      console.error('Erreur lors du rafraîchissement du token:', err);
      setError(err);
      logout();
      return null;
    }
  }, [refreshToken, decodeToken]);

  // Configurer les intercepteurs Axios pour gérer automatiquement les tokens
  useEffect(() => {
    // Intercepteur pour ajouter le token à toutes les requêtes
    const requestInterceptor = axios.interceptors.request.use(
      async (config) => {
        // Ne pas ajouter de token pour les requêtes d'authentification
        if (config.url.includes('/auth/login') || config.url.includes('/auth/refresh')) {
          return config;
        }

        let currentToken = token;

        // Si le token est expiré, essayer de le rafraîchir
        if (isTokenExpired(currentToken)) {
          currentToken = await refreshAuthToken();
        }

        if (currentToken) {
          config.headers.Authorization = `Bearer ${currentToken}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Intercepteur pour gérer les erreurs 401 (non autorisé)
    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Si l'erreur est 401 et que ce n'est pas déjà une tentative de rafraîchissement
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await refreshAuthToken();
            
            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return axios(originalRequest);
            }
          } catch (refreshError) {
            // Si le rafraîchissement échoue, déconnecter l'utilisateur
            logout();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    // Nettoyer les intercepteurs lors du démontage
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [token, refreshToken, refreshAuthToken, isTokenExpired]);

  // Initialiser l'état utilisateur à partir du token au chargement
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      
      try {
        if (token) {
          if (isTokenExpired(token)) {
            await refreshAuthToken();
          } else {
            const decodedUser = decodeToken(token);
            setUser(decodedUser);
            setIsAuthenticated(true);
          }
        }
      } catch (err) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', err);
        setError(err);
        logout();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [token, decodeToken, isTokenExpired, refreshAuthToken]);

  // Fonction de connexion
  const login = async (credentials) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/auth/login', credentials);
      const { token: newToken, refreshToken: newRefreshToken } = response.data;
      
      localStorage.setItem('auth_token', newToken);
      localStorage.setItem('refresh_token', newRefreshToken);
      
      setToken(newToken);
      setRefreshToken(newRefreshToken);
      setIsAuthenticated(true);
      
      const decodedUser = decodeToken(newToken);
      setUser(decodedUser);
      setError(null);
      
      return decodedUser;
    } catch (err) {
      console.error('Erreur lors de la connexion:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const logout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    
    setToken(null);
    setRefreshToken(null);
    setUser(null);
    setIsAuthenticated(false);
  };

  return {
    token,
    refreshToken,
    user,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    refreshAuthToken
  };
};

export default useAuthToken;

