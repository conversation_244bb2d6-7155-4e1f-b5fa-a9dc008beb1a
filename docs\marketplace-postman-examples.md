# Exemples de Requêtes Postman - Module Marketplace

## Configuration Globale

### Variables d'Environnement
```
BASE_URL: http://localhost:3000/api
JWT_TOKEN: <votre_token_jwt>
```

### Headers Globaux
```
Content-Type: application/json
Authorization: Bearer {{JWT_TOKEN}}
```

## Exemples de Requêtes

### 1. Lister les Annonces avec Filtres

```http
GET {{BASE_URL}}/marketplace/annonces?page=1&limit=10&espece=poulet&prix_min=300&prix_max=600
```

Réponse attendue (200 OK):
```json
{
  "annonces": [
    {
      "id": 1,
      "titre": "Lot de poulets de chair",
      "description": "Poulets de chair de 45 jours, race Cobb500",
      "espece": "poulet",
      "race": "Cobb500",
      "quantite": 100,
      "prix_unitaire": 450.00,
      "images": ["https://storage.poultry-dz.com/images/annonce1-1.jpg"],
      "status": "active",
      "vendeur": {
        "id": 123,
        "username": "eleveur_pro",
        "email": "<EMAIL>"
      },
      "created_at": "2024-01-15T14:30:00.000Z",
      "updated_at": "2024-01-15T14:30:00.000Z"
    }
  ],
  "total": 50,
  "pages": 5,
  "currentPage": 1
}
```

### 2. Créer une Nouvelle Annonce

```http
POST {{BASE_URL}}/marketplace/annonces
Content-Type: application/json
Authorization: Bearer {{JWT_TOKEN}}

{
  "titre": "Dindes de qualité supérieure",
  "description": "Lot de dindes élevées en plein air, alimentation 100% naturelle",
  "espece": "dinde",
  "race": "BUT Big 6",
  "quantite": 50,
  "prix_unitaire": 1200.00,
  "images": [
    "https://storage.poultry-dz.com/images/annonce2-1.jpg",
    "https://storage.poultry-dz.com/images/annonce2-2.jpg"
  ]
}
```

Réponse attendue (201 Created):
```json
{
  "id": 2,
  "titre": "Dindes de qualité supérieure",
  "description": "Lot de dindes élevées en plein air, alimentation 100% naturelle",
  "espece": "dinde",
  "race": "BUT Big 6",
  "quantite": 50,
  "prix_unitaire": 1200.00,
  "images": [
    "https://storage.poultry-dz.com/images/annonce2-1.jpg",
    "https://storage.poultry-dz.com/images/annonce2-2.jpg"
  ],
  "status": "active",
  "vendeur_id": 123,
  "created_at": "2024-01-15T15:00:00.000Z",
  "updated_at": "2024-01-15T15:00:00.000Z"
}
```

### 3. Gérer les Favoris

```http
POST {{BASE_URL}}/marketplace/annonces/1/favoris
Authorization: Bearer {{JWT_TOKEN}}
```

Réponse attendue (200 OK):
```json
{
  "message": "Annonce ajoutée aux favoris",
  "favoris": [1, 3, 5]
}
```

```http
GET {{BASE_URL}}/marketplace/favoris
Authorization: Bearer {{JWT_TOKEN}}
```

Réponse attendue (200 OK):
```json
[
  {
    "id": 1,
    "titre": "Lot de poulets de chair",
    "description": "Poulets de chair de 45 jours, race Cobb500",
    "espece": "poulet",
    "prix_unitaire": 450.00,
    "vendeur": {
      "id": 123,
      "username": "eleveur_pro"
    }
  }
]
```

## Notes sur l'Authentification

1. Obtenir un token JWT en s'authentifiant :
```http
POST {{BASE_URL}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "votre_mot_de_passe"
}
```

2. Utiliser le token retourné dans les requêtes suivantes :
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Gestion des Erreurs

Exemples de réponses d'erreur :

### Non Autorisé (401)
```json
{
  "error": "Token d'authentification non valide ou expiré"
}
```

### Interdit (403)
```json
{
  "error": "Non autorisé à modifier cette annonce"
}
```

### Non Trouvé (404)
```json
{
  "error": "Annonce non trouvée"
}
```

### Erreur de Validation (400)
```json
{
  "error": "Le titre et la description sont requis"
}
```
