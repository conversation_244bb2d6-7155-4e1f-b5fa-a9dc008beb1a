const { Prescription, User, Volaille } = require('../models');
const { Op } = require('sequelize');

// Obtenir toutes les prescriptions d'un vétérinaire
const getPrescriptionsByVeterinaire = async (req, res) => {
  try {
    const { veterinaireId } = req.params;
    const { page = 1, limit = 10, statut, urgence, dateDebut, dateFin } = req.query;
    
    const offset = (page - 1) * limit;
    
    // Construire les conditions de recherche
    const whereConditions = {
      veterinaire_id: veterinaireId
    };
    
    if (statut) {
      whereConditions.statut = statut;
    }
    
    if (urgence) {
      whereConditions.urgence = urgence;
    }
    
    if (dateDebut && dateFin) {
      whereConditions.date_prescription = {
        [Op.between]: [new Date(dateDebut), new Date(dateFin)]
      };
    }
    
    const prescriptions = await Prescription.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: <PERSON><PERSON><PERSON>,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ],
      order: [['date_prescription', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        prescriptions: prescriptions.rows,
        pagination: {
          total: prescriptions.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(prescriptions.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des prescriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des prescriptions',
      error: error.message
    });
  }
};

// Créer une nouvelle prescription
const createPrescription = async (req, res) => {
  try {
    const {
      veterinaire_id,
      eleveur_id,
      volaille_id,
      diagnostic,
      symptomes,
      medicaments,
      posologie,
      duree_traitement,
      instructions_speciales,
      date_debut_traitement,
      urgence,
      suivi_requis,
      date_suivi,
      cout_estime
    } = req.body;
    
    // Validation des données requises
    if (!veterinaire_id || !eleveur_id || !diagnostic || !medicaments || !posologie || !duree_traitement) {
      return res.status(400).json({
        success: false,
        message: 'Les champs veterinaire_id, eleveur_id, diagnostic, medicaments, posologie et duree_traitement sont requis'
      });
    }
    
    const nouvellePrescription = await Prescription.create({
      veterinaire_id,
      eleveur_id,
      volaille_id,
      diagnostic,
      symptomes,
      medicaments,
      posologie,
      duree_traitement,
      instructions_speciales,
      date_debut_traitement,
      urgence: urgence || 'normale',
      suivi_requis: suivi_requis || false,
      date_suivi,
      cout_estime
    });
    
    // Récupérer la prescription créée avec les associations
    const prescriptionComplete = await Prescription.findByPk(nouvellePrescription.id, {
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: User,
          as: 'veterinaire',
          attributes: ['id', 'nom', 'email']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      message: 'Prescription créée avec succès',
      data: prescriptionComplete
    });
  } catch (error) {
    console.error('Erreur lors de la création de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la prescription',
      error: error.message
    });
  }
};

// Mettre à jour une prescription
const updatePrescription = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const [updatedRowsCount] = await Prescription.update(updateData, {
      where: { id }
    });
    
    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Prescription non trouvée'
      });
    }
    
    // Récupérer la prescription mise à jour
    const prescriptionMiseAJour = await Prescription.findByPk(id, {
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: User,
          as: 'veterinaire',
          attributes: ['id', 'nom', 'email']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ]
    });
    
    res.json({
      success: true,
      message: 'Prescription mise à jour avec succès',
      data: prescriptionMiseAJour
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la prescription',
      error: error.message
    });
  }
};

// Supprimer une prescription
const deletePrescription = async (req, res) => {
  try {
    const { id } = req.params;
    
    const deletedRowsCount = await Prescription.destroy({
      where: { id }
    });
    
    if (deletedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Prescription non trouvée'
      });
    }
    
    res.json({
      success: true,
      message: 'Prescription supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la prescription',
      error: error.message
    });
  }
};

// Obtenir les statistiques de prescription pour un vétérinaire
const getStatistiquesPrescriptions = async (req, res) => {
  try {
    const { veterinaireId } = req.params;
    
    const stats = await Prescription.getStatsByVeterinaire(veterinaireId);
    
    // Obtenir les prescriptions expirées
    const prescriptionsExpirees = await Prescription.getPrescriptionsExpirees(veterinaireId);
    
    // Obtenir les prescriptions en attente de suivi
    const prescriptionsEnAttenteSuivi = await Prescription.getPrescriptionsEnAttenteSuivi(veterinaireId);
    
    res.json({
      success: true,
      data: {
        ...stats,
        prescriptions_expirees: prescriptionsExpirees.length,
        prescriptions_suivi_requis: prescriptionsEnAttenteSuivi.length,
        details_expirees: prescriptionsExpirees.slice(0, 5), // Limiter à 5 pour l'aperçu
        details_suivi: prescriptionsEnAttenteSuivi.slice(0, 5)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
};

// Obtenir une prescription par ID
const getPrescriptionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const prescription = await Prescription.findByPk(id, {
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: User,
          as: 'veterinaire',
          attributes: ['id', 'nom', 'email']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ]
    });
    
    if (!prescription) {
      return res.status(404).json({
        success: false,
        message: 'Prescription non trouvée'
      });
    }
    
    res.json({
      success: true,
      data: prescription
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la prescription',
      error: error.message
    });
  }
};

// Obtenir les prescriptions d'un éleveur
const getPrescriptionsByEleveur = async (req, res) => {
  try {
    const { eleveurId } = req.params;
    const { page = 1, limit = 10, statut } = req.query;
    
    const offset = (page - 1) * limit;
    
    const whereConditions = {
      eleveur_id: eleveurId
    };
    
    if (statut) {
      whereConditions.statut = statut;
    }
    
    const prescriptions = await Prescription.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'veterinaire',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ],
      order: [['date_prescription', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        prescriptions: prescriptions.rows,
        pagination: {
          total: prescriptions.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(prescriptions.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des prescriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des prescriptions',
      error: error.message
    });
  }
};

// Marquer une prescription comme terminée
const terminerPrescription = async (req, res) => {
  try {
    const { id } = req.params;
    const { notes_suivi } = req.body;
    
    const [updatedRowsCount] = await Prescription.update({
      statut: 'terminee',
      notes_suivi
    }, {
      where: { id }
    });
    
    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Prescription non trouvée'
      });
    }
    
    const prescriptionMiseAJour = await Prescription.findByPk(id, {
      include: [
        {
          model: User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: User,
          as: 'veterinaire',
          attributes: ['id', 'nom', 'email']
        },
        {
          model: Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age']
        }
      ]
    });
    
    res.json({
      success: true,
      message: 'Prescription marquée comme terminée',
      data: prescriptionMiseAJour
    });
  } catch (error) {
    console.error('Erreur lors de la finalisation de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la finalisation de la prescription',
      error: error.message
    });
  }
};

module.exports = {
  getPrescriptionsByVeterinaire,
  createPrescription,
  updatePrescription,
  deletePrescription,
  getStatistiquesPrescriptions,
  getPrescriptionById,
  getPrescriptionsByEleveur,
  terminerPrescription
};