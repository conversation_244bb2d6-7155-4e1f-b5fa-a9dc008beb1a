/**
 * Configuration Axios pour Poultray DZ
 * Ce fichier configure une instance Axios avec des intercepteurs pour gérer l'authentification
 * et les erreurs de manière cohérente dans toute l'application.
 */

import axios from 'axios';

// URL de base de l'API
const API_URL = 'http://localhost:3003/api';

// Configuration des retries
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 seconde entre chaque retry

// Création d'une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30 secondes de timeout
  headers: {
    'Content-Type': 'application/json'
  }
});

// Fonction utilitaire pour attendre un délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Intercepteur pour les requêtes - ajoute le token à chaque requête
axiosInstance.interceptors.request.use(
  config => {
    // Ajouter le compteur de retries à la configuration
    config.retryCount = config.retryCount || 0;

    // TEMPORARY FIX FOR DEVELOPMENT: Always use admin token if available
    // This should be removed in production
    const token = localStorage.getItem('token') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.7y9iO9MN_mMSJEgSNFuOE-ZD0fOvcrXc9MsAXcjR8_o';

    if (token) {
      // Utiliser à la fois l'en-tête standard Authorization et x-auth-token
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['x-auth-token'] = token;
    }

    // Supprimer le double /api dans l'URL si présent
    if (config.url.startsWith('/api/api/')) {
      config.url = config.url.replace('/api/api/', '/api/');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses - gère les erreurs et les retries
axiosInstance.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;

    // Si l'erreur n'a pas de configuration ou a déjà atteint le nombre maximum de retries
    if (!config || config.retryCount >= MAX_RETRIES) {
      return Promise.reject(error);
    }

    // Incrémenter le compteur de retries
    config.retryCount += 1;

    // Attendre avant de réessayer
    await wait(RETRY_DELAY * config.retryCount);

    // Réessayer la requête
    return axiosInstance(config);
  }
);

export default axiosInstance;
