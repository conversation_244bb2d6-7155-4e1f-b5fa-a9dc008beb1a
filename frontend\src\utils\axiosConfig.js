/**
 * Configuration Axios pour Poultray DZ
 * Ce fichier configure une instance Axios avec des intercepteurs pour gérer l'authentification
 * et les erreurs de manière cohérente dans toute l'application.
 */

import axios from 'axios';
import errorHandler from '../services/errorHandler';

// URL de base de l'API
const API_URL = 'http://localhost:3003/api';

// Configuration des retries
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 seconde entre chaque retry

// Création d'une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30 secondes de timeout
  headers: {
    'Content-Type': 'application/json'
  }
});

// Fonction utilitaire pour attendre un délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Intercepteur pour les requêtes - ajoute le token à chaque requête
axiosInstance.interceptors.request.use(
  config => {
    // Ajouter le compteur de retries à la configuration
    config.retryCount = config.retryCount || 0;

    // Récupérer le token depuis localStorage
    const token = localStorage.getItem('token');

    if (token) {
      // Vérifier si le token n'est pas expiré avant de l'utiliser
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        if (tokenPayload.exp && tokenPayload.exp < currentTime) {
          console.warn('🔒 Token expiré détecté, nettoyage du localStorage');
          localStorage.removeItem('token');

          // Émettre un événement pour notifier l'expiration
          window.dispatchEvent(new CustomEvent('auth:token-expired', {
            detail: { reason: 'Token expiré côté client' }
          }));
        } else {
          // Token valide, l'ajouter aux en-têtes
          config.headers['Authorization'] = `Bearer ${token}`;
          config.headers['x-auth-token'] = token;
        }
      } catch (error) {
        console.warn('🔒 Token malformé détecté, nettoyage du localStorage');
        localStorage.removeItem('token');
      }
    }

    // Supprimer le double /api dans l'URL si présent
    if (config.url && config.url.startsWith('/api/api/')) {
      config.url = config.url.replace('/api/api/', '/api/');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses - gère les erreurs, les sessions expirées et les retries
axiosInstance.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;

    // Gestion des erreurs d'authentification (token expiré ou invalide)
    if (error.response?.status === 401) {
      const errorCode = error.response?.data?.code;

      // Si le token est expiré ou invalide, nettoyer le localStorage et rediriger
      if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'TOKEN_INVALID' || errorCode === 'TOKEN_MISSING') {
        console.warn('🔒 Session expirée ou token invalide, nettoyage et redirection...');

        // Nettoyer le localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('originalAdminToken');
        localStorage.removeItem('impersonationExpiresAt');

        // Émettre un événement personnalisé pour notifier l'application
        window.dispatchEvent(new CustomEvent('auth:session-expired', {
          detail: {
            reason: errorCode,
            message: error.response?.data?.message || 'Session expirée'
          }
        }));

        // Rediriger vers la page de connexion après un court délai
        setTimeout(() => {
          if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
            window.location.href = '/login?reason=session-expired';
          }
        }, 100);

        return Promise.reject(error);
      }
    }

    // Gestion des retries pour les autres erreurs (réseau, serveur temporairement indisponible)
    if (error.response?.status >= 500 || error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
      // Si l'erreur n'a pas de configuration ou a déjà atteint le nombre maximum de retries
      if (!config || config.retryCount >= MAX_RETRIES) {
        // Traiter l'erreur avec notre service de gestion d'erreurs
        const standardError = errorHandler.handleAxiosError(error);
        return Promise.reject(standardError);
      }

      // Incrémenter le compteur de retries
      config.retryCount += 1;

      console.log(`🔄 Tentative ${config.retryCount}/${MAX_RETRIES} pour ${config.url}`);

      // Attendre avant de réessayer
      await wait(RETRY_DELAY * config.retryCount);

      // Réessayer la requête
      return axiosInstance(config);
    }

    // Pour toutes les autres erreurs, les traiter avec notre service de gestion d'erreurs
    const standardError = errorHandler.handleAxiosError(error);
    return Promise.reject(standardError);
  }
);

export default axiosInstance;
