const nodemailer = require('nodemailer');
const { Pool } = require('pg');
const { admin, isInitialized: isFirebaseInitialized } = require('../config/firebase');

// Initialize PostgreSQL connection
// Utiliser une chaîne de connexion au lieu des paramètres individuels
const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`;
const pool = new Pool({
  connectionString,
});

// Create email transporter if credentials are available
let emailTransporterInitialized = false;
let transporter;

if (process.env.EMAIL_SERVICE && process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
  transporter = nodemailer.createTransport({
    service: process.env.EMAIL_SERVICE,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  });
  emailTransporterInitialized = true;
  console.log('Email transporter initialized successfully');
} else {
  console.log('Email credentials not provided, email notifications will be disabled');
}

/**
 * Create a notification in the database
 * @param {Object} notification - Notification data
 * @returns {Promise<Object>} - Created notification
 */
async function createNotification(notification) {
  const { user_id, title, message, type, data, is_read = false } = notification;

  const query = {
    text: `
      INSERT INTO notifications (user_id, title, message, type, data, is_read)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `,
    values: [user_id, title, message, type, data || {}, is_read],
  };

  try {
    const result = await pool.query(query);
    return result.rows[0];
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Send an email notification
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.text - Plain text content
 * @param {string} options.html - HTML content
 * @returns {Promise<Object>} - Nodemailer info object
 */
async function sendEmail(options) {
  const { to, subject, text, html } = options;

  if (!emailTransporterInitialized) {
    console.warn('Email transporter not initialized, skipping email');
    return { success: false, reason: 'email_not_initialized' };
  }

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to,
    subject,
    text,
    html: html || text,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Send a push notification via Firebase Cloud Messaging
 * @param {Object} options - Push notification options
 * @param {string} options.token - FCM token
 * @param {string} options.title - Notification title
 * @param {string} options.body - Notification body
 * @param {Object} options.data - Additional data
 * @returns {Promise<Object>} - FCM response
 */
async function sendPushNotification(options) {
  const { token, title, body, data } = options;

  if (!firebaseInitialized) {
    console.warn('Firebase not initialized, skipping push notification');
    return { success: false, reason: 'firebase_not_initialized' };
  }

  if (!token) {
    throw new Error('FCM token is required');
  }

  const message = {
    notification: {
      title,
      body,
    },
    data: data || {},
    token,
  };

  try {
    const response = await admin.messaging().send(message);
    console.log('Push notification sent:', response);
    return response;
  } catch (error) {
    console.error('Error sending push notification:', error);
    throw error;
  }
}

/**
 * Send a notification to a user via multiple channels
 * @param {Object} options - Notification options
 * @param {number} options.userId - User ID
 * @param {string} options.title - Notification title
 * @param {string} options.message - Notification message
 * @param {string} options.type - Notification type
 * @param {Object} options.data - Additional data
 * @param {boolean} options.email - Whether to send email
 * @param {boolean} options.push - Whether to send push notification
 * @returns {Promise<Object>} - Notification results
 */
async function notifyUser(options) {
  const { userId, title, message, type, data, email = false, push = false } = options;

  try {
    // Create notification in database
    const notification = await createNotification({
      user_id: userId,
      title,
      message,
      type,
      data,
    });

    const results = { notification };

    // Get user details
    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [userId]);
    const user = userResult.rows[0];

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Send email if requested
    if (email && user.email) {
      const emailResult = await sendEmail({
        to: user.email,
        subject: title,
        text: message,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2E7D32;">${title}</h2>
            <p>${message}</p>
            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
              <p>Ceci est un message automatique de Poultray DZ. Veuillez ne pas répondre à cet email.</p>
            </div>
          </div>
        `,
      });

      results.email = emailResult;
    }

    // Send push notification if requested
    if (push && user.fcm_token) {
      const pushResult = await sendPushNotification({
        token: user.fcm_token,
        title,
        body: message,
        data: {
          type,
          ...data,
        },
      });

      results.push = pushResult;
    }

    return results;
  } catch (error) {
    console.error('Error notifying user:', error);
    throw error;
  }
}

/**
 * Get notifications for a user
 * @param {number} userId - User ID
 * @param {Object} options - Query options
 * @param {number} options.limit - Maximum number of notifications to return
 * @param {number} options.offset - Offset for pagination
 * @param {boolean} options.unreadOnly - Whether to return only unread notifications
 * @returns {Promise<Array>} - Notifications
 */
async function getUserNotifications(userId, options = {}) {
  const { limit = 20, offset = 0, unreadOnly = false } = options;

  let query = 'SELECT * FROM notifications WHERE user_id = $1';
  const values = [userId];

  if (unreadOnly) {
    query += ' AND is_read = false';
  }

  query += ' ORDER BY created_at DESC LIMIT $2 OFFSET $3';
  values.push(limit, offset);

  try {
    const result = await pool.query(query, values);
    return result.rows;
  } catch (error) {
    console.error('Error getting user notifications:', error);
    throw error;
  }
}

/**
 * Mark notifications as read
 * @param {number} userId - User ID
 * @param {Array<number>} notificationIds - Notification IDs to mark as read
 * @returns {Promise<number>} - Number of notifications updated
 */
async function markNotificationsAsRead(userId, notificationIds = []) {
  let query = 'UPDATE notifications SET is_read = true, updated_at = NOW() WHERE user_id = $1';
  const values = [userId];

  if (notificationIds.length > 0) {
    query += ' AND id = ANY($2)';
    values.push(notificationIds);
  }

  try {
    const result = await pool.query(query, values);
    return result.rowCount;
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    throw error;
  }
}

/**
 * Delete notifications
 * @param {number} userId - User ID
 * @param {Array<number>} notificationIds - Notification IDs to delete
 * @returns {Promise<number>} - Number of notifications deleted
 */
async function deleteNotifications(userId, notificationIds = []) {
  let query = 'DELETE FROM notifications WHERE user_id = $1';
  const values = [userId];

  if (notificationIds.length > 0) {
    query += ' AND id = ANY($2)';
    values.push(notificationIds);
  }

  try {
    const result = await pool.query(query, values);
    return result.rowCount;
  } catch (error) {
    console.error('Error deleting notifications:', error);
    throw error;
  }
}

/**
 * Create the notifications table if it doesn't exist
 */
async function createNotificationsTable() {
  // First check if users table exists
  try {
    const checkUsersTable = await pool.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users')"
    );

    if (!checkUsersTable.rows[0].exists) {
      console.log('Users table does not exist yet, skipping notifications table creation');
      return;
    }

    // Create notifications table
    const query = `
      CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) NOT NULL,
        data JSONB DEFAULT '{}'::jsonb,
        is_read BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await pool.query(query);
    console.log('Notifications table created successfully');
  } catch (error) {
    console.error('Error creating notifications table:', error);
    // Don't throw the error, just log it
    console.log('Will try to create notifications table again later');
  }
}

module.exports = {
  createNotification,
  sendEmail,
  sendPushNotification,
  notifyUser,
  getUserNotifications,
  markNotificationsAsRead,
  deleteNotifications,
  createNotificationsTable,
};
