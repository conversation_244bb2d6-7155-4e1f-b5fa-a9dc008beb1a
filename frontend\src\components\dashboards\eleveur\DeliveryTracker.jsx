import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepContent,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Tooltip,
  Tab,
  Tabs
} from '@mui/material';
import { 
  LocalShipping, 
  Inventory, 
  CheckCircle, 
  Schedule, 
  LocationOn,
  Phone,
  Message,
  MoreVert,
  ArrowForward,
  ArrowBack
} from '@mui/icons-material';

/**
 * Composant de suivi des livraisons
 * Affiche l'état des livraisons en cours et à venir
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {Array} props.deliveries - Liste des livraisons à afficher
 * @returns {JSX.Element} - Le composant de suivi des livraisons
 */
const DeliveryTracker = ({ deliveries = [] }) => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  
  // Données simulées pour les livraisons
  const mockDeliveries = [
    {
      id: 'DEL-001',
      type: 'incoming', // incoming ou outgoing
      status: 'in_transit', // pending, in_transit, delivered, cancelled
      product: 'Aliments pour volailles',
      quantity: '500 kg',
      supplier: 'Nutri-Volaille SARL',
      expectedDate: '15/12/2023',
      currentLocation: 'Blida',
      trackingSteps: [
        { label: 'Commande confirmée', date: '10/12/2023 09:15', completed: true },
        { label: 'En préparation', date: '11/12/2023 14:30', completed: true },
        { label: 'Expédié', date: '12/12/2023 08:45', completed: true },
        { label: 'En transit', date: '13/12/2023 10:20', completed: true, current: true },
        { label: 'Livré', date: '', completed: false }
      ],
      contact: {
        name: 'Ahmed Kader',
        phone: '0550123456',
        email: '<EMAIL>'
      }
    },
    {
      id: 'DEL-002',
      type: 'outgoing',
      status: 'pending',
      product: 'Poulets de chair',
      quantity: '200 unités',
      customer: 'Marché Central Alger',
      expectedDate: '18/12/2023',
      trackingSteps: [
        { label: 'Commande reçue', date: '12/12/2023 11:30', completed: true },
        { label: 'En préparation', date: '14/12/2023 09:00', completed: true, current: true },
        { label: 'Prêt pour expédition', date: '', completed: false },
        { label: 'En livraison', date: '', completed: false },
        { label: 'Livré', date: '', completed: false }
      ],
      contact: {
        name: 'Karim Benali',
        phone: '0660789123',
        email: '<EMAIL>'
      }
    },
    {
      id: 'DEL-003',
      type: 'incoming',
      status: 'delivered',
      product: 'Vaccins Newcastle',
      quantity: '1000 doses',
      supplier: 'VetoPharma',
      expectedDate: '05/12/2023',
      deliveredDate: '05/12/2023',
      trackingSteps: [
        { label: 'Commande confirmée', date: '01/12/2023 10:00', completed: true },
        { label: 'En préparation', date: '02/12/2023 14:00', completed: true },
        { label: 'Expédié', date: '03/12/2023 09:30', completed: true },
        { label: 'En transit', date: '04/12/2023 11:45', completed: true },
        { label: 'Livré', date: '05/12/2023 14:20', completed: true, current: true }
      ],
      contact: {
        name: 'Dr. Samira Hadj',
        phone: '0770456789',
        email: '<EMAIL>'
      }
    }
  ];
  
  // Utiliser les livraisons fournies ou les livraisons simulées
  const allDeliveries = deliveries.length > 0 ? deliveries : mockDeliveries;
  
  // Filtrer les livraisons en fonction de l'onglet sélectionné
  const getFilteredDeliveries = () => {
    switch (tabValue) {
      case 0: // Toutes
        return allDeliveries;
      case 1: // Entrantes
        return allDeliveries.filter(delivery => delivery.type === 'incoming');
      case 2: // Sortantes
        return allDeliveries.filter(delivery => delivery.type === 'outgoing');
      default:
        return allDeliveries;
    }
  };
  
  // Obtenir la couleur en fonction du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#F59E0B'; // orange
      case 'in_transit':
        return '#3B82F6'; // bleu
      case 'delivered':
        return '#10B981'; // vert
      case 'cancelled':
        return '#EF4444'; // rouge
      default:
        return '#6B7280'; // gris
    }
  };
  
  // Obtenir le libellé en fonction du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'in_transit':
        return 'En transit';
      case 'delivered':
        return 'Livré';
      case 'cancelled':
        return 'Annulé';
      default:
        return 'Inconnu';
    }
  };
  
  // Gérer le changement d'onglet
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setSelectedDelivery(null);
  };
  
  // Gérer la sélection d'une livraison
  const handleDeliverySelect = (delivery) => {
    setSelectedDelivery(delivery);
  };
  
  // Gérer le retour à la liste
  const handleBackToList = () => {
    setSelectedDelivery(null);
  };
  
  // Rendu de la liste des livraisons
  const renderDeliveryList = () => {
    const filteredDeliveries = getFilteredDeliveries();
    
    if (filteredDeliveries.length === 0) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Aucune livraison à afficher
          </Typography>
        </Box>
      );
    }
    
    return (
      <List disablePadding>
        {filteredDeliveries.map((delivery) => (
          <ListItem 
            key={delivery.id}
            button
            onClick={() => handleDeliverySelect(delivery)}
            sx={{ 
              borderLeft: `4px solid ${getStatusColor(delivery.status)}`,
              '&:not(:last-child)': {
                borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
              }
            }}
          >
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: delivery.type === 'incoming' ? 'primary.light' : 'secondary.light' }}>
                {delivery.type === 'incoming' ? <Inventory /> : <LocalShipping />}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="subtitle2" noWrap sx={{ maxWidth: '150px' }}>
                    {delivery.product}
                  </Typography>
                  <Chip 
                    label={getStatusLabel(delivery.status)} 
                    size="small"
                    sx={{ 
                      bgcolor: `${getStatusColor(delivery.status)}20`,
                      color: getStatusColor(delivery.status),
                      fontWeight: 'bold',
                      fontSize: '0.7rem'
                    }}
                  />
                </Box>
              }
              secondary={
                <>
                  <Typography variant="caption" display="block" noWrap>
                    {delivery.type === 'incoming' ? `De: ${delivery.supplier}` : `À: ${delivery.customer}`}
                  </Typography>
                  <Typography variant="caption" display="block">
                    {delivery.status === 'delivered' 
                      ? `Livré le: ${delivery.deliveredDate}` 
                      : `Prévu le: ${delivery.expectedDate}`}
                  </Typography>
                </>
              }
            />
          </ListItem>
        ))}
      </List>
    );
  };
  
  // Rendu du détail d'une livraison
  const renderDeliveryDetail = () => {
    if (!selectedDelivery) return null;
    
    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Button 
            startIcon={<ArrowBack />} 
            onClick={handleBackToList}
            size="small"
            sx={{ mr: 2 }}
          >
            Retour
          </Button>
          <Typography variant="subtitle1" fontWeight="bold">
            Détails de la livraison
          </Typography>
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {selectedDelivery.product}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {selectedDelivery.type === 'incoming' ? 'Livraison entrante' : 'Livraison sortante'}
            </Typography>
            <Chip 
              label={getStatusLabel(selectedDelivery.status)} 
              size="small"
              sx={{ 
                bgcolor: `${getStatusColor(selectedDelivery.status)}20`,
                color: getStatusColor(selectedDelivery.status),
                fontWeight: 'bold'
              }}
            />
          </Box>
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Informations
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">ID:</Typography>
            <Typography variant="body2">{selectedDelivery.id}</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">Quantité:</Typography>
            <Typography variant="body2">{selectedDelivery.quantity}</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {selectedDelivery.type === 'incoming' ? 'Fournisseur:' : 'Client:'}
            </Typography>
            <Typography variant="body2">
              {selectedDelivery.type === 'incoming' ? selectedDelivery.supplier : selectedDelivery.customer}
            </Typography>
          </Box>
          {selectedDelivery.currentLocation && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">Position actuelle:</Typography>
              <Typography variant="body2">{selectedDelivery.currentLocation}</Typography>
            </Box>
          )}
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Suivi de la livraison
          </Typography>
          <Stepper orientation="vertical" sx={{ mt: 2 }}>
            {selectedDelivery.trackingSteps.map((step, index) => (
              <Step key={index} active={step.completed} completed={step.completed}>
                <StepLabel StepIconProps={{ 
                  sx: { 
                    color: step.current ? getStatusColor(selectedDelivery.status) : undefined 
                  } 
                }}>
                  {step.label}
                </StepLabel>
                <StepContent>
                  <Typography variant="caption">
                    {step.date || 'À venir'}
                  </Typography>
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Contact
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.75rem' }}>
              {selectedDelivery.contact.name.charAt(0)}
            </Avatar>
            <Typography variant="body2">{selectedDelivery.contact.name}</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button size="small" startIcon={<Phone />} variant="outlined">
              Appeler
            </Button>
            <Button size="small" startIcon={<Message />} variant="outlined">
              Message
            </Button>
            <Button size="small" startIcon={<LocationOn />} variant="outlined">
              Carte
            </Button>
          </Box>
        </Box>
      </Box>
    );
  };
  
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader 
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocalShipping sx={{ mr: 1 }} />
            <Typography variant="h6">Suivi des Livraisons</Typography>
          </Box>
        }
      />
      <Divider />
      <CardContent sx={{ p: 0 }}>
        {!selectedDelivery && (
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Toutes" />
            <Tab label="Entrantes" />
            <Tab label="Sortantes" />
          </Tabs>
        )}
        
        {selectedDelivery ? renderDeliveryDetail() : renderDeliveryList()}
      </CardContent>
    </Card>
  );
};

export default DeliveryTracker;
