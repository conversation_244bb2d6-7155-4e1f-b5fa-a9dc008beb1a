const express = require('express');
const router = express.Router();
const { generateBlogPost, analyzeData, generatePageContent } = require('../services/openai');
const { auth, checkRole } = require('../middleware/auth');

// @route   POST /api/ai/blog
// @desc    Generate a blog post
// @access  Private/Admin
router.post('/blog', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { title, topic, keywords, language, maxLength } = req.body;
    
    if (!title || !topic) {
      return res.status(400).json({ message: 'Le titre et le sujet sont requis pour la génération de blog.' });
    }
    if (maxLength && (isNaN(maxLength) || maxLength <= 0)) {
      return res.status(400).json({ message: 'La longueur maximale doit être un nombre positif.' });
    }
    if (language && !['fr', 'en', 'ar'].includes(language)) {
      return res.status(400).json({ message: 'La langue spécifiée n\'est pas supportée (fr, en, ar).' });
    }
    
    const blogContent = await generateBlogPost({
      title,
      topic,
      keywords: keywords || '',
      language: language || 'fr',
      maxLength: maxLength || 500
    });
    
    res.json({ content: blogContent });
  } catch (error) {
    console.error('Error in /api/ai/blog:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   POST /api/ai/analyze
// @desc    Analyze platform data
// @access  Private/Admin
router.post('/analyze', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { data, language } = req.body;
    
    if (!data || (typeof data !== 'string' && typeof data !== 'object')) {
      return res.status(400).json({ message: 'Les données à analyser sont requises et doivent être une chaîne ou un objet.' });
    }
    if (language && !['fr', 'en', 'ar'].includes(language)) {
      return res.status(400).json({ message: 'La langue spécifiée n\'est pas supportée (fr, en, ar).' });
    }
    
    const analysis = await analyzeData(data, language || 'fr');
    
    res.json(analysis);
  } catch (error) {
    console.error('Error in /api/ai/analyze:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   POST /api/ai/page-content
// @desc    Generate static page content
// @access  Private/Admin
router.post('/page-content', auth, checkRole(['admin']), async (req, res) => {
  try {
    const { pageType, keywords, language } = req.body;
    
    if (!pageType) {
      return res.status(400).json({ message: 'Le type de page est requis' });
    }
    
    const pageContent = await generatePageContent({
      pageType,
      keywords: keywords || '',
      language: language || 'fr'
    });
    
    res.json({ content: pageContent });
  } catch (error) {
    console.error('Error in /api/ai/page-content:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
