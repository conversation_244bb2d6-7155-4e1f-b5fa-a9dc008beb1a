import React from 'react';
import { render, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { PoussinManagement } from '../index';

describe('PoussinManagement Component - Simple Tests', () => {
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };

  test('renders loading state initially', () => {
    renderWithI18n(<PoussinManagement />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});