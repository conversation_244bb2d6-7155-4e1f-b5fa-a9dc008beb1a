import { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuthToken } from './useAuthToken';

/**
 * Hook personnalisé pour récupérer et gérer les données de l'éleveur
 * @param {number} eleveurId - ID de l'éleveur (optionnel, utilise l'ID de l'utilisateur connecté par défaut)
 * @returns {Object} Données et fonctions pour gérer les données de l'éleveur
 */
export const useEleveurData = (eleveurId = null) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token, isAuthenticated } = useAuthToken();

  // Récupérer les données de l'éleveur
  const fetchEleveurData = async () => {
    if (!isAuthenticated) {
      setError(new Error('Non authentifié'));
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const endpoint = eleveurId 
        ? `/api/eleveurs/${eleveurId}` 
        : '/api/eleveurs/profile';
      
      const response = await axios.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setData(response.data);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des données de l\'éleveur:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour les données de l'éleveur
  const updateEleveurData = async (updatedData) => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const endpoint = eleveurId 
        ? `/api/eleveurs/${eleveurId}` 
        : '/api/eleveurs/profile';
      
      const response = await axios.put(endpoint, updatedData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setData(response.data);
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la mise à jour des données de l\'éleveur:', err);
      throw err;
    }
  };

  // Récupérer les statistiques de l'éleveur
  const fetchEleveurStats = async () => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const endpoint = eleveurId 
        ? `/api/eleveurs/${eleveurId}/stats` 
        : '/api/eleveurs/stats';
      
      const response = await axios.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques de l\'éleveur:', err);
      throw err;
    }
  };

  // Charger les données au montage du composant
  useEffect(() => {
    fetchEleveurData();
  }, [eleveurId, isAuthenticated]);

  return {
    data,
    loading,
    error,
    fetchEleveurData,
    updateEleveurData,
    fetchEleveurStats
  };
};

export default useEleveurData;

