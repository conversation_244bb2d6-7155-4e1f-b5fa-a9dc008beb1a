import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuthToken } from './useAuthToken';
import { useDashboardSync } from './useDataSync';

/**
 * Hook personnalisé pour récupérer et gérer les données de l'éleveur
 * @param {number} eleveurId - ID de l'éleveur (optionnel, utilise l'ID de l'utilisateur connecté par défaut)
 * @returns {Object} Données et fonctions pour gérer les données de l'éleveur
 */
export const useEleveurData = (eleveurId = null, options = {}) => {
  const { enableSync = true, syncInterval = 60000 } = options;
  const [manualData, setManualData] = useState(null);
  const [manualLoading, setManualLoading] = useState(!enableSync);
  const [manualError, setManualError] = useState(null);
  const { token, isAuthenticated } = useAuthToken();

  // Fonction de récupération des données pour la synchronisation
  const fetchEleveurData = useCallback(async () => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    const endpoint = eleveurId
      ? `/api/eleveurs/${eleveurId}`
      : '/api/eleveurs/profile';

    const response = await axios.get(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  }, [eleveurId, isAuthenticated, token]);

  // Utiliser la synchronisation automatique si activée
  const {
    data: syncData,
    loading: syncLoading,
    error: syncError,
    lastSync,
    isOnline,
    pendingUpdates,
    forceSync
  } = useDashboardSync(
    fetchEleveurData,
    {
      interval: syncInterval,
      enabled: enableSync && isAuthenticated,
      immediate: true
    }
  );

  // Fonction manuelle de récupération (pour compatibilité)
  const fetchManualData = async () => {
    if (!enableSync) {
      setManualLoading(true);
      try {
        const data = await fetchEleveurData();
        setManualData(data);
        setManualError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des données de l\'éleveur:', err);
        setManualError(err);
      } finally {
        setManualLoading(false);
      }
    }
  };

  // Mettre à jour les données de l'éleveur
  const updateEleveurData = async (updatedData) => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const endpoint = eleveurId
        ? `/api/eleveurs/${eleveurId}`
        : '/api/eleveurs/profile';

      const response = await axios.put(endpoint, updatedData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      setData(response.data);
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la mise à jour des données de l\'éleveur:', err);
      throw err;
    }
  };

  // Récupérer les statistiques de l'éleveur
  const fetchEleveurStats = async () => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const endpoint = eleveurId
        ? `/api/eleveurs/${eleveurId}/stats`
        : '/api/eleveurs/stats';

      const response = await axios.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data;
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques de l\'éleveur:', err);
      throw err;
    }
  };

  // Charger les données manuellement si la synchronisation est désactivée
  useEffect(() => {
    if (!enableSync && isAuthenticated) {
      fetchManualData();
    }
  }, [eleveurId, isAuthenticated, enableSync]);

  // Déterminer quelles données utiliser
  const data = enableSync ? syncData : manualData;
  const loading = enableSync ? syncLoading : manualLoading;
  const error = enableSync ? syncError : manualError;

  return {
    data,
    loading,
    error,
    lastSync: enableSync ? lastSync : null,
    isOnline: enableSync ? isOnline : navigator.onLine,
    pendingUpdates: enableSync ? pendingUpdates : 0,
    fetchEleveurData: enableSync ? forceSync : fetchManualData,
    updateEleveurData,
    fetchEleveurStats,
    // Nouvelles propriétés pour la synchronisation
    syncEnabled: enableSync,
    forceSync: enableSync ? forceSync : null
  };
};

export default useEleveurData;
