const axios = require('axios');
const colors = require('colors');

const API_URL = 'http://localhost:3003/api';

async function testUserUpdate() {
  console.log('Test de mise à jour d\'un utilisateur...'.yellow);

  try {
    // 1. Connexion en tant qu'admin
    console.log('Connexion en tant qu\'admin...'.blue);
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Password1!'
    });

    if (loginResponse.data.token) {
      console.log('✅ Connexion réussie!'.green);
      const token = loginResponse.data.token;

      // 2. Récupérer la liste des utilisateurs pour choisir un utilisateur à mettre à jour
      console.log('Récupération de la liste des utilisateurs...'.blue);
      const usersResponse = await axios.get(`${API_URL}/admin/users`, {
        headers: {
          'x-auth-token': token
        }
      });

      if (usersResponse.data && usersResponse.data.users && usersResponse.data.users.length > 0) {
        console.log('✅ Liste des utilisateurs récupérée!'.green);

        // Choisir un utilisateur qui n'est pas l'admin
        const updateUser = usersResponse.data.users.find(user => user.username !== 'admin');

        if (!updateUser) {
          console.log('❌ Aucun utilisateur non-admin trouvé pour le test'.red);
          return;
        }

        console.log(`Utilisateur sélectionné pour la mise à jour: ${updateUser.username} (ID: ${updateUser.id})`.cyan);

        // 3. Mettre à jour l'utilisateur
        console.log('Mise à jour de l\'utilisateur...'.blue);

        const updatedData = {
          username: updateUser.username,
          email: updateUser.email,
          first_name: updateUser.first_name || 'Prénom Modifié',
          last_name: updateUser.last_name || 'Nom Modifié',
          role: updateUser.role,
          status: updateUser.status,
          password: 'NouveauPassword123!' // Nouveau mot de passe
        };

        const updateResponse = await axios.put(
          `${API_URL}/admin/users/${updateUser.id}`,
          updatedData,
          {
            headers: {
              'x-auth-token': token
            }
          }
        );

        if (updateResponse.data) {
          console.log('✅ Utilisateur mis à jour avec succès!'.green);
          console.log('Réponse:'.cyan);
          console.log(JSON.stringify(updateResponse.data, null, 2));
        }
      } else {
        console.log('❌ Erreur lors de la récupération des utilisateurs'.red);
      }
    } else {
      console.log('❌ Échec de la connexion!'.red);
    }
  } catch (error) {
    console.log('❌ Erreur:'.red);
    if (error.response) {
      console.log(`Statut: ${error.response.status}`.red);
      console.log('Données:'.red);
      console.log(JSON.stringify(error.response.data, null, 2));
    } else {
      console.log(error.message.red);
    }
  }
}

testUserUpdate();
