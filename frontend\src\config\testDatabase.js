/**
 * Configuration de la base de données PostgreSQL pour les tests
 * Ce fichier contient les paramètres de connexion à la base de données de test
 */

// Configuration de la connexion à la base de données de test
const TEST_DB_CONFIG = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: process.env.TEST_DB_PORT || 5432,
  database: process.env.TEST_DB_NAME || 'poultraydz_db',
  user: process.env.TEST_DB_USER || 'poultraydz_user',
  password: process.env.TEST_DB_PASSWORD || 'poultraydz_password',
  ssl: false
};

// Exporter la configuration pour les tests
export default TEST_DB_CONFIG;

// Exporter les constantes pour les noms de tables
export const TABLES = {
  USERS: 'users',
  ELEVEURS: 'eleveurs',
  VETERINAIRES: 'veterinaires',
  MARCHANDS: 'marchands',
  VOLAILLES: 'volailles',
  VENTES: 'ventes',
  PRESCRIPTIONS: 'prescriptions',
  CONSULTATIONS: 'consultations',
  PRODUITS: 'produits',
  COMMANDES: 'commandes',
  BLOG_POSTS: 'blog_posts',
  TRANSLATIONS: 'translations',
  NOTIFICATIONS: 'notifications',
  SETTINGS: 'settings',
  POUSSINS: 'poussins',
  PRODUCTION_OEUFS: 'production_oeufs',
  ALERTES_STOCK: 'alertes_stock'
};

// Exporter les constantes pour les relations entre tables
export const RELATIONS = {
  USER_ELEVEUR: 'user_id',
  USER_VETERINAIRE: 'user_id',
  USER_MARCHAND: 'user_id',
  ELEVEUR_VOLAILLE: 'eleveur_id',
  VETERINAIRE_PRESCRIPTION: 'veterinaire_id',
  MARCHAND_PRODUIT: 'marchand_id'
};

