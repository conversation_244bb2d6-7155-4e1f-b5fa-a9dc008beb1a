import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Fab,
  Tooltip,
  Pagination,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as CartIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import axios from 'axios';

const VentesEleveur = () => {
  const [ventes, setVentes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedVente, setSelectedVente] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState({
    acheteur_id: '',
    volaille_id: '',
    quantite: '',
    prix_unitaire: '',
    notes: ''
  });
  const [statistiques, setStatistiques] = useState({
    total_ventes: 0,
    ventes_confirmees: 0,
    chiffre_affaires: 0,
    repartition_statut: []
  });

  // Récupérer l'ID de l'éleveur depuis le contexte d'authentification
  const eleveurId = localStorage.getItem('userId'); // À adapter selon votre système d'auth

  useEffect(() => {
    fetchVentes();
    fetchStatistiques();
  }, [page]);

  const fetchVentes = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`/api/ventes/eleveur/${eleveurId}?page=${page}&limit=10`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setVentes(response.data.data.ventes);
      setTotalPages(response.data.data.pagination.totalPages);
    } catch (err) {
      setError('Erreur lors du chargement des ventes');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistiques = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`/api/ventes/eleveur/${eleveurId}/statistiques`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setStatistiques(response.data.data);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  const handleOpenDialog = (vente = null) => {
    if (vente) {
      setSelectedVente(vente);
      setFormData({
        client_nom: vente.client_nom,
        client_email: vente.client_email,
        client_telephone: vente.client_telephone,
        produit_type: vente.produit_type,
        quantite: vente.quantite,
        prix_unitaire: vente.prix_unitaire,
        date_vente: new Date(vente.date_vente),
        statut: vente.statut,
        notes: vente.notes || ''
      });
    } else {
      setSelectedVente(null);
      setFormData({
        client_nom: '',
        client_email: '',
        client_telephone: '',
        produit_type: '',
        quantite: '',
        prix_unitaire: '',
        date_vente: new Date(),
        statut: 'en_attente',
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedVente(null);
  };

  const handleSubmit = async () => {
    try {
      const venteData = {
        ...formData,
        quantite: parseInt(formData.quantite),
        prix_unitaire: parseFloat(formData.prix_unitaire)
      };

      if (selectedVente) {
        await axiosInstance.put(`/api/eleveur/ventes/${selectedVente.id}`, venteData);
      } else {
        await axiosInstance.post('/api/eleveur/ventes', venteData);
      }

      fetchVentes();
      fetchStats();
      handleCloseDialog();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la vente');
      console.error('Erreur:', err);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette vente ?')) {
      try {
        await axiosInstance.delete(`/api/eleveur/ventes/${id}`);
        fetchVentes();
        fetchStats();
      } catch (err) {
        setError('Erreur lors de la suppression de la vente');
        console.error('Erreur:', err);
      }
    }
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'confirmee': return 'success';
      case 'en_attente': return 'warning';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD'
    }).format(amount);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Mes Ventes
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CartIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Ventes
                    </Typography>
                    <Typography variant="h5">
                      {stats.total_ventes}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Revenus Total
                    </Typography>
                    <Typography variant="h5">
                      {formatCurrency(stats.revenus_total)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Ventes ce mois
                    </Typography>
                    <Typography variant="h5">
                      {stats.ventes_mois}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Revenus ce mois
                    </Typography>
                    <Typography variant="h5">
                      {formatCurrency(stats.revenus_mois)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Actions */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Vente
          </Button>
        </Box>

        {/* Table des ventes */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Client</TableCell>
                <TableCell>Produit</TableCell>
                <TableCell>Quantité</TableCell>
                <TableCell>Prix Unitaire</TableCell>
                <TableCell>Total</TableCell>
                <TableCell>Statut</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {ventes.map((vente) => (
                <TableRow key={vente.id}>
                  <TableCell>
                    {new Date(vente.date_vente).toLocaleDateString('fr-FR')}
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {vente.client_nom}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {vente.client_email}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{vente.produit_type}</TableCell>
                  <TableCell>{vente.quantite}</TableCell>
                  <TableCell>{formatCurrency(vente.prix_unitaire)}</TableCell>
                  <TableCell>
                    <Typography fontWeight="bold">
                      {formatCurrency(vente.quantite * vente.prix_unitaire)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={vente.statut.replace('_', ' ')}
                      color={getStatutColor(vente.statut)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(vente)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDelete(vente.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Dialog pour ajouter/modifier une vente */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {selectedVente ? 'Modifier la vente' : 'Nouvelle vente'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nom du client"
                  value={formData.client_nom}
                  onChange={(e) => setFormData({ ...formData, client_nom: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email du client"
                  type="email"
                  value={formData.client_email}
                  onChange={(e) => setFormData({ ...formData, client_email: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Téléphone du client"
                  value={formData.client_telephone}
                  onChange={(e) => setFormData({ ...formData, client_telephone: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Type de produit</InputLabel>
                  <Select
                    value={formData.produit_type}
                    onChange={(e) => setFormData({ ...formData, produit_type: e.target.value })}
                  >
                    <MenuItem value="oeufs">Œufs</MenuItem>
                    <MenuItem value="poulets">Poulets</MenuItem>
                    <MenuItem value="poules">Poules</MenuItem>
                    <MenuItem value="coqs">Coqs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Quantité"
                  type="number"
                  value={formData.quantite}
                  onChange={(e) => setFormData({ ...formData, quantite: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Prix unitaire (DZD)"
                  type="number"
                  value={formData.prix_unitaire}
                  onChange={(e) => setFormData({ ...formData, prix_unitaire: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Date de vente"
                  value={formData.date_vente}
                  onChange={(date) => setFormData({ ...formData, date_vente: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={formData.statut}
                    onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                  >
                    <MenuItem value="en_attente">En attente</MenuItem>
                    <MenuItem value="confirmee">Confirmée</MenuItem>
                    <MenuItem value="annulee">Annulée</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedVente ? 'Modifier' : 'Ajouter'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default VentesEleveur;