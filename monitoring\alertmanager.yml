# Configuration AlertManager pour Poultray DZ
global:
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '${SMTP_USER}'
  smtp_auth_password: '${SMTP_PASS}'
  smtp_require_tls: true
  
  # Configuration Slack
  slack_api_url: '${SLACK_WEBHOOK_URL}'
  
  # Configuration générale
  resolve_timeout: 5m

# Templates pour les notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Configuration du routage des alertes
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  
  routes:
  # Alertes critiques - notification immédiate
  - match:
      severity: critical
    receiver: 'critical-alerts'
    group_wait: 0s
    repeat_interval: 5m
    
  # Alertes de sécurité - notification immédiate
  - match:
      service: security
    receiver: 'security-alerts'
    group_wait: 0s
    repeat_interval: 15m
    
  # Alertes infrastructure
  - match:
      service: infrastructure
    receiver: 'infrastructure-alerts'
    group_interval: 5m
    repeat_interval: 30m
    
  # Alertes base de données
  - match:
      service: database
    receiver: 'database-alerts'
    group_interval: 2m
    repeat_interval: 15m
    
  # Alertes application
  - match:
      service: backend
    receiver: 'backend-alerts'
    group_interval: 5m
    repeat_interval: 30m
    
  # Alertes métier - moins urgentes
  - match:
      service: business
    receiver: 'business-alerts'
    group_interval: 30m
    repeat_interval: 4h
    
  # Alertes de maintenance
  - match:
      severity: info
    receiver: 'maintenance-alerts'
    group_interval: 1h
    repeat_interval: 24h

# Configuration des inhibitions
inhibit_rules:
  # Inhiber les alertes de warning si une alerte critique existe
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
    
  # Inhiber les alertes de service si le serveur est down
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      service: '.*'
    equal: ['instance']

# Configuration des récepteurs
receivers:
  # Récepteur par défaut
  - name: 'default'
    slack_configs:
    - channel: '#alerts'
      title: '🔔 Alerte Poultray DZ'
      text: |
        {{ range .Alerts }}
        *Alerte:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Sévérité:* {{ .Labels.severity }}
        *Service:* {{ .Labels.service }}
        {{ end }}
      send_resolved: true
      
  # Alertes critiques
  - name: 'critical-alerts'
    email_configs:
    - to: '<EMAIL>'
      subject: '🚨 CRITIQUE - {{ .GroupLabels.alertname }}'
      body: |
        Alerte critique détectée sur Poultray DZ !
        
        {{ range .Alerts }}
        Alerte: {{ .Annotations.summary }}
        Description: {{ .Annotations.description }}
        Sévérité: {{ .Labels.severity }}
        Service: {{ .Labels.service }}
        Instance: {{ .Labels.instance }}
        Heure: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
        {{ end }}
        
        Veuillez intervenir immédiatement.
        
        Dashboard: https://monitoring.poultraydz.com
        
    slack_configs:
    - channel: '#critical-alerts'
      title: '🚨 ALERTE CRITIQUE'
      text: |
        <!channel> Alerte critique sur Poultray DZ !
        
        {{ range .Alerts }}
        *🔥 {{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        *Service:* {{ .Labels.service }}
        *Instance:* {{ .Labels.instance }}
        {{ end }}
        
        <https://monitoring.poultraydz.com|Voir le dashboard>
      color: 'danger'
      send_resolved: true
      
  # Alertes de sécurité
  - name: 'security-alerts'
    email_configs:
    - to: '<EMAIL>'
      subject: '🛡️ SÉCURITÉ - {{ .GroupLabels.alertname }}'
      body: |
        Incident de sécurité détecté sur Poultray DZ !
        
        {{ range .Alerts }}
        Alerte: {{ .Annotations.summary }}
        Description: {{ .Annotations.description }}
        Instance: {{ .Labels.instance }}
        Heure: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
        {{ end }}
        
        Veuillez vérifier immédiatement.
        
    slack_configs:
    - channel: '#security'
      title: '🛡️ Alerte Sécurité'
      text: |
        Incident de sécurité détecté !
        
        {{ range .Alerts }}
        *⚠️ {{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        *Instance:* {{ .Labels.instance }}
        {{ end }}
      color: 'warning'
      send_resolved: true
      
  # Alertes infrastructure
  - name: 'infrastructure-alerts'
    slack_configs:
    - channel: '#infrastructure'
      title: '🖥️ Alerte Infrastructure'
      text: |
        {{ range .Alerts }}
        *{{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        *Instance:* {{ .Labels.instance }}
        {{ end }}
      color: 'warning'
      send_resolved: true
      
  # Alertes base de données
  - name: 'database-alerts'
    email_configs:
    - to: '<EMAIL>'
      subject: '🗄️ Base de données - {{ .GroupLabels.alertname }}'
      body: |
        Problème détecté sur la base de données Poultray DZ.
        
        {{ range .Alerts }}
        Alerte: {{ .Annotations.summary }}
        Description: {{ .Annotations.description }}
        Instance: {{ .Labels.instance }}
        {{ end }}
        
    slack_configs:
    - channel: '#database'
      title: '🗄️ Alerte Base de Données'
      text: |
        {{ range .Alerts }}
        *{{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        *Instance:* {{ .Labels.instance }}
        {{ end }}
      color: 'warning'
      send_resolved: true
      
  # Alertes backend
  - name: 'backend-alerts'
    slack_configs:
    - channel: '#backend'
      title: '⚙️ Alerte Backend'
      text: |
        {{ range .Alerts }}
        *{{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        *Instance:* {{ .Labels.instance }}
        {{ end }}
      send_resolved: true
      
  # Alertes métier
  - name: 'business-alerts'
    slack_configs:
    - channel: '#business'
      title: '📊 Alerte Métier'
      text: |
        {{ range .Alerts }}
        *{{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        {{ end }}
      color: 'good'
      send_resolved: true
      
  # Alertes de maintenance
  - name: 'maintenance-alerts'
    slack_configs:
    - channel: '#maintenance'
      title: '🔧 Information Maintenance'
      text: |
        {{ range .Alerts }}
        *{{ .Annotations.summary }}*
        *Description:* {{ .Annotations.description }}
        {{ end }}
      color: '#439FE0'
      send_resolved: true

# Configuration des silences automatiques
# (pour éviter le spam pendant les maintenances)
silences:
  # Exemple de silence automatique pendant les déploiements
  - matchers:
    - name: alertname
      value: ServiceDown
    - name: job
      value: backend
    comment: "Déploiement en cours"
    createdBy: "alertmanager"
    startsAt: "2024-01-01T00:00:00Z"
    endsAt: "2024-01-01T01:00:00Z"
