const { DataTypes } = require('sequelize');

/**
 * Migration to create the SMTP configuration table
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Direct creation - Sequelize handles existence gracefully
    await queryInterface.createTable('smtp_configurations', {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        host: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        port: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        secure: {
          type: DataTypes.BOOLEAN,
          defaultValue: true, // true for 465, false for other ports
        },
        user: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        pass: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        fromName: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        fromEmail: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        replyTo: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        testEmailRecipient: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        isEnabled: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        }
      });

      // Add a default configuration
      await queryInterface.bulkInsert('smtp_configurations', [{
        host: 'smtp.example.com',
        port: 587,
        secure: false,
        user: '<EMAIL>',
        pass: 'password', // This should be properly secured in production
        fromName: 'Poultray DZ',
        fromEmail: '<EMAIL>',
        replyTo: '<EMAIL>',
        testEmailRecipient: '<EMAIL>',
        isEnabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('smtp_configurations');
  }
};
