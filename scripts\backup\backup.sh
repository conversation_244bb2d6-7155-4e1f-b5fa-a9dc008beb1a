#!/bin/bash

# Script de sauvegarde automatique pour Poultray DZ
# Sauvegarde la base de données, les fichiers uploadés et les configurations

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="${BACKUP_DIR:-/backups}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="poultraydz_backup_$TIMESTAMP"

# Variables d'environnement
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-poultraydz}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"

# Configuration S3 (optionnel)
S3_BUCKET="${S3_BUCKET:-}"
S3_ACCESS_KEY="${S3_ACCESS_KEY:-}"
S3_SECRET_KEY="${S3_SECRET_KEY:-}"
S3_REGION="${S3_REGION:-eu-west-1}"

# Configuration de notification
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
EMAIL_TO="${EMAIL_TO:-}"
SMTP_SERVER="${SMTP_SERVER:-}"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌${NC} $1"
}

# Fonction de nettoyage en cas d'erreur
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Erreur lors de la sauvegarde (code: $exit_code)"
        if [ -d "$BACKUP_DIR/$BACKUP_NAME" ]; then
            log "Nettoyage du répertoire de sauvegarde incomplet..."
            rm -rf "$BACKUP_DIR/$BACKUP_NAME"
        fi
        send_notification "❌ Échec de la sauvegarde Poultray DZ" "La sauvegarde du $(date) a échoué avec le code d'erreur $exit_code"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Fonction de notification Slack
send_slack_notification() {
    local title="$1"
    local message="$2"
    local color="${3:-#36a64f}"
    
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"$title\",
                    \"text\": \"$message\",
                    \"footer\": \"Poultray DZ Backup System\",
                    \"ts\": $(date +%s)
                }]
            }" \
            "$SLACK_WEBHOOK" > /dev/null 2>&1 || true
    fi
}

# Fonction de notification email
send_email_notification() {
    local subject="$1"
    local body="$2"
    
    if [ -n "$EMAIL_TO" ] && [ -n "$SMTP_SERVER" ]; then
        echo "$body" | mail -s "$subject" "$EMAIL_TO" > /dev/null 2>&1 || true
    fi
}

# Fonction de notification générique
send_notification() {
    local title="$1"
    local message="$2"
    local color="${3:-#36a64f}"
    
    send_slack_notification "$title" "$message" "$color"
    send_email_notification "$title" "$message"
}

# Vérifier les prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    # Vérifier que pg_dump est disponible
    if ! command -v pg_dump &> /dev/null; then
        log_error "pg_dump n'est pas installé"
        exit 1
    fi
    
    # Vérifier que le répertoire de sauvegarde existe
    if [ ! -d "$BACKUP_DIR" ]; then
        log "Création du répertoire de sauvegarde: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
    
    # Vérifier la connexion à la base de données
    if ! PGPASSWORD="$DB_PASSWORD" pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        log_error "Impossible de se connecter à la base de données"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Créer le répertoire de sauvegarde
create_backup_directory() {
    log "Création du répertoire de sauvegarde: $BACKUP_DIR/$BACKUP_NAME"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
}

# Sauvegarder la base de données
backup_database() {
    log "Sauvegarde de la base de données..."
    
    local db_backup_file="$BACKUP_DIR/$BACKUP_NAME/database.sql"
    local db_backup_compressed="$BACKUP_DIR/$BACKUP_NAME/database.sql.gz"
    
    # Dump de la base de données
    PGPASSWORD="$DB_PASSWORD" pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-owner \
        --no-privileges \
        --format=plain \
        --file="$db_backup_file"
    
    # Compression du dump
    gzip "$db_backup_file"
    
    # Vérifier la taille du fichier
    local file_size=$(stat -f%z "$db_backup_compressed" 2>/dev/null || stat -c%s "$db_backup_compressed" 2>/dev/null)
    if [ "$file_size" -lt 1024 ]; then
        log_error "Le fichier de sauvegarde de la base de données semble trop petit ($file_size bytes)"
        exit 1
    fi
    
    log_success "Base de données sauvegardée ($(numfmt --to=iec $file_size))"
}

# Sauvegarder les fichiers uploadés
backup_uploads() {
    log "Sauvegarde des fichiers uploadés..."
    
    local uploads_source="$PROJECT_ROOT/backend/uploads"
    local uploads_backup="$BACKUP_DIR/$BACKUP_NAME/uploads"
    
    if [ -d "$uploads_source" ]; then
        # Créer une archive tar des uploads
        tar -czf "$uploads_backup.tar.gz" -C "$(dirname "$uploads_source")" "$(basename "$uploads_source")"
        
        local file_count=$(find "$uploads_source" -type f | wc -l)
        local archive_size=$(stat -f%z "$uploads_backup.tar.gz" 2>/dev/null || stat -c%s "$uploads_backup.tar.gz" 2>/dev/null)
        
        log_success "Fichiers uploadés sauvegardés ($file_count fichiers, $(numfmt --to=iec $archive_size))"
    else
        log_warning "Répertoire uploads non trouvé: $uploads_source"
        touch "$uploads_backup.empty"
    fi
}

# Sauvegarder les configurations
backup_configs() {
    log "Sauvegarde des configurations..."
    
    local config_backup="$BACKUP_DIR/$BACKUP_NAME/configs"
    mkdir -p "$config_backup"
    
    # Sauvegarder les fichiers de configuration importants
    local config_files=(
        "$PROJECT_ROOT/docker-compose.production.yml"
        "$PROJECT_ROOT/.env.production"
        "$PROJECT_ROOT/nginx/nginx.conf"
        "$PROJECT_ROOT/monitoring/prometheus.yml"
        "$PROJECT_ROOT/monitoring/alertmanager.yml"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            cp "$config_file" "$config_backup/"
            log "Configuration sauvegardée: $(basename "$config_file")"
        fi
    done
    
    # Créer un fichier avec les informations système
    cat > "$config_backup/system_info.txt" << EOF
Backup Date: $(date)
Hostname: $(hostname)
Docker Version: $(docker --version 2>/dev/null || echo "Not installed")
Docker Compose Version: $(docker-compose --version 2>/dev/null || echo "Not installed")
System Info: $(uname -a)
Disk Usage: $(df -h)
Memory Usage: $(free -h 2>/dev/null || echo "Not available")
EOF
    
    log_success "Configurations sauvegardées"
}

# Créer les métadonnées de sauvegarde
create_metadata() {
    log "Création des métadonnées de sauvegarde..."
    
    local metadata_file="$BACKUP_DIR/$BACKUP_NAME/metadata.json"
    
    cat > "$metadata_file" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "timestamp": "$TIMESTAMP",
    "date": "$(date -Iseconds)",
    "version": "1.0",
    "database": {
        "host": "$DB_HOST",
        "port": $DB_PORT,
        "name": "$DB_NAME",
        "user": "$DB_USER"
    },
    "components": {
        "database": true,
        "uploads": $([ -f "$BACKUP_DIR/$BACKUP_NAME/uploads.tar.gz" ] && echo "true" || echo "false"),
        "configs": true
    },
    "system": {
        "hostname": "$(hostname)",
        "os": "$(uname -s)",
        "arch": "$(uname -m)"
    }
}
EOF
    
    log_success "Métadonnées créées"
}

# Créer l'archive finale
create_final_archive() {
    log "Création de l'archive finale..."
    
    local archive_file="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    
    # Créer l'archive
    tar -czf "$archive_file" -C "$BACKUP_DIR" "$BACKUP_NAME"
    
    # Supprimer le répertoire temporaire
    rm -rf "$BACKUP_DIR/$BACKUP_NAME"
    
    # Calculer et afficher la taille
    local archive_size=$(stat -f%z "$archive_file" 2>/dev/null || stat -c%s "$archive_file" 2>/dev/null)
    log_success "Archive finale créée: ${BACKUP_NAME}.tar.gz ($(numfmt --to=iec $archive_size))"
    
    # Créer un checksum
    if command -v sha256sum &> /dev/null; then
        sha256sum "$archive_file" > "${archive_file}.sha256"
        log "Checksum SHA256 créé"
    fi
}

# Uploader vers S3 (optionnel)
upload_to_s3() {
    if [ -n "$S3_BUCKET" ] && [ -n "$S3_ACCESS_KEY" ] && [ -n "$S3_SECRET_KEY" ]; then
        log "Upload vers S3..."
        
        local archive_file="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
        local s3_key="backups/$(date +%Y)/$(date +%m)/${BACKUP_NAME}.tar.gz"
        
        # Configurer AWS CLI ou utiliser curl pour S3
        if command -v aws &> /dev/null; then
            AWS_ACCESS_KEY_ID="$S3_ACCESS_KEY" \
            AWS_SECRET_ACCESS_KEY="$S3_SECRET_KEY" \
            AWS_DEFAULT_REGION="$S3_REGION" \
            aws s3 cp "$archive_file" "s3://$S3_BUCKET/$s3_key"
            
            log_success "Sauvegarde uploadée vers S3: s3://$S3_BUCKET/$s3_key"
        else
            log_warning "AWS CLI non disponible, upload S3 ignoré"
        fi
    fi
}

# Nettoyer les anciennes sauvegardes
cleanup_old_backups() {
    log "Nettoyage des anciennes sauvegardes (> $BACKUP_RETENTION_DAYS jours)..."
    
    # Supprimer les sauvegardes locales anciennes
    find "$BACKUP_DIR" -name "poultraydz_backup_*.tar.gz" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "poultraydz_backup_*.tar.gz.sha256" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    
    local remaining_backups=$(find "$BACKUP_DIR" -name "poultraydz_backup_*.tar.gz" | wc -l)
    log_success "Nettoyage terminé ($remaining_backups sauvegardes conservées)"
}

# Fonction principale
main() {
    log "🚀 Début de la sauvegarde Poultray DZ"
    local start_time=$(date +%s)
    
    check_prerequisites
    create_backup_directory
    backup_database
    backup_uploads
    backup_configs
    create_metadata
    create_final_archive
    upload_to_s3
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "✅ Sauvegarde terminée avec succès en ${duration}s"
    
    # Notification de succès
    send_notification \
        "✅ Sauvegarde Poultray DZ réussie" \
        "Sauvegarde du $(date) terminée avec succès en ${duration}s\nArchive: ${BACKUP_NAME}.tar.gz" \
        "#36a64f"
}

# Exécuter le script principal
main "$@"
