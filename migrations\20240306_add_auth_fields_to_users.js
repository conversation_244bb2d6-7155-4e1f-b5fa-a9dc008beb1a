'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // We need to use raw SQL to add columns if they don't exist
      await queryInterface.sequelize.query(`
        DO $$
        BEGIN
          BEGIN
            ALTER TABLE users ADD COLUMN firebase_uid VARCHAR(255) UNIQUE;
          EXCEPTION
            WHEN duplicate_column THEN
              RAISE NOTICE 'Column firebase_uid already exists in users';
          END;

          BEGIN
            ALTER TABLE users ADD COLUMN phone VARCHAR(50);
          EXCEPTION
            WHEN duplicate_column THEN
              RAISE NOTICE 'Column phone already exists in users';
          END;

          BEGIN
            ALTER TABLE users ADD COLUMN address TEXT;
          EXCEPTION
            WHEN duplicate_column THEN
              RAISE NOTICE 'Column address already exists in users';
          END;
        END $$;
      `);

      console.log('Successfully added auth fields to users table');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, <PERSON>quelize) {
    try {
      await queryInterface.sequelize.query(`
        ALTER TABLE users
        DROP COLUMN IF EXISTS firebase_uid,
        DROP COLUMN IF EXISTS phone,
        DROP COLUMN IF EXISTS address;
      `);
      console.log('Successfully removed auth fields from users table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
