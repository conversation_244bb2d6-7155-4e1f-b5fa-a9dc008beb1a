import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/chat/chat_thread.dart';
import '../models/chat/chat_message.dart';
import '../services/chat/chat_service.dart';
import '../services/connectivity_service.dart';
import '../services/notification_service.dart';

class ChatProvider extends ChangeNotifier {
  final ChatService _chatService;
  final ConnectivityService _connectivityService;
  final NotificationService _notificationService;

  ChatProvider({
    required ChatService chatService,
    required ConnectivityService connectivityService,
    required NotificationService notificationService,
  }) : _chatService = chatService,
       _connectivityService = connectivityService,
       _notificationService = notificationService {
    _initializeProvider();
  }

  // Chat State
  List<ChatThread> _chatThreads = [];
  Map<int, List<ChatMessage>> _threadMessages = {};
  ChatThread? _selectedThread;
  List<ChatMessage> _currentMessages = [];
  List<ChatMessage> _pendingMessages = [];
  
  // UI State
  bool _isLoadingThreads = false;
  bool _isLoadingMessages = false;
  bool _isSendingMessage = false;
  bool _isCreatingThread = false;
  bool _isTyping = false;
  
  String? _error;
  bool _hasMoreThreads = true;
  bool _hasMoreMessages = true;
  int _threadsPage = 1;
  int _messagesPage = 1;
  
  // Typing indicators
  Map<int, Map<int, bool>> _typingUsers = {}; // threadId -> userId -> isTyping
  
  // Connectivity
  bool _isOnline = true;
  
  // Search and filters
  String _searchQuery = '';
  List<ChatThread> _searchResults = [];
  bool _isSearching = false;

  // Getters
  List<ChatThread> get chatThreads => _chatThreads;
  Map<int, List<ChatMessage>> get threadMessages => _threadMessages;
  ChatThread? get selectedThread => _selectedThread;
  List<ChatMessage> get currentMessages => _currentMessages;
  List<ChatMessage> get pendingMessages => _pendingMessages;
  
  bool get isLoadingThreads => _isLoadingThreads;
  bool get isLoadingMessages => _isLoadingMessages;
  bool get isSendingMessage => _isSendingMessage;
  bool get isCreatingThread => _isCreatingThread;
  bool get isTyping => _isTyping;
  
  String? get error => _error;
  bool get hasMoreThreads => _hasMoreThreads;
  bool get hasMoreMessages => _hasMoreMessages;
  bool get isOnline => _isOnline;
  
  String get searchQuery => _searchQuery;
  List<ChatThread> get searchResults => _searchResults;
  bool get isSearching => _isSearching;
  
  bool get isLoading => _isLoadingThreads || _isLoadingMessages || 
                       _isSendingMessage || _isCreatingThread || _isSearching;

  // Initialize provider
  void _initializeProvider() {
    _loadInitialData();
    _setupConnectivityListener();
    _setupNotificationListener();
  }

  // Load initial data
  Future<void> _loadInitialData() async {
    await Future.wait([
      loadChatThreads(refresh: true),
      loadPendingMessages(),
    ]);
  }

  // Setup connectivity listener
  void _setupConnectivityListener() {
    _connectivityService.connectivityStream.listen((isConnected) {
      _isOnline = isConnected;
      if (isConnected) {
        _syncOfflineData();
      }
      notifyListeners();
    });
  }

  // Setup notification listener for new messages
  void _setupNotificationListener() {
    _notificationService.messageStream.listen((messageData) {
      _handleNewMessageNotification(messageData);
    });
  }

  // Handle new message notification
  void _handleNewMessageNotification(Map<String, dynamic> messageData) {
    try {
      final threadId = messageData['threadId'] as int?;
      final message = ChatMessage.fromJson(messageData['message']);
      
      if (threadId != null) {
        // Update thread with new message
        final threadIndex = _chatThreads.indexWhere((t) => t.id == threadId);
        if (threadIndex != -1) {
          final thread = _chatThreads[threadIndex];
          final updatedThread = thread.copyWith(
            lastMessageContent: message.content,
            lastMessageTimestamp: message.createdAt,
            lastMessageSenderId: message.senderId,
            unreadCount: thread.unreadCount + 1,
          );
          _chatThreads[threadIndex] = updatedThread;
          
          // Move thread to top
          _chatThreads.removeAt(threadIndex);
          _chatThreads.insert(0, updatedThread);
        }
        
        // Add message to current conversation if it's the selected thread
        if (_selectedThread?.id == threadId) {
          _currentMessages.add(message);
          _threadMessages[threadId]?.add(message);
        }
        
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling new message notification: $e');
      }
    }
  }

  // Sync offline data when coming back online
  Future<void> _syncOfflineData() async {
    try {
      await _chatService.syncPendingMessages();
      await loadPendingMessages();
      await refreshAllData();
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing offline chat data: $e');
      }
    }
  }

  // Chat Threads Methods
  
  // Load chat threads
  Future<void> loadChatThreads({
    bool refresh = false,
    bool loadMore = false,
  }) async {
    if (_isLoadingThreads) return;
    
    try {
      _isLoadingThreads = true;
      _error = null;
      
      if (refresh) {
        _threadsPage = 1;
        _hasMoreThreads = true;
        _chatThreads.clear();
      } else if (loadMore && !_hasMoreThreads) {
        return;
      }
      
      notifyListeners();

      final page = loadMore ? _threadsPage + 1 : _threadsPage;
      
      final newThreads = await _chatService.getChatThreads(page: page);

      if (loadMore) {
        _chatThreads.addAll(newThreads);
        _threadsPage = page;
      } else {
        _chatThreads = newThreads;
        _threadsPage = 1;
      }
      
      _hasMoreThreads = newThreads.length >= 20; // Assuming page size is 20
      
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error loading chat threads: $e');
      }
    } finally {
      _isLoadingThreads = false;
      notifyListeners();
    }
  }

  // Load messages for a thread
  Future<void> loadMessages({
    required int threadId,
    bool refresh = false,
    bool loadMore = false,
  }) async {
    if (_isLoadingMessages) return;
    
    try {
      _isLoadingMessages = true;
      _error = null;
      
      if (refresh) {
        _messagesPage = 1;
        _hasMoreMessages = true;
        _currentMessages.clear();
        _threadMessages[threadId]?.clear();
      } else if (loadMore && !_hasMoreMessages) {
        return;
      }
      
      notifyListeners();

      final page = loadMore ? _messagesPage + 1 : _messagesPage;
      
      final newMessages = await _chatService.getMessages(
        threadId: threadId,
        page: page,
      );

      if (loadMore) {
        // Insert at beginning for older messages
        _currentMessages.insertAll(0, newMessages.reversed);
        _threadMessages[threadId]?.insertAll(0, newMessages.reversed);
        _messagesPage = page;
      } else {
        _currentMessages = newMessages.reversed.toList();
        _threadMessages[threadId] = List.from(_currentMessages);
        _messagesPage = 1;
      }
      
      _hasMoreMessages = newMessages.length >= 50; // Assuming page size is 50
      
      // Mark messages as read
      await markMessagesAsRead(threadId);
      
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error loading messages: $e');
      }
    } finally {
      _isLoadingMessages = false;
      notifyListeners();
    }
  }

  // Create a new chat thread
  Future<ChatThread?> createChatThread({
    required int listingId,
    required int sellerId,
    String? initialMessage,
  }) async {
    try {
      _isCreatingThread = true;
      _error = null;
      notifyListeners();

      final thread = await _chatService.createChatThread(
        listingId: listingId,
        sellerId: sellerId,
        initialMessage: initialMessage,
      );
      
      if (thread != null) {
        _chatThreads.insert(0, thread);
        _threadMessages[thread.id] = [];
        
        // If there was an initial message, add it
        if (initialMessage != null && initialMessage.isNotEmpty) {
          final message = MessageTemplates.createTextMessage(
            threadId: thread.id,
            senderId: thread.buyerId, // Assuming current user is buyer
            receiverId: thread.sellerId,
            content: initialMessage,
          );
          _currentMessages = [message];
          _threadMessages[thread.id] = [message];
        }
        
        notifyListeners();
      }
      
      return thread;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error creating chat thread: $e');
      }
      return null;
    } finally {
      _isCreatingThread = false;
      notifyListeners();
    }
  }

  // Send a text message
  Future<bool> sendTextMessage({
    required int threadId,
    required int receiverId,
    required String content,
    int? replyToId,
  }) async {
    try {
      _isSendingMessage = true;
      notifyListeners();

      final message = await _chatService.sendTextMessage(
        threadId: threadId,
        receiverId: receiverId,
        content: content,
        replyToId: replyToId,
      );
      
      if (message != null) {
        _addMessageToThread(threadId, message);
        _updateThreadWithNewMessage(threadId, message);
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error sending text message: $e');
      }
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  // Send an image message
  Future<bool> sendImageMessage({
    required int threadId,
    required int receiverId,
    required File imageFile,
    String? caption,
  }) async {
    try {
      _isSendingMessage = true;
      notifyListeners();

      final message = await _chatService.sendImageMessage(
        threadId: threadId,
        receiverId: receiverId,
        imageFile: imageFile,
        caption: caption,
      );
      
      if (message != null) {
        _addMessageToThread(threadId, message);
        _updateThreadWithNewMessage(threadId, message);
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error sending image message: $e');
      }
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  // Send an offer message
  Future<bool> sendOfferMessage({
    required int threadId,
    required int receiverId,
    required double price,
    required int quantity,
    DateTime? expiryDate,
    String? message,
  }) async {
    try {
      _isSendingMessage = true;
      notifyListeners();

      final offerMessage = await _chatService.sendOfferMessage(
        threadId: threadId,
        receiverId: receiverId,
        price: price,
        quantity: quantity,
        expiryDate: expiryDate,
        message: message,
      );
      
      if (offerMessage != null) {
        _addMessageToThread(threadId, offerMessage);
        _updateThreadWithNewMessage(threadId, offerMessage);
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error sending offer message: $e');
      }
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  // Send location message
  Future<bool> sendLocationMessage({
    required int threadId,
    required int receiverId,
    required double latitude,
    required double longitude,
    String? locationName,
  }) async {
    try {
      _isSendingMessage = true;
      notifyListeners();

      final message = await _chatService.sendLocationMessage(
        threadId: threadId,
        receiverId: receiverId,
        latitude: latitude,
        longitude: longitude,
        locationName: locationName,
      );
      
      if (message != null) {
        _addMessageToThread(threadId, message);
        _updateThreadWithNewMessage(threadId, message);
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error sending location message: $e');
      }
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  // Respond to an offer
  Future<bool> respondToOffer({
    required int messageId,
    required String response, // 'accept', 'reject', 'counter'
    double? counterPrice,
    int? counterQuantity,
    String? message,
  }) async {
    try {
      final success = await _chatService.respondToOffer(
        messageId: messageId,
        response: response,
        counterPrice: counterPrice,
        counterQuantity: counterQuantity,
        message: message,
      );
      
      if (success) {
        // Refresh current thread messages to get updated offer status
        if (_selectedThread != null) {
          await loadMessages(threadId: _selectedThread!.id, refresh: true);
        }
      }
      
      return success;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error responding to offer: $e');
      }
      return false;
    }
  }

  // Message Stream Methods
  Stream<List<ChatMessage>> messageStream(int threadId) {
    return _chatService.getMessageStream(threadId);
  }

  int getUnreadCount(int threadId) {
    return _chatThreads.firstWhere((thread) => thread.id == threadId).unreadCount;
  }

  String getOtherUserName(int threadId) {
    return _chatThreads.firstWhere((thread) => thread.id == threadId).otherUserName;
  }

  String getLastMessageContent(int threadId) {
    return _chatThreads.firstWhere((thread) => thread.id == threadId).lastMessageContent ?? '';
  }

  // Message Management Methods
  Future<void> sendMessage(int threadId, String content, {File? attachment}) async {
    try {
      await _chatService.sendMessage(threadId, content, attachment: attachment);
    } catch (e) {
      _error = 'Failed to send message';
      notifyListeners();
      throw Exception('Failed to send message: $e');
    }
  }

  Future<void> markMessagesAsRead(int threadId) async {
    try {
      await _chatService.markMessagesAsRead(threadId);
      final threadIndex = _chatThreads.indexWhere((t) => t.id == threadId);
      if (threadIndex != -1) {
        _chatThreads[threadIndex] = _chatThreads[threadIndex].copyWith(unreadCount: 0);
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to mark messages as read';
      notifyListeners();
    }
  }

  Future<void> blockUser(int userId) async {
    try {
      await _chatService.blockUser(userId);
    } catch (e) {
      _error = 'Failed to block user';
      notifyListeners();
      throw Exception('Failed to block user: $e');
    }
  }

  // Set typing indicator
  Future<void> setTypingIndicator(int threadId, bool isTyping) async {
    try {
      _isTyping = isTyping;
      notifyListeners();
      
      await _chatService.setTypingIndicator(threadId, isTyping);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting typing indicator: $e');
      }
    }
  }

  // Update typing status for other users
  void updateTypingStatus(int threadId, int userId, bool isTyping) {
    if (!_typingUsers.containsKey(threadId)) {
      _typingUsers[threadId] = {};
    }
    
    _typingUsers[threadId]![userId] = isTyping;
    
    // Update thread typing status
    final threadIndex = _chatThreads.indexWhere((t) => t.id == threadId);
    if (threadIndex != -1) {
      final thread = _chatThreads[threadIndex];
      _chatThreads[threadIndex] = thread.copyWith(
        isOtherUserTyping: isTyping,
      );
    }
    
    notifyListeners();
  }

  // Archive a thread
  Future<bool> archiveThread(int threadId) async {
    try {
      final success = await _chatService.archiveThread(threadId);
      
      if (success) {
        _chatThreads.removeWhere((t) => t.id == threadId);
        _threadMessages.remove(threadId);
        
        if (_selectedThread?.id == threadId) {
          _selectedThread = null;
          _currentMessages.clear();
        }
        
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error archiving thread: $e');
      }
      return false;
    }
  }

  // Block a user
  Future<bool> blockUser(int userId) async {
    try {
      final success = await _chatService.blockUser(userId);
      
      if (success) {
        // Remove threads with blocked user
        _chatThreads.removeWhere((t) => 
          t.buyerId == userId || t.sellerId == userId);
        
        // Clear selected thread if it involves blocked user
        if (_selectedThread != null && 
            (_selectedThread!.buyerId == userId || _selectedThread!.sellerId == userId)) {
          _selectedThread = null;
          _currentMessages.clear();
        }
        
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error blocking user: $e');
      }
      return false;
    }
  }

  // Report content
  Future<bool> reportContent({
    required int threadId,
    int? messageId,
    required String reason,
    String? description,
  }) async {
    try {
      return await _chatService.reportContent(
        threadId: threadId,
        messageId: messageId,
        reason: reason,
        description: description,
      );
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error reporting content: $e');
      }
      return false;
    }
  }

  // Search threads
  Future<void> searchThreads(String query) async {
    if (_isSearching) return;
    
    try {
      _isSearching = true;
      _searchQuery = query;
      _error = null;
      notifyListeners();

      if (query.isEmpty) {
        _searchResults.clear();
      } else {
        _searchResults = _chatThreads.where((thread) {
          return thread.listingTitle.toLowerCase().contains(query.toLowerCase()) ||
                 thread.otherUserName.toLowerCase().contains(query.toLowerCase()) ||
                 (thread.lastMessageContent?.toLowerCase().contains(query.toLowerCase()) ?? false);
        }).toList();
      }
      
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Error searching threads: $e');
      }
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  // Clear search
  void clearSearch() {
    _searchResults.clear();
    _searchQuery = '';
    notifyListeners();
  }

  // Load pending messages
  Future<void> loadPendingMessages() async {
    try {
      _pendingMessages = await _chatService.getPendingMessages();
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading pending messages: $e');
      }
    }
  }

  // Set selected thread
  void setSelectedThread(ChatThread? thread) {
    _selectedThread = thread;
    if (thread != null) {
      _currentMessages = _threadMessages[thread.id] ?? [];
      if (_currentMessages.isEmpty) {
        loadMessages(threadId: thread.id, refresh: true);
      } else {
        markMessagesAsRead(thread.id);
      }
    } else {
      _currentMessages.clear();
    }
    notifyListeners();
  }

  // Refresh all data
  Future<void> refreshAllData() async {
    await Future.wait([
      loadChatThreads(refresh: true),
      if (_selectedThread != null)
        loadMessages(threadId: _selectedThread!.id, refresh: true),
    ]);
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Helper methods
  
  void _addMessageToThread(int threadId, ChatMessage message) {
    if (_selectedThread?.id == threadId) {
      _currentMessages.add(message);
    }
    
    if (!_threadMessages.containsKey(threadId)) {
      _threadMessages[threadId] = [];
    }
    _threadMessages[threadId]!.add(message);
  }

  void _updateThreadWithNewMessage(int threadId, ChatMessage message) {
    final threadIndex = _chatThreads.indexWhere((t) => t.id == threadId);
    if (threadIndex != -1) {
      final thread = _chatThreads[threadIndex];
      final updatedThread = thread.copyWith(
        lastMessageContent: message.content,
        lastMessageTimestamp: message.createdAt,
        lastMessageSenderId: message.senderId,
      );
      
      // Move thread to top
      _chatThreads.removeAt(threadIndex);
      _chatThreads.insert(0, updatedThread);
    }
  }

  // Get unread messages count
  int get totalUnreadCount {
    return _chatThreads.fold(0, (sum, thread) => sum + thread.unreadCount);
  }

  // Get typing users for a thread
  List<int> getTypingUsers(int threadId) {
    if (!_typingUsers.containsKey(threadId)) return [];
    
    return _typingUsers[threadId]!
        .entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  // Check if user is typing in thread
  bool isUserTyping(int threadId, int userId) {
    return _typingUsers[threadId]?[userId] ?? false;
  }

  @override
  void dispose() {
    super.dispose();
  }
}