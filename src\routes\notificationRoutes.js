const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const {
  notifyUser,
  getUserNotifications,
  markNotificationsAsRead,
  deleteNotifications,
} = require('../services/notifications');

// @route   GET /api/notifications
// @desc    Get user notifications
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { limit, offset, unreadOnly } = req.query;
    
    const notifications = await getUserNotifications(req.user.id, {
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      unreadOnly: unreadOnly === 'true',
    });
    
    res.json(notifications);
  } catch (error) {
    console.error('Error in GET /api/notifications:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   POST /api/notifications/read
// @desc    Mark notifications as read
// @access  Private
router.post('/read', auth, async (req, res) => {
  try {
    const { notificationIds } = req.body;
    
    const count = await markNotificationsAsRead(req.user.id, notificationIds);
    
    res.json({ success: true, count });
  } catch (error) {
    console.error('Error in POST /api/notifications/read:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   DELETE /api/notifications
// @desc    Delete notifications
// @access  Private
router.delete('/', auth, async (req, res) => {
  try {
    const { notificationIds } = req.body;
    
    const count = await deleteNotifications(req.user.id, notificationIds);
    
    res.json({ success: true, count });
  } catch (error) {
    console.error('Error in DELETE /api/notifications:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   POST /api/notifications/test
// @desc    Send a test notification
// @access  Private
router.post('/test', auth, async (req, res) => {
  try {
    const { title, message, type, email, push } = req.body;
    
    if (!title || !message) {
      return res.status(400).json({ message: 'Title and message are required' });
    }
    
    const result = await notifyUser({
      userId: req.user.id,
      title,
      message,
      type: type || 'test',
      data: { test: true },
      email: email === true,
      push: push === true,
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error in POST /api/notifications/test:', error);
    res.status(500).json({ message: error.message });
  }
});

// @route   POST /api/notifications/send
// @desc    Send a notification to a user (admin only)
// @access  Private/Admin
router.post('/send', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Unauthorized' });
    }
    
    const { userId, title, message, type, data, email, push } = req.body;
    
    if (!userId || !title || !message) {
      return res.status(400).json({ message: 'User ID, title, and message are required' });
    }
    
    const result = await notifyUser({
      userId,
      title,
      message,
      type: type || 'admin',
      data: data || {},
      email: email === true,
      push: push === true,
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error in POST /api/notifications/send:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
