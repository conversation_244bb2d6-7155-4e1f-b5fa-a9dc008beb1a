/**
 * Configuration Axios avec intercepteurs pour la gestion des tokens JWT
 * Résout les problèmes d'erreurs 401 en implémentant un système de rafraîchissement automatique
 */

import axios from 'axios';

// Création d'une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Intercepteur pour les requêtes - ajoute le token à chaque requête
axiosInstance.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      // Utiliser à la fois l'en-tête standard Authorization et x-auth-token
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['x-auth-token'] = token;

      // Log pour le débogage (à supprimer en production)
      console.log('Token ajouté aux en-têtes de la requête:', config.url);
    } else {
      console.warn('Aucun token trouvé dans localStorage pour la requête:', config.url);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses - gère les erreurs 401 et le rafraîchissement du token
axiosInstance.interceptors.response.use(
  response => {
    // Vérifier si un nouveau token a été fourni dans l'en-tête de réponse
    const newToken = response.headers['x-auth-token-refreshed'];
    if (newToken) {
      // Mettre à jour le token dans le stockage local
      localStorage.setItem('token', newToken);
      console.log('Token JWT mis à jour depuis la réponse du serveur');
    }
    return response;
  },
  async error => {
    const originalRequest = error.config;

    // Si l'erreur est 401 (non autorisé) et que nous n'avons pas déjà essayé de rafraîchir
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Essayer de récupérer les informations d'identification stockées
        const userCredentials = JSON.parse(localStorage.getItem('user_credentials'));

        if (!userCredentials) {
          throw new Error('Informations d\'authentification non disponibles');
        }

        // Réauthentifier l'utilisateur
        const response = await axios.post('/api/auth/login', userCredentials);
        const { token } = response.data;

        // Mettre à jour le token dans le stockage local
        localStorage.setItem('token', token);

        // Mettre à jour l'en-tête d'autorisation pour la requête originale
        originalRequest.headers['x-auth-token'] = token;

        // Réessayer la requête originale
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // En cas d'échec du rafraîchissement, déconnecter l'utilisateur
        localStorage.removeItem('token');
        localStorage.removeItem('user_credentials');

        // Rediriger vers la page de connexion
        window.location.href = '/login?session=expired';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
