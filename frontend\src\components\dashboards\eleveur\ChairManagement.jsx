import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import { useTranslation } from 'react-i18next';

function ChairManagement() {
  const { t } = useTranslation();
  const [volaillesChair, setVolaillesChair] = useState([]);
  const [croissanceData, setCroissanceData] = useState({}); // { volailleId: { date: poids } }
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch volailles de chair (broilers) data for the current eleveur
    const fetchVolaillesChair = async () => {
      try {
        setLoading(true);
        // const response = await api.get("/api/eleveur/volailles?type=chair"); // Example API call
        // setVolaillesChair(response.data);
        setVolaillesChair([ { id: 3, nom: 'Lot 1', race: 'Ross 308', age: 30, poids_actuel: 1.5 }, { id: 4, nom: 'Lot 2', race: '<PERSON> 500', age: 25, poids_actuel: 1.2 } ]); // Placeholder data
        setError(null);
      } catch (err) {
        console.error("Erreur chargement volailles de chair:", err);
        setError("Impossible de charger les données des volailles de chair.");
        setVolaillesChair([]);
      } finally {
        setLoading(false);
      }
    };

    fetchVolaillesChair();
  }, []);

  const handleWeightChange = async (volailleId, newWeight) => {
    try {
      // Simulated API call to update weight
      // await api.post(`/api/eleveur/volailles/${volailleId}/poids`, { poids: newWeight });
      
      // Update local state
      setCroissanceData(prev => ({
        ...prev,
        [volailleId]: {
          ...prev[volailleId],
          [new Date().toISOString().split('T')[0]]: newWeight
        }
      }));
    } catch (err) {
      console.error("Erreur mise à jour poids:", err);
      setError("Impossible de mettre à jour le poids.");
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">{error}</Alert>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h5" gutterBottom>
        {t('Gestion des Volailles de Chair')}
      </Typography>
      
      <Grid container spacing={3}>
        {volaillesChair.map((volaille) => (
          <Grid item xs={12} md={6} key={volaille.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{volaille.nom}</Typography>
                <Typography color="textSecondary" gutterBottom>
                  {t('Race')}: {volaille.race}
                </Typography>
                <Typography>
                  {t('Âge')}: {volaille.age} {t('jours')}
                </Typography>
                <Typography>
                  {t('Poids actuel')}: {volaille.poids_actuel} kg
                </Typography>
                
                <Box mt={2}>
                  <TextField
                    label={t('Nouveau poids (kg)')}
                    type="number"
                    size="small"
                    onChange={(e) => handleWeightChange(volaille.id, parseFloat(e.target.value))}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

export default ChairManagement;