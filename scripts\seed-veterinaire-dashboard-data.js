const sequelize = require('../src/config/database');
const { QueryTypes } = require('sequelize');

async function seedTestData() {
  try {
    console.log('Setting up test data for veterinaire dashboard...');

    // Check if there's a veterinaire user with ID 8
    const checkVeterinaireUserQuery = `
      SELECT * FROM "users" WHERE id = 8 AND role = 'veterinaire' LIMIT 1
    `;

    const veterinaireUsers = await sequelize.query(checkVeterinaireUserQuery, {
      type: QueryTypes.SELECT
    });

    if (veterinaireUsers.length === 0) {
      console.log('⚠️ No veterinaire user with ID 8 found. Creating one...');

      // Create a veterinaire user
      const createUserQuery = `
        INSERT INTO "users" (
          id, username, email, password, role, status,
          created_at, updated_at
        ) VALUES (
          8, 'vetirinaire', '<EMAIL>',
          '$2a$10$V4eoZe4G9X4IfWMQGOJrJelQ6xh07YBK0w8q.iQr5VlB5Y7PQxq3S',
          'veterinaire', 'active', NOW(), NOW()
        ) ON CONFLICT (id) DO NOTHING
        RETURNING id
      `;

      const [userResult] = await sequelize.query(createUserQuery, {
        type: QueryTypes.INSERT
      });

      if (userResult && userResult.length > 0) {
        console.log(`✅ Created veterinaire user with ID: 8`);
      } else {
        console.log('⚠️ Could not create veterinaire user. Continuing with existing user...');
      }
    }

    console.log('✅ Found veterinaire user with ID 8');

    // Check if there's a veterinaire profile for the user
    const checkVeterinaireProfileQuery = `
      SELECT * FROM "veterinaires" WHERE user_id = 8 LIMIT 1
    `;

    const veterinaireProfiles = await sequelize.query(checkVeterinaireProfileQuery, {
      type: QueryTypes.SELECT
    });

    if (veterinaireProfiles.length === 0) {
      console.log('⚠️ No veterinaire profile found. Creating one...');

      // Create a veterinaire profile
      const createProfileQuery = `
        INSERT INTO "veterinaires" (
          user_id, numero_ordre, specialites,
          telephone, email_professionnel, adresse_cabinet,
          description, nombre_avis, statut, created_at, updated_at
        ) VALUES (
          8, 'VET-2025-1234', ARRAY['volaille', 'bovins'],
          '0555123456', '<EMAIL>', 'Alger, Algérie',
          'Vétérinaire spécialiste en volaille', 0, 'actif', NOW(), NOW()
        )
        RETURNING id
      `;

      try {
        const [profileResult] = await sequelize.query(createProfileQuery, {
          type: QueryTypes.INSERT
        });

        console.log(`✅ Created veterinaire profile with ID: ${profileResult[0].id}`);
      } catch (error) {
        console.error('❌ Error creating veterinaire profile:', error.message);
      }
    } else {
      console.log('✅ Found existing veterinaire profile');
    }

    // Check if there are eleveurs
    const checkEleveursQuery = `SELECT * FROM eleveurs LIMIT 1`;
    const eleveurs = await sequelize.query(checkEleveursQuery, {
      type: QueryTypes.SELECT
    });

    if (eleveurs.length === 0) {
      console.log('⚠️ No eleveurs found. Creating a sample eleveur...');

      // Create a sample eleveur
      const createEleveurQuery = `
        INSERT INTO eleveurs (
          nom, prenom, email, telephone, adresse, date_inscription, statut, created_at, updated_at, date_modification
        ) VALUES (
          'Test', 'Eleveur', '<EMAIL>', '0555123456', 'Alger, Algérie',
          NOW(), 'actif', NOW(), NOW(), NOW()
        ) RETURNING id
      `;

      const [eleveurResult] = await sequelize.query(createEleveurQuery, {
        type: QueryTypes.INSERT
      });

      console.log(`✅ Created sample eleveur with ID: ${eleveurResult[0].id}`);
    } else {
      console.log('✅ Found existing eleveurs');
    }

    // Create volaille for testing
    console.log('Creating test volaille...');
    const createVolailleQuery = `
      INSERT INTO volailles (
        eleveur_id, espece, race, quantite, age,
        date_acquisition, prix_unitaire, date_ajout, date_modification,
        statut, description, created_at, updated_at
      )
      SELECT
        id, 'Poulet de chair', 'Cobb500', 100, 12,
        NOW() - INTERVAL '12 weeks', 450.00, NOW(), NOW(),
        'disponible', 'Lot de poulets pour test vétérinaire', NOW(), NOW()
      FROM eleveurs
      ORDER BY id
      LIMIT 1
      RETURNING id
    `;

    const [volailleResult] = await sequelize.query(createVolailleQuery, {
      type: QueryTypes.INSERT
    });

    const volailleId = volailleResult[0].id;
    console.log(`✅ Created test volaille with ID: ${volailleId}`);

    // Create consultations for the veterinaire
    console.log('Creating test consultations...');
    const createConsultationsQuery = `
      INSERT INTO consultations (
        veterinaire_id, eleveur_id, volaille_id, date, symptomes,
        diagnostic, traitement, statut, cout, created_at, updated_at
      )
      VALUES
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
          NOW() + INTERVAL '2 days', 'Toux et éternuements',
          'Suspicion d''infection respiratoire', 'Antibiotiques et repos',
          'programmee', 2500, NOW(), NOW()),
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
          NOW() + INTERVAL '5 days', 'Perte d''appétit',
          'Stress thermique probable', 'Ajustement des conditions d''élevage',
          'programmee', 1500, NOW(), NOW()),
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
          NOW() - INTERVAL '10 days', 'Diarrhée',
          'Infection intestinale', 'Traitement antibiotique et probiotiques',
          'terminee', 3000, NOW(), NOW()),
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
          NOW() - INTERVAL '25 days', 'Faiblesse générale',
          'Malnutrition', 'Compléments alimentaires',
          'terminee', 2000, NOW(), NOW())
      RETURNING id
    `;

    const [consultationsResult] = await sequelize.query(createConsultationsQuery, {
      type: QueryTypes.INSERT
    });

    console.log(`✅ Created ${consultationsResult.length} test consultations`);

    // Create prescriptions for the veterinaire
    console.log('Creating test prescriptions...');
    const createPrescriptionsQuery = `
      INSERT INTO prescriptions (
        veterinaire_id, eleveur_id, volaille_id, numero_prescription,
        date_prescription, diagnostic, medicaments, posologie,
        duree_traitement, statut, created_at, updated_at
      )
      VALUES
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
         'RX-20250618-12345', NOW(), 'Infection respiratoire',
         '[{"nom": "Enrofloxacine", "dosage": "10mg/kg", "instructions": "Administrer dans l''eau de boisson"}]',
         '10mg/kg dans l''eau de boisson', 7, 'en_attente', NOW(), NOW()),
        (8, (SELECT id FROM eleveurs ORDER BY id LIMIT 1), ${volailleId},
         'RX-20250618-67890', NOW() - INTERVAL '15 days', 'Parasites externes',
         '[{"nom": "Ivermectine", "dosage": "0.2mg/kg", "instructions": "Application cutanée"}]',
         '0.2mg/kg application cutanée', 1, 'terminé', NOW(), NOW())
      RETURNING id
    `;

    const [prescriptionsResult] = await sequelize.query(createPrescriptionsQuery, {
      type: QueryTypes.INSERT
    });

    console.log(`✅ Created ${prescriptionsResult.length} test prescriptions`);

    // Create alertes_stock for the veterinaire
    console.log('Creating test alertes_stock...');

    // First check if the table has the expected column structure
    const checkAlerteStockColumnsQuery = `
      SELECT column_name FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'alertes_stock'
      AND column_name IN ('visible', 'type_alerte', 'priorite', 'statut')
    `;

    const alerteStockColumns = await sequelize.query(checkAlerteStockColumnsQuery, {
      type: QueryTypes.SELECT
    });

    const columnNames = alerteStockColumns.map(col => col.column_name);
    console.log('Alerte stock columns available:', columnNames);

    // Create alerts with correct field names based on table structure
    if (columnNames.length >= 3) {
      const createAlertesStockQuery = `
        INSERT INTO alertes_stock (
          eleveur_id, type_alerte, priorite, statut, titre, message,
          created_at, updated_at
        )
        VALUES
          ((SELECT id FROM eleveurs ORDER BY id LIMIT 1),
           'mortalite_elevee', 'haute', 'active',
           'Taux de mortalité élevé détecté',
           'Un taux de mortalité anormal a été détecté dans votre élevage. Contactez votre vétérinaire.',
           NOW(), NOW()),
          ((SELECT id FROM eleveurs ORDER BY id LIMIT 1),
           'stock_medicament_bas', 'normale', 'active',
           'Stock d''antibiotiques bas',
           'Votre stock d''antibiotiques est presque épuisé. Pensez à vous réapprovisionner.',
           NOW(), NOW())
        RETURNING id
      `;

      try {
        const [alertesResult] = await sequelize.query(createAlertesStockQuery, {
          type: QueryTypes.INSERT
        });

        console.log(`✅ Created ${alertesResult.length} test alertes_stock`);
      } catch (error) {
        console.error('❌ Error creating alertes_stock:', error.message);
      }
    } else {
      console.log('⚠️ The alertes_stock table does not have the expected column structure');
    }

    console.log('✅ Test data setup complete!');

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the seed function
seedTestData();
