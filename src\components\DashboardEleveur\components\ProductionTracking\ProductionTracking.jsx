import React, { useState } from 'react';
import { Typo<PERSON>, Box, Divider, FormControl, InputLabel, Select, MenuItem, Grid } from '@mui/material';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON>r, <PERSON>, <PERSON><PERSON><PERSON>, Bar } from 'recharts';

const ProductionTracking = ({ data }) => {
  const [periodeSelected, setPeriodeSelected] = useState('mois');
  const [typeVolailleSelected, setTypeVolailleSelected] = useState('tous');

  // Données fictives pour la démonstration
  const mockData = {
    production: {
      mois: [
        { date: '01/06', poulets: 450, poules: 1200, total: 1650 },
        { date: '02/06', poulets: 480, poules: 1250, total: 1730 },
        { date: '03/06', poulets: 470, poules: 1300, total: 1770 },
        { date: '04/06', poulets: 500, poules: 1280, total: 1780 },
        { date: '05/06', poulets: 520, poules: 1320, total: 1840 },
        { date: '06/06', poulets: 510, poules: 1340, total: 1850 },
        { date: '07/06', poulets: 530, poules: 1360, total: 1890 }
      ],
      semaine: [
        { date: 'Sem 1', poulets: 3000, poules: 8500, total: 11500 },
        { date: 'Sem 2', poulets: 3200, poules: 8700, total: 11900 },
        { date: 'Sem 3', poulets: 3300, poules: 8900, total: 12200 },
        { date: 'Sem 4', poulets: 3500, poules: 9100, total: 12600 }
      ],
      moisAnnee: [
        { date: 'Jan', poulets: 12000, poules: 35000, total: 47000 },
        { date: 'Fév', poulets: 13000, poules: 36000, total: 49000 },
        { date: 'Mar', poulets: 13500, poules: 37000, total: 50500 },
        { date: 'Avr', poulets: 14000, poules: 38000, total: 52000 },
        { date: 'Mai', poulets: 14500, poules: 39000, total: 53500 },
        { date: 'Juin', poulets: 15000, poules: 40000, total: 55000 }
      ]
    },
    comparaison: [
      { mois: 'Jan', anneeActuelle: 47000, anneePrecedente: 42000 },
      { mois: 'Fév', anneeActuelle: 49000, anneePrecedente: 43500 },
      { mois: 'Mar', anneeActuelle: 50500, anneePrecedente: 45000 },
      { mois: 'Avr', anneeActuelle: 52000, anneePrecedente: 46500 },
      { mois: 'Mai', anneeActuelle: 53500, anneePrecedente: 48000 },
      { mois: 'Juin', anneeActuelle: 55000, anneePrecedente: 49500 }
    ]
  };

  // Utiliser les données réelles si disponibles, sinon utiliser les données fictives
  const productionData = data?.production || mockData.production;
  const comparaisonData = data?.comparaison || mockData.comparaison;

  // Sélectionner les données en fonction de la période choisie
  const selectedData = productionData[periodeSelected] || productionData.mois;

  const handlePeriodeChange = (event) => {
    setPeriodeSelected(event.target.value);
  };

  const handleTypeVolailleChange = (event) => {
    setTypeVolailleSelected(event.target.value);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Suivi de Production</Typography>
      <Divider sx={{ mb: 2 }} />

      {/* Filtres */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="periode-select-label">Période</InputLabel>
            <Select
              labelId="periode-select-label"
              id="periode-select"
              value={periodeSelected}
              label="Période"
              onChange={handlePeriodeChange}
            >
              <MenuItem value="mois">Jours (mois en cours)</MenuItem>
              <MenuItem value="semaine">Semaines (mois en cours)</MenuItem>
              <MenuItem value="moisAnnee">Mois (année en cours)</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="type-volaille-select-label">Type de Volaille</InputLabel>
            <Select
              labelId="type-volaille-select-label"
              id="type-volaille-select"
              value={typeVolailleSelected}
              label="Type de Volaille"
              onChange={handleTypeVolailleChange}
            >
              <MenuItem value="tous">Tous</MenuItem>
              <MenuItem value="poulets">Poulets</MenuItem>
              <MenuItem value="poules">Poules Pondeuses</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Graphique de production */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" gutterBottom>Production par Période</Typography>
        <div className="chart-container">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={selectedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              {typeVolailleSelected === 'tous' || typeVolailleSelected === 'poulets' ? (
                <Area type="monotone" dataKey="poulets" stackId="1" stroke="#F59E0B" fill="#F59E0B" fillOpacity={0.6} name="Poulets" />
              ) : null}
              {typeVolailleSelected === 'tous' || typeVolailleSelected === 'poules' ? (
                <Area type="monotone" dataKey="poules" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} name="Poules Pondeuses" />
              ) : null}
              {typeVolailleSelected === 'tous' ? (
                <Area type="monotone" dataKey="total" stroke="#10B981" fill="#10B981" fillOpacity={0.1} name="Total" />
              ) : null}
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </Box>

      {/* Graphique de comparaison */}
      <Box>
        <Typography variant="subtitle1" gutterBottom>Comparaison avec l'Année Précédente</Typography>
        <div className="chart-container">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={comparaisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="mois" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="anneePrecedente" fill="#94A3B8" name="Année Précédente" />
              <Bar dataKey="anneeActuelle" fill="#3B82F6" name="Année Actuelle" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Box>
    </Box>
  );
};

export default ProductionTracking;
