const { Sequelize } = require('sequelize');
const sequelize = require('../config/database');

async function createMissingTables() {
  try {
    console.log('🔄 Création des tables manquantes...');

    // Table des marchands (si elle n'existe pas)
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS marchands (
        id SERIAL PRIMARY KEY,
        nom VARCHAR(100) NOT NULL,
        prenom VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE,
        telephone VARCHAR(20),
        adresse TEXT,
        ville VARCHAR(100),
        pays VARCHAR(100),
        latitude DECIMAL,
        longitude DECIMAL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Table marchands créée');

    // Table des annonces
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS annonces (
        id SERIAL PRIMARY KEY,
        marchand_id INTEGER REFERENCES marchands(id),
        titre VARCHAR(200) NOT NULL,
        description TEXT,
        prix DECIMAL(10,2),
        quantite INTEGER,
        unite VARCHAR(50),
        categorie VARCHAR(100),
        type_transaction VARCHAR(50),
        images TEXT[],
        statut VARCHAR(50),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Table annonces créée');

    // Table des favoris
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS favoris (
        id SERIAL PRIMARY KEY,
        utilisateur_id INTEGER REFERENCES users(id),
        annonce_id INTEGER REFERENCES annonces(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(utilisateur_id, annonce_id)
      );
    `);
    console.log('✅ Table favoris créée');

    // Table des messages
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        expediteur_id INTEGER REFERENCES users(id),
        destinataire_id INTEGER REFERENCES users(id),
        contenu TEXT NOT NULL,
        lu BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Table messages créée');

    // Table des notes
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS notes (
        id SERIAL PRIMARY KEY,
        evaluateur_id INTEGER REFERENCES users(id),
        evalue_id INTEGER REFERENCES users(id),
        note INTEGER CHECK (note >= 1 AND note <= 5),
        commentaire TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(evaluateur_id, evalue_id)
      );
    `);
    console.log('✅ Table notes créée');

    // Table des notifications
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        type VARCHAR(50) NOT NULL,
        contenu TEXT NOT NULL,
        lu BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Table notifications créée');

    // Table des utilisateurs
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(20) NOT NULL DEFAULT 'eleveur',
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        profile_id INTEGER,
        status VARCHAR(20) DEFAULT 'active',
        preferences JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table users créée');

    // Table des ventes
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS ventes (
        id SERIAL PRIMARY KEY,
        eleveur_id INTEGER REFERENCES eleveurs(id) ON DELETE CASCADE,
        marchand_id INTEGER REFERENCES marchands(id) ON DELETE SET NULL,
        volaille_id INTEGER REFERENCES volailles(id) ON DELETE CASCADE,
        quantite INTEGER NOT NULL CHECK (quantite > 0),
        prix_unitaire DECIMAL(10,2) NOT NULL CHECK (prix_unitaire > 0),
        prix_total DECIMAL(12,2) NOT NULL CHECK (prix_total > 0),
        date_vente TIMESTAMP NOT NULL DEFAULT NOW(),
        status VARCHAR(20) DEFAULT 'en_attente' CHECK (status IN ('en_attente', 'confirmee', 'annulee', 'livree')),
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table ventes créée');

    // Table des prescriptions (pour vétérinaires)
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS prescriptions (
        id SERIAL PRIMARY KEY,
        veterinaire_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        eleveur_id INTEGER REFERENCES eleveurs(id) ON DELETE CASCADE,
        volaille_id INTEGER REFERENCES volailles(id) ON DELETE SET NULL,
        medicament VARCHAR(255) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        duree_traitement VARCHAR(100),
        instructions TEXT,
        diagnostic TEXT,
        status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'termine', 'annule')),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table prescriptions créée');

    // Table des consultations (pour vétérinaires)
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS consultations (
        id SERIAL PRIMARY KEY,
        veterinaire_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        eleveur_id INTEGER REFERENCES eleveurs(id) ON DELETE CASCADE,
        volaille_id INTEGER REFERENCES volailles(id) ON DELETE SET NULL,
        date_consultation TIMESTAMP NOT NULL DEFAULT NOW(),
        symptomes TEXT,
        diagnostic TEXT,
        traitement TEXT,
        notes TEXT,
        statut VARCHAR(20) DEFAULT 'programmee' CHECK (statut IN ('programmee', 'en_cours', 'terminee', 'annulee')),
        cout DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table consultations créée');

    // Table des traductions
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS translations (
        id SERIAL PRIMARY KEY,
        key VARCHAR(255) NOT NULL,
        value TEXT NOT NULL,
        language VARCHAR(10) NOT NULL DEFAULT 'fr',
        category VARCHAR(100) DEFAULT 'general',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(key, language)
      )
    `);
    console.log('✅ Table translations créée');

    // Table des poussins
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS poussins (
        id SERIAL PRIMARY KEY,
        eleveur_id INTEGER REFERENCES eleveurs(id) ON DELETE CASCADE,
        lot_id VARCHAR(100) NOT NULL,
        race VARCHAR(100),
        quantite_initiale INTEGER NOT NULL,
        date_eclosion DATE,
        poids_moyen DECIMAL(5,2),
        mortalite INTEGER DEFAULT 0,
        notes TEXT,
        statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'termine', 'vendu')),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table poussins créée');

    // Table de production d'œufs
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS production_oeufs (
        id SERIAL PRIMARY KEY,
        eleveur_id INTEGER REFERENCES eleveurs(id) ON DELETE CASCADE,
        date_collecte DATE NOT NULL,
        quantite_totale INTEGER NOT NULL,
        oeufs_intacts INTEGER,
        oeufs_casses INTEGER,
        oeufs_sales INTEGER,
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Table production_oeufs créée');

    // Ajouter des colonnes manquantes à la table eleveurs
    try {
      await sequelize.query(`
        ALTER TABLE eleveurs
        ADD COLUMN IF NOT EXISTS veterinaire_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS date_naissance DATE,
        ADD COLUMN IF NOT EXISTS experience_annees INTEGER,
        ADD COLUMN IF NOT EXISTS specialite VARCHAR(255),
        ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'actif' CHECK (status IN ('actif', 'inactif', 'suspendu')),
        ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW(),
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW()
      `);
      console.log('✅ Colonnes ajoutées à la table eleveurs');
    } catch (error) {
      console.log('ℹ️ Colonnes eleveurs déjà existantes ou erreur:', error.message);
    }

    // Ajouter des colonnes manquantes à la table volailles
    try {
      await sequelize.query(`
        ALTER TABLE volailles
        ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL DEFAULT 'chair',
        ADD COLUMN IF NOT EXISTS race VARCHAR(100),
        ADD COLUMN IF NOT EXISTS age INTEGER,
        ADD COLUMN IF NOT EXISTS poids DECIMAL(5,2),
        ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'disponible' CHECK (status IN ('disponible', 'vendu', 'reserve', 'indisponible')),
        ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW(),
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW()
      `);
      console.log('✅ Colonnes ajoutées à la table volailles');
    } catch (error) {
      console.log('ℹ️ Colonnes volailles déjà existantes ou erreur:', error.message);
    }

    // Créer des index pour améliorer les performances
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_ventes_eleveur_id ON ventes(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_ventes_marchand_id ON ventes(marchand_id);
      CREATE INDEX IF NOT EXISTS idx_ventes_date ON ventes(date_vente);
      CREATE INDEX IF NOT EXISTS idx_ventes_status ON ventes(status);

      CREATE INDEX IF NOT EXISTS idx_prescriptions_veterinaire_id ON prescriptions(veterinaire_id);
      CREATE INDEX IF NOT EXISTS idx_prescriptions_eleveur_id ON prescriptions(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_prescriptions_status ON prescriptions(status);

      CREATE INDEX IF NOT EXISTS idx_consultations_veterinaire_id ON consultations(veterinaire_id);
      CREATE INDEX IF NOT EXISTS idx_consultations_eleveur_id ON consultations(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_consultations_date ON consultations(date_consultation);

      CREATE INDEX IF NOT EXISTS idx_translations_language ON translations(language);
      CREATE INDEX IF NOT EXISTS idx_translations_category ON translations(category);
      CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key);

      CREATE INDEX IF NOT EXISTS idx_volailles_eleveur_id ON volailles(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_volailles_type ON volailles(type);
      CREATE INDEX IF NOT EXISTS idx_volailles_status ON volailles(status);

      CREATE INDEX IF NOT EXISTS idx_poussins_eleveur_id ON poussins(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_poussins_lot_id ON poussins(lot_id);
      CREATE INDEX IF NOT EXISTS idx_poussins_statut ON poussins(statut);

      CREATE INDEX IF NOT EXISTS idx_production_oeufs_eleveur_id ON production_oeufs(eleveur_id);
      CREATE INDEX IF NOT EXISTS idx_production_oeufs_date ON production_oeufs(date_collecte);

      CREATE INDEX IF NOT EXISTS idx_eleveurs_status ON eleveurs(status);
      CREATE INDEX IF NOT EXISTS idx_eleveurs_veterinaire_id ON eleveurs(veterinaire_id)
    `);
    console.log('✅ Index créés');

    console.log('🎉 Toutes les tables manquantes ont été créées avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors de la création des tables:', error);
    throw error;
  }
}

// Exporter la fonction pour l'utiliser dans d'autres fichiers
module.exports = { createMissingTables };

// Exécuter le script si appelé directement
if (require.main === module) {
  createMissingTables()
    .then(() => {
      console.log('✅ Script terminé avec succès');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur dans le script:', error);
      process.exit(1);
    });
}
