-- Migration script to add missing tables for enhanced dashboards
-- This script should be run to create the missing tables referenced in the enhanced routes

-- Create ENUM types first
DO $$ BEGIN
    CREATE TYPE enum_alertes_stock_type_alerte AS ENUM ('stock_faible', 'rupture_stock', 'sante_animaux', 'mortalite_elevee', 'maladie_detectee', 'maintenance_equipement', 'alerte_meteo', 'rappel_vaccination', 'expiration_medicament');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE enum_alertes_stock_priorite AS ENUM ('critique', 'haute', 'normale', 'faible');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE enum_alertes_stock_statut AS ENUM ('active', 'traitee', 'ignoree', 'expiree');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Table fermes (farms)
CREATE TABLE IF NOT EXISTS "public"."fermes" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "nom" character varying(200) NOT NULL,
    "adresse" text,
    "superficie" numeric(10,2),
    "capacite_maximale" integer,
    "type_elevage" character varying(50),
    "coordonnees_gps" jsonb DEFAULT '{}',
    "equipements" jsonb DEFAULT '[]',
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table fermes_ouvriers (farm workers assignment)
CREATE TABLE IF NOT EXISTS "public"."fermes_ouvriers" (
    "id" SERIAL PRIMARY KEY,
    "ouvrier_id" integer NOT NULL,
    "ferme_id" integer NOT NULL,
    "date_assignation" timestamptz DEFAULT NOW(),
    "permissions" jsonb DEFAULT '{"lecture": true, "ecriture": true, "saisie_quotidienne": true}',
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW(),
    UNIQUE("ouvrier_id", "ferme_id")
);

-- Table saisies_quotidiennes (daily data entry)
CREATE TABLE IF NOT EXISTS "public"."saisies_quotidiennes" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "ouvrier_id" integer NOT NULL,
    "ferme_id" integer,
    "volaille_id" integer,
    "date_saisie" date NOT NULL,
    "nombre_morts" integer DEFAULT 0,
    "nombre_malades" integer DEFAULT 0,
    "temperature_moyenne" numeric(5,2),
    "humidite_moyenne" numeric(5,2),
    "consommation_eau" numeric(10,2),
    "consommation_aliment" numeric(10,2),
    "incidents" text,
    "besoins_materiels" text,
    "observations" text,
    "donnees_supplementaires" jsonb DEFAULT '{}',
    "valide_par_eleveur" boolean DEFAULT false,
    "date_validation" timestamptz,
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table products (for marchands)
CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" SERIAL PRIMARY KEY,
    "marchand_id" integer NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" text,
    "category" character varying(100),
    "price" numeric(12,2) NOT NULL,
    "stock_quantity" integer DEFAULT 0,
    "stock_alert_threshold" integer DEFAULT 10,
    "unit" character varying(50) DEFAULT 'piece',
    "sku" character varying(100),
    "image_url" character varying(500),
    "specifications" jsonb DEFAULT '{}',
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table orders (for marchands)
CREATE TABLE IF NOT EXISTS "public"."orders" (
    "id" SERIAL PRIMARY KEY,
    "marchand_id" integer NOT NULL,
    "user_id" integer,
    "order_number" character varying(50) UNIQUE,
    "status" character varying(20) DEFAULT 'pending',
    "total_amount" numeric(12,2) NOT NULL,
    "shipping_address" jsonb DEFAULT '{}',
    "billing_address" jsonb DEFAULT '{}',
    "payment_method" character varying(50),
    "payment_status" character varying(20) DEFAULT 'pending',
    "notes" text,
    "rating" integer CHECK (rating >= 1 AND rating <= 5),
    "review" text,
    "shipped_at" timestamptz,
    "delivered_at" timestamptz,
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table order_items (order line items)
CREATE TABLE IF NOT EXISTS "public"."order_items" (
    "id" SERIAL PRIMARY KEY,
    "order_id" integer NOT NULL,
    "product_id" integer NOT NULL,
    "quantity" integer NOT NULL,
    "unit_price" numeric(12,2) NOT NULL,
    "total_price" numeric(12,2) NOT NULL,
    "product_snapshot" jsonb DEFAULT '{}',
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table consultations (for veterinaires)
CREATE TABLE IF NOT EXISTS "public"."consultations" (
    "id" SERIAL PRIMARY KEY,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "date_consultation" timestamptz NOT NULL,
    "motif" text,
    "diagnostic" text,
    "traitement_recommande" text,
    "notes" text,
    "status" character varying(20) DEFAULT 'programmee',
    "urgence" boolean DEFAULT false,
    "duree_estimee" integer,
    "cout" numeric(10,2),
    "note_satisfaction" integer CHECK (note_satisfaction >= 1 AND note_satisfaction <= 5),
    "commentaire_eleveur" text,
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table prescriptions (for veterinaires)
CREATE TABLE IF NOT EXISTS "public"."prescriptions" (
    "id" SERIAL PRIMARY KEY,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "volaille_id" integer,
    "consultation_id" integer,
    "medicament" character varying(255) NOT NULL,
    "dosage" character varying(255),
    "duree_traitement" character varying(100),
    "instructions" text,
    "diagnostic" text,
    "status" character varying(20) DEFAULT 'active',
    "date_debut" date,
    "date_fin" date,
    "effets_secondaires" text,
    "contre_indications" text,
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Table production_oeufs (egg production tracking)
CREATE TABLE IF NOT EXISTS "public"."production_oeufs" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "ferme_id" integer,
    "volaille_id" integer,
    "date_production" date NOT NULL,
    "quantite_oeufs" integer NOT NULL,
    "taux_ponte" numeric(5,2),
    "qualite_moyenne" character varying(50),
    "oeufs_casses" integer DEFAULT 0,
    "oeufs_doubles" integer DEFAULT 0,
    "poids_moyen" numeric(6,2),
    "observations" text,
    "conditions_environnement" jsonb DEFAULT '{}',
    "created_at" timestamptz NOT NULL DEFAULT NOW(),
    "updated_at" timestamptz NOT NULL DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_fermes_eleveur_id" ON "public"."fermes" ("eleveur_id");
CREATE INDEX IF NOT EXISTS "idx_fermes_ouvriers_ouvrier_id" ON "public"."fermes_ouvriers" ("ouvrier_id");
CREATE INDEX IF NOT EXISTS "idx_fermes_ouvriers_ferme_id" ON "public"."fermes_ouvriers" ("ferme_id");
CREATE INDEX IF NOT EXISTS "idx_saisies_quotidiennes_eleveur_id" ON "public"."saisies_quotidiennes" ("eleveur_id");
CREATE INDEX IF NOT EXISTS "idx_saisies_quotidiennes_date_saisie" ON "public"."saisies_quotidiennes" ("date_saisie");
CREATE INDEX IF NOT EXISTS "idx_saisies_quotidiennes_ouvrier_id" ON "public"."saisies_quotidiennes" ("ouvrier_id");
CREATE INDEX IF NOT EXISTS "idx_products_marchand_id" ON "public"."products" ("marchand_id");
CREATE INDEX IF NOT EXISTS "idx_products_category" ON "public"."products" ("category");
CREATE INDEX IF NOT EXISTS "idx_products_status" ON "public"."products" ("status");
CREATE INDEX IF NOT EXISTS "idx_orders_marchand_id" ON "public"."orders" ("marchand_id");
CREATE INDEX IF NOT EXISTS "idx_orders_status" ON "public"."orders" ("status");
CREATE INDEX IF NOT EXISTS "idx_orders_created_at" ON "public"."orders" ("created_at");
CREATE INDEX IF NOT EXISTS "idx_order_items_order_id" ON "public"."order_items" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_order_items_product_id" ON "public"."order_items" ("product_id");
CREATE INDEX IF NOT EXISTS "idx_consultations_veterinaire_id" ON "public"."consultations" ("veterinaire_id");
CREATE INDEX IF NOT EXISTS "idx_consultations_eleveur_id" ON "public"."consultations" ("eleveur_id");
CREATE INDEX IF NOT EXISTS "idx_consultations_date" ON "public"."consultations" ("date_consultation");
CREATE INDEX IF NOT EXISTS "idx_prescriptions_veterinaire_id" ON "public"."prescriptions" ("veterinaire_id");
CREATE INDEX IF NOT EXISTS "idx_prescriptions_eleveur_id" ON "public"."prescriptions" ("eleveur_id");
CREATE INDEX IF NOT EXISTS "idx_prescriptions_status" ON "public"."prescriptions" ("status");
CREATE INDEX IF NOT EXISTS "idx_production_oeufs_eleveur_id" ON "public"."production_oeufs" ("eleveur_id");
CREATE INDEX IF NOT EXISTS "idx_production_oeufs_date" ON "public"."production_oeufs" ("date_production");

-- Add foreign key constraints (optional, but recommended)
-- Note: These assume the referenced tables exist
-- ALTER TABLE "public"."fermes" ADD CONSTRAINT "fk_fermes_eleveur" FOREIGN KEY ("eleveur_id") REFERENCES "public"."eleveurs" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."fermes_ouvriers" ADD CONSTRAINT "fk_fermes_ouvriers_ouvrier" FOREIGN KEY ("ouvrier_id") REFERENCES "public"."Users" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."fermes_ouvriers" ADD CONSTRAINT "fk_fermes_ouvriers_ferme" FOREIGN KEY ("ferme_id") REFERENCES "public"."fermes" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."saisies_quotidiennes" ADD CONSTRAINT "fk_saisies_eleveur" FOREIGN KEY ("eleveur_id") REFERENCES "public"."eleveurs" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."saisies_quotidiennes" ADD CONSTRAINT "fk_saisies_ouvrier" FOREIGN KEY ("ouvrier_id") REFERENCES "public"."Users" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."products" ADD CONSTRAINT "fk_products_marchand" FOREIGN KEY ("marchand_id") REFERENCES "public"."Users" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."orders" ADD CONSTRAINT "fk_orders_marchand" FOREIGN KEY ("marchand_id") REFERENCES "public"."Users" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."order_items" ADD CONSTRAINT "fk_order_items_order" FOREIGN KEY ("order_id") REFERENCES "public"."orders" ("id") ON DELETE CASCADE;
-- ALTER TABLE "public"."order_items" ADD CONSTRAINT "fk_order_items_product" FOREIGN KEY ("product_id") REFERENCES "public"."products" ("id") ON DELETE CASCADE;

-- Generate order numbers automatically
CREATE OR REPLACE FUNCTION generate_order_number() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(NEW.id::text, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for order number generation
DROP TRIGGER IF EXISTS trigger_generate_order_number ON "public"."orders";
CREATE TRIGGER trigger_generate_order_number
    BEFORE INSERT ON "public"."orders"
    FOR EACH ROW
    EXECUTE FUNCTION generate_order_number();

-- Update order_items total_price automatically
CREATE OR REPLACE FUNCTION calculate_order_item_total() RETURNS TRIGGER AS $$
BEGIN
    NEW.total_price := NEW.quantity * NEW.unit_price;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for order item total calculation
DROP TRIGGER IF EXISTS trigger_calculate_order_item_total ON "public"."order_items";
CREATE TRIGGER trigger_calculate_order_item_total
    BEFORE INSERT OR UPDATE ON "public"."order_items"
    FOR EACH ROW
    EXECUTE FUNCTION calculate_order_item_total();

-- Comments for documentation
COMMENT ON TABLE "public"."fermes" IS 'Table des fermes gérées par les éleveurs';
COMMENT ON TABLE "public"."fermes_ouvriers" IS 'Association entre ouvriers et fermes';
COMMENT ON TABLE "public"."saisies_quotidiennes" IS 'Saisies quotidiennes effectuées par les ouvriers';
COMMENT ON TABLE "public"."products" IS 'Produits vendus par les marchands';
COMMENT ON TABLE "public"."orders" IS 'Commandes passées aux marchands';
COMMENT ON TABLE "public"."order_items" IS 'Articles dans les commandes';
COMMENT ON TABLE "public"."consultations" IS 'Consultations vétérinaires';
COMMENT ON TABLE "public"."prescriptions" IS 'Prescriptions médicales pour les animaux';
COMMENT ON TABLE "public"."production_oeufs" IS 'Suivi de la production d\'œufs';

