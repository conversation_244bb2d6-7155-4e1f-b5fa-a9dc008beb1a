const express = require('express');
const router = express.Router();
const {
  getStatistics,
  getUserStatistics,
  getTransactionStatistics
} = require('../controllers/adminStatisticsController');
const { auth, checkRole } = require('../middleware/auth');

// Routes protégées pour les statistiques admin
router.get('/statistics',
  auth,
  checkRole(['admin']),
  getStatistics
);

// Route pour les statistiques détaillées des utilisateurs
router.get('/statistics/users',
  auth,
  checkRole(['admin']),
  getUserStatistics
);

// Route pour les statistiques détaillées des transactions
router.get('/statistics/transactions',
  auth,
  checkRole(['admin']),
  getTransactionStatistics
);

module.exports = router;
