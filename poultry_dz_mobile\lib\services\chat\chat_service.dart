import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../models/chat/chat_thread.dart';
import '../../models/chat/chat_message.dart';
import '../api_service.dart';
import '../storage_service.dart';
import '../connectivity_service.dart';
import '../notification_service.dart';

class ChatService {
  final ApiService _apiService;
  final StorageService _storageService;
  final ConnectivityService _connectivityService;
  final NotificationService _notificationService;
  
  static const String _chatThreadsCacheKey = 'chat_threads';
  static const String _chatMessagesCacheKey = 'chat_messages';
  static const String _pendingMessagesCacheKey = 'pending_messages';
  static const Duration _cacheExpiry = Duration(hours: 24);

  ChatService({
    required ApiService apiService,
    required StorageService storageService,
    required ConnectivityService connectivityService,
    required NotificationService notificationService,
  }) : _apiService = apiService,
       _storageService = storageService,
       _connectivityService = connectivityService,
       _notificationService = notificationService;

  // Get all chat threads for the current user
  Future<List<ChatThread>> getChatThreads({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      // Check connectivity
      final isConnected = await _connectivityService.isConnected();
      
      if (!isConnected) {
        // Return cached data if offline
        return await _getCachedChatThreads();
      }

      // Build query parameters
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;

      final response = await _apiService.get('/chat/threads', queryParameters: queryParams);
      
      final threads = (response.data['threads'] as List)
          .map((json) => ChatThread.fromJson(json))
          .toList();

      // Cache the results
      if (page == 1) {
        await _cacheChatThreads(threads);
      }

      return threads;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching chat threads: $e');
      }
      // Return cached data on error
      return await _getCachedChatThreads();
    }
  }

  // Get a specific chat thread by ID
  Future<ChatThread?> getChatThread(String threadId) async {
    try {
      final response = await _apiService.get('/chat/threads/$threadId');
      return ChatThread.fromJson(response.data['thread']);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching chat thread $threadId: $e');
      }
      return null;
    }
  }

  // Create a new chat thread for a listing
  Future<ChatThread?> createChatThread({
    required int listingId,
    required String sellerId,
    String? initialMessage,
  }) async {
    try {
      final data = <String, dynamic>{
        'listing_id': listingId,
        'seller_id': sellerId,
      };

      if (initialMessage != null && initialMessage.isNotEmpty) {
        data['initial_message'] = initialMessage;
      }

      final response = await _apiService.post('/chat/threads', data: data);
      
      final thread = ChatThread.fromJson(response.data['thread']);
      
      // Update cache
      await _updateThreadInCache(thread);
      
      return thread;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating chat thread: $e');
      }
      return null;
    }
  }

  // Get messages for a specific thread
  Future<List<ChatMessage>> getMessages({
    required String threadId,
    int page = 1,
    int limit = 50,
    DateTime? before,
    DateTime? after,
  }) async {
    try {
      // Check connectivity
      final isConnected = await _connectivityService.isConnected();
      
      if (!isConnected) {
        // Return cached messages if offline
        return await _getCachedMessages(threadId);
      }

      // Build query parameters
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (before != null) queryParams['before'] = before.toIso8601String();
      if (after != null) queryParams['after'] = after.toIso8601String();

      final response = await _apiService.get('/chat/threads/$threadId/messages', queryParameters: queryParams);
      
      final messages = (response.data['messages'] as List)
          .map((json) => ChatMessage.fromJson(json))
          .toList();

      // Cache the messages
      if (page == 1) {
        await _cacheMessages(threadId, messages);
      }

      return messages;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching messages for thread $threadId: $e');
      }
      // Return cached messages on error
      return await _getCachedMessages(threadId);
    }
  }

  // Send a text message
  Future<ChatMessage?> sendMessage({
    required String threadId,
    required String content,
    String? replyToMessageId,
  }) async {
    try {
      final isConnected = await _connectivityService.isConnected();
      
      // Create message object
      final message = MessageTemplates.createTextMessage(
        threadId: threadId,
        senderId: '', // Will be set by backend
        content: content,
        replyToMessageId: replyToMessageId,
      );

      if (!isConnected) {
        // Save as pending message for later sync
        return await _savePendingMessage(message);
      }

      final data = <String, dynamic>{
        'content': content,
        'type': 'text',
      };

      if (replyToMessageId != null) {
        data['reply_to_message_id'] = replyToMessageId;
      }

      final response = await _apiService.post('/chat/threads/$threadId/messages', data: data);
      
      final sentMessage = ChatMessage.fromJson(response.data['message']);
      
      // Update cache
      await _addMessageToCache(threadId, sentMessage);
      
      return sentMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error sending message: $e');
      }
      // Save as pending message on error
      final message = MessageTemplates.createTextMessage(
        threadId: threadId,
        senderId: '',
        content: content,
        replyToMessageId: replyToMessageId,
      );
      return await _savePendingMessage(message);
    }
  }

  // Send an image message
  Future<ChatMessage?> sendImageMessage({
    required String threadId,
    required File imageFile,
    String? caption,
  }) async {
    try {
      final isConnected = await _connectivityService.isConnected();
      
      if (!isConnected) {
        // Save as pending message with local file path
        final message = MessageTemplates.createImageMessage(
          threadId: threadId,
          senderId: '',
          imageUrl: imageFile.path,
          caption: caption,
        );
        return await _savePendingMessage(message);
      }

      // Prepare form data
      final formData = FormData.fromMap({
        'type': 'image',
        'image': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'image.jpg',
        ),
      });

      if (caption != null && caption.isNotEmpty) {
        formData.fields.add(MapEntry('caption', caption));
      }

      final response = await _apiService.post('/chat/threads/$threadId/messages', data: formData);
      
      final sentMessage = ChatMessage.fromJson(response.data['message']);
      
      // Update cache
      await _addMessageToCache(threadId, sentMessage);
      
      return sentMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error sending image message: $e');
      }
      // Save as pending message on error
      final message = MessageTemplates.createImageMessage(
        threadId: threadId,
        senderId: '',
        imageUrl: imageFile.path,
        caption: caption,
      );
      return await _savePendingMessage(message);
    }
  }

  // Send an offer message
  Future<ChatMessage?> sendOfferMessage({
    required String threadId,
    required double price,
    required int quantity,
    DateTime? expiryDate,
    String? notes,
  }) async {
    try {
      final data = <String, dynamic>{
        'type': 'offer',
        'offer_price': price,
        'offer_quantity': quantity,
      };

      if (expiryDate != null) {
        data['offer_expiry'] = expiryDate.toIso8601String();
      }

      if (notes != null && notes.isNotEmpty) {
        data['content'] = notes;
      }

      final response = await _apiService.post('/chat/threads/$threadId/messages', data: data);
      
      final sentMessage = ChatMessage.fromJson(response.data['message']);
      
      // Update cache
      await _addMessageToCache(threadId, sentMessage);
      
      return sentMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error sending offer message: $e');
      }
      return null;
    }
  }

  // Send location message
  Future<ChatMessage?> sendLocationMessage({
    required String threadId,
    required double latitude,
    required double longitude,
    String? locationName,
  }) async {
    try {
      final data = <String, dynamic>{
        'type': 'location',
        'location_latitude': latitude,
        'location_longitude': longitude,
      };

      if (locationName != null && locationName.isNotEmpty) {
        data['location_name'] = locationName;
      }

      final response = await _apiService.post('/chat/threads/$threadId/messages', data: data);
      
      final sentMessage = ChatMessage.fromJson(response.data['message']);
      
      // Update cache
      await _addMessageToCache(threadId, sentMessage);
      
      return sentMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error sending location message: $e');
      }
      return null;
    }
  }

  // Respond to an offer
  Future<ChatMessage?> respondToOffer({
    required String messageId,
    required String response, // 'accept', 'reject', 'counter'
    double? counterPrice,
    int? counterQuantity,
    String? notes,
  }) async {
    try {
      final data = <String, dynamic>{
        'response': response,
      };

      if (counterPrice != null) data['counter_price'] = counterPrice;
      if (counterQuantity != null) data['counter_quantity'] = counterQuantity;
      if (notes != null) data['notes'] = notes;

      final response_data = await _apiService.post('/chat/messages/$messageId/respond', data: data);
      
      final responseMessage = ChatMessage.fromJson(response_data.data['message']);
      
      // Update cache
      await _addMessageToCache(responseMessage.threadId, responseMessage);
      
      return responseMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error responding to offer: $e');
      }
      return null;
    }
  }

  // Mark messages as read
  Future<bool> markMessagesAsRead(String threadId, List<String> messageIds) async {
    try {
      await _apiService.post('/chat/threads/$threadId/read', data: {
        'message_ids': messageIds,
      });
      
      // Update local cache
      await _markMessagesAsReadInCache(threadId, messageIds);
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error marking messages as read: $e');
      }
      return false;
    }
  }

  // Set typing indicator
  Future<void> setTypingIndicator(String threadId, bool isTyping) async {
    try {
      await _apiService.post('/chat/threads/$threadId/typing', data: {
        'is_typing': isTyping,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error setting typing indicator: $e');
      }
    }
  }

  // Archive a chat thread
  Future<bool> archiveThread(String threadId) async {
    try {
      await _apiService.patch('/chat/threads/$threadId', data: {
        'status': 'archived',
      });
      
      // Update cache
      await _updateThreadStatusInCache(threadId, 'archived');
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error archiving thread: $e');
      }
      return false;
    }
  }

  // Block a user in a thread
  Future<bool> blockUser(String threadId, String userId) async {
    try {
      await _apiService.post('/chat/threads/$threadId/block', data: {
        'user_id': userId,
      });
      
      // Update thread status in cache
      await _updateThreadStatusInCache(threadId, 'blocked');
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error blocking user: $e');
      }
      return false;
    }
  }

  // Report a message or thread
  Future<bool> reportContent({
    required String threadId,
    String? messageId,
    required String reason,
    String? description,
  }) async {
    try {
      final data = <String, dynamic>{
        'thread_id': threadId,
        'reason': reason,
      };

      if (messageId != null) data['message_id'] = messageId;
      if (description != null) data['description'] = description;

      await _apiService.post('/chat/report', data: data);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error reporting content: $e');
      }
      return false;
    }
  }

  // Get pending messages for sync
  Future<List<ChatMessage>> getPendingMessages() async {
    try {
      final pendingData = await _storageService.getList(_pendingMessagesCacheKey);
      return pendingData.map((json) => ChatMessage.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting pending messages: $e');
      }
      return [];
    }
  }

  // Sync pending messages when online
  Future<void> syncPendingMessages() async {
    try {
      final isConnected = await _connectivityService.isConnected();
      if (!isConnected) return;

      final pendingMessages = await getPendingMessages();
      
      for (final message in pendingMessages) {
        try {
          if (message.type == MessageType.text) {
            await sendMessage(
              threadId: message.threadId,
              content: message.content,
              replyToMessageId: message.replyToMessageId,
            );
          } else if (message.type == MessageType.image && message.attachmentTempPath != null) {
            await sendImageMessage(
              threadId: message.threadId,
              imageFile: File(message.attachmentTempPath!),
              caption: message.content.isNotEmpty ? message.content : null,
            );
          }
          
          // Remove from pending after successful sync
          await _removePendingMessage(message.localId ?? '');
        } catch (e) {
          if (kDebugMode) {
            print('Error syncing pending message: $e');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing pending messages: $e');
      }
    }
  }

  // Cache management methods
  Future<void> _cacheChatThreads(List<ChatThread> threads) async {
    try {
      final cacheData = {
        'threads': threads.map((t) => t.toJson()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _storageService.setMap(_chatThreadsCacheKey, cacheData);
    } catch (e) {
      if (kDebugMode) {
        print('Error caching chat threads: $e');
      }
    }
  }

  Future<List<ChatThread>> _getCachedChatThreads() async {
    try {
      final cacheData = await _storageService.getMap(_chatThreadsCacheKey);
      if (cacheData == null) return [];

      final timestamp = cacheData['timestamp'] as int?;
      if (timestamp != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        if (cacheAge > _cacheExpiry.inMilliseconds) {
          return []; // Cache expired
        }
      }

      final threads = (cacheData['threads'] as List)
          .map((json) => ChatThread.fromJson(json))
          .toList();
      
      return threads;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached chat threads: $e');
      }
      return [];
    }
  }

  Future<void> _cacheMessages(String threadId, List<ChatMessage> messages) async {
    try {
      final cacheKey = '${_chatMessagesCacheKey}_$threadId';
      final cacheData = {
        'messages': messages.map((m) => m.toJson()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _storageService.setMap(cacheKey, cacheData);
    } catch (e) {
      if (kDebugMode) {
        print('Error caching messages: $e');
      }
    }
  }

  Future<List<ChatMessage>> _getCachedMessages(String threadId) async {
    try {
      final cacheKey = '${_chatMessagesCacheKey}_$threadId';
      final cacheData = await _storageService.getMap(cacheKey);
      if (cacheData == null) return [];

      final messages = (cacheData['messages'] as List)
          .map((json) => ChatMessage.fromJson(json))
          .toList();
      
      return messages;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached messages: $e');
      }
      return [];
    }
  }

  Future<void> _updateThreadInCache(ChatThread thread) async {
    try {
      final cachedThreads = await _getCachedChatThreads();
      final index = cachedThreads.indexWhere((t) => t.id == thread.id);
      
      if (index != -1) {
        cachedThreads[index] = thread;
      } else {
        cachedThreads.insert(0, thread);
      }
      
      await _cacheChatThreads(cachedThreads);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating thread in cache: $e');
      }
    }
  }

  Future<void> _addMessageToCache(String threadId, ChatMessage message) async {
    try {
      final cachedMessages = await _getCachedMessages(threadId);
      cachedMessages.add(message);
      await _cacheMessages(threadId, cachedMessages);
    } catch (e) {
      if (kDebugMode) {
        print('Error adding message to cache: $e');
      }
    }
  }

  Future<void> _markMessagesAsReadInCache(String threadId, List<String> messageIds) async {
    try {
      final cachedMessages = await _getCachedMessages(threadId);
      
      for (final message in cachedMessages) {
        if (messageIds.contains(message.id)) {
          // Update read status - would need to modify ChatMessage to be mutable or create new instance
        }
      }
      
      await _cacheMessages(threadId, cachedMessages);
    } catch (e) {
      if (kDebugMode) {
        print('Error marking messages as read in cache: $e');
      }
    }
  }

  Future<void> _updateThreadStatusInCache(String threadId, String status) async {
    try {
      final cachedThreads = await _getCachedChatThreads();
      final index = cachedThreads.indexWhere((t) => t.id == threadId);
      
      if (index != -1) {
        // Update thread status - would need to modify ChatThread or create new instance
        await _cacheChatThreads(cachedThreads);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating thread status in cache: $e');
      }
    }
  }

  Future<ChatMessage> _savePendingMessage(ChatMessage message) async {
    try {
      final localId = DateTime.now().millisecondsSinceEpoch.toString();
      final pendingMessage = message.copyWith(
        localId: localId,
        syncStatus: 'pending',
        createdAt: DateTime.now(),
      );

      final pendingMessages = await getPendingMessages();
      pendingMessages.add(pendingMessage);
      
      await _storageService.setList(
        _pendingMessagesCacheKey,
        pendingMessages.map((m) => m.toJson()).toList(),
      );

      return pendingMessage;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving pending message: $e');
      }
      rethrow;
    }
  }

  Future<void> _removePendingMessage(String localId) async {
    try {
      final pendingMessages = await getPendingMessages();
      pendingMessages.removeWhere((m) => m.localId == localId);
      
      await _storageService.setList(
        _pendingMessagesCacheKey,
        pendingMessages.map((m) => m.toJson()).toList(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error removing pending message: $e');
      }
    }
  }

  // Clear all chat caches
  Future<void> clearCache() async {
    try {
      await _storageService.remove(_chatThreadsCacheKey);
      await _storageService.remove(_pendingMessagesCacheKey);
      
      // Clear all message caches
      final keys = await _storageService.getAllKeys();
      final messageKeys = keys.where((key) => key.startsWith(_chatMessagesCacheKey)).toList();
      
      for (final key in messageKeys) {
        await _storageService.remove(key);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing chat cache: $e');
      }
    }
  }
}