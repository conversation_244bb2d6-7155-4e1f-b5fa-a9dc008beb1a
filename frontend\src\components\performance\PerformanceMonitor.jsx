import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Button,
  Collapse,
  IconButton
} from '@mui/material';
import {
  Speed,
  Memory,
  NetworkCheck,
  Warning,
  CheckCircle,
  Error,
  ExpandMore,
  ExpandLess,
  Refresh
} from '@mui/icons-material';
import { useGlobalPerformance } from '../../hooks/usePerformance';

/**
 * Composant de surveillance des performances
 */
const PerformanceMonitor = ({ 
  variant = 'detailed', // 'minimal' | 'detailed' | 'dashboard'
  autoRefresh = true,
  refreshInterval = 10000
}) => {
  const {
    isMonitoring,
    stats,
    recommendations,
    startMonitoring,
    stopMonitoring,
    generateReport
  } = useGlobalPerformance({
    autoStart: true,
    reportInterval: refreshInterval
  });

  const [expanded, setExpanded] = useState(false);

  // Formater les valeurs de performance
  const formatValue = (value, type) => {
    if (typeof value !== 'number') return 'N/A';
    
    switch (type) {
      case 'time':
        return `${value.toFixed(2)}ms`;
      case 'memory':
        return `${(value / 1024 / 1024).toFixed(2)}MB`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toFixed(2);
    }
  };

  // Obtenir la couleur selon la performance
  const getPerformanceColor = (value, threshold, inverted = false) => {
    if (typeof value !== 'number') return 'default';
    
    const isGood = inverted ? value < threshold : value > threshold;
    if (isGood) return 'success';
    if (value > threshold * 0.8) return 'warning';
    return 'error';
  };

  // Obtenir l'icône selon le type de métrique
  const getMetricIcon = (type) => {
    switch (type) {
      case 'render_time':
      case 'api_response_time':
        return <Speed />;
      case 'memory_usage':
        return <Memory />;
      case 'network':
        return <NetworkCheck />;
      default:
        return <Speed />;
    }
  };

  if (variant === 'minimal') {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Chip
          icon={isMonitoring ? <CheckCircle /> : <Error />}
          label={isMonitoring ? 'Monitoring' : 'Arrêté'}
          color={isMonitoring ? 'success' : 'error'}
          size="small"
        />
        
        {stats && stats.renders && (
          <Chip
            label={`${formatValue(stats.renders.averageDuration, 'time')}`}
            color={getPerformanceColor(stats.renders.averageDuration, 16, true)}
            size="small"
          />
        )}
        
        {recommendations.length > 0 && (
          <Chip
            icon={<Warning />}
            label={`${recommendations.length} alertes`}
            color="warning"
            size="small"
          />
        )}
      </Box>
    );
  }

  if (variant === 'dashboard') {
    return (
      <Paper elevation={2} sx={{ p: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            📊 Performances
          </Typography>
          
          <Box display="flex" gap={1}>
            <Button
              size="small"
              onClick={generateReport}
              startIcon={<Refresh />}
            >
              Actualiser
            </Button>
            
            <Button
              size="small"
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              color={isMonitoring ? 'error' : 'success'}
            >
              {isMonitoring ? 'Arrêter' : 'Démarrer'}
            </Button>
          </Box>
        </Box>

        <Grid container spacing={2}>
          {/* Métriques principales */}
          {stats && (
            <>
              {stats.renders && (
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color={getPerformanceColor(stats.renders.averageDuration, 16, true)}>
                      {formatValue(stats.renders.averageDuration, 'time')}
                    </Typography>
                    <Typography variant="caption">
                      Temps de rendu moyen
                    </Typography>
                  </Box>
                </Grid>
              )}

              {stats.memory_usage && (
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color={getPerformanceColor(stats.memory_usage.current, 50 * 1024 * 1024)}>
                      {formatValue(stats.memory_usage.current, 'memory')}
                    </Typography>
                    <Typography variant="caption">
                      Mémoire utilisée
                    </Typography>
                  </Box>
                </Grid>
              )}

              {stats.apis && Object.keys(stats.apis).length > 0 && (
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="info">
                      {Object.keys(stats.apis).length}
                    </Typography>
                    <Typography variant="caption">
                      APIs surveillées
                    </Typography>
                  </Box>
                </Grid>
              )}

              <Grid item xs={12} sm={6} md={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color={recommendations.length > 0 ? 'warning' : 'success'}>
                    {recommendations.length}
                  </Typography>
                  <Typography variant="caption">
                    Recommandations
                  </Typography>
                </Box>
              </Grid>
            </>
          )}
        </Grid>

        {/* Recommandations */}
        {recommendations.length > 0 && (
          <Box mt={2}>
            <Alert severity="warning">
              <Typography variant="subtitle2" gutterBottom>
                Optimisations recommandées:
              </Typography>
              <List dense>
                {recommendations.slice(0, 3).map((rec, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={rec.message}
                      secondary={`Priorité: ${rec.priority}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Alert>
          </Box>
        )}
      </Paper>
    );
  }

  // Variant 'detailed'
  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          🔍 Moniteur de Performance
        </Typography>
        
        <Box display="flex" gap={1} alignItems="center">
          <Chip
            icon={isMonitoring ? <CheckCircle /> : <Error />}
            label={isMonitoring ? 'Actif' : 'Inactif'}
            color={isMonitoring ? 'success' : 'error'}
            size="small"
          />
          
          <Button
            size="small"
            onClick={generateReport}
            startIcon={<Refresh />}
          >
            Rapport
          </Button>
          
          <Button
            size="small"
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
            color={isMonitoring ? 'error' : 'success'}
          >
            {isMonitoring ? 'Arrêter' : 'Démarrer'}
          </Button>
          
          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>
      </Box>

      {/* Métriques principales */}
      {stats && (
        <Grid container spacing={2} mb={2}>
          {Object.entries(stats).map(([key, value]) => {
            if (typeof value !== 'object' || !value.current) return null;
            
            return (
              <Grid item xs={12} sm={6} md={4} key={key}>
                <Box
                  p={2}
                  border={1}
                  borderColor="divider"
                  borderRadius={1}
                  textAlign="center"
                >
                  <Box display="flex" alignItems="center" justifyContent="center" mb={1}>
                    {getMetricIcon(key)}
                    <Typography variant="subtitle2" sx={{ ml: 1 }}>
                      {key.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </Box>
                  
                  <Typography variant="h6" color={getPerformanceColor(value.current, 100)}>
                    {formatValue(value.current, key.includes('memory') ? 'memory' : 'time')}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary">
                    Moy: {formatValue(value.average, key.includes('memory') ? 'memory' : 'time')}
                  </Typography>
                </Box>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Détails étendus */}
      <Collapse in={expanded}>
        {/* Recommandations */}
        {recommendations.length > 0 && (
          <Box mb={2}>
            <Typography variant="subtitle1" gutterBottom>
              🎯 Recommandations d'optimisation
            </Typography>
            
            <List>
              {recommendations.map((rec, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Warning color={rec.priority === 'high' ? 'error' : 'warning'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={rec.message}
                    secondary={
                      <Box>
                        <Typography variant="caption">
                          Priorité: {rec.priority} | Valeur: {formatValue(rec.value, 'time')}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Statistiques détaillées */}
        {stats && stats.apis && Object.keys(stats.apis).length > 0 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              🌐 Performance des APIs
            </Typography>
            
            <List>
              {Object.entries(stats.apis).map(([endpoint, apiStats]) => (
                <ListItem key={endpoint}>
                  <ListItemIcon>
                    <NetworkCheck color={getPerformanceColor(apiStats.averageTime, 1000, true)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={endpoint}
                    secondary={
                      <Box>
                        <Typography variant="caption">
                          {apiStats.calls} appels | Moy: {formatValue(apiStats.averageTime, 'time')} | 
                          Min: {formatValue(apiStats.minTime, 'time')} | 
                          Max: {formatValue(apiStats.maxTime, 'time')}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((apiStats.averageTime / 2000) * 100, 100)}
                          color={getPerformanceColor(apiStats.averageTime, 1000, true)}
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </Collapse>
    </Paper>
  );
};

export default PerformanceMonitor;
