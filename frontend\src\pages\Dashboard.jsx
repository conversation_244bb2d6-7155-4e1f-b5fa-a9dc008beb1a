import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Tabs,
  Tab,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import axiosInstance from '../utils/axiosConfig';

// Composant pour afficher un graphique amélioré avec animation
const EnhancedBarChart = ({ data, title, color }) => {
  const maxValue = Math.max(...data.map(item => item.value));
  const [animated, setAnimated] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setAnimated(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Card sx={{ height: '100%', boxShadow: 3, transition: 'all 0.3s ease' }}>
      <CardHeader
        title={title}
        titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
        sx={{ backgroundColor: 'rgba(0, 0, 0, 0.03)', pb: 1 }}
      />
      <Divider />
      <CardContent>
        {data.map((item, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="body2" fontWeight="medium">{item.label}</Typography>
              <Typography variant="body2" fontWeight="bold">{item.value}</Typography>
            </Box>
            <Box
              sx={{
                height: 12,
                width: '100%',
                bgcolor: 'grey.200',
                borderRadius: 6,
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  height: '100%',
                  width: animated ? `${(item.value / maxValue) * 100}%` : '0%',
                  bgcolor: color,
                  borderRadius: 6,
                  transition: 'width 1s ease-out',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                }}
              />
            </Box>
          </Box>
        ))}
      </CardContent>
    </Card>
  );
};

// Composant pour afficher une carte de statistique améliorée
const StatCard = ({ title, value, icon, color }) => {
  const [animated, setAnimated] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setAnimated(true), 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Card
      sx={{
        height: '100%',
        boxShadow: 3,
        transform: animated ? 'translateY(0)' : 'translateY(20px)',
        opacity: animated ? 1 : 0,
        transition: 'all 0.5s ease',
        '&:hover': {
          boxShadow: 6,
          transform: 'translateY(-5px)'
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                bgcolor: color,
                width: 56,
                height: 56,
                boxShadow: `0 4px 14px 0 ${color}40`
              }}
            >
              {icon}
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="subtitle1" component="div" color="text.secondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

// Composant pour afficher les dernières activités
const RecentActivities = ({ activities }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title="Activités Récentes" />
      <Divider />
      <CardContent sx={{ p: 0 }}>
        <List>
          {activities.map((activity, index) => (
            <React.Fragment key={index}>
              <ListItem alignItems="flex-start">
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: activity.color }}>{activity.icon}</Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={activity.title}
                  secondary={
                    <>
                      <Typography variant="body2" color="text.primary" component="span">
                        {activity.description}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        {activity.time}
                      </Typography>
                    </>
                  }
                />
              </ListItem>
              {index < activities.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

function Dashboard() {
  const [stats, setStats] = useState({
    totalEleveurs: 0,
    totalVolailles: 0,
    totalValeur: 0,
  });

  const [tabValue, setTabValue] = useState(0);
  const [volailles, setVolailles] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [filtreEspece, setFiltreEspece] = useState('');
  const [filtreRegion, setFiltreRegion] = useState('');
  const [filtreDate, setFiltreDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isExporting, setIsExporting] = useState(false);

  // Données simulées pour les graphiques
  const especesData = [
    { label: 'Poulets', value: 120 },
    { label: 'Dindes', value: 80 },
    { label: 'Canards', value: 40 },
    { label: 'Cailles', value: 25 },
  ];

  const regionsData = [
    { label: 'Alger', value: 45 },
    { label: 'Oran', value: 35 },
    { label: 'Constantine', value: 30 },
    { label: 'Annaba', value: 20 },
    { label: 'Autres', value: 50 },
  ];

  // Activités récentes simulées
  const recentActivities = [
    {
      title: 'Nouvel éleveur',
      description: 'Ahmed Benali a rejoint la plateforme',
      time: 'Il y a 2 heures',
      icon: <PersonIcon />,
      color: 'primary.main',
    },
    {
      title: 'Nouveau lot de volailles',
      description: '200 poulets ajoutés par Karim Hadj',
      time: 'Il y a 5 heures',
      icon: <PetsIcon />,
      color: 'secondary.main',
    },
    {
      title: 'Vente réalisée',
      description: 'Lot de 50 dindes vendu',
      time: 'Hier, 15:30',
      icon: <MoneyIcon />,
      color: 'success.main',
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [eleveursRes, volaillesRes] = await Promise.all([
          axiosInstance.get('/eleveurs'),
          axiosInstance.get('/volailles')
        ]);

        setEleveurs(eleveursRes.data);
        setVolailles(volaillesRes.data);

        // Calcul des statistiques
        const totalValeur = volaillesRes.data.reduce(
          (sum, volaille) => sum + (volaille.prix_unitaire * volaille.quantite), 0
        );

        setStats({
          totalEleveurs: eleveursRes.data.length,
          totalVolailles: volaillesRes.data.length,
          totalValeur: totalValeur,
        });
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
      }
    };

    fetchData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFiltreEspeceChange = (event) => {
    setFiltreEspece(event.target.value);
  };

  const handleFiltreRegionChange = (event) => {
    setFiltreRegion(event.target.value);
  };

  const handleFiltreSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleFiltreDateChange = (event) => {
    setFiltreDate(event.target.value);
  };

  // Fonction pour exporter les données en CSV
  const exportToCSV = (data, filename) => {
    setIsExporting(true);
    try {
      // Créer les en-têtes du CSV en fonction du type de données
      let headers = [];
      let csvContent = '';

      if (filename === 'volailles.csv') {
        headers = ['ID', 'Espèce', 'Race', 'Éleveur', 'Quantité', 'Prix Unitaire', 'Valeur Totale'];
        csvContent = headers.join(',') + '\n';

        data.forEach(item => {
          const row = [
            item.id,
            item.espece,
            item.race,
            `${item.eleveur_nom || ''} ${item.eleveur_prenom || ''}`,
            item.quantite,
            item.prix_unitaire,
            (item.prix_unitaire * item.quantite)
          ];
          csvContent += row.join(',') + '\n';
        });
      } else if (filename === 'eleveurs.csv') {
        headers = ['ID', 'Nom', 'Prénom', 'Email', 'Téléphone', 'Nombre de Lots'];
        csvContent = headers.join(',') + '\n';

        data.forEach(item => {
          const lotsCount = volailles.filter(v => v.eleveur_id === item.id).length;
          const row = [
            item.id,
            item.nom,
            item.prenom,
            item.email,
            item.telephone,
            lotsCount
          ];
          csvContent += row.join(',') + '\n';
        });
      }

      // Créer un objet Blob et un lien de téléchargement
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Erreur lors de l\'exportation:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Filtrer les volailles avec tous les filtres appliqués
  const volaillesFiltrees = volailles.filter(volaille => {
    const matchEspece = filtreEspece ? volaille.espece === filtreEspece : true;
    const matchRegion = filtreRegion ? volaille.region === filtreRegion : true;
    const matchSearch = searchTerm ?
      (volaille.espece?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       volaille.race?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       volaille.eleveur_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       volaille.eleveur_prenom?.toLowerCase().includes(searchTerm.toLowerCase()))
      : true;
    return matchEspece && matchRegion && matchSearch;
  });

  // Filtrer les éleveurs avec la recherche
  const eleveursFiltres = eleveurs.filter(eleveur => {
    return searchTerm ?
      (eleveur.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       eleveur.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       eleveur.email?.toLowerCase().includes(searchTerm.toLowerCase()))
      : true;
  });

  // Obtenir la liste des espèces uniques pour le filtre
  const especesUniques = [...new Set(volailles.map(volaille => volaille.espece))];

  // Obtenir la liste des régions uniques pour le filtre
  const regionsUniques = [...new Set(volailles.map(volaille => volaille.region).filter(Boolean))];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Tableau de Bord Administratif
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          Aperçu des statistiques et des activités de la plateforme
        </Typography>
      </Box>

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Éleveurs"
            value={stats.totalEleveurs}
            icon={<PersonIcon />}
            color="primary.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Lots de Volailles"
            value={stats.totalVolailles}
            icon={<PetsIcon />}
            color="secondary.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Valeur Totale"
            value={`${stats.totalValeur.toLocaleString()} DA`}
            icon={<MoneyIcon />}
            color="success.main"
          />
        </Grid>
      </Grid>

      {/* Graphiques et activités récentes */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <EnhancedBarChart
                data={especesData}
                title="Répartition par Espèce"
                color="primary.main"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <EnhancedBarChart
                data={regionsData}
                title="Répartition par Région"
                color="secondary.main"
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={4}>
          <RecentActivities activities={recentActivities} />
        </Grid>
      </Grid>

      {/* Onglets pour les données détaillées */}
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Volailles" icon={<PetsIcon />} />
          <Tab label="Éleveurs" icon={<PersonIcon />} />
        </Tabs>

        {/* Contenu de l'onglet Volailles */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Liste des Volailles</Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => exportToCSV(volaillesFiltrees, 'volailles.csv')}
                  disabled={isExporting}
                  startIcon={<TrendingUpIcon />}
                >
                  Exporter en CSV
                </Button>
              </Box>

              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="filtre-espece-label">Filtrer par espèce</InputLabel>
                    <Select
                      labelId="filtre-espece-label"
                      value={filtreEspece}
                      label="Filtrer par espèce"
                      onChange={handleFiltreEspeceChange}
                    >
                      <MenuItem value="">Toutes les espèces</MenuItem>
                      {especesUniques.map((espece, index) => (
                        <MenuItem key={index} value={espece}>{espece}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="filtre-region-label">Filtrer par région</InputLabel>
                    <Select
                      labelId="filtre-region-label"
                      value={filtreRegion}
                      label="Filtrer par région"
                      onChange={handleFiltreRegionChange}
                    >
                      <MenuItem value="">Toutes les régions</MenuItem>
                      {regionsUniques.map((region, index) => (
                        <MenuItem key={index} value={region}>{region}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Rechercher"
                    variant="outlined"
                    value={searchTerm}
                    onChange={handleFiltreSearchChange}
                    placeholder="Rechercher par espèce, race ou éleveur..."
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ color: 'action.active', mr: 1, my: 0.5 }}>
                          <NotificationsIcon />
                        </Box>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <TableContainer component={Paper} sx={{ boxShadow: 3, borderRadius: 2, overflow: 'hidden' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Espèce</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Race</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Éleveur</TableCell>
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>Quantité</TableCell>
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>Prix Unitaire</TableCell>
                    <TableCell align="right" sx={{ color: 'white', fontWeight: 'bold' }}>Valeur Totale</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {volaillesFiltrees.slice(0, 5).map((volaille, index) => (
                    <TableRow
                      key={volaille.id}
                      sx={{
                        '&:nth-of-type(odd)': { bgcolor: 'rgba(0, 0, 0, 0.03)' },
                        '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.07)' },
                        animation: 'fadeIn 0.5s ease-in-out',
                        animationDelay: `${index * 0.1}s`,
                        '@keyframes fadeIn': {
                          '0%': { opacity: 0, transform: 'translateY(10px)' },
                          '100%': { opacity: 1, transform: 'translateY(0)' }
                        }
                      }}
                    >
                      <TableCell>{volaille.espece}</TableCell>
                      <TableCell>{volaille.race}</TableCell>
                      <TableCell>{`${volaille.eleveur_nom || ''} ${volaille.eleveur_prenom || ''}`}</TableCell>
                      <TableCell align="right">{volaille.quantite}</TableCell>
                      <TableCell align="right">{`${volaille.prix_unitaire} DA`}</TableCell>
                      <TableCell align="right">{`${(volaille.prix_unitaire * volaille.quantite).toLocaleString()} DA`}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {volaillesFiltrees.length > 5 && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Button variant="outlined">Voir plus</Button>
              </Box>
            )}
          </Box>
        )}

        {/* Contenu de l'onglet Éleveurs */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Liste des Éleveurs</Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => exportToCSV(eleveursFiltres, 'eleveurs.csv')}
                  disabled={isExporting}
                  startIcon={<TrendingUpIcon />}
                >
                  Exporter en CSV
                </Button>
              </Box>

              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Rechercher"
                    variant="outlined"
                    value={searchTerm}
                    onChange={handleFiltreSearchChange}
                    placeholder="Rechercher par nom, prénom ou email..."
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ color: 'action.active', mr: 1, my: 0.5 }}>
                          <NotificationsIcon />
                        </Box>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nom</TableCell>
                    <TableCell>Prénom</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Téléphone</TableCell>
                    <TableCell align="right">Nombre de Lots</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {eleveursFiltres.slice(0, 5).map((eleveur) => {
                    const lotsCount = volailles.filter(v => v.eleveur_id === eleveur.id).length;
                    return (
                      <TableRow key={eleveur.id}>
                        <TableCell>{eleveur.nom}</TableCell>
                        <TableCell>{eleveur.prenom}</TableCell>
                        <TableCell>{eleveur.email}</TableCell>
                        <TableCell>{eleveur.telephone}</TableCell>
                        <TableCell align="right">{lotsCount}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            {eleveursFiltres.length > 5 && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Button variant="outlined">Voir plus</Button>
              </Box>
            )}
          </Box>
        )}
      </Paper>
    </Container>
  );
}

export default Dashboard;
