'use strict';

const { HomepageSection } = require('../models');

class HomepageSectionController {
  // Get all homepage sections
  async getAllSections(req, res) {
    try {
      const sections = await HomepageSection.findAll({
        where: { status: 'active' },
        order: [['order', 'ASC']]
      });
      res.json(sections);
    } catch (error) {
      console.error('Error getting homepage sections:', error);
      res.status(500).json({ error: 'Error retrieving homepage sections' });
    }
  }

  // Create a new section
  async createSection(req, res) {
    try {
      const { title, content } = req.body;
      // Get max order and increment by 1
      const maxOrder = await HomepageSection.max('order') || 0;

      const section = await HomepageSection.create({
        title,
        content,
        order: maxOrder + 1
      });

      res.status(201).json(section);
    } catch (error) {
      console.error('Error creating homepage section:', error);
      res.status(500).json({ error: 'Error creating homepage section' });
    }
  }

  // Update a section
  async updateSection(req, res) {
    try {
      const { id } = req.params;
      const { title, content, order } = req.body;

      const section = await HomepageSection.findByPk(id);
      if (!section) {
        return res.status(404).json({ error: 'Section not found' });
      }

      await section.update({ title, content, order });
      res.json(section);
    } catch (error) {
      console.error('Error updating homepage section:', error);
      res.status(500).json({ error: 'Error updating homepage section' });
    }
  }

  // Delete a section
  async deleteSection(req, res) {
    try {
      const { id } = req.params;

      const section = await HomepageSection.findByPk(id);
      if (!section) {
        return res.status(404).json({ error: 'Section not found' });
      }

      await section.update({ status: 'inactive' });
      // Alternatively, use hard delete:
      // await section.destroy();

      res.json({ message: 'Section deleted successfully' });
    } catch (error) {
      console.error('Error deleting homepage section:', error);
      res.status(500).json({ error: 'Error deleting homepage section' });
    }
  }

  // Update section orders
  async updateSectionOrders(req, res) {
    try {
      const { sections } = req.body;

      // Use transaction to ensure all updates succeed or none do
      await HomepageSection.sequelize.transaction(async transaction => {
        const updatePromises = sections.map((section, index) =>
          HomepageSection.update(
            { order: index },
            {
              where: { id: section.id },
              transaction
            }
          )
        );

        await Promise.all(updatePromises);
      });

      const updatedSections = await HomepageSection.findAll({
        where: { status: 'active' },
        order: [['order', 'ASC']]
      });

      res.json(updatedSections);
    } catch (error) {
      console.error('Error updating section orders:', error);
      res.status(500).json({ error: 'Error updating section orders' });
    }
  }
}

module.exports = new HomepageSectionController();
