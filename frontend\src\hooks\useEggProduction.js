import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const useEggProduction = () => {
  const [productions, setProductions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);

  const fetchProductions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/egg-production');
      setProductions(response.data);
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors de la récupération des productions');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const response = await axios.get('/api/egg-production/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques:', err);
    }
  }, []);

  const addProduction = useCallback(async (productionData) => {
    try {
      const response = await axios.post('/api/egg-production', productionData);
      await fetchProductions();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de l\'ajout de la production');
    }
  }, [fetchProductions, fetchStats]);

  const updateProduction = useCallback(async (id, productionData) => {
    try {
      const response = await axios.put(`/api/egg-production/${id}`, productionData);
      await fetchProductions();
      await fetchStats();
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la mise à jour de la production');
    }
  }, [fetchProductions, fetchStats]);

  const deleteProduction = useCallback(async (id) => {
    try {
      await axios.delete(`/api/egg-production/${id}`);
      await fetchProductions();
      await fetchStats();
    } catch (err) {
      throw new Error(err.response?.data?.message || 'Erreur lors de la suppression de la production');
    }
  }, [fetchProductions, fetchStats]);

  const calculateDailyAverage = useCallback((productions) => {
    if (!productions.length) return 0;
    const total = productions.reduce((sum, prod) => sum + prod.quantite, 0);
    return total / productions.length;
  }, []);

  const calculateWeeklyTrend = useCallback((productions) => {
    if (productions.length < 7) return 0;
    const lastWeek = productions.slice(-7);
    const weeklyTotal = lastWeek.reduce((sum, prod) => sum + prod.quantite, 0);
    return weeklyTotal / 7;
  }, []);

  const getProductionQuality = useCallback((production) => {
    const qualityRatios = {
      premium: (production.oeufs_intacts / production.quantite) * 100,
      standard: (production.oeufs_casses / production.quantite) * 100,
      defectueux: (production.oeufs_defectueux / production.quantite) * 100
    };
    return qualityRatios;
  }, []);

  useEffect(() => {
    fetchProductions();
    fetchStats();
  }, [fetchProductions, fetchStats]);

  return {
    productions,
    loading,
    error,
    stats,
    fetchProductions,
    fetchStats,
    addProduction,
    updateProduction,
    deleteProduction,
    calculateDailyAverage,
    calculateWeeklyTrend,
    getProductionQuality
  };
};

export default useEggProduction;
