import React, { memo } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Skeleton
} from '@mui/material';
import { usePerformance } from '../../hooks/usePerformance';

/**
 * Composant Card optimisé avec React.memo et surveillance des performances
 */
const OptimizedCard = memo(({
  title,
  subtitle,
  content,
  actions,
  loading = false,
  elevation = 1,
  sx = {},
  onClick,
  ...props
}) => {
  const { renderCount, lastRenderTime } = usePerformance('OptimizedCard', {
    trackRenders: true,
    logSlowRenders: true
  });

  // Afficher un skeleton pendant le chargement
  if (loading) {
    return (
      <Card elevation={elevation} sx={sx} {...props}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          {subtitle && <Skeleton variant="text" width="40%" height={20} />}
          <Box mt={2}>
            <Skeleton variant="rectangular" height={60} />
          </Box>
        </CardContent>
        {actions && (
          <CardActions>
            <Skeleton variant="rectangular" width={80} height={36} />
            <Skeleton variant="rectangular" width={80} height={36} />
          </CardActions>
        )}
      </Card>
    );
  }

  return (
    <Card 
      elevation={elevation} 
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: (theme) => theme.shadows[elevation + 2]
        } : {},
        ...sx
      }}
      onClick={onClick}
      {...props}
    >
      <CardContent>
        {title && (
          <Typography variant="h6" component="h2" gutterBottom>
            {title}
          </Typography>
        )}
        
        {subtitle && (
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            {subtitle}
          </Typography>
        )}
        
        {content && (
          <Box mt={1}>
            {typeof content === 'string' ? (
              <Typography variant="body2">
                {content}
              </Typography>
            ) : (
              content
            )}
          </Box>
        )}
      </CardContent>
      
      {actions && (
        <CardActions>
          {actions}
        </CardActions>
      )}
      
      {/* Indicateur de performance en mode développement */}
      {process.env.NODE_ENV === 'development' && (
        <Box
          position="absolute"
          top={4}
          right={4}
          bgcolor="rgba(0,0,0,0.7)"
          color="white"
          px={1}
          py={0.5}
          borderRadius={1}
          fontSize="0.7rem"
          sx={{ opacity: 0.7 }}
        >
          R:{renderCount} T:{lastRenderTime.toFixed(1)}ms
        </Box>
      )}
    </Card>
  );
}, (prevProps, nextProps) => {
  // Comparaison personnalisée pour éviter les re-rendus inutiles
  const keysToCompare = ['title', 'subtitle', 'content', 'loading', 'elevation'];
  
  for (const key of keysToCompare) {
    if (prevProps[key] !== nextProps[key]) {
      return false;
    }
  }
  
  // Comparaison spéciale pour les actions (array de composants)
  if (prevProps.actions !== nextProps.actions) {
    if (!prevProps.actions && !nextProps.actions) return true;
    if (!prevProps.actions || !nextProps.actions) return false;
    if (prevProps.actions.length !== nextProps.actions.length) return false;
  }
  
  // Comparaison des styles
  if (JSON.stringify(prevProps.sx) !== JSON.stringify(nextProps.sx)) {
    return false;
  }
  
  return true;
});

OptimizedCard.displayName = 'OptimizedCard';

export default OptimizedCard;
