import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  TextField,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { DateRangePicker } from '@mui/x-date-pickers-pro';
import {
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useStockAlerts } from '../../../hooks/useStockAlerts';

const SEVERITY_CONFIG = {
  critique: { color: 'error', label: 'Critique' },
  urgent: { color: 'warning', label: 'Urgent' },
  normal: { color: 'info', label: 'Normal' },
  resolu: { color: 'success', label: 'Résolu' }
};

const AlertHistory = () => {
  const { t } = useTranslation();
  const {
    alertHistory,
    loading,
    error,
    fetchAlertHistory,
    exportAlertHistory
  } = useStockAlerts();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    dateRange: [null, null],
    severity: '',
    status: ''
  });
  const [filteredHistory, setFilteredHistory] = useState([]);

  useEffect(() => {
    if (alertHistory) {
      let filtered = [...alertHistory];

      // Filtrer par plage de dates
      if (filters.dateRange[0] && filters.dateRange[1]) {
        filtered = filtered.filter(alert => {
          const alertDate = new Date(alert.created_at);
          return alertDate >= filters.dateRange[0] && alertDate <= filters.dateRange[1];
        });
      }

      // Filtrer par sévérité
      if (filters.severity) {
        filtered = filtered.filter(alert => alert.severity === filters.severity);
      }

      // Filtrer par statut
      if (filters.status) {
        filtered = filtered.filter(alert => alert.status === filters.status);
      }

      setFilteredHistory(filtered);
    }
  }, [alertHistory, filters]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  };

  const handleExport = async () => {
    try {
      await exportAlertHistory(filteredHistory);
    } catch (err) {
      console.error('Erreur lors de l\'export:', err);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h5">
                {t('Historique des Alertes')}
              </Typography>
              <IconButton onClick={handleExport} title={t('Exporter')}>
                <ExportIcon />
              </IconButton>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={4}>
                <DateRangePicker
                  value={filters.dateRange}
                  onChange={(newValue) => handleFilterChange('dateRange', newValue)}
                  renderInput={(startProps, endProps) => (
                    <>
                      <TextField {...startProps} />
                      <Box sx={{ mx: 2 }}> {t('au')} </Box>
                      <TextField {...endProps} />
                    </>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <TextField
                  select
                  fullWidth
                  label={t('Sévérité')}
                  value={filters.severity}
                  onChange={(e) => handleFilterChange('severity', e.target.value)}
                >
                  <MenuItem value="">{t('Toutes')}</MenuItem>
                  {Object.entries(SEVERITY_CONFIG).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {t(value.label)}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} md={4}>
                <TextField
                  select
                  fullWidth
                  label={t('Statut')}
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">{t('Tous')}</MenuItem>
                  <MenuItem value="active">{t('Active')}</MenuItem>
                  <MenuItem value="resolved">{t('Résolue')}</MenuItem>
                  <MenuItem value="ignored">{t('Ignorée')}</MenuItem>
                </TextField>
              </Grid>
            </Grid>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('Date')}</TableCell>
                    <TableCell>{t('Produit')}</TableCell>
                    <TableCell>{t('Sévérité')}</TableCell>
                    <TableCell>{t('Quantité')}</TableCell>
                    <TableCell>{t('Seuil')}</TableCell>
                    <TableCell>{t('Statut')}</TableCell>
                    <TableCell>{t('Actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(rowsPerPage > 0
                    ? filteredHistory.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    : filteredHistory
                  ).map((alert) => (
                    <TableRow key={alert.id}>
                      <TableCell>{formatDate(alert.created_at)}</TableCell>
                      <TableCell>{alert.product_name}</TableCell>
                      <TableCell>
                        <Chip
                          label={t(SEVERITY_CONFIG[alert.severity].label)}
                          color={SEVERITY_CONFIG[alert.severity].color}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {alert.current_quantity} {alert.unit}
                      </TableCell>
                      <TableCell>
                        {alert.alert_threshold} {alert.unit}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={t(alert.status === 'active' ? 'Active' :
                            alert.status === 'resolved' ? 'Résolue' : 'Ignorée')}
                          color={alert.status === 'active' ? 'warning' :
                            alert.status === 'resolved' ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => {}}
                          title={t('Voir les détails')}
                        >
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={filteredHistory.length}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[5, 10, 25, 50]}
              labelRowsPerPage={t('Lignes par page')}
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default AlertHistory;
