const axios = require('axios');
require('colors');

const API_URL = 'http://localhost:3003';

// Admin credentials for login
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

async function login() {
  console.log('Logging in as admin...'.yellow);
  try {
    const response = await axios.post(`${API_URL}/api/auth/login`, adminCredentials);
    console.log('✅ Login successful!'.green);
    return response.data.token;
  } catch (error) {
    console.log('❌ Login failed:'.red);
    if (error.response) {
      console.log(`Status: ${error.response.status}`.yellow);
      console.log(`Data: ${JSON.stringify(error.response.data, null, 2)}`.yellow);
    } else {
      console.log(error.message);
    }
    return null;
  }
}

async function testSmtpEndpoint() {
  console.log('Testing SMTP endpoint...'.cyan);

  try {
    // First, log in to get a valid token
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication token'.red);
      return false;
    }

    console.log('Fetching SMTP configuration...'.yellow);
    const response = await axios.get(`${API_URL}/api/admin/settings/smtp`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ SMTP endpoint is working!'.green);
    console.log('Response data:'.cyan);
    console.log(JSON.stringify(response.data, null, 2));

    return true;
  } catch (error) {
    console.log('❌ Error fetching SMTP configuration:'.red);

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.log(`Status: ${error.response.status}`.yellow);
      console.log(`Data: ${JSON.stringify(error.response.data, null, 2)}`.yellow);
      console.log(`Headers: ${JSON.stringify(error.response.headers, null, 2)}`.gray);
    } else if (error.request) {
      // The request was made but no response was received
      console.log('No response received from server'.red);
      console.log(error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.log('Error setting up request:'.red, error.message);
    }

    return false;
  }
}

// Run the test
testSmtpEndpoint();
