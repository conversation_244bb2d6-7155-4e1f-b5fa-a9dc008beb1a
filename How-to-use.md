# Guide d'Utilisation de Poultray DZ

Bienvenue dans le guide d'utilisation de Poultray DZ, la première plateforme algérienne dédiée à la gestion et à la commercialisation des volailles.

## Table des Matières

1. [Installation](#installation)
   - [Prérequis](#prérequis)
   - [Installation du Frontend](#installation-du-frontend)
   - [Installation du Backend](#installation-du-backend)
   - [Configuration de l'Environnement](#configuration-de-lenvironnement)
2. [Utilisation de l'Application Web](#utilisation-de-lapplication-web)
   - [Connexion et Inscription](#connexion-et-inscription)
   - [Navigation dans l'Interface](#navigation-dans-linterface)
   - [Gestion des Éleveurs](#gestion-des-éleveurs)
   - [Gestion des Volailles](#gestion-des-volailles)
   - [Marketplace](#marketplace)
   - [Suivi Vétérinaire](#suivi-vétérinaire)
3. [Utilisation de l'Application Mobile](#utilisation-de-lapplication-mobile)
4. [Fonctionnalités Avancées](#fonctionnalités-avancées)
5. [Résolution des Problèmes Courants](#résolution-des-problèmes-courants)
6. [Support et Contact](#support-et-contact)

## Installation

### Prérequis

Avant d'installer Poultray DZ, assurez-vous que votre système dispose des éléments suivants :

- Node.js (version 18 ou supérieure)
- npm ou yarn
- Un éditeur de code (VS Code recommandé)
- Git pour la gestion des versions

### Installation du Frontend

1. Clonez le dépôt Git (si ce n'est pas déjà fait) :
   ```bash
   git clone [URL_DU_REPO]
   ```

2. Accédez au répertoire du frontend :
   ```bash
   cd Web_App/frontend
   ```

3. Installez les dépendances :
   ```bash
   npm install
   # ou
   yarn install
   ```

4. Lancez le serveur de développement :
   ```bash
   npm run dev
   # ou
   yarn dev
   ```

5. Le frontend sera accessible à l'adresse : `http://localhost:5173` (ou un autre port si celui-ci est déjà utilisé)

### Installation du Backend

1. Accédez au répertoire du backend :
   ```bash
   cd Web_App
   ```

2. Installez les dépendances :
   ```bash
   npm install
   # ou
   yarn install
   ```

3. Lancez le serveur de développement :
   ```bash
   npm run dev
   # ou
   yarn dev
   ```

4. Le backend sera accessible à l'adresse : `http://localhost:3000` (ou un autre port configuré)

### Configuration de l'Environnement

1. Créez un fichier `.env` à la racine du dossier backend en vous basant sur le fichier `.env.example` :
   ```
   PORT=3000
   NODE_ENV=development
   DATABASE_URL=votre_url_de_base_de_données
   JWT_SECRET=votre_clé_secrète
   ```

2. Configurez la base de données selon les instructions spécifiques à votre environnement.

## Utilisation de l'Application Web

### Connexion et Inscription

1. **Inscription** : Cliquez sur "Créer un compte" dans la barre de navigation et remplissez le formulaire avec vos informations personnelles.

2. **Connexion** : Utilisez votre email et mot de passe pour vous connecter à l'application.

### Navigation dans l'Interface

L'interface de Poultray DZ est organisée en plusieurs sections accessibles depuis le menu principal :

- **Tableau de bord** : Vue d'ensemble de votre activité
- **Éleveurs** : Gestion des profils d'éleveurs
- **Volailles** : Suivi des lots de volailles
- **Marketplace** : Achat et vente de volailles
- **Suivi Vétérinaire** : Consultations et rapports de santé
- **Paramètres** : Configuration de votre compte

### Gestion des Éleveurs

1. **Ajouter un éleveur** : Cliquez sur "Ajouter un éleveur" et remplissez le formulaire avec les informations requises (nom, prénom, email, téléphone, adresse).

2. **Modifier un éleveur** : Sélectionnez un éleveur dans la liste et cliquez sur l'icône de modification pour mettre à jour ses informations.

3. **Supprimer un éleveur** : Sélectionnez un éleveur et cliquez sur l'icône de suppression. Confirmez votre action.

4. **Consulter les détails** : Cliquez sur le nom d'un éleveur pour accéder à sa fiche détaillée et voir ses statistiques.

### Gestion des Volailles

1. **Enregistrer un lot de volailles** : Accédez à la section "Volailles" et cliquez sur "Ajouter un lot". Renseignez les informations demandées (espèce, race, âge, poids, quantité, prix).

2. **Associer à un éleveur** : Lors de l'ajout d'un lot, sélectionnez l'éleveur concerné dans la liste déroulante.

3. **Suivre l'évolution** : Mettez à jour régulièrement les informations de poids et de santé pour suivre la croissance des volailles.

4. **Gérer le stock** : Utilisez les fonctionnalités de gestion de stock pour suivre les entrées et sorties de volailles.

### Marketplace

1. **Publier une annonce** : Cliquez sur "Publier une annonce" et remplissez le formulaire avec les détails de votre offre.

2. **Rechercher des produits** : Utilisez les filtres disponibles pour trouver des volailles correspondant à vos critères.

3. **Contacter un vendeur** : Cliquez sur "Contacter" pour envoyer un message au vendeur.

4. **Finaliser une transaction** : Suivez les instructions pour finaliser l'achat ou la vente de volailles.

### Suivi Vétérinaire

1. **Demander une consultation** : Accédez à la section "Suivi Vétérinaire" et cliquez sur "Nouvelle consultation".

2. **Consulter les rapports** : Visualisez l'historique des consultations et les recommandations des vétérinaires.

3. **Programmer des rappels** : Configurez des rappels pour les vaccinations et traitements à venir.

## Utilisation de l'Application Mobile

L'application mobile Poultray DZ est en cours de développement. Une fois disponible, elle offrira les mêmes fonctionnalités que l'application web avec une interface optimisée pour les appareils mobiles.

## Fonctionnalités Avancées

### Analyses et Statistiques

Accédez à des graphiques et rapports détaillés sur vos activités d'élevage :

1. Naviguez vers la section "Analyses" du tableau de bord
2. Sélectionnez la période et les métriques à analyser
3. Exportez les rapports au format PDF ou Excel si nécessaire

### Notifications

Configurez vos préférences de notifications pour rester informé des événements importants :

1. Accédez aux "Paramètres" > "Notifications"
2. Activez ou désactivez les types de notifications souhaités
3. Choisissez vos canaux de notification préférés (email, SMS, application)

## Résolution des Problèmes Courants

### Problèmes de Connexion

- **Mot de passe oublié** : Utilisez l'option "Mot de passe oublié" sur la page de connexion
- **Compte bloqué** : Contactez le support technique

### Problèmes Techniques

- **Application lente** : Vérifiez votre connexion internet et videz le cache de votre navigateur
- **Erreurs d'affichage** : Essayez de rafraîchir la page ou utilisez un autre navigateur
- **Données non sauvegardées** : Vérifiez que tous les champs obligatoires sont remplis correctement

## Support et Contact

Pour toute question ou assistance technique :

- **Email** : <EMAIL>
- **Téléphone** : +213 XX XX XX XX
- **Chat en direct** : Disponible dans l'application pendant les heures de bureau

---

© 2023 Poultray DZ. Tous droits réservés.
