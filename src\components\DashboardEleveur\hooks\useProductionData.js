import { useState, useEffect } from 'react';
import axios from 'axios';

/**
 * Hook personnalisé pour gérer les données de production
 * Récupère et formate les données de production pour les graphiques
 */
const useProductionData = () => {
  const [productionData, setProductionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProductionData = async () => {
      try {
        setLoading(true);

        // Récupérer les données de production journalière
        const dailyResponse = await axios.get('/api/production/daily');

        // Récupérer les données de production hebdomadaire
        const weeklyResponse = await axios.get('/api/production/weekly');

        // Récupérer les données de production mensuelle
        const monthlyResponse = await axios.get('/api/production/monthly');

        // Récupérer les données de comparaison avec l'année précédente
        const comparisonResponse = await axios.get('/api/production/comparison');

        // Formater les données pour les graphiques
        setProductionData({
          production: {
            mois: dailyResponse.data,
            semaine: weeklyResponse.data,
            moisAnnee: monthlyResponse.data
          },
          comparaison: comparisonResponse.data
        });

        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des données de production:', err);
        setError(err);

        // Utiliser des données fictives en cas d'erreur (pour la démonstration)
        setProductionData({
          production: {
            mois: [
              { date: '01/06', poulets: 450, poules: 1200, total: 1650 },
              { date: '02/06', poulets: 480, poules: 1250, total: 1730 },
              { date: '03/06', poulets: 470, poules: 1300, total: 1770 },
              { date: '04/06', poulets: 500, poules: 1280, total: 1780 },
              { date: '05/06', poulets: 520, poules: 1320, total: 1840 },
              { date: '06/06', poulets: 510, poules: 1340, total: 1850 },
              { date: '07/06', poulets: 530, poules: 1360, total: 1890 }
            ],
            semaine: [
              { date: 'Sem 1', poulets: 3000, poules: 8500, total: 11500 },
              { date: 'Sem 2', poulets: 3200, poules: 8700, total: 11900 },
              { date: 'Sem 3', poulets: 3300, poules: 8900, total: 12200 },
              { date: 'Sem 4', poulets: 3500, poules: 9100, total: 12600 }
            ],
            moisAnnee: [
              { date: 'Jan', poulets: 12000, poules: 35000, total: 47000 },
              { date: 'Fév', poulets: 13000, poules: 36000, total: 49000 },
              { date: 'Mar', poulets: 13500, poules: 37000, total: 50500 },
              { date: 'Avr', poulets: 14000, poules: 38000, total: 52000 },
              { date: 'Mai', poulets: 14500, poules: 39000, total: 53500 },
              { date: 'Juin', poulets: 15000, poules: 40000, total: 55000 }
            ]
          },
          comparaison: [
            { mois: 'Jan', anneeActuelle: 47000, anneePrecedente: 42000 },
            { mois: 'Fév', anneeActuelle: 49000, anneePrecedente: 43500 },
            { mois: 'Mar', anneeActuelle: 50500, anneePrecedente: 45000 },
            { mois: 'Avr', anneeActuelle: 52000, anneePrecedente: 46500 },
            { mois: 'Mai', anneeActuelle: 53500, anneePrecedente: 48000 },
            { mois: 'Juin', anneeActuelle: 55000, anneePrecedente: 49500 }
          ]
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProductionData();

    // Rafraîchir les données toutes les 5 minutes
    const intervalId = setInterval(fetchProductionData, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Fonction pour calculer les statistiques de production
  const calculateStats = (data) => {
    if (!data || !data.production || !data.production.mois) return null;

    const currentDay = data.production.mois[data.production.mois.length - 1];
    const previousDay = data.production.mois[data.production.mois.length - 2];

    const dailyChange = previousDay ?
      ((currentDay.total - previousDay.total) / previousDay.total) * 100 : 0;

    return {
      dailyProduction: currentDay.total,
      dailyChange: dailyChange.toFixed(1),
      weeklyAverage: data.production.semaine.reduce((acc, curr) => acc + curr.total, 0) / data.production.semaine.length,
      monthlyTotal: data.production.moisAnnee[data.production.moisAnnee.length - 1].total
    };
  };

  // Fonction pour mettre à jour manuellement les données
  const refreshData = async () => {
    setLoading(true);
    try {
      const dailyResponse = await axios.get('/api/production/daily');
      const weeklyResponse = await axios.get('/api/production/weekly');
      const monthlyResponse = await axios.get('/api/production/monthly');
      const comparisonResponse = await axios.get('/api/production/comparison');

      setProductionData({
        production: {
          mois: dailyResponse.data,
          semaine: weeklyResponse.data,
          moisAnnee: monthlyResponse.data
        },
        comparaison: comparisonResponse.data
      });

      setError(null);
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des données de production:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return {
    productionData,
    productionStats: productionData ? calculateStats(productionData) : null,
    loading,
    error,
    refreshData
  };
};

export default useProductionData;