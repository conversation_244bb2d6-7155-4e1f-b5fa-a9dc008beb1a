-- Adminer 4.8.4 PostgreSQL 17.4 dump

-- Drop tables with CASCAD<PERSON> to handle dependencies
DROP TABLE IF EXISTS "ApiConfigs" CASCADE;
DROP TABLE IF EXISTS "SequelizeMeta" CASCADE;
DROP TABLE IF EXISTS "Users" CASCADE;
DROP TABLE IF EXISTS "alertes_stock" CASCADE;
DROP TABLE IF EXISTS "annonces" CASCADE;
DROP TABLE IF EXISTS "blog_posts" CASCADE;
DROP TABLE IF EXISTS "clients" CASCADE;
DROP TABLE IF EXISTS "consultations" CASCADE;
DROP TABLE IF EXISTS "eleveurs" CASCADE;
DROP TABLE IF EXISTS "favoris" CASCADE;
DROP TABLE IF EXISTS "feed_consumption_logs" CASCADE;
DROP TABLE IF EXISTS "feed_plans" CASCADE;
DROP TABLE IF EXISTS "marchands" CASCADE;
DROP TABLE IF EXISTS "notifications" CASCADE;
DROP TABLE IF EXISTS "ouvriers" CASCADE;
DROP TABLE IF EXISTS "prescriptions" CASCADE;
DROP TABLE IF EXISTS "produits" CASCADE;
DROP TABLE IF EXISTS "production_oeufs" CASCADE;
DROP TABLE IF EXISTS "poussins" CASCADE;
DROP TABLE IF EXISTS "roles" CASCADE;
DROP TABLE IF EXISTS "suivi_veterinaire" CASCADE;
DROP TABLE IF EXISTS "volailles" CASCADE;
DROP TABLE IF EXISTS "veterinaires" CASCADE;

-- Drop sequences with CASCADE
DROP SEQUENCE IF EXISTS "ApiConfigs_id_seq" CASCADE;
DROP SEQUENCE IF EXISTS "Users_id_seq" CASCADE;
DROP SEQUENCE IF EXISTS alertes_stock_id_seq CASCADE;
DROP SEQUENCE IF EXISTS annonces_id_seq CASCADE;
DROP SEQUENCE IF EXISTS blog_posts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS clients_id_seq CASCADE;
DROP SEQUENCE IF EXISTS consultations_id_seq CASCADE;
DROP SEQUENCE IF EXISTS eleveurs_id_seq CASCADE;
DROP SEQUENCE IF EXISTS favoris_id_seq CASCADE;
DROP SEQUENCE IF EXISTS feed_consumption_logs_id_seq CASCADE;
DROP SEQUENCE IF EXISTS feed_plans_id_seq CASCADE;
DROP SEQUENCE IF EXISTS marchands_id_seq CASCADE;
DROP SEQUENCE IF EXISTS notifications_id_seq CASCADE;
DROP SEQUENCE IF EXISTS ouvriers_id_seq CASCADE;
DROP SEQUENCE IF EXISTS prescriptions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS produits_id_seq CASCADE;
DROP SEQUENCE IF EXISTS production_oeufs_id_seq CASCADE;
DROP SEQUENCE IF EXISTS poussins_id_seq CASCADE;
DROP SEQUENCE IF EXISTS roles_id_seq CASCADE;
DROP SEQUENCE IF EXISTS suivi_veterinaire_id_seq CASCADE;
DROP SEQUENCE IF EXISTS volailles_id_seq CASCADE;
DROP SEQUENCE IF EXISTS veterinaires_id_seq CASCADE;

-- Drop types with CASCADE
DROP TYPE IF EXISTS enum_alertes_stock_type_alerte CASCADE;
DROP TYPE IF EXISTS enum_alertes_stock_priorite CASCADE;
DROP TYPE IF EXISTS enum_alertes_stock_statut CASCADE;

-- Re-create the update_modified_column function if it's used by triggers
-- This function was missing in the previous run
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$
LANGUAGE plpgsql;


CREATE TABLE "public"."ApiConfigs" (
    "id" SERIAL PRIMARY KEY,
    "serviceName" character varying(255) NOT NULL UNIQUE,
    "apiKey" text NOT NULL,
    "apiSecret" text,
    "createdAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "description" text,
    "isEnabled" boolean DEFAULT true
) WITH (oids = false);


CREATE TABLE "public"."SequelizeMeta" (
    "name" character varying(255) NOT NULL,
    CONSTRAINT "SequelizeMeta_pkey" PRIMARY KEY ("name")
) WITH (oids = false);


CREATE TABLE "public"."Users" (
    "id" SERIAL PRIMARY KEY,
    "username" character varying(100) NOT NULL UNIQUE,
    "email" character varying(255) NOT NULL UNIQUE,
    "password" character varying(255) NOT NULL,
    "role" character varying(20) DEFAULT 'eleveur' NOT NULL,
    "first_name" character varying(100),
    "last_name" character varying(100),
    "profile_id" integer,
    "status" character varying(20) DEFAULT 'active',
    "preferences" jsonb DEFAULT '{}',
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    "role_id" integer,
    "subscription_plan_id" integer,
    "firebase_uid" character varying(255) UNIQUE,
    "phone" character varying(50),
    "address" text
) WITH (oids = false);


CREATE TYPE enum_alertes_stock_type_alerte AS ENUM (
    'stock_faible',
    'production_baisse',
    'sante_animale',
    'maintenance_equipement',
    'autre'
);

CREATE TYPE enum_alertes_stock_priorite AS ENUM (
    'basse',
    'normale',
    'haute',
    'critique'
);

CREATE TYPE enum_alertes_stock_statut AS ENUM (
    'active',
    'traitee',
    'archivee'
);

CREATE TABLE "public"."alertes_stock" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "type_alerte" enum_alertes_stock_type_alerte NOT NULL,
    "priorite" enum_alertes_stock_priorite DEFAULT 'normale',
    "statut" enum_alertes_stock_statut DEFAULT 'active',
    "titre" character varying(200) NOT NULL,
    "message" text NOT NULL,
    "message_ar" text,
    "donnees_contexte" jsonb DEFAULT '{}',
    "seuil_declenche" jsonb DEFAULT '{"unite": null, "operateur": null, "valeur_seuil": null, "valeur_actuelle": null}',
    "source_donnees" jsonb DEFAULT '{"table_source": null, "champ_surveille": null, "derniere_valeur": null, "id_enregistrement": null}',
    "actions_recommandees" jsonb DEFAULT '[]',
    "actions_entreprises" jsonb DEFAULT '[]',
    "date_declenchement" timestamptz NOT NULL,
    "date_vue" timestamptz,
    "date_traitee" timestamptz,
    "date_expiration" timestamptz,
    "frequence_rappel" integer,
    "nombre_rappels" integer DEFAULT '0',
    "dernier_rappel" timestamptz,
    "canaux_notification" jsonb DEFAULT '{"sms": false, "push": false, "email": false, "whatsapp": false, "dashboard": true}',
    "notifications_envoyees" jsonb DEFAULT '[]',
    "impact_estime" jsonb DEFAULT '{"financier": null, "production": null, "duree_estimee": null, "sante_animaux": null}',
    "cout_inaction" numeric(12,2),
    "cout_resolution" numeric(12,2),
    "automatique" boolean DEFAULT true,
    "recurrente" boolean DEFAULT false,
    "conditions_resolution" jsonb DEFAULT '{}',
    "liens_utiles" jsonb DEFAULT '[]',
    "contacts_urgence" jsonb DEFAULT '[]',
    "historique_similaires" jsonb DEFAULT '[]',
    "feedback_eleveur" jsonb DEFAULT '{"timing": null, "utilite": null, "precision": null, "suggestions": null}',
    "tags" jsonb DEFAULT '[]',
    "visible" boolean DEFAULT true,
    "archivee" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "alertes_stock_archivee" ON "public"."alertes_stock" USING btree ("archivee");

CREATE INDEX "alertes_stock_automatique" ON "public"."alertes_stock" USING btree ("automatique");

CREATE INDEX "alertes_stock_date_declenchement" ON "public"."alertes_stock" USING btree ("date_declenchement");

CREATE INDEX "alertes_stock_eleveur_id" ON "public"."alertes_stock" USING btree ("eleveur_id");

CREATE INDEX "alertes_stock_eleveur_id_statut_visible" ON "public"."alertes_stock" USING btree ("eleveur_id", "statut", "visible");

CREATE INDEX "alertes_stock_priorite" ON "public"."alertes_stock" USING btree ("priorite");

CREATE INDEX "alertes_stock_statut" ON "public"."alertes_stock" USING btree ("statut");

CREATE INDEX "alertes_stock_type_alerte" ON "public"."alertes_stock" USING btree ("type_alerte");

CREATE INDEX "alertes_stock_visible" ON "public"."alertes_stock" USING btree ("visible");

COMMENT ON COLUMN "public"."alertes_stock"."eleveur_id" IS 'Référence vers l''éleveur';

COMMENT ON COLUMN "public"."alertes_stock"."type_alerte" IS 'Type d''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."priorite" IS 'Niveau de priorité';

COMMENT ON COLUMN "public"."alertes_stock"."statut" IS 'Statut de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."titre" IS 'Titre de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."message" IS 'Message détaillé de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."message_ar" IS 'Message en arabe';

COMMENT ON COLUMN "public"."alertes_stock"."donnees_contexte" IS 'Données contextuelles de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."seuil_declenche" IS 'Informations sur le seuil qui a déclenché l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."source_donnees" IS 'Source des données qui ont déclenché l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."actions_recommandees" IS 'Actions recommandées pour résoudre l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."actions_entreprises" IS 'Actions déjà entreprises par l''éleveur';

COMMENT ON COLUMN "public"."alertes_stock"."date_declenchement" IS 'Date de déclenchement de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."date_vue" IS 'Date à laquelle l''alerte a été vue';

COMMENT ON COLUMN "public"."alertes_stock"."date_traitee" IS 'Date de traitement de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."date_expiration" IS 'Date d''expiration de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."frequence_rappel" IS 'Fréquence de rappel en heures';

COMMENT ON COLUMN "public"."alertes_stock"."nombre_rappels" IS 'Nombre de rappels envoyés';

COMMENT ON COLUMN "public"."alertes_stock"."dernier_rappel" IS 'Date du dernier rappel';

COMMENT ON COLUMN "public"."alertes_stock"."canaux_notification" IS 'Canaux de notification activés';

COMMENT ON COLUMN "public"."alertes_stock"."notifications_envoyees" IS 'Historique des notifications envoyées';

COMMENT ON COLUMN "public"."alertes_stock"."impact_estime" IS 'Impact estimé de l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."cout_inaction" IS 'Coût estimé de l''inaction en DA';

COMMENT ON COLUMN "public"."alertes_stock"."cout_resolution" IS 'Coût estimé de la résolution en DA';

COMMENT ON COLUMN "public"."alertes_stock"."automatique" IS 'Alerte générée automatiquement';

COMMENT ON COLUMN "public"."alertes_stock"."recurrente" IS 'Alerte récurrente';

COMMENT ON COLUMN "public"."alertes_stock"."conditions_resolution" IS 'Conditions pour considérer l''alerte comme résolue';

COMMENT ON COLUMN "public"."alertes_stock"."liens_utiles" IS 'Liens vers des ressources utiles';

COMMENT ON COLUMN "public"."alertes_stock"."contacts_urgence" IS 'Contacts d''urgence pour ce type d''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."historique_similaires" IS 'Références vers des alertes similaires passées';

COMMENT ON COLUMN "public"."alertes_stock"."feedback_eleveur" IS 'Feedback de l''éleveur sur l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."tags" IS 'Tags pour catégoriser l''alerte';

COMMENT ON COLUMN "public"."alertes_stock"."visible" IS 'Alerte visible dans le dashboard';

COMMENT ON COLUMN "public"."alertes_stock"."archivee" IS 'Alerte archivée';


CREATE TABLE "public"."annonces" (
    "id" SERIAL PRIMARY KEY,
    "titre" character varying(255) NOT NULL,
    "description" text,
    "categorie" character varying(100) NOT NULL,
    "prix" numeric(10,2) NOT NULL,
    "localisation" character varying(255),
    "images" text[] DEFAULT '{}'::text[],
    "est_active" boolean DEFAULT true,
    "utilisateur_id" integer NOT NULL,
    "date_creation" timestamptz,
    "date_mise_a_jour" timestamptz
) WITH (oids = false);


CREATE TABLE "public"."blog_posts" (
    "id" SERIAL PRIMARY KEY,
    "title" character varying(200) NOT NULL,
    "slug" character varying(200) NOT NULL UNIQUE,
    "content" text NOT NULL,
    "excerpt" text,
    "author_id" integer,
    "status" character varying(20) DEFAULT 'draft',
    "tags" jsonb DEFAULT '[]',
    "featured_image" character varying(255),
    "published_at" timestamptz,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);


CREATE TABLE "public"."clients" (
    "id" SERIAL PRIMARY KEY,
    "marchand_id" integer,
    "user_id" integer,
    "name" character varying(100) NOT NULL,
    "email" character varying(255),
    "phone" character varying(20),
    "address" text,
    "notes" text,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP
) WITH (oids = false);

CREATE INDEX "idx_clients_marchand_id" ON "public"."clients" USING btree ("marchand_id");

COMMENT ON TABLE "public"."clients" IS 'Clients des marchands';


CREATE TRIGGER "update_clients_modtime" BEFORE UPDATE ON "public"."clients" FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TABLE "public"."consultations" (
    "id" SERIAL PRIMARY KEY,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "diagnostic" text,
    "notes" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "volaille_id" integer NOT NULL,
    "date" timestamptz,
    "symptomes" text,
    "traitement" text,
    "statut" character varying(20) DEFAULT 'en_cours',
    "cout" numeric(10,2)
) WITH (oids = false);

CREATE INDEX "idx_consultations_eleveur_id" ON "public"."consultations" USING btree ("eleveur_id");

CREATE INDEX "idx_consultations_veterinaire_id" ON "public"."consultations" USING btree ("veterinaire_id");

COMMENT ON TABLE "public"."consultations" IS 'Consultations vétérinaires';


CREATE TABLE "public"."eleveurs" (
    "id" SERIAL PRIMARY KEY,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL UNIQUE,
    "telephone" character varying(20),
    "adresse" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "date_modification" timestamptz NOT NULL,
    "user_id" integer
) WITH (oids = false);


CREATE TABLE "public"."favoris" (
    "id" SERIAL PRIMARY KEY,
    "utilisateur_id" integer NOT NULL,
    "annonce_id" integer NOT NULL,
    "date_ajout" timestamptz DEFAULT CURRENT_TIMESTAMP
) WITH (oids = false);

CREATE INDEX "idx_favoris_annonce_id" ON "public"."favoris" USING btree ("annonce_id");

CREATE INDEX "idx_favoris_utilisateur_id" ON "public"."favoris" USING btree ("utilisateur_id");

COMMENT ON TABLE "public"."favoris" IS 'Annonces favorites des utilisateurs';


CREATE TABLE "public"."feed_consumption_logs" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "feed_plan_id" integer,
    "date" date NOT NULL,
    "quantite_distribuee" numeric(10,2) NOT NULL,
    "quantite_consommee" numeric(10,2),
    "gaspillage" numeric(10,2),
    "observations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_feed_consumption_logs_eleveur_id" ON "public"."feed_consumption_logs" USING btree ("eleveur_id");

CREATE INDEX "idx_feed_consumption_logs_feed_plan_id" ON "public"."feed_consumption_logs" USING btree ("feed_plan_id");

COMMENT ON TABLE "public"."feed_consumption_logs" IS 'Logs de consommation d''aliments';


CREATE TABLE "public"."feed_plans" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "nom_plan" character varying(200) NOT NULL,
    "type_aliment" character varying(100) NOT NULL,
    "quantite_journaliere" numeric(10,2) NOT NULL,
    "heure_distribution" time,
    "batiment_cible" character varying(100),
    "date_debut" date NOT NULL,
    "date_fin" date,
    "est_actif" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_feed_plans_eleveur_id" ON "public"."feed_plans" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."feed_plans" IS 'Plans d''alimentation pour les volailles';


CREATE TABLE "public"."marchands" (
    "id" SERIAL PRIMARY KEY,
    "nom_entreprise" character varying(200) NOT NULL,
    "contact_nom" character varying(100),
    "contact_prenom" character varying(100),
    "email" character varying(255) NOT NULL UNIQUE,
    "telephone" character varying(20),
    "adresse" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "user_id" integer
) WITH (oids = false);


CREATE TABLE "public"."notifications" (
    "id" SERIAL PRIMARY KEY,
    "user_id" integer NOT NULL,
    "type" character varying(50) NOT NULL,
    "message" text NOT NULL,
    "is_read" boolean DEFAULT false,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP
) WITH (oids = false);

CREATE INDEX "idx_notifications_user_id" ON "public"."notifications" USING btree ("user_id");

COMMENT ON TABLE "public"."notifications" IS 'Notifications pour les utilisateurs';


CREATE TABLE "public"."ouvriers" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "role" character varying(100),
    "telephone" character varying(20),
    "date_embauche" date,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_ouvriers_eleveur_id" ON "public"."ouvriers" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."ouvriers" IS 'Ouvriers travaillant pour les éleveurs';


CREATE TABLE "public"."prescriptions" (
    "id" SERIAL PRIMARY KEY,
    "consultation_id" integer NOT NULL,
    "medicament" character varying(200) NOT NULL,
    "dosage" character varying(100),
    "frequence" character varying(100),
    "duree" character varying(100),
    "notes" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_prescriptions_consultation_id" ON "public"."prescriptions" USING btree ("consultation_id");

COMMENT ON TABLE "public"."prescriptions" IS 'Prescriptions vétérinaires';


CREATE TABLE "public"."produits" (
    "id" SERIAL PRIMARY KEY,
    "marchand_id" integer NOT NULL,
    "nom" character varying(200) NOT NULL,
    "description" text,
    "categorie" character varying(100),
    "prix" numeric(10,2) NOT NULL,
    "quantite_stock" integer NOT NULL,
    "unite_mesure" character varying(50),
    "images" text[] DEFAULT '{}'::text[],
    "est_disponible" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_produits_marchand_id" ON "public"."produits" USING btree ("marchand_id");

COMMENT ON TABLE "public"."produits" IS 'Produits vendus par les marchands';


CREATE TABLE "public"."production_oeufs" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "date" date NOT NULL,
    "nombre_oeufs" integer NOT NULL,
    "nombre_poules_pondeuses" integer,
    "taux_ponte" numeric(5,2),
    "observations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_production_oeufs_eleveur_id" ON "public"."production_oeufs" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."production_oeufs" IS 'Enregistrements de production d''œufs';


CREATE TABLE "public"."poussins" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "date_arrivee" date NOT NULL,
    "nombre_initial" integer NOT NULL,
    "race" character varying(100),
    "fournisseur" character varying(200),
    "mortalite_journaliere" integer DEFAULT 0,
    "poids_moyen_journalier" numeric(10,2),
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_poussins_eleveur_id" ON "public"."poussins" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."poussins" IS 'Suivi des poussins';


CREATE TABLE "public"."roles" (
    "id" SERIAL PRIMARY KEY,
    "name" character varying(50) NOT NULL UNIQUE,
    "description" text,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP
) WITH (oids = false);


CREATE TABLE "public"."suivi_veterinaire" (
    "id" SERIAL PRIMARY KEY,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "date_visite" date NOT NULL,
    "observations" text,
    "recommandations" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_suivi_veterinaire_eleveur_id" ON "public"."suivi_veterinaire" USING btree ("eleveur_id");

CREATE INDEX "idx_suivi_veterinaire_veterinaire_id" ON "public"."suivi_veterinaire" USING btree ("veterinaire_id");

COMMENT ON TABLE "public"."suivi_veterinaire" IS 'Suivi des visites vétérinaires';


CREATE TABLE "public"."volailles" (
    "id" SERIAL PRIMARY KEY,
    "eleveur_id" integer NOT NULL,
    "type_volaille" character varying(100) NOT NULL,
    "race" character varying(100),
    "date_naissance" date,
    "sexe" character varying(10),
    "statut_sante" character varying(50) DEFAULT 'sain',
    "poids_actuel" numeric(10,2),
    "date_acquisition" date,
    "source_acquisition" character varying(200),
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL
) WITH (oids = false);

CREATE INDEX "idx_volailles_eleveur_id" ON "public"."volailles" USING btree ("eleveur_id");

COMMENT ON TABLE "public"."volailles" IS 'Informations sur les volailles';


CREATE TABLE "public"."veterinaires" (
    "id" SERIAL PRIMARY KEY,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL UNIQUE,
    "telephone" character varying(20),
    "adresse_cabinet" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "user_id" integer
) WITH (oids = false);


-- 2024-06-15 18:17:55.413 +00


