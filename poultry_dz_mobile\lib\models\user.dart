import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart'; // Temporarily commented out

@JsonSerializable()
class User {
  final int id;
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'role_id')
  final int roleId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String? lastName;
  final String status;
  final Map<String, dynamic>? preferences;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  final Role? role;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'firebase_uid')
  final String? firebaseUid;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.roleId,
    this.firstName,
    this.lastName,
    required this.status,
    this.preferences,
    required this.createdAt,
    required this.updatedAt,
    this.role,
    this.firebaseUid,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$User<PERSON>rom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$UserTo<PERSON>son(this);

  // Helper getters for role checking
  bool get isAdmin => role?.name == 'admin';
  bool get isEleveur => role?.name == 'eleveur';
  bool get isVeterinaire => role?.name == 'veterinaire';
  bool get isMarchand => role?.name == 'marchand';

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
  String get displayName => fullName.isNotEmpty ? fullName : username;
}

@JsonSerializable()
class Role {
  final int id;
  final String name;
  final String? description;
  final Map<String, dynamic>? permissions;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const Role({
    required this.id,
    required this.name,
    this.description,
    this.permissions,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  Map<String, dynamic> toJson() => _$RoleToJson(this);
}
