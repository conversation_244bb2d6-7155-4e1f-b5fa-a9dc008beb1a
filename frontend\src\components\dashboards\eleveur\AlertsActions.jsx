import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  useTheme
} from '@mui/material';
import {
  Warning,
  Error,
  Info,
  CheckCircle,
  Add,
  Edit,
  Delete,
  Notifications,
  NotificationsOff,
  MoreVert,
  Done,
  Schedule
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useAuthToken } from '../../../hooks/useAuthToken';

/**
 * Composant Alertes et Actions pour le dashboard éleveur
 * Affiche les alertes importantes et les actions rapides
 */
const AlertsActions = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { token, isAuthenticated } = useAuthToken();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dueDate: '',
    priority: 'medium'
  });

  // Charger les alertes et les tâches
  useEffect(() => {
    const fetchData = async () => {
      if (!isAuthenticated) {
        setError(new Error('Non authentifié'));
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Récupérer les alertes
        const alertsResponse = await axios.get('/api/eleveur/alerts', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        // Récupérer les tâches
        const tasksResponse = await axios.get('/api/eleveur/tasks', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        setAlerts(alertsResponse.data);
        setTasks(tasksResponse.data);
        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des données:', err);
        setError(err);
        
        // Données de démonstration en cas d'erreur
        setAlerts([
          {
            id: 1,
            type: 'warning',
            message: 'Stock d\'aliment faible (moins de 100kg)',
            date: '2024-06-01T10:30:00.000Z',
            status: 'active'
          },
          {
            id: 2,
            type: 'error',
            message: 'Taux de mortalité élevé dans le lot LOT003',
            date: '2024-06-02T08:15:00.000Z',
            status: 'active'
          },
          {
            id: 3,
            type: 'info',
            message: 'Vaccination prévue dans 3 jours',
            date: '2024-06-03T14:00:00.000Z',
            status: 'active'
          }
        ]);
        
        setTasks([
          {
            id: 1,
            title: 'Commander des aliments',
            description: 'Passer commande de 500kg d\'aliment pour pondeuses',
            dueDate: '2024-06-10T00:00:00.000Z',
            status: 'pending',
            priority: 'high'
          },
          {
            id: 2,
            title: 'Nettoyer les abreuvoirs',
            description: 'Nettoyer et désinfecter tous les abreuvoirs',
            dueDate: '2024-06-07T00:00:00.000Z',
            status: 'pending',
            priority: 'medium'
          },
          {
            id: 3,
            title: 'Préparer rapport mensuel',
            description: 'Compiler les données de production du mois',
            dueDate: '2024-06-15T00:00:00.000Z',
            status: 'pending',
            priority: 'low'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated, token]);

  // Obtenir l'icône d'alerte en fonction du type
  const getAlertIcon = (type) => {
    switch (type) {
      case 'error':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'success':
        return <CheckCircle color="success" />;
      default:
        return <Info color="info" />;
    }
  };

  // Obtenir la couleur de priorité
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.info.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  // Formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Ouvrir le dialogue pour ajouter une tâche
  const handleOpenAddTaskDialog = () => {
    setDialogType('addTask');
    setFormData({
      title: '',
      description: '',
      dueDate: '',
      priority: 'medium'
    });
    setOpenDialog(true);
  };

  // Ouvrir le dialogue pour modifier une tâche
  const handleOpenEditTaskDialog = (task) => {
    setDialogType('editTask');
    setSelectedItem(task);
    setFormData({
      title: task.title,
      description: task.description,
      dueDate: task.dueDate.split('T')[0], // Format YYYY-MM-DD
      priority: task.priority
    });
    setOpenDialog(true);
  };

  // Gérer la fermeture du dialogue
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
  };

  // Gérer les changements dans le formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Soumettre le formulaire
  const handleSubmitTask = async () => {
    try {
      if (dialogType === 'addTask') {
        // Ajouter une nouvelle tâche
        const response = await axios.post('/api/eleveur/tasks', formData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        setTasks([...tasks, response.data]);
      } else if (dialogType === 'editTask' && selectedItem) {
        // Modifier une tâche existante
        const response = await axios.put(`/api/eleveur/tasks/${selectedItem.id}`, formData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        setTasks(tasks.map(task => task.id === selectedItem.id ? response.data : task));
      }
      handleCloseDialog();
    } catch (err) {
      console.error('Erreur lors de la soumission de la tâche:', err);
      // En cas d'erreur, simuler l'ajout/modification pour la démonstration
      if (dialogType === 'addTask') {
        const newTask = {
          id: tasks.length + 1,
          ...formData,
          status: 'pending'
        };
        setTasks([...tasks, newTask]);
      } else if (dialogType === 'editTask' && selectedItem) {
        setTasks(tasks.map(task => task.id === selectedItem.id ? { ...task, ...formData } : task));
      }
      handleCloseDialog();
    }
  };

  // Marquer une alerte comme résolue
  const handleResolveAlert = async (alertId) => {
    try {
      await axios.patch(`/api/eleveur/alerts/${alertId}/resolve`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setAlerts(alerts.map(alert => alert.id === alertId ? { ...alert, status: 'resolved' } : alert));
    } catch (err) {
      console.error('Erreur lors de la résolution de l\'alerte:', err);
      // Simuler la résolution pour la démonstration
      setAlerts(alerts.map(alert => alert.id === alertId ? { ...alert, status: 'resolved' } : alert));
    }
  };

  // Marquer une tâche comme terminée
  const handleCompleteTask = async (taskId) => {
    try {
      await axios.patch(`/api/eleveur/tasks/${taskId}/complete`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setTasks(tasks.map(task => task.id === taskId ? { ...task, status: 'completed' } : task));
    } catch (err) {
      console.error('Erreur lors de la complétion de la tâche:', err);
      // Simuler la complétion pour la démonstration
      setTasks(tasks.map(task => task.id === taskId ? { ...task, status: 'completed' } : task));
    }
  };

  // Supprimer une tâche
  const handleDeleteTask = async (taskId) => {
    try {
      await axios.delete(`/api/eleveur/tasks/${taskId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setTasks(tasks.filter(task => task.id !== taskId));
    } catch (err) {
      console.error('Erreur lors de la suppression de la tâche:', err);
      // Simuler la suppression pour la démonstration
      setTasks(tasks.filter(task => task.id !== taskId));
    }
  };

  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Filtrer les alertes actives
  const activeAlerts = alerts.filter(alert => alert.status === 'active');

  // Filtrer les tâches en attente
  const pendingTasks = tasks.filter(task => task.status === 'pending');

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Section Alertes */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('alerts.title')}
              </Typography>
              <IconButton size="small">
                <Notifications />
              </IconButton>
            </Box>
            <Divider sx={{ mb: 2 }} />

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {t('errors.fetchFailed')}: {error.message}
              </Alert>
            )}

            {activeAlerts.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <CheckCircle color="success" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="body1">
                  {t('alerts.noActiveAlerts')}
                </Typography>
              </Box>
            ) : (
              <List>
                {activeAlerts.map((alert) => (
                  <ListItem key={alert.id} divider>
                    <ListItemIcon>
                      {getAlertIcon(alert.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={alert.message}
                      secondary={formatDate(alert.date)}
                    />
                    <ListItemSecondaryAction>
                      <IconButton 
                        edge="end" 
                        aria-label="resolve" 
                        onClick={() => handleResolveAlert(alert.id)}
                      >
                        <Done />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Section Actions Rapides */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('tasks.title')}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Add />}
                size="small"
                onClick={handleOpenAddTaskDialog}
              >
                {t('tasks.addTask')}
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />

            {pendingTasks.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <CheckCircle color="success" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="body1">
                  {t('tasks.noTasks')}
                </Typography>
              </Box>
            ) : (
              <List>
                {pendingTasks.map((task) => (
                  <ListItem key={task.id} divider>
                    <ListItemIcon>
                      <Schedule style={{ color: getPriorityColor(task.priority) }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1" component="span">
                            {task.title}
                          </Typography>
                          <Chip
                            size="small"
                            label={t(`tasks.priorities.${task.priority}`)}
                            sx={{
                              ml: 1,
                              bgcolor: getPriorityColor(task.priority),
                              color: 'white',
                              fontSize: '0.7rem'
                            }}
                          />
                        </Box>
                      }
                      secondary={`${task.description} - ${t('tasks.due')}: ${formatDate(task.dueDate)}`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        aria-label="complete"
                        onClick={() => handleCompleteTask(task.id)}
                        sx={{ mr: 1 }}
                      >
                        <Done />
                      </IconButton>
                      <IconButton
                        edge="end"
                        aria-label="edit"
                        onClick={() => handleOpenEditTaskDialog(task)}
                        sx={{ mr: 1 }}
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        edge="end"
                        aria-label="delete"
                        onClick={() => handleDeleteTask(task.id)}
                      >
                        <Delete />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Dialogue pour ajouter/modifier une tâche */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>
          {dialogType === 'addTask' ? t('tasks.addTask') : t('tasks.editTask')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            {dialogType === 'addTask' 
              ? t('tasks.addTaskDescription') 
              : t('tasks.editTaskDescription')}
          </DialogContentText>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                name="title"
                label={t('tasks.title')}
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                required
                margin="dense"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label={t('tasks.description')}
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={3}
                margin="dense"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="dueDate"
                label={t('tasks.dueDate')}
                type="date"
                value={formData.dueDate}
                onChange={handleInputChange}
                fullWidth
                margin="dense"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="priority"
                label={t('tasks.priority')}
                select
                value={formData.priority}
                onChange={handleInputChange}
                fullWidth
                margin="dense"
                SelectProps={{
                  native: true
                }}
              >
                <option value="high">{t('tasks.priorities.high')}</option>
                <option value="medium">{t('tasks.priorities.medium')}</option>
                <option value="low">{t('tasks.priorities.low')}</option>
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmitTask} variant="contained" color="primary">
            {dialogType === 'addTask' ? t('common.add') : t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AlertsActions;

