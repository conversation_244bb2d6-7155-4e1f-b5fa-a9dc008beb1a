const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SmtpConfig extends Model {
    // Pas d'associations définies dans le code original pour ce modèle
    // static associate(models) {
    //   // Définir les associations ici si nécessaire
    // }
  }

  SmtpConfig.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    host: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    port: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    secure: {
      type: DataTypes.BOOLEAN,
      defaultValue: true, // true for 465, false for other ports
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    pass: {
      type: DataTypes.STRING, // Should be stored encrypted (e.g., using a getter/setter with encryption)
      allowNull: false,
    },
    fromName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fromEmail: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    replyTo: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    testEmailRecipient: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
          isEmail: true,
      }
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    }
  }, {
    sequelize,
    modelName: 'SmtpConfig',
    tableName: 'smtp_configurations',
    timestamps: true,
  });

  return SmtpConfig;
};
