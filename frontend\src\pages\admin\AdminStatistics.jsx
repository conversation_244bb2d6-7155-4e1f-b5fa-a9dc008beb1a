import React, { useState, useEffect } from 'react';
import { Box, Card, CardContent, Grid, Typography, CircularProgress } from '@mui/material';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import axiosInstance from '../../utils/axiosConfig';

const AdminStatistics = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    usersCount: {
      total: 0,
      byRole: {
        eleveur: 0,
        veterinaire: 0,
        marchand: 0
      }
    },
    transactions: {
      total: 0,
      monthly: []
    },
    volailles: {
      total: 0,
      byType: []
    }
  });

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get('/admin/statistics');
        setStats(response.data);
        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des statistiques');
        console.error('Erreur:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Statistiques Globales
      </Typography>

      <Grid container spacing={3}>
        {/* Statistiques des utilisateurs */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Utilisateurs
              </Typography>
              <Typography variant="h3">
                {stats.usersCount.total}
              </Typography>
              <Typography color="textSecondary">
                Éleveurs: {stats.usersCount.byRole.eleveur}
              </Typography>
              <Typography color="textSecondary">
                Vétérinaires: {stats.usersCount.byRole.veterinaire}
              </Typography>
              <Typography color="textSecondary">
                Marchands: {stats.usersCount.byRole.marchand}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Statistiques des transactions */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Transactions Mensuelles
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats.transactions.monthly}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="amount" fill="#2E7D32" name="Montant" />
                    <Bar dataKey="count" fill="#FFA000" name="Nombre" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Statistiques des volailles */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Distribution des Volailles
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats.volailles.byType}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" fill="#2E7D32" name="Quantité" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminStatistics;
