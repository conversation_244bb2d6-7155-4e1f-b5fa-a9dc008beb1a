/* Styles pour le tableau de bord éleveur */

/* Animation pour les cartes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Styles généraux */
.dashboard-container {
  animation: fadeInUp 0.5s ease-out;
}

.dashboard-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Styles pour les cartes de statistiques */
.stat-card {
  background-color: #F3F4F6;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card-title {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 0.5rem;
}

.stat-card-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1F2937;
}

.stat-card-trend {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.trend-up {
  color: #10B981;
}

.trend-down {
  color: #EF4444;
}

/* Styles pour les graphiques */
.chart-container {
  width: 100%;
  height: 300px;
  margin-top: 1rem;
}

/* Styles pour les alertes */
.alert-item {
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.alert-critical {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #EF4444;
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid #F59E0B;
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3B82F6;
}

/* Styles pour les activités récentes */
.activity-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.activity-icon {
  min-width: 40px;
}

.activity-time {
  color: #6B7280;
  font-size: 0.75rem;
}

/* Styles pour les recommandations IA */
.recommendation-item {
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
}

.recommendation-high {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #EF4444;
}

.recommendation-medium {
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid #F59E0B;
}

.recommendation-low {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3B82F6;
}

/* Styles pour le widget météo */
.weather-widget {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-radius: 8px;
  overflow: hidden;
}

.weather-current {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
}

.weather-forecast {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.5);
}

.weather-day {
  text-align: center;
  padding: 0.5rem;
}

/* Styles pour le suivi des livraisons */
.delivery-item {
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.delivery-pending {
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid #F59E0B;
}

.delivery-in-transit {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3B82F6;
}

.delivery-delivered {
  background-color: rgba(16, 185, 129, 0.1);
  border-left: 4px solid #10B981;
}

.delivery-cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #EF4444;
}

/* Styles pour les boutons d'action */
.action-button {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Styles pour les animations de chargement */
.loading-pulse {
  animation: pulse 2s infinite;
}

.loading-spin {
  animation: spin 1s linear infinite;
}

/* Styles pour les éléments interactifs */
.interactive-element {
  cursor: pointer;
  transition: all 0.2s ease;
}

.interactive-element:hover {
  opacity: 0.8;
}

/* Styles pour les badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Styles responsifs */
@media (max-width: 600px) {
  .dashboard-card {
    margin-bottom: 1rem;
  }
  
  .stat-card-value {
    font-size: 1.25rem;
  }
  
  .chart-container {
    height: 250px;
  }
}

@media (max-width: 960px) {
  .weather-forecast {
    flex-wrap: wrap;
  }
  
  .weather-day {
    width: 33.33%;
  }
}
