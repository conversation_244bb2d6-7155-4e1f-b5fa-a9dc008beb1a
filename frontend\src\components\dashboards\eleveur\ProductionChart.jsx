import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardHeader, 
  CardContent, 
  Divider, 
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ToggleButtonGroup,
  ToggleButton,
  Tooltip,
  IconButton
} from '@mui/material';
import { 
  BarChart, 
  Bar, 
  LineChart,
  Line,
  ComposedChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine,
  Label,
  Brush
} from 'recharts';
import { 
  Timeline,
  BarChart as BarChartIcon,
  ShowChart,
  Info,
  ZoomIn,
  ZoomOut,
  Refresh
} from '@mui/icons-material';

/**
 * Composant amélioré pour le suivi de production
 * Affiche des graphiques interactifs avec plusieurs options de visualisation
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {Array} props.data - Les données de production à afficher
 * @param {Array} props.events - Les événements importants à marquer sur le graphique (vaccinations, livraisons, etc.)
 * @returns {JSX.Element} - Le composant de suivi de production
 */
const ProductionChart = ({ data = [], events = [] }) => {
  const [chartType, setChartType] = useState('composed');
  const [timeRange, setTimeRange] = useState('month');
  const [dataType, setDataType] = useState('all');
  
  // Données simulées pour la démonstration
  const mockMonthlyData = [
    { date: '01/11', poulets: 450, poules: 1200, dindes: 300, total: 1950, objectif: 1800 },
    { date: '05/11', poulets: 480, poules: 1250, dindes: 320, total: 2050, objectif: 1800 },
    { date: '10/11', poulets: 470, poules: 1300, dindes: 310, total: 2080, objectif: 1800 },
    { date: '15/11', poulets: 500, poules: 1280, dindes: 330, total: 2110, objectif: 1800 },
    { date: '20/11', poulets: 520, poules: 1320, dindes: 350, total: 2190, objectif: 1800 },
    { date: '25/11', poulets: 510, poules: 1340, dindes: 340, total: 2190, objectif: 1800 },
    { date: '30/11', poulets: 530, poules: 1360, dindes: 360, total: 2250, objectif: 1800 }
  ];

  const mockWeeklyData = [
    { date: 'Sem 1', poulets: 3000, poules: 8500, dindes: 2000, total: 13500, objectif: 12000 },
    { date: 'Sem 2', poulets: 3200, poules: 8700, dindes: 2100, total: 14000, objectif: 12000 },
    { date: 'Sem 3', poulets: 3300, poules: 8900, dindes: 2200, total: 14400, objectif: 12000 },
    { date: 'Sem 4', poulets: 3500, poules: 9100, dindes: 2300, total: 14900, objectif: 12000 }
  ];

  const mockYearlyData = [
    { date: 'Jan', poulets: 12000, poules: 35000, dindes: 8000, total: 55000, objectif: 50000 },
    { date: 'Fév', poulets: 13000, poules: 36000, dindes: 8500, total: 57500, objectif: 50000 },
    { date: 'Mar', poulets: 12500, poules: 37000, dindes: 8200, total: 57700, objectif: 50000 },
    { date: 'Avr', poulets: 13500, poules: 38000, dindes: 8700, total: 60200, objectif: 50000 },
    { date: 'Mai', poulets: 14000, poules: 39000, dindes: 9000, total: 62000, objectif: 50000 },
    { date: 'Juin', poulets: 14500, poules: 40000, dindes: 9200, total: 63700, objectif: 50000 },
    { date: 'Juil', poulets: 15000, poules: 41000, dindes: 9500, total: 65500, objectif: 50000 },
    { date: 'Août', poulets: 14800, poules: 40500, dindes: 9300, total: 64600, objectif: 50000 },
    { date: 'Sep', poulets: 14600, poules: 40000, dindes: 9100, total: 63700, objectif: 50000 },
    { date: 'Oct', poulets: 14200, poules: 39500, dindes: 8900, total: 62600, objectif: 50000 },
    { date: 'Nov', poulets: 14700, poules: 41000, dindes: 9200, total: 64900, objectif: 50000 },
    { date: 'Déc', poulets: 15200, poules: 42000, dindes: 9600, total: 66800, objectif: 50000 }
  ];

  // Événements simulés
  const mockEvents = [
    { date: '10/11', type: 'vaccination', description: 'Vaccination lot #A123' },
    { date: '20/11', type: 'delivery', description: 'Livraison d\'aliments' },
    { date: 'Mar', type: 'health', description: 'Contrôle sanitaire' },
    { date: 'Juin', type: 'market', description: 'Pic de demande' },
    { date: 'Oct', type: 'weather', description: 'Vague de chaleur' }
  ];

  // Sélectionner les données en fonction de la période
  const getDataByTimeRange = () => {
    switch (timeRange) {
      case 'week':
        return data.length > 0 ? data.filter(item => item.timeRange === 'week') : mockWeeklyData;
      case 'year':
        return data.length > 0 ? data.filter(item => item.timeRange === 'year') : mockYearlyData;
      case 'month':
      default:
        return data.length > 0 ? data.filter(item => item.timeRange === 'month') : mockMonthlyData;
    }
  };

  // Obtenir les événements pour la période sélectionnée
  const getEventsForTimeRange = () => {
    const selectedEvents = events.length > 0 ? events : mockEvents;
    
    // Filtrer les événements en fonction de la période
    switch (timeRange) {
      case 'week':
        return selectedEvents.filter(event => event.date.startsWith('Sem'));
      case 'year':
        return selectedEvents.filter(event => !event.date.includes('/'));
      case 'month':
      default:
        return selectedEvents.filter(event => event.date.includes('/'));
    }
  };

  // Gérer le changement de type de graphique
  const handleChartTypeChange = (event, newType) => {
    if (newType !== null) {
      setChartType(newType);
    }
  };

  // Gérer le changement de période
  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  // Gérer le changement de type de données
  const handleDataTypeChange = (event) => {
    setDataType(event.target.value);
  };

  // Obtenir les données filtrées par type
  const getFilteredData = () => {
    const selectedData = getDataByTimeRange();
    
    // Créer une copie des données pour ne pas modifier l'original
    return selectedData.map(item => {
      const newItem = { date: item.date };
      
      // Ajouter les propriétés en fonction du filtre
      if (dataType === 'all' || dataType === 'poulets') {
        newItem.poulets = item.poulets;
      }
      if (dataType === 'all' || dataType === 'poules') {
        newItem.poules = item.poules;
      }
      if (dataType === 'all' || dataType === 'dindes') {
        newItem.dindes = item.dindes;
      }
      if (dataType === 'all') {
        newItem.total = item.total;
      }
      
      // Toujours inclure l'objectif
      newItem.objectif = item.objectif;
      
      return newItem;
    });
  };

  // Rendu du graphique en fonction du type sélectionné
  const renderChart = () => {
    const chartData = getFilteredData();
    const relevantEvents = getEventsForTimeRange();
    
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataType === 'all' || dataType === 'poulets' ? (
                <Bar dataKey="poulets" name="Poulets" fill="#8884d8" />
              ) : null}
              {dataType === 'all' || dataType === 'poules' ? (
                <Bar dataKey="poules" name="Poules Pondeuses" fill="#82ca9d" />
              ) : null}
              {dataType === 'all' || dataType === 'dindes' ? (
                <Bar dataKey="dindes" name="Dindes" fill="#ffc658" />
              ) : null}
              <ReferenceLine y={chartData[0]?.objectif} stroke="red" strokeDasharray="3 3">
                <Label value="Objectif" position="insideTopRight" />
              </ReferenceLine>
              {relevantEvents.map((event, index) => (
                <ReferenceLine 
                  key={index} 
                  x={event.date} 
                  stroke="#ff7300" 
                  label={{ value: event.type, angle: -90, position: 'insideTopRight' }} 
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataType === 'all' || dataType === 'poulets' ? (
                <Line type="monotone" dataKey="poulets" name="Poulets" stroke="#8884d8" activeDot={{ r: 8 }} />
              ) : null}
              {dataType === 'all' || dataType === 'poules' ? (
                <Line type="monotone" dataKey="poules" name="Poules Pondeuses" stroke="#82ca9d" />
              ) : null}
              {dataType === 'all' || dataType === 'dindes' ? (
                <Line type="monotone" dataKey="dindes" name="Dindes" stroke="#ffc658" />
              ) : null}
              {dataType === 'all' ? (
                <Line type="monotone" dataKey="total" name="Total" stroke="#ff7300" strokeWidth={2} />
              ) : null}
              <ReferenceLine y={chartData[0]?.objectif} stroke="red" strokeDasharray="3 3">
                <Label value="Objectif" position="insideTopRight" />
              </ReferenceLine>
              {relevantEvents.map((event, index) => (
                <ReferenceLine 
                  key={index} 
                  x={event.date} 
                  stroke="#ff7300" 
                  label={{ value: event.type, angle: -90, position: 'insideTopRight' }} 
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
      case 'composed':
      default:
        return (
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataType === 'all' || dataType === 'poulets' ? (
                <Bar dataKey="poulets" name="Poulets" fill="#8884d8" />
              ) : null}
              {dataType === 'all' || dataType === 'poules' ? (
                <Bar dataKey="poules" name="Poules Pondeuses" fill="#82ca9d" />
              ) : null}
              {dataType === 'all' || dataType === 'dindes' ? (
                <Bar dataKey="dindes" name="Dindes" fill="#ffc658" />
              ) : null}
              {dataType === 'all' ? (
                <Line type="monotone" dataKey="total" name="Total" stroke="#ff7300" strokeWidth={2} />
              ) : null}
              <ReferenceLine y={chartData[0]?.objectif} stroke="red" strokeDasharray="3 3">
                <Label value="Objectif" position="insideTopRight" />
              </ReferenceLine>
              {relevantEvents.map((event, index) => (
                <ReferenceLine 
                  key={index} 
                  x={event.date} 
                  stroke="#ff7300" 
                  label={{ value: event.type, angle: -90, position: 'insideTopRight' }} 
                />
              ))}
              <Brush dataKey="date" height={30} stroke="#8884d8" />
            </ComposedChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader 
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Suivi de Production</Typography>
            <Box>
              <Tooltip title="Rafraîchir les données">
                <IconButton size="small">
                  <Refresh fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Plus d'informations">
                <IconButton size="small">
                  <Info fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        }
      />
      <Divider />
      <CardContent>
        {/* Contrôles du graphique */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, flexWrap: 'wrap', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="time-range-label">Période</InputLabel>
            <Select
              labelId="time-range-label"
              id="time-range-select"
              value={timeRange}
              label="Période"
              onChange={handleTimeRangeChange}
            >
              <MenuItem value="week">Semaine</MenuItem>
              <MenuItem value="month">Mois</MenuItem>
              <MenuItem value="year">Année</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="data-type-label">Données</InputLabel>
            <Select
              labelId="data-type-label"
              id="data-type-select"
              value={dataType}
              label="Données"
              onChange={handleDataTypeChange}
            >
              <MenuItem value="all">Toutes</MenuItem>
              <MenuItem value="poulets">Poulets</MenuItem>
              <MenuItem value="poules">Poules Pondeuses</MenuItem>
              <MenuItem value="dindes">Dindes</MenuItem>
            </Select>
          </FormControl>
          
          <ToggleButtonGroup
            value={chartType}
            exclusive
            onChange={handleChartTypeChange}
            aria-label="type de graphique"
            size="small"
          >
            <ToggleButton value="bar" aria-label="graphique à barres">
              <Tooltip title="Graphique à barres">
                <BarChartIcon fontSize="small" />
              </Tooltip>
            </ToggleButton>
            <ToggleButton value="line" aria-label="graphique linéaire">
              <Tooltip title="Graphique linéaire">
                <ShowChart fontSize="small" />
              </Tooltip>
            </ToggleButton>
            <ToggleButton value="composed" aria-label="graphique composé">
              <Tooltip title="Graphique composé">
                <Timeline fontSize="small" />
              </Tooltip>
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
        
        {/* Graphique */}
        {renderChart()}
        
        {/* Légende des événements */}
        <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {getEventsForTimeRange().map((event, index) => (
            <Tooltip key={index} title={event.description}>
              <Box
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  bgcolor: 'rgba(255, 115, 0, 0.1)',
                  color: '#ff7300',
                  border: '1px solid #ff7300'
                }}
              >
                {event.date}: {event.type}
              </Box>
            </Tooltip>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProductionChart;
