import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  TrendingDown as TrendingDownIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useStockAlerts } from '../../../hooks/useStockAlerts';
import { PieC<PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

const SEVERITY_CONFIG = {
  critique: { color: 'error', icon: ErrorIcon, label: 'Critique' },
  urgent: { color: 'warning', icon: WarningIcon, label: 'Urgent' },
  normal: { color: 'info', icon: InfoIcon, label: 'Normal' },
  resolu: { color: 'success', icon: CheckCircleIcon, label: 'Résolu' }
};

const CHART_COLORS = {
  critique: '#f44336',
  urgent: '#ff9800',
  normal: '#2196f3',
  resolu: '#4caf50'
};

const StockDashboard = () => {
  const { t } = useTranslation();
  const {
    alerts,
    loading,
    error,
    fetchAlerts,
    updateAlertSettings,
    calculateDaysUntilStockout,
    getRecommendedAction
  } = useStockAlerts();

  const [openSettings, setOpenSettings] = useState(false);
  const [settings, setSettings] = useState({
    seuil_critique: '',
    seuil_alerte: '',
    delai_notification: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const loadSettings = async () => {
      // Charger les paramètres actuels
      setSettings({
        seuil_critique: '10',
        seuil_alerte: '20',
        delai_notification: '7'
      });
    };
    loadSettings();
  }, []);

  const handleRefresh = () => {
    fetchAlerts();
  };

  const handleOpenSettings = () => {
    setOpenSettings(true);
  };

  const handleCloseSettings = () => {
    setOpenSettings(false);
    setSuccess(false);
  };

  const handleSettingChange = (event) => {
    const { name, value } = event.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveSettings = async () => {
    setSubmitting(true);
    try {
      await updateAlertSettings(settings);
      setSuccess(true);
      setTimeout(() => {
        handleCloseSettings();
      }, 2000);
    } catch (err) {
      console.error('Erreur lors de la mise à jour des paramètres:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const getAlertStats = () => {
    if (!alerts) return [];

    const stats = {
      critique: 0,
      urgent: 0,
      normal: 0,
      resolu: 0
    };

    alerts.forEach(alert => {
      stats[alert.severity]++;
    });

    return Object.entries(stats).map(([name, value]) => ({
      name: t(SEVERITY_CONFIG[name].label),
      value
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">
            {t('Tableau de Bord des Stocks')}
          </Typography>
          <Box>
            <IconButton onClick={handleRefresh} title={t('Rafraîchir')}>
              <RefreshIcon />
            </IconButton>
            <IconButton onClick={handleOpenSettings} title={t('Paramètres')}>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </Grid>

      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('Alertes Actives')}
            </Typography>

            <List>
              {alerts?.length === 0 ? (
                <Alert severity="info">
                  {t('Aucune alerte active')}
                </Alert>
              ) : (
                alerts?.map((alert) => {
                  const SeverityIcon = SEVERITY_CONFIG[alert.severity].icon;
                  const daysUntilStockout = calculateDaysUntilStockout(alert);
                  const recommendedAction = getRecommendedAction(alert);

                  return (
                    <React.Fragment key={alert.id}>
                      <ListItem>
                        <ListItemIcon>
                          <SeverityIcon color={SEVERITY_CONFIG[alert.severity].color} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="subtitle1">
                                {alert.product_name}
                              </Typography>
                              <Chip
                                label={t(SEVERITY_CONFIG[alert.severity].label)}
                                color={SEVERITY_CONFIG[alert.severity].color}
                                size="small"
                              />
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" color="text.secondary">
                                {t('Quantité actuelle')}: {alert.current_quantity} {alert.unit}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {t('Seuil d\'alerte')}: {alert.alert_threshold} {alert.unit}
                              </Typography>
                              <Box display="flex" alignItems="center" gap={1}>
                                {daysUntilStockout < 0 ? (
                                  <TrendingUpIcon color="success" />
                                ) : (
                                  <TrendingDownIcon color="error" />
                                )}
                                <Typography variant="body2" color="text.secondary">
                                  {t('Jours avant rupture')}: {Math.abs(daysUntilStockout)}
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary">
                                {t('Action recommandée')}: {recommendedAction}
                              </Typography>
                            </>
                          }
                        />
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  );
                })
              )}
            </List>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('Répartition des Alertes')}
            </Typography>

            <Box height={300}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={getAlertStats()}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {getAlertStats().map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={CHART_COLORS[Object.keys(SEVERITY_CONFIG)[index]]}
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Dialog open={openSettings} onClose={handleCloseSettings} maxWidth="sm" fullWidth>
        <DialogTitle>
          {t('Paramètres des Alertes')}
        </DialogTitle>
        <DialogContent>
          {success ? (
            <Alert severity="success">
              {t('Paramètres mis à jour avec succès')}
            </Alert>
          ) : (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Seuil critique (%)')}
                  name="seuil_critique"
                  type="number"
                  value={settings.seuil_critique}
                  onChange={handleSettingChange}
                  InputProps={{ inputProps: { min: 0, max: 100 } }}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Seuil d\'alerte (%)')}
                  name="seuil_alerte"
                  type="number"
                  value={settings.seuil_alerte}
                  onChange={handleSettingChange}
                  InputProps={{ inputProps: { min: 0, max: 100 } }}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Délai de notification (jours)')}
                  name="delai_notification"
                  type="number"
                  value={settings.delai_notification}
                  onChange={handleSettingChange}
                  InputProps={{ inputProps: { min: 1 } }}
                  required
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSettings}>
            {t('Annuler')}
          </Button>
          <Button
            onClick={handleSaveSettings}
            color="primary"
            disabled={submitting || !settings.seuil_critique || !settings.seuil_alerte || !settings.delai_notification}
          >
            {submitting ? <CircularProgress size={24} /> : t('Enregistrer')}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default StockDashboard;
