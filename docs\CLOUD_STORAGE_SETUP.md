# Configuration du Stockage Cloud pour Poultray DZ

## Prérequis

1. Un compte Google Cloud Platform (GCP)
2. Un projet GCP actif
3. Un bucket Cloud Storage créé
4. Un fichier de clé de service (JSON)

## Variables d'Environnement Requises

Ajoutez ces variables dans votre fichier `.env` :

```env
GOOGLE_CLOUD_PROJECT_ID=votre-projet-id
GOOGLE_CLOUD_STORAGE_BUCKET=nom-de-votre-bucket
GOOGLE_CLOUD_STORAGE_KEY_PATH=chemin/vers/votre-fichier-cle.json
```

## Installation des Dépendances

Installer la dépendance Google Cloud Storage :

```bash
npm install @google-cloud/storage
```

## Utilisation

Le module de stockage cloud est utilisé pour :

- Uploader des images de produits
- Stocker des documents
- Gérer les fichiers médias

Exemple d'utilisation dans un contrôleur :

```javascript
const { uploadToCloudStorage } = require("../utils/cloudStorage");

async function uploadImage(req, res) {
  try {
    const imageUrl = await uploadToCloudStorage(req.file);
    res.json({ url: imageUrl });
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de l'upload" });
  }
}
```

## Sécurité

- Ne jamais commiter le fichier de clé de service
- Utiliser des variables d'environnement pour les configurations sensibles
- Configurer les règles CORS appropriées sur le bucket

## Dépannage

1. Erreur "Could not load the default credentials"

   - Vérifier que le chemin vers le fichier de clé est correct
   - Vérifier que les permissions du fichier sont correctes

2. Erreur "Permission denied"

   - Vérifier que le service account a les bonnes permissions sur le bucket
   - Vérifier que le bucket existe et est accessible

3. Erreur "Bucket not found"
   - Vérifier que le nom du bucket est correct
   - Vérifier que le bucket est créé dans le bon projet
