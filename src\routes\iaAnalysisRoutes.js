const express = require('express');
const router = express.Router();
const { auth, checkRole } = require('../middleware/auth');
const { analyserTendancesSante, predireRisques } = require('../controllers/iaAnalysisController');

// Middleware pour vérifier si l'utilisateur est un vétérinaire
const isVeterinaire = async (req, res, next) => {
  try {
    if (req.user.role !== 'veterinaire') {
      return res.status(403).json({ message: 'Accès refusé. Rôle vétérinaire requis.' });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isVeterinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Route pour analyser les tendances de santé
router.get('/tendances', auth, isVeterinaire, analyserTendancesSante);

// Route pour prédire les risques de maladies
router.post('/prediction-risques', auth, isVeterinaire, predireRisques);

module.exports = router;