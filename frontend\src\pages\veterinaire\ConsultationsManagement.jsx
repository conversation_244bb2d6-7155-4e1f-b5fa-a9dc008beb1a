/**
 * Composant de gestion des consultations pour les vétérinaires
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Autocomplete,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  MedicalServices as MedicalIcon,
  Assignment as ConsultationIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Pets as PetsIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { veterinaireAPI } from '../../services/veterinaireService';

const ConsultationsManagement = () => {
  const { user } = useAuth();
  const [consultations, setConsultations] = useState([]);
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedConsultation, setSelectedConsultation] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  
  const [formData, setFormData] = useState({
    patient_id: '',
    date_consultation: new Date(),
    motif: '',
    symptomes: '',
    diagnostic: '',
    traitement: '',
    recommandations: '',
    statut: 'programmee',
    urgence: false,
    notes: '',
    cout: ''
  });

  const [stats, setStats] = useState({
    totalConsultations: 0,
    consultationsAujourdhui: 0,
    consultationsUrgentes: 0,
    consultationsTerminees: 0
  });

  useEffect(() => {
    loadConsultations();
    loadPatients();
    loadStats();
  }, []);

  const loadConsultations = async () => {
    try {
      setLoading(true);
      const response = await veterinaireAPI.getConsultations();
      setConsultations(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des consultations');
      console.error('Erreur consultations:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    try {
      const response = await veterinaireAPI.getPatients();
      setPatients(response.data || []);
    } catch (err) {
      console.error('Erreur patients:', err);
    }
  };

  const loadStats = async () => {
    try {
      const response = await veterinaireAPI.getConsultationsStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats consultations:', err);
    }
  };

  const handleOpenDialog = (consultation = null) => {
    if (consultation) {
      setSelectedConsultation(consultation);
      setFormData({
        patient_id: consultation.patient_id || '',
        date_consultation: new Date(consultation.date_consultation) || new Date(),
        motif: consultation.motif || '',
        symptomes: consultation.symptomes || '',
        diagnostic: consultation.diagnostic || '',
        traitement: consultation.traitement || '',
        recommandations: consultation.recommandations || '',
        statut: consultation.statut || 'programmee',
        urgence: consultation.urgence || false,
        notes: consultation.notes || '',
        cout: consultation.cout || ''
      });
    } else {
      setSelectedConsultation(null);
      setFormData({
        patient_id: '',
        date_consultation: new Date(),
        motif: '',
        symptomes: '',
        diagnostic: '',
        traitement: '',
        recommandations: '',
        statut: 'programmee',
        urgence: false,
        notes: '',
        cout: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedConsultation(null);
  };

  const handleSubmit = async () => {
    try {
      const consultationData = {
        ...formData,
        veterinaire_id: user.profile_id
      };

      if (selectedConsultation) {
        await veterinaireAPI.updateConsultation(selectedConsultation.id, consultationData);
      } else {
        await veterinaireAPI.createConsultation(consultationData);
      }

      handleCloseDialog();
      loadConsultations();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la consultation');
      console.error('Erreur sauvegarde consultation:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getStatutColor = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'programmee': return 'info';
      case 'en_cours': return 'warning';
      case 'terminee': return 'success';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'programmee': return 'Programmée';
      case 'en_cours': return 'En cours';
      case 'terminee': return 'Terminée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  const getUrgenceColor = (urgence) => {
    return urgence ? 'error' : 'default';
  };

  const StatCard = ({ title, value, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ 
            p: 1, 
            borderRadius: 1, 
            bgcolor: `${color}.light`, 
            color: `${color}.contrastText`,
            mr: 2 
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const filteredConsultations = consultations.filter(consultation => {
    const today = new Date();
    const consultationDate = new Date(consultation.date_consultation);
    
    switch (tabValue) {
      case 0: // Toutes
        return true;
      case 1: // Aujourd'hui
        return consultationDate.toDateString() === today.toDateString();
      case 2: // À venir
        return consultationDate > today && consultation.statut === 'programmee';
      case 3: // Urgentes
        return consultation.urgence;
      default:
        return true;
    }
  });

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des consultations...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Consultations
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Consultation
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Consultations"
              value={stats.totalConsultations}
              icon={<ConsultationIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Aujourd'hui"
              value={stats.consultationsAujourdhui}
              icon={<ScheduleIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Urgentes"
              value={stats.consultationsUrgentes}
              icon={<WarningIcon />}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Terminées"
              value={stats.consultationsTerminees}
              icon={<CheckIcon />}
              color="success"
            />
          </Grid>
        </Grid>

        {/* Onglets de filtrage */}
        <Card sx={{ mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
            <Tab label="Toutes" />
            <Tab label="Aujourd'hui" />
            <Tab label="À venir" />
            <Tab label="Urgentes" />
          </Tabs>
        </Card>

        {/* Table des consultations */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Liste des Consultations
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date/Heure</TableCell>
                    <TableCell>Patient</TableCell>
                    <TableCell>Motif</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Urgence</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredConsultations.map((consultation) => (
                    <TableRow key={consultation.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {new Date(consultation.date_consultation).toLocaleDateString('fr-FR')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(consultation.date_consultation).toLocaleTimeString('fr-FR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PetsIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {consultation.patient_nom}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {consultation.eleveur_nom}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>{consultation.motif}</TableCell>
                      <TableCell>
                        <Chip
                          label={getStatutLabel(consultation.statut)}
                          color={getStatutColor(consultation.statut)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {consultation.urgence && (
                          <Chip
                            label="URGENT"
                            color="error"
                            size="small"
                            icon={<WarningIcon />}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(consultation)}
                          title="Modifier"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => {/* Voir détails */}}
                          title="Voir détails"
                        >
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Dialog pour créer/modifier une consultation */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>
            {selectedConsultation ? 'Modifier la Consultation' : 'Nouvelle Consultation'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={patients}
                  getOptionLabel={(option) => `${option.nom} - ${option.eleveur_nom}`}
                  value={patients.find(p => p.id === formData.patient_id) || null}
                  onChange={(event, newValue) => {
                    setFormData({ ...formData, patient_id: newValue?.id || '' });
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Patient" fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label="Date et Heure"
                  value={formData.date_consultation}
                  onChange={(date) => setFormData({ ...formData, date_consultation: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Motif de la consultation"
                  value={formData.motif}
                  onChange={(e) => setFormData({ ...formData, motif: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={formData.statut}
                    onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                  >
                    <MenuItem value="programmee">Programmée</MenuItem>
                    <MenuItem value="en_cours">En cours</MenuItem>
                    <MenuItem value="terminee">Terminée</MenuItem>
                    <MenuItem value="annulee">Annulée</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Symptômes observés"
                  multiline
                  rows={3}
                  value={formData.symptomes}
                  onChange={(e) => setFormData({ ...formData, symptomes: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Diagnostic"
                  multiline
                  rows={3}
                  value={formData.diagnostic}
                  onChange={(e) => setFormData({ ...formData, diagnostic: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Traitement prescrit"
                  multiline
                  rows={3}
                  value={formData.traitement}
                  onChange={(e) => setFormData({ ...formData, traitement: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Recommandations"
                  multiline
                  rows={2}
                  value={formData.recommandations}
                  onChange={(e) => setFormData({ ...formData, recommandations: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Coût (DA)"
                  type="number"
                  value={formData.cout}
                  onChange={(e) => setFormData({ ...formData, cout: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Urgence</InputLabel>
                  <Select
                    value={formData.urgence}
                    onChange={(e) => setFormData({ ...formData, urgence: e.target.value })}
                  >
                    <MenuItem value={false}>Non</MenuItem>
                    <MenuItem value={true}>Oui - Urgent</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes additionnelles"
                  multiline
                  rows={2}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedConsultation ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default ConsultationsManagement;
