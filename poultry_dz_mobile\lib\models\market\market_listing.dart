import 'package:json_annotation/json_annotation.dart';
import '../location.dart';

part 'market_listing.g.dart';

@JsonSerializable()
class MarketListing {
  final int? id;
  @Json<PERSON>ey(name: 'user_id')
  final int userId;
  final String title;
  final String description;
  @JsonKey(name: 'product_type')
  final String productType; // 'poultry', 'eggs', 'feed', 'equipment', 'chicks'
  @JsonKey(name: 'poultry_type')
  final String? poultryType; // 'chicken', 'turkey', 'duck', 'goose'
  final int quantity;
  final String unit; // 'pieces', 'kg', 'tons', 'boxes'
  final double price;
  @JsonKey(name: 'price_unit')
  final String priceUnit; // 'per_piece', 'per_kg', 'per_ton', 'total'
  final String? breed;
  final int? age; // in days for poultry
  final String? weight; // weight range or average
  @JsonKey(name: 'quality_grade')
  final String qualityGrade; // 'premium', 'standard', 'economy'
  @JsonKey(fromJson: _locationFromJson, toJson: _locationToJson)
  final Location location;
  final List<String> images;
  @JsonKey(name: 'contact_phone')
  final String? contactPhone;
  @JsonKey(name: 'contact_whatsapp')
  final String? contactWhatsapp;
  @JsonKey(name: 'is_negotiable')
  final bool isNegotiable;
  @JsonKey(name: 'min_order')
  final int? minOrder;
  @JsonKey(name: 'max_order')
  final int? maxOrder;
  @JsonKey(name: 'delivery_available')
  final bool deliveryAvailable;
  @JsonKey(name: 'delivery_radius')
  final int? deliveryRadius; // in km
  @JsonKey(name: 'delivery_cost')
  final double? deliveryCost;
  final String status; // 'draft', 'active', 'pending', 'sold', 'expired', 'suspended'
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  @JsonKey(name: 'featured_until')
  final DateTime? featuredUntil;
  @JsonKey(name: 'view_count')
  final int viewCount;
  @JsonKey(name: 'inquiry_count')
  final int inquiryCount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  
  // Additional fields for display
  @JsonKey(name: 'seller_name')
  final String? sellerName;
  @JsonKey(name: 'seller_rating')
  final double? sellerRating;
  @JsonKey(name: 'seller_verified')
  final bool? sellerVerified;
  @JsonKey(name: 'distance_km')
  final double? distanceKm;
  @JsonKey(name: 'is_favorited')
  final bool? isFavorited;
  @JsonKey(name: 'last_price_update')
  final DateTime? lastPriceUpdate;

  MarketListing({
    this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.productType,
    this.poultryType,
    required this.quantity,
    required this.unit,
    required this.price,
    required this.priceUnit,
    this.breed,
    this.age,
    this.weight,
    required this.qualityGrade,
    required this.location,
    this.images = const [],
    this.contactPhone,
    this.contactWhatsapp,
    this.isNegotiable = true,
    this.minOrder,
    this.maxOrder,
    this.deliveryAvailable = false,
    this.deliveryRadius,
    this.deliveryCost,
    this.status = 'draft',
    this.expiresAt,
    this.featuredUntil,
    this.viewCount = 0,
    this.inquiryCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.sellerName,
    this.sellerRating,
    this.sellerVerified,
    this.distanceKm,
    this.isFavorited,
    this.lastPriceUpdate,
  });

  factory MarketListing.fromJson(Map<String, dynamic> json) => _$MarketListingFromJson(json);
  Map<String, dynamic> toJson() => _$MarketListingToJson(this);

  // Helper getters
  bool get isActive => status == 'active';
  bool get isDraft => status == 'draft';
  bool get isSold => status == 'sold';
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  bool get isFeatured => featuredUntil != null && DateTime.now().isBefore(featuredUntil!);
  bool get hasImages => images.isNotEmpty;
  bool get hasLocation => location.latitude != 0 && location.longitude != 0;
  bool get isVerifiedSeller => sellerVerified == true;
  
  String get formattedPrice {
    switch (priceUnit) {
      case 'per_piece':
        return '${price.toStringAsFixed(0)} DA/pièce';
      case 'per_kg':
        return '${price.toStringAsFixed(0)} DA/kg';
      case 'per_ton':
        return '${price.toStringAsFixed(0)} DA/tonne';
      case 'total':
        return '${price.toStringAsFixed(0)} DA total';
      default:
        return '${price.toStringAsFixed(0)} DA';
    }
  }
  
  String get formattedQuantity {
    return '$quantity $unit';
  }
  
  String get formattedLocation {
    return location.toString();
  }
  
  String get ageDescription {
    if (age == null) return '';
    if (age! < 7) return '${age!} jour${age! > 1 ? 's' : ''}';
    if (age! < 30) return '${(age! / 7).floor()} semaine${(age! / 7).floor() > 1 ? 's' : ''}';
    return '${(age! / 30).floor()} mois';
  }
  
  String get distanceDescription {
    if (distanceKm == null) return '';
    if (distanceKm! < 1) return '${(distanceKm! * 1000).round()}m';
    return '${distanceKm!.toStringAsFixed(1)}km';
  }

  // Copy with method for updates
  MarketListing copyWith({
    int? id,
    int? userId,
    String? title,
    String? description,
    String? productType,
    String? poultryType,
    int? quantity,
    String? unit,
    double? price,
    String? priceUnit,
    String? breed,
    int? age,
    String? weight,
    String? qualityGrade,
    Location? location,
    List<String>? images,
    String? contactPhone,
    String? contactWhatsapp,
    bool? isNegotiable,
    int? minOrder,
    int? maxOrder,
    bool? deliveryAvailable,
    int? deliveryRadius,
    double? deliveryCost,
    String? status,
    DateTime? expiresAt,
    DateTime? featuredUntil,
    int? viewCount,
    int? inquiryCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? sellerName,
    double? sellerRating,
    bool? sellerVerified,
    double? distanceKm,
    bool? isFavorited,
    DateTime? lastPriceUpdate,
  }) {
    return MarketListing(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      productType: productType ?? this.productType,
      poultryType: poultryType ?? this.poultryType,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      price: price ?? this.price,
      priceUnit: priceUnit ?? this.priceUnit,
      breed: breed ?? this.breed,
      age: age ?? this.age,
      weight: weight ?? this.weight,
      qualityGrade: qualityGrade ?? this.qualityGrade,
      location: location ?? this.location,
      images: images ?? this.images,
      contactPhone: contactPhone ?? this.contactPhone,
      contactWhatsapp: contactWhatsapp ?? this.contactWhatsapp,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      minOrder: minOrder ?? this.minOrder,
      maxOrder: maxOrder ?? this.maxOrder,
      deliveryAvailable: deliveryAvailable ?? this.deliveryAvailable,
      deliveryRadius: deliveryRadius ?? this.deliveryRadius,
      deliveryCost: deliveryCost ?? this.deliveryCost,
      status: status ?? this.status,
      expiresAt: expiresAt ?? this.expiresAt,
      featuredUntil: featuredUntil ?? this.featuredUntil,
      viewCount: viewCount ?? this.viewCount,
      inquiryCount: inquiryCount ?? this.inquiryCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sellerName: sellerName ?? this.sellerName,
      sellerRating: sellerRating ?? this.sellerRating,
      sellerVerified: sellerVerified ?? this.sellerVerified,
      distanceKm: distanceKm ?? this.distanceKm,
      isFavorited: isFavorited ?? this.isFavorited,
      lastPriceUpdate: lastPriceUpdate ?? this.lastPriceUpdate,
    );
  }
}

// Enums for better type safety
enum ProductType {
  poultry('poultry'),
  eggs('eggs'),
  feed('feed'),
  equipment('equipment'),
  chicks('chicks');

  const ProductType(this.value);
  final String value;
}

enum PoultryType {
  chicken('chicken'),
  turkey('turkey'),
  duck('duck'),
  goose('goose');

  const PoultryType(this.value);
  final String value;
}

enum QualityGrade {
  premium('premium'),
  standard('standard'),
  economy('economy');

  const QualityGrade(this.value);
  final String value;
}

enum ListingStatus {
  draft('draft'),
  active('active'),
  pending('pending'),
  sold('sold'),
  expired('expired'),
  suspended('suspended');

  const ListingStatus(this.value);
  final String value;
}

  static Location _locationFromJson(Map<String, dynamic> json) {
    return Location(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      wilaya: json['wilaya'] as String?,
      commune: json['commune'] as String?,
      address: json['location'] as String?,
    );
  }

  static Map<String, dynamic> _locationToJson(Location location) => {
    'latitude': location.latitude,
    'longitude': location.longitude,
    'wilaya': location.wilaya,
    'commune': location.commune,
    'location': location.address,
  };