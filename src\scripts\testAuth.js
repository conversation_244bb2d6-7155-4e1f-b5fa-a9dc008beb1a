require('dotenv').config({ path: 'K:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/.env' });
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Utiliser des valeurs par défaut si les variables d'environnement ne sont pas définies
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '5432';
const DB_NAME = process.env.DB_NAME || 'poultraydz';

console.log('Connexion à la base de données avec les paramètres suivants:');
console.log(`User: ${DB_USER}`);
console.log(`Host: ${DB_HOST}`);
console.log(`Database: ${DB_NAME}`);
console.log(`Port: ${DB_PORT}`);

// Utiliser une chaîne de connexion au lieu des paramètres individuels
const connectionString = `postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
console.log(`Connection string: ${connectionString}`);

const pool = new Pool({
  connectionString,
});

async function testLogin(email, password) {
  try {
    console.log('Début de la méthode login pour:', { email });

    // Find user by email
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    console.log('Résultat de la requête utilisateur:', { rowCount: userResult.rowCount });

    const user = userResult.rows[0];

    if (!user) {
      console.log('Utilisateur non trouvé pour:', { email });
      throw new Error('Utilisateur non trouvé');
    }

    console.log('Utilisateur trouvé:', { id: user.id, username: user.username, role: user.role });

    // Check password
    console.log('Vérification du mot de passe pour:', { email });
    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) {
      console.log('Mot de passe incorrect pour:', { email });
      throw new Error('Mot de passe incorrect');
    }

    console.log('Mot de passe correct pour:', { email });

    // Update last login
    await pool.query('UPDATE users SET last_login = NOW() WHERE id = $1', [user.id]);
    console.log('Dernière connexion mise à jour pour:', { id: user.id });

    // Generate JWT token
    const payload = {
      user: {
        id: user.id,
        role: user.role
      }
    };

    console.log('Génération du token JWT pour:', { id: user.id, role: user.role });
    console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Défini' : 'Non défini');

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET || 'poultray_dz_secret_key_2023',
      { expiresIn: '24h' }
    );

    console.log('Token JWT généré avec succès pour:', { id: user.id });

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name
      }
    };
  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    throw error;
  }
}

async function testAuth() {
  try {
    console.log('Test d\'authentification...');
    
    // Test de connexion à la base de données
    await pool.query('SELECT NOW()');
    console.log('Connexion à la base de données établie avec succès');
    
    // Test de connexion avec l'utilisateur admin
    const email = '<EMAIL>';
    const password = 'admin123';
    
    console.log('Test de connexion avec:', { email, password });
    
    const authData = await testLogin(email, password);
    console.log('Connexion réussie:', authData);
    
    // Fermer la connexion à la base de données
    await pool.end();
    console.log('Connexion à la base de données fermée');
    
    process.exit(0);
  } catch (error) {
    console.error('Erreur lors du test d\'authentification:', error);
    process.exit(1);
  }
}

testAuth();
