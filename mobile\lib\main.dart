import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/services/notification_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/connectivity_service.dart';
import 'core/providers/app_providers.dart';
import 'generated/l10n.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configuration de l'orientation
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Initialisation Firebase
  await Firebase.initializeApp();
  
  // Initialisation Hive
  await Hive.initFlutter();
  
  // Initialisation des services
  await _initializeServices();
  
  runApp(
    const ProviderScope(
      child: PoultrayDZApp(),
    ),
  );
}

Future<void> _initializeServices() async {
  try {
    // Initialiser le stockage local
    await StorageService.instance.initialize();
    
    // Initialiser les notifications
    await NotificationService.instance.initialize();
    
    // Initialiser la connectivité
    await ConnectivityService.instance.initialize();
    
    print('✅ Services initialisés avec succès');
  } catch (e) {
    print('❌ Erreur lors de l\'initialisation des services: $e');
  }
}

class PoultrayDZApp extends ConsumerWidget {
  const PoultrayDZApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(localeProvider);

    return MaterialApp.router(
      title: 'Poultray DZ',
      debugShowCheckedModeBanner: false,
      
      // Configuration du routeur
      routerConfig: router,
      
      // Configuration du thème
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      
      // Configuration de la localisation
      locale: locale,
      supportedLocales: AppConfig.supportedLocales,
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      // Configuration du builder pour les overlays
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: 1.0, // Empêcher le scaling automatique
          ),
          child: child ?? const SizedBox.shrink(),
        );
      },
    );
  }
}

/// Configuration globale de l'application
class AppConfig {
  static const String appName = 'Poultray DZ';
  static const String version = '1.0.0';
  
  // URLs de l'API
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api.poultraydz.com',
  );
  
  static const String wsUrl = String.fromEnvironment(
    'WS_URL',
    defaultValue: 'wss://api.poultraydz.com/ws',
  );
  
  // Configuration de l'authentification
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // Configuration du cache
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100; // MB
  
  // Configuration des notifications
  static const String fcmSenderId = '123456789';
  static const String fcmServerKey = 'your-server-key';
  
  // Langues supportées
  static const List<Locale> supportedLocales = [
    Locale('fr', 'FR'),
    Locale('ar', 'DZ'),
  ];
  
  // Configuration de la géolocalisation
  static const double defaultLatitude = 36.7538;
  static const double defaultLongitude = 3.0588; // Alger
  static const double locationAccuracy = 100.0; // mètres
  
  // Configuration des médias
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxVideoSize = 50 * 1024 * 1024; // 50MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedVideoFormats = ['mp4', 'mov', 'avi'];
  
  // Configuration de la synchronisation
  static const Duration syncInterval = Duration(minutes: 15);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 5);
  
  // Configuration de la sécurité
  static const bool enableBiometrics = true;
  static const bool enablePinCode = true;
  static const Duration autoLockTimeout = Duration(minutes: 5);
  
  // Configuration du développement
  static const bool isDebugMode = bool.fromEnvironment('DEBUG', defaultValue: false);
  static const bool enableLogging = bool.fromEnvironment('ENABLE_LOGGING', defaultValue: true);
  static const bool enableAnalytics = bool.fromEnvironment('ENABLE_ANALYTICS', defaultValue: true);
}

/// Gestionnaire d'erreurs global
class GlobalErrorHandler {
  static void initialize() {
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      _logError(details.exception, details.stack);
    };
  }
  
  static void _logError(dynamic error, StackTrace? stackTrace) {
    if (AppConfig.enableLogging) {
      print('🔥 Erreur Flutter: $error');
      if (stackTrace != null) {
        print('📍 Stack trace: $stackTrace');
      }
    }
    
    // En production, envoyer à un service de monitoring
    // comme Crashlytics, Sentry, etc.
  }
  
  static void handleError(dynamic error, [StackTrace? stackTrace]) {
    _logError(error, stackTrace);
  }
}

/// Utilitaires pour l'application
class AppUtils {
  /// Formater une taille de fichier
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// Valider un email
  static bool isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
  }
  
  /// Valider un numéro de téléphone algérien
  static bool isValidPhoneNumber(String phone) {
    return RegExp(r'^(\+213|0)[5-7][0-9]{8}$').hasMatch(phone);
  }
  
  /// Générer un ID unique
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        '_' +
        (1000 + (999 * (DateTime.now().microsecond / 1000000))).round().toString();
  }
  
  /// Débouncer une fonction
  static Timer? _debounceTimer;
  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }
  
  /// Throttler une fonction
  static DateTime? _lastThrottleTime;
  static void throttle(Duration delay, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      callback();
    }
  }
}

/// Extensions utiles
extension StringExtensions on String {
  /// Capitaliser la première lettre
  String capitalize() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }
  
  /// Tronquer le texte
  String truncate(int maxLength, [String suffix = '...']) {
    if (length <= maxLength) return this;
    return substring(0, maxLength - suffix.length) + suffix;
  }
  
  /// Supprimer les accents
  String removeAccents() {
    const accents = 'àáäâèéëêìíïîòóöôùúüûñç';
    const withoutAccents = 'aaaaeeeeiiiioooouuuunc';
    
    String result = this;
    for (int i = 0; i < accents.length; i++) {
      result = result.replaceAll(accents[i], withoutAccents[i]);
      result = result.replaceAll(accents[i].toUpperCase(), withoutAccents[i].toUpperCase());
    }
    return result;
  }
}

extension DateTimeExtensions on DateTime {
  /// Vérifier si c'est aujourd'hui
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  /// Vérifier si c'est hier
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
  
  /// Obtenir le début de la journée
  DateTime get startOfDay {
    return DateTime(year, month, day);
  }
  
  /// Obtenir la fin de la journée
  DateTime get endOfDay {
    return DateTime(year, month, day, 23, 59, 59, 999);
  }
}

import 'dart:async';
