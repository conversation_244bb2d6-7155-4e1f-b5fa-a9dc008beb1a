version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: poultraydz_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-poultraydz}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=fr_FR.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - poultraydz_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-poultraydz}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis pour le cache et les sessions
  redis:
    image: redis:7-alpine
    container_name: poultraydz_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - poultraydz_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Node.js
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
      args:
        NODE_ENV: production
    container_name: poultraydz_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-poultraydz}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://app.poultraydz.com}
      FILE_UPLOAD_PATH: /app/uploads
      LOG_LEVEL: ${LOG_LEVEL:-info}
      SENTRY_DSN: ${SENTRY_DSN}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    ports:
      - "3000:3000"
    networks:
      - poultraydz_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend React (build statique servi par Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-https://api.poultraydz.com}
        REACT_APP_WS_URL: ${REACT_APP_WS_URL:-wss://api.poultraydz.com/ws}
        REACT_APP_SENTRY_DSN: ${REACT_APP_SENTRY_DSN}
        REACT_APP_GOOGLE_MAPS_API_KEY: ${REACT_APP_GOOGLE_MAPS_API_KEY}
    container_name: poultraydz_frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - logs_data:/var/log/nginx
    networks:
      - poultraydz_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service de monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: poultraydz_prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - poultraydz_network

  # Grafana pour les dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: poultraydz_grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - poultraydz_network
    depends_on:
      - prometheus

  # Service de sauvegarde automatique
  backup:
    build:
      context: ./scripts/backup
      dockerfile: Dockerfile
    container_name: poultraydz_backup
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_NAME: ${DB_NAME:-poultraydz}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-0 2 * * *}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-30}
      S3_BUCKET: ${S3_BACKUP_BUCKET}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      S3_REGION: ${S3_REGION:-eu-west-1}
    volumes:
      - ./database/backups:/backups
      - uploads_data:/app/uploads:ro
    networks:
      - poultraydz_network
    depends_on:
      postgres:
        condition: service_healthy

  # Service de logs centralisé
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: poultraydz_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - poultraydz_network

  # Logstash pour le traitement des logs
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: poultraydz_logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - logs_data:/logs:ro
    networks:
      - poultraydz_network
    depends_on:
      - elasticsearch

  # Kibana pour la visualisation des logs
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: poultraydz_kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - poultraydz_network
    depends_on:
      - elasticsearch

  # Service de notification et alertes
  alertmanager:
    image: prom/alertmanager:latest
    container_name: poultraydz_alertmanager
    restart: unless-stopped
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - poultraydz_network

  # Service de reverse proxy et load balancer
  traefik:
    image: traefik:v3.0
    container_name: poultraydz_traefik
    restart: unless-stopped
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_data:/letsencrypt
    networks:
      - poultraydz_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.poultraydz.com`)"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  alertmanager_data:
    driver: local
  traefik_data:
    driver: local

networks:
  poultraydz_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
