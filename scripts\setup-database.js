#!/usr/bin/env node

/**
 * Script de configuration de la base de données
 * Crée toutes les tables nécessaires pour l'application Poultray DZ
 */

const { createMissingTables } = require('../src/database/createMissingTables');

async function setupDatabase() {
  console.log('🚀 Configuration de la base de données Poultray DZ...');
  console.log('=====================================');
  
  try {
    await createMissingTables();
    console.log('=====================================');
    console.log('✅ Configuration terminée avec succès !');
    console.log('📊 La base de données est prête à être utilisée.');
    
  } catch (error) {
    console.error('=====================================');
    console.error('❌ Erreur lors de la configuration:', error.message);
    console.error('📋 Détails:', error);
    process.exit(1);
  }
}

// Exécuter le script
setupDatabase();
