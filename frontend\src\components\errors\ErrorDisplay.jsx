import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Collapse,
  IconButton,
  Typography,
  Chip,
  Stack
} from '@mui/material';
import {
  Close,
  Refresh,
  WifiOff,
  Error as ErrorIcon,
  Warning,
  Info,
  CheckCircle
} from '@mui/icons-material';
import { ERROR_TYPES } from '../../services/errorHandler';

/**
 * Composant pour afficher une erreur individuelle
 */
const ErrorItem = ({ 
  error, 
  onClose, 
  onRetry, 
  showDetails = false,
  allowRetry = true 
}) => {
  const [expanded, setExpanded] = React.useState(false);

  const getSeverity = () => {
    switch (error.type) {
      case ERROR_TYPES.AUTH:
      case ERROR_TYPES.FORBIDDEN:
        return 'error';
      case ERROR_TYPES.NETWORK:
      case ERROR_TYPES.TIMEOUT:
        return 'warning';
      case ERROR_TYPES.VALIDATION:
        return 'info';
      case ERROR_TYPES.SERVER:
        return 'error';
      default:
        return 'warning';
    }
  };

  const getIcon = () => {
    switch (error.type) {
      case ERROR_TYPES.NETWORK:
        return <WifiOff />;
      case ERROR_TYPES.AUTH:
      case ERROR_TYPES.FORBIDDEN:
        return <ErrorIcon />;
      case ERROR_TYPES.VALIDATION:
        return <Info />;
      case ERROR_TYPES.SERVER:
        return <Warning />;
      default:
        return <ErrorIcon />;
    }
  };

  const getTitle = () => {
    switch (error.type) {
      case ERROR_TYPES.NETWORK:
        return 'Problème de connexion';
      case ERROR_TYPES.TIMEOUT:
        return 'Délai d\'attente dépassé';
      case ERROR_TYPES.AUTH:
        return 'Erreur d\'authentification';
      case ERROR_TYPES.FORBIDDEN:
        return 'Accès refusé';
      case ERROR_TYPES.VALIDATION:
        return 'Données invalides';
      case ERROR_TYPES.SERVER:
        return 'Erreur serveur';
      case ERROR_TYPES.NOT_FOUND:
        return 'Ressource introuvable';
      case ERROR_TYPES.RATE_LIMIT:
        return 'Trop de requêtes';
      default:
        return 'Erreur';
    }
  };

  return (
    <Alert
      severity={getSeverity()}
      icon={getIcon()}
      action={
        <Box display="flex" alignItems="center" gap={1}>
          {allowRetry && error.retryable && onRetry && (
            <Button
              size="small"
              startIcon={<Refresh />}
              onClick={() => onRetry(error)}
              variant="outlined"
            >
              Réessayer
            </Button>
          )}
          {onClose && (
            <IconButton
              size="small"
              onClick={() => onClose(error.timestamp)}
            >
              <Close fontSize="small" />
            </IconButton>
          )}
        </Box>
      }
      sx={{ mb: 1 }}
    >
      <AlertTitle>{getTitle()}</AlertTitle>
      
      <Typography variant="body2" gutterBottom>
        {error.userMessage}
      </Typography>

      {showDetails && (
        <Box mt={1}>
          <Button
            size="small"
            onClick={() => setExpanded(!expanded)}
            variant="text"
          >
            {expanded ? 'Masquer les détails' : 'Voir les détails'}
          </Button>
          
          <Collapse in={expanded}>
            <Box mt={2} p={2} bgcolor="grey.100" borderRadius={1}>
              <Stack spacing={1}>
                <Box>
                  <Typography variant="caption" fontWeight="bold">
                    Code d'erreur:
                  </Typography>
                  <Chip label={error.code} size="small" sx={{ ml: 1 }} />
                </Box>
                
                <Box>
                  <Typography variant="caption" fontWeight="bold">
                    Timestamp:
                  </Typography>
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    {new Date(error.timestamp).toLocaleString()}
                  </Typography>
                </Box>

                {error.details.statusCode && (
                  <Box>
                    <Typography variant="caption" fontWeight="bold">
                      Code HTTP:
                    </Typography>
                    <Typography variant="caption" sx={{ ml: 1 }}>
                      {error.details.statusCode}
                    </Typography>
                  </Box>
                )}

                {error.context && Object.keys(error.context).length > 0 && (
                  <Box>
                    <Typography variant="caption" fontWeight="bold">
                      Contexte:
                    </Typography>
                    <Typography variant="caption" component="pre" sx={{ ml: 1, fontSize: '0.7rem' }}>
                      {JSON.stringify(error.context, null, 2)}
                    </Typography>
                  </Box>
                )}
              </Stack>
            </Box>
          </Collapse>
        </Box>
      )}
    </Alert>
  );
};

/**
 * Composant pour afficher une liste d'erreurs
 */
const ErrorList = ({ 
  errors, 
  onClearError, 
  onClearAll, 
  onRetry,
  maxVisible = 5,
  showDetails = false,
  allowRetry = true
}) => {
  const visibleErrors = errors.slice(-maxVisible);
  const hiddenCount = errors.length - visibleErrors.length;

  if (errors.length === 0) {
    return null;
  }

  return (
    <Box>
      {hiddenCount > 0 && (
        <Alert severity="info" sx={{ mb: 1 }}>
          <Typography variant="body2">
            {hiddenCount} erreur(s) supplémentaire(s) masquée(s).
          </Typography>
          <Button size="small" onClick={onClearAll} sx={{ mt: 1 }}>
            Tout effacer
          </Button>
        </Alert>
      )}

      {visibleErrors.map((error) => (
        <ErrorItem
          key={error.timestamp}
          error={error}
          onClose={onClearError}
          onRetry={onRetry}
          showDetails={showDetails}
          allowRetry={allowRetry}
        />
      ))}

      {errors.length > 1 && (
        <Box mt={2} textAlign="center">
          <Button
            variant="outlined"
            size="small"
            onClick={onClearAll}
            startIcon={<Close />}
          >
            Effacer toutes les erreurs
          </Button>
        </Box>
      )}
    </Box>
  );
};

/**
 * Composant principal pour afficher les erreurs
 */
const ErrorDisplay = ({ 
  errors = [], 
  onClearError, 
  onClearAll, 
  onRetry,
  variant = 'list', // 'list' | 'summary' | 'toast'
  maxVisible = 5,
  showDetails = false,
  allowRetry = true,
  position = 'relative' // 'fixed' | 'relative'
}) => {
  if (errors.length === 0) {
    return null;
  }

  const containerProps = position === 'fixed' ? {
    position: 'fixed',
    top: 16,
    right: 16,
    zIndex: 9999,
    maxWidth: 400,
    width: '100%'
  } : {};

  if (variant === 'summary') {
    const errorCounts = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {});

    return (
      <Box {...containerProps}>
        <Alert
          severity="warning"
          action={
            <IconButton size="small" onClick={onClearAll}>
              <Close fontSize="small" />
            </IconButton>
          }
        >
          <AlertTitle>Erreurs détectées ({errors.length})</AlertTitle>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {Object.entries(errorCounts).map(([type, count]) => (
              <Chip
                key={type}
                label={`${type}: ${count}`}
                size="small"
                variant="outlined"
              />
            ))}
          </Stack>
        </Alert>
      </Box>
    );
  }

  return (
    <Box {...containerProps}>
      <ErrorList
        errors={errors}
        onClearError={onClearError}
        onClearAll={onClearAll}
        onRetry={onRetry}
        maxVisible={maxVisible}
        showDetails={showDetails}
        allowRetry={allowRetry}
      />
    </Box>
  );
};

/**
 * Composant pour afficher l'état de connexion
 */
export const ConnectionStatus = ({ isOnline }) => {
  if (isOnline) {
    return null;
  }

  return (
    <Alert
      severity="error"
      icon={<WifiOff />}
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10000,
        borderRadius: 0
      }}
    >
      <AlertTitle>Connexion perdue</AlertTitle>
      Vérifiez votre connexion internet. L'application se reconnectera automatiquement.
    </Alert>
  );
};

export default ErrorDisplay;
