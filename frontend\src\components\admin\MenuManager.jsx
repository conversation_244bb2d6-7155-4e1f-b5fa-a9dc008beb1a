import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragHandle as DragHandleIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const MenuManager = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [headerMenu, setHeaderMenu] = useState([]);
  const [footerMenu, setFooterMenu] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [menuItem, setMenuItem] = useState({
    title: '',
    link: '',
    type: 'link',
    parent: null
  });

  useEffect(() => {
    loadMenus();
  }, []);

  const loadMenus = async () => {
    try {
      // Implémenter le chargement des menus depuis l'API
      const mockHeaderMenu = [
        { id: 1, title: 'Accueil', link: '/', type: 'link' },
        { id: 2, title: 'Services', link: '/services', type: 'link' }
      ];
      const mockFooterMenu = [
        { id: 1, title: 'À propos', link: '/about', type: 'link' },
        { id: 2, title: 'Contact', link: '/contact', type: 'link' }
      ];
      setHeaderMenu(mockHeaderMenu);
      setFooterMenu(mockFooterMenu);
    } catch (error) {
      console.error('Erreur lors du chargement des menus:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleOpenDialog = (item = null) => {
    if (item) {
      setEditingItem(item);
      setMenuItem(item);
    } else {
      setEditingItem(null);
      setMenuItem({ title: '', link: '', type: 'link', parent: null });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingItem(null);
    setMenuItem({ title: '', link: '', type: 'link', parent: null });
  };

  const handleSaveMenuItem = async () => {
    try {
      const currentMenu = activeTab === 0 ? headerMenu : footerMenu;
      const updatedMenu = editingItem
        ? currentMenu.map((item) =>
            item.id === editingItem.id ? { ...item, ...menuItem } : item
          )
        : [...currentMenu, { ...menuItem, id: Date.now() }];

      if (activeTab === 0) {
        setHeaderMenu(updatedMenu);
      } else {
        setFooterMenu(updatedMenu);
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du menu:', error);
    }
  };

  const handleDeleteMenuItem = async (itemId) => {
    try {
      const currentMenu = activeTab === 0 ? headerMenu : footerMenu;
      const updatedMenu = currentMenu.filter((item) => item.id !== itemId);

      if (activeTab === 0) {
        setHeaderMenu(updatedMenu);
      } else {
        setFooterMenu(updatedMenu);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du menu:', error);
    }
  };

  const onDragEnd = (result) => {
    if (!result.destination) return;

    const items = activeTab === 0 ? [...headerMenu] : [...footerMenu];
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    if (activeTab === 0) {
      setHeaderMenu(items);
    } else {
      setFooterMenu(items);
    }
  };

  const renderMenuItems = (items) => (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="menu-items">
        {(provided) => (
          <List {...provided.droppableProps} ref={provided.innerRef}>
            {items.map((item, index) => (
              <Draggable key={item.id} draggableId={String(item.id)} index={index}>
                {(provided) => (
                  <ListItem
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <DragHandleIcon sx={{ mr: 2 }} />
                    <ListItemText primary={item.title} secondary={item.link} />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        aria-label="edit"
                        onClick={() => handleOpenDialog(item)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        aria-label="delete"
                        onClick={() => handleDeleteMenuItem(item.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </List>
        )}
      </Droppable>
    </DragDropContext>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Gestion des Menus
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Menu Principal" />
          <Tab label="Menu Footer" />
        </Tabs>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Ajouter un élément
        </Button>
      </Box>

      <Card>
        <CardContent>
          {activeTab === 0 ? renderMenuItems(headerMenu) : renderMenuItems(footerMenu)}
        </CardContent>
      </Card>

      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>
          {editingItem ? 'Modifier l\'élément' : 'Ajouter un élément'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Titre"
              value={menuItem.title}
              onChange={(e) => setMenuItem({ ...menuItem, title: e.target.value })}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Lien"
              value={menuItem.link}
              onChange={(e) => setMenuItem({ ...menuItem, link: e.target.value })}
              margin="normal"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Type</InputLabel>
              <Select
                value={menuItem.type}
                onChange={(e) => setMenuItem({ ...menuItem, type: e.target.value })}
                label="Type"
              >
                <MenuItem value="link">Lien</MenuItem>
                <MenuItem value="dropdown">Menu déroulant</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSaveMenuItem} variant="contained" color="primary">
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MenuManager;
