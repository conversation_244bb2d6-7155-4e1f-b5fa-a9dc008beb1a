const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const auth = (req, res, next) => {
  try {
    console.log('=== Début de la vérification du token JWT ===');
    console.log('Route accédée:', req.originalUrl);
    // Get token from header (check both x-auth-token and Authorization header)
    let token = req.header('x-auth-token');
    console.log('Token trouvé dans x-auth-token:', token ? 'Oui' : 'Non');

    // Check Authorization header (Bearer token)
    const authHeader = req.header('Authorization');
    console.log('En-tête Authorization présent:', authHeader ? 'Oui' : 'Non');
    
    if (!token && authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log('Token extrait de l\'en-tête Authorization');
    }

    // Check if no token or empty token
    if (!token || !token.trim()) {
      console.log('Accès refusé, token manquant ou vide pour la route:', req.originalUrl);
      return res.status(401).json({
        status: 'error',
        code: 'TOKEN_MISSING',
        message: 'Accès refusé, token manquant ou invalide'
      });
    }

    // Verify JWT_SECRET is configured
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET non défini');
      return res.status(500).json({
        status: 'error',
        code: 'JWT_CONFIG_ERROR',
        message: 'Erreur de configuration du serveur'
      });
    }

    // Verify token
    console.log('Tentative de vérification du token JWT...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('Token décodé avec succès');

    // Validate decoded token structure
    console.log('Validation de la structure du token...');
    if (!decoded || typeof decoded !== 'object') {
      console.error('Token malformé - Structure invalide');
      throw new Error('Token malformé');
    }

    // Validate required fields
    const requiredFields = ['id', 'email', 'role', 'username'];
    const missingFields = requiredFields.filter(field => !decoded[field]);
    
    if (missingFields.length > 0) {
      console.error('Champs manquants dans le token:', missingFields);
      throw new Error(`Token invalide: champs manquants: ${missingFields.join(', ')}`);
    }

    // Validate token expiration
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < now) {
      console.error('Token expiré:', {
        expiration: new Date(decoded.exp * 1000),
        now: new Date(now * 1000)
      });
      throw new Error('Token expiré');
    }

    // Set user info in request
    req.user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      username: decoded.username,
      first_name: decoded.first_name,
      last_name: decoded.last_name,
      status: decoded.status
    };

    console.log('Informations utilisateur extraites avec succès:', {
      id: '***',
      email: '***',
      role: req.user.role,
      username: req.user.username
    });

    next();
  } catch (err) {
    console.error('=== ERREUR de vérification du token JWT ===');
    console.error('Route:', req.originalUrl);
    console.error('Type d\'erreur:', err.name);
    console.error('Message:', err.message);
    console.error('Stack:', err.stack);

    // Détails de l'erreur pour le développement
    const errorDetails = process.env.NODE_ENV === 'development' ? {
      error_type: err.name,
      error_message: err.message,
      stack: err.stack
    } : undefined;

    // Handle specific JWT errors
    switch (err.name) {
      case 'TokenExpiredError':
        return res.status(401).json({
          status: 'error',
          code: 'TOKEN_EXPIRED',
          message: 'Votre session a expiré. Veuillez vous reconnecter.',
          details: errorDetails
        });

      case 'JsonWebTokenError':
        return res.status(401).json({
          status: 'error',
          code: 'TOKEN_INVALID',
          message: 'Token d\'authentification invalide.',
          details: errorDetails
        });

      case 'NotBeforeError':
        return res.status(401).json({
          status: 'error',
          code: 'TOKEN_NOT_ACTIVE',
          message: 'Token non encore actif.',
          details: errorDetails
        });

      default:
        return res.status(401).json({
          status: 'error',
          code: 'AUTH_ERROR',
          message: 'Erreur d\'authentification. Veuillez vous reconnecter.',
          details: errorDetails
        });
    }
    console.error('=== FIN de l\'erreur de vérification du token JWT ===');
  }
};

// Middleware to check user role
const checkRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Accès refusé, utilisateur non authentifié' });
    }

    const { role } = req.user;

    if (!roles.includes(role)) {
      return res.status(403).json({ message: 'Accès refusé, permissions insuffisantes' });
    }

    next();
  };
};

module.exports = {
  auth,
  checkRole
};
