import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton
} from '@mui/material';
import {
  Save as SaveIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useStockAlerts } from '../../../hooks/useStockAlerts';

const NOTIFICATION_METHODS = {
  email: 'Email',
  sms: 'SMS',
  app: 'Application mobile',
  all: 'Toutes les méthodes'
};

const PRODUCT_CATEGORIES = {
  aliments: 'Aliments',
  medicaments: 'Médicaments',
  equipements: 'Équipements',
  autres: 'Autres'
};

const AlertSettings = () => {
  const { t } = useTranslation();
  const {
    settings,
    loading,
    error,
    updateSettings,
    deleteThreshold,
    testNotification
  } = useStockAlerts();

  const [formData, setFormData] = useState({
    category: '',
    threshold_type: 'quantity',
    threshold_value: '',
    notification_method: 'all',
    notification_frequency: 'daily',
    enabled: true
  });

  const [globalSettings, setGlobalSettings] = useState({
    email_notifications: true,
    sms_notifications: false,
    app_notifications: true,
    notification_time: '08:00',
    weekend_notifications: false
  });

  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [editingId, setEditingId] = useState(null);

  useEffect(() => {
    if (settings) {
      setGlobalSettings(settings.global || globalSettings);
    }
  }, [settings]);

  const handleChange = (event) => {
    const { name, value, checked } = event.target;
    if (name.startsWith('global_')) {
      setGlobalSettings(prev => ({
        ...prev,
        [name.replace('global_', '')]: event.target.type === 'checkbox' ? checked : value
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: event.target.type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setSubmitting(true);

    try {
      const settingsData = {
        thresholds: {
          ...formData,
          id: editingId || Date.now()
        },
        global: globalSettings
      };

      await updateSettings(settingsData);
      setSuccess(true);
      setEditingId(null);
      setFormData({
        category: '',
        threshold_type: 'quantity',
        threshold_value: '',
        notification_method: 'all',
        notification_frequency: 'daily',
        enabled: true
      });

      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Erreur lors de la mise à jour des paramètres:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (threshold) => {
    setEditingId(threshold.id);
    setFormData({
      category: threshold.category,
      threshold_type: threshold.threshold_type,
      threshold_value: threshold.threshold_value,
      notification_method: threshold.notification_method,
      notification_frequency: threshold.notification_frequency,
      enabled: threshold.enabled
    });
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('Êtes-vous sûr de vouloir supprimer ce seuil ?'))) {
      try {
        await deleteThreshold(id);
      } catch (err) {
        console.error('Erreur lors de la suppression du seuil:', err);
      }
    }
  };

  const handleTestNotification = async () => {
    try {
      await testNotification();
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Erreur lors du test de notification:', err);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {t('Paramètres des Alertes')}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {t('Paramètres mis à jour avec succès')}
              </Alert>
            )}

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              {t('Paramètres Globaux')}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 4 }}>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={globalSettings.email_notifications}
                      onChange={handleChange}
                      name="global_email_notifications"
                    />
                  }
                  label={t('Notifications par email')}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={globalSettings.sms_notifications}
                      onChange={handleChange}
                      name="global_sms_notifications"
                    />
                  }
                  label={t('Notifications par SMS')}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={globalSettings.app_notifications}
                      onChange={handleChange}
                      name="global_app_notifications"
                    />
                  }
                  label={t('Notifications dans l\'application')}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={globalSettings.weekend_notifications}
                      onChange={handleChange}
                      name="global_weekend_notifications"
                    />
                  }
                  label={t('Notifications le weekend')}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="time"
                  label={t('Heure des notifications')}
                  name="global_notification_time"
                  value={globalSettings.notification_time}
                  onChange={handleChange}
                />
              </Grid>

              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  startIcon={<NotificationsIcon />}
                  onClick={handleTestNotification}
                >
                  {t('Tester les notifications')}
                </Button>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              {t('Seuils d\'Alerte par Catégorie')}
            </Typography>

            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    select
                    fullWidth
                    label={t('Catégorie')}
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                  >
                    {Object.entries(PRODUCT_CATEGORIES).map(([key, value]) => (
                      <MenuItem key={key} value={key}>
                        {t(value)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    select
                    fullWidth
                    label={t('Type de seuil')}
                    name="threshold_type"
                    value={formData.threshold_type}
                    onChange={handleChange}
                    required
                  >
                    <MenuItem value="quantity">{t('Quantité')}</MenuItem>
                    <MenuItem value="percentage">{t('Pourcentage')}</MenuItem>
                    <MenuItem value="days">{t('Jours restants')}</MenuItem>
                  </TextField>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label={t('Valeur du seuil')}
                    name="threshold_value"
                    type="number"
                    value={formData.threshold_value}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    select
                    fullWidth
                    label={t('Méthode de notification')}
                    name="notification_method"
                    value={formData.notification_method}
                    onChange={handleChange}
                    required
                  >
                    {Object.entries(NOTIFICATION_METHODS).map(([key, value]) => (
                      <MenuItem key={key} value={key}>
                        {t(value)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    select
                    fullWidth
                    label={t('Fréquence des notifications')}
                    name="notification_frequency"
                    value={formData.notification_frequency}
                    onChange={handleChange}
                    required
                  >
                    <MenuItem value="daily">{t('Quotidienne')}</MenuItem>
                    <MenuItem value="weekly">{t('Hebdomadaire')}</MenuItem>
                    <MenuItem value="immediate">{t('Immédiate')}</MenuItem>
                  </TextField>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enabled}
                        onChange={handleChange}
                        name="enabled"
                      />
                    }
                    label={t('Activer ce seuil')}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    disabled={submitting}
                  >
                    {submitting ? (
                      <CircularProgress size={24} />
                    ) : editingId ? (
                      t('Mettre à jour')
                    ) : (
                      t('Enregistrer')
                    )}
                  </Button>
                </Grid>
              </Grid>
            </form>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              {t('Seuils Configurés')}
            </Typography>

            <List>
              {settings?.thresholds?.map((threshold) => (
                <ListItem key={threshold.id}>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle1">
                        {t(PRODUCT_CATEGORIES[threshold.category])}
                      </Typography>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" color="text.secondary">
                          {t('Seuil')}: {threshold.threshold_value}
                          {threshold.threshold_type === 'percentage' ? '%' :
                            threshold.threshold_type === 'days' ? t(' jours') : ''}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {t('Notifications')}: {t(NOTIFICATION_METHODS[threshold.notification_method])}
                        </Typography>
                      </>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleEdit(threshold)}
                      title={t('Modifier')}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      edge="end"
                      onClick={() => handleDelete(threshold.id)}
                      title={t('Supprimer')}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default AlertSettings;
