import axiosInstance from '../utils/axiosConfig';

class HomepageSectionService {
  async getSections() {
    const response = await axiosInstance.get('/api/admin/homepage/sections');
    return response.data;
  }

  async createSection(sectionData) {
    const response = await axiosInstance.post('/api/admin/homepage/sections', sectionData);
    return response.data;
  }

  async updateSection(id, sectionData) {
    const response = await axiosInstance.put(`/api/admin/homepage/sections/${id}`, sectionData);
    return response.data;
  }

  async deleteSection(id) {
    const response = await axiosInstance.delete(`/api/admin/homepage/sections/${id}`);
    return response.data;
  }

  async updateSectionOrders(sections) {
    const response = await axiosInstance.put('/api/admin/homepage/sections/order/update', { sections });
    return response.data;
  }
}

export default new HomepageSectionService();
