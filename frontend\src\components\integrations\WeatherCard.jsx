/**
 * Composant d'affichage des données météorologiques
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  Box,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  WbSunny,
  Cloud,
  Grain,
  Air,
  Visibility,
  Thermostat,
  Water,
  Warning,
  Refresh,
  ExpandMore
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { useTranslation } from 'react-i18next';
import { weatherAPI } from '../../services/api';

const WeatherCard = ({ farmId }) => {
  const { t } = useTranslation();
  const [currentWeather, setCurrentWeather] = useState(null);
  const [forecast, setForecast] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    if (farmId) {
      loadWeatherData();
      
      // Actualiser toutes les 10 minutes
      const interval = setInterval(loadWeatherData, 10 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [farmId]);

  const loadWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [currentData, forecastData, alertsData] = await Promise.all([
        weatherAPI.getCurrentWeather(farmId),
        weatherAPI.getForecast(farmId),
        weatherAPI.getAlerts(farmId)
      ]);

      setCurrentWeather(currentData.data);
      setForecast(forecastData.data);
      setAlerts(alertsData.data.alerts || []);
      setLastUpdate(new Date());

    } catch (err) {
      setError(err.message);
      console.error('Erreur lors du chargement des données météo:', err);
    } finally {
      setLoading(false);
    }
  };

  const getWeatherIcon = (weatherMain) => {
    const iconMap = {
      'Clear': <WbSunny color="warning" />,
      'Clouds': <Cloud color="action" />,
      'Rain': <Grain color="primary" />,
      'Snow': <Grain color="info" />,
      'Thunderstorm': <Warning color="error" />,
      'Drizzle': <Grain color="primary" />,
      'Mist': <Cloud color="action" />,
      'Fog': <Cloud color="action" />
    };

    return iconMap[weatherMain] || <WbSunny />;
  };

  const getTemperatureColor = (temp) => {
    if (temp < 10) return 'primary';
    if (temp < 20) return 'info';
    if (temp < 30) return 'success';
    return 'error';
  };

  const getAlertSeverityColor = (severity) => {
    const colorMap = {
      'minor': 'info',
      'moderate': 'warning',
      'severe': 'error',
      'extreme': 'error'
    };
    return colorMap[severity] || 'info';
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      weekday: 'short',
      day: 'numeric',
      month: 'short'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error" action={
            <IconButton onClick={loadWeatherData} size="small">
              <Refresh />
            </IconButton>
          }>
            {t('weather.error')}: {error}
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">
              {t('weather.title')}
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              {lastUpdate && (
                <Typography variant="caption" color="text.secondary">
                  {t('weather.lastUpdate')}: {formatTime(lastUpdate)}
                </Typography>
              )}
              <Tooltip title={t('weather.refresh')}>
                <IconButton onClick={loadWeatherData} size="small">
                  <Refresh />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        }
      />
      <CardContent>
        {/* Alertes météo */}
        {alerts.length > 0 && (
          <Box mb={2}>
            {alerts.map((alert, index) => (
              <Alert 
                key={index} 
                severity={getAlertSeverityColor(alert.severity)}
                sx={{ mb: 1 }}
              >
                <Typography variant="subtitle2">{alert.event}</Typography>
                <Typography variant="body2">{alert.description}</Typography>
              </Alert>
            ))}
          </Box>
        )}

        {/* Météo actuelle */}
        {currentWeather && (
          <Grid container spacing={3}>
            {/* Température principale */}
            <Grid item xs={12} md={6}>
              <Box display="flex" alignItems="center" gap={2}>
                {getWeatherIcon(currentWeather.weather.main)}
                <Box>
                  <Typography variant="h3" component="div">
                    {currentWeather.temperature}°C
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {currentWeather.weather.description}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('weather.feelsLike')}: {currentWeather.feelsLike}°C
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Détails météo */}
            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Water color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {t('weather.humidity')}
                      </Typography>
                      <Typography variant="body1">
                        {currentWeather.humidity}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Air color="action" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {t('weather.wind')}
                      </Typography>
                      <Typography variant="body1">
                        {currentWeather.windSpeed} m/s
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Visibility color="action" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {t('weather.visibility')}
                      </Typography>
                      <Typography variant="body1">
                        {currentWeather.visibility} km
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Thermostat color="action" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {t('weather.pressure')}
                      </Typography>
                      <Typography variant="body1">
                        {currentWeather.pressure} hPa
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Grid>

            {/* Lever/coucher du soleil */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="space-around" mt={2}>
                <Box textAlign="center">
                  <Typography variant="body2" color="text.secondary">
                    {t('weather.sunrise')}
                  </Typography>
                  <Typography variant="body1">
                    {formatTime(currentWeather.sunrise)}
                  </Typography>
                </Box>
                <Box textAlign="center">
                  <Typography variant="body2" color="text.secondary">
                    {t('weather.sunset')}
                  </Typography>
                  <Typography variant="body1">
                    {formatTime(currentWeather.sunset)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        )}

        {/* Prévisions */}
        {forecast && forecast.forecasts && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">
                {t('weather.forecast')} (5 {t('weather.days')})
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                {forecast.forecasts.map((day, index) => (
                  <Grid item xs={12} sm={6} md={2.4} key={index}>
                    <Card variant="outlined">
                      <CardContent sx={{ textAlign: 'center', py: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          {formatDate(day.date)}
                        </Typography>
                        {getWeatherIcon(day.weather.main)}
                        <Typography variant="h6">
                          {day.tempMax}°
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {day.tempMin}°
                        </Typography>
                        <Typography variant="caption" display="block">
                          {day.weather.description}
                        </Typography>
                        {day.precipitation > 0 && (
                          <Chip 
                            label={`${day.precipitation}mm`} 
                            size="small" 
                            color="primary"
                            sx={{ mt: 0.5 }}
                          />
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Graphique des températures */}
              <Box mt={3}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('weather.temperatureTrend')}
                </Typography>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={forecast.forecasts}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={(date) => formatDate(date)}
                    />
                    <YAxis />
                    <RechartsTooltip 
                      labelFormatter={(date) => formatDate(date)}
                      formatter={(value, name) => [
                        `${value}°C`, 
                        name === 'tempMax' ? t('weather.maxTemp') : t('weather.minTemp')
                      ]}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="tempMax" 
                      stroke="#ff6b6b" 
                      strokeWidth={2}
                      name="tempMax"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="tempMin" 
                      stroke="#4ecdc4" 
                      strokeWidth={2}
                      name="tempMin"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Recommandations */}
        {alerts.length > 0 && alerts[0].recommendations && (
          <Accordion sx={{ mt: 1 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">
                {t('weather.recommendations')}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {alerts[0].recommendations.map((rec, index) => (
                <Alert 
                  key={index} 
                  severity={rec.priority === 'high' ? 'warning' : 'info'}
                  sx={{ mb: 1 }}
                >
                  <Typography variant="subtitle2">{rec.action}</Typography>
                  <Typography variant="body2">{rec.description}</Typography>
                </Alert>
              ))}
            </AccordionDetails>
          </Accordion>
        )}
      </CardContent>
    </Card>
  );
};

export default WeatherCard;
