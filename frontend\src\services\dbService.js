/**
 * Service de connexion à la base de données PostgreSQL pour Poultray DZ
 * Ce service gère les connexions et requêtes à la base de données pour les différentes sections du tableau de bord
 */

import axiosInstance from '../utils/axiosConfig';
import dbConfig, { TABLES } from '../config/database';

// Informations de connexion à la base de données PostgreSQL
const DB_INFO = {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  ssl: dbConfig.ssl
};

// Services pour le tableau de bord Admin
const adminService = {
  // Statistiques générales
  getStats: async () => {
    try {
      const response = await axiosInstance.get('/admin/stats');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  },

  // Gestion des utilisateurs
  getUsers: async (role, page = 1, limit = 10) => {
    try {
      const response = await axiosInstance.get(
        `/admin/users?role=${role}&page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des utilisateurs ${role}:`, error);
      throw error;
    }
  },

  // Gestion du blog
  getBlogPosts: async (page = 1, limit = 10) => {
    try {
      const response = await axiosInstance.get(
        `/admin/blog?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des articles de blog:', error);
      throw error;
    }
  },

  // Gestion des traductions
  getTranslations: async (language) => {
    try {
      const response = await axiosInstance.get(
        `/admin/translations/${language}`
      );
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des traductions ${language}:`, error);
      throw error;
    }
  },
};

// Services pour le tableau de bord Éleveur
const eleveurService = {
  // Gestion des volailles
  getVolailles: async (page = 1, limit = 10, filters = {}) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...filters
      }).toString();

      const response = await axiosInstance.get(
        `/eleveur/volailles?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des volailles:', error);
      throw error;
    }
  },

  // Gestion des ventes
  getVentes: async (page = 1, limit = 10, dateDebut, dateFin) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(dateDebut && { dateDebut }),
        ...(dateFin && { dateFin })
      }).toString();

      const response = await axiosInstance.get(
        `/eleveur/ventes?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des ventes:', error);
      throw error;
    }
  },

  // Statistiques de l'éleveur
  getStats: async (periode = 'mois') => {
    try {
      const response = await axiosInstance.get(
        `/eleveur/stats?periode=${periode}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  },
};

// Services pour le tableau de bord Vétérinaire
const veterinaireService = {
  // Gestion des prescriptions
  getPrescriptions: async (page = 1, limit = 10, filters = {}) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...filters
      }).toString();

      const response = await axiosInstance.get(
        `/veterinaire/prescriptions?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des prescriptions:', error);
      throw error;
    }
  },

  // Gestion des consultations
  getConsultations: async (page = 1, limit = 10, dateDebut, dateFin) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(dateDebut && { dateDebut }),
        ...(dateFin && { dateFin })
      }).toString();

      const response = await axiosInstance.get(
        `/veterinaire/consultations?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des consultations:', error);
      throw error;
    }
  },
};

// Services pour le tableau de bord Marchand
const marchandService = {
  // Gestion des produits
  getProduits: async (page = 1, limit = 10, filters = {}) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...filters
      }).toString();

      const response = await axiosInstance.get(
        `/marchand/produits?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des produits:', error);
      throw error;
    }
  },

  // Gestion des commandes
  getCommandes: async (page = 1, limit = 10, statut, dateDebut, dateFin) => {
    try {
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(statut && { statut }),
        ...(dateDebut && { dateDebut }),
        ...(dateFin && { dateFin })
      }).toString();

      const response = await axiosInstance.get(
        `/marchand/commandes?${queryParams}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des commandes:', error);
      throw error;
    }
  },

  // Statistiques des ventes
  getStatsVentes: async (periode = 'mois') => {
    try {
      const response = await axiosInstance.get(
        `/marchand/stats/ventes?periode=${periode}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques de ventes:', error);
      throw error;
    }
  },
};

// Service commun pour les fonctionnalités partagées entre les tableaux de bord
const commonService = {
  // Profil utilisateur
  getUserProfile: async () => {
    try {
      const response = await axiosInstance.get('/user/profile');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du profil utilisateur:', error);
      throw error;
    }
  },

  // Paramètres utilisateur
  getUserSettings: async () => {
    try {
      const response = await axiosInstance.get('/user/settings');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres utilisateur:', error);
      throw error;
    }
  },

  // Notifications
  getNotifications: async (page = 1, limit = 10) => {
    try {
      const response = await axiosInstance.get(
        `/notifications?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des notifications:', error);
      throw error;
    }
  },
};

// Exporter tous les services
export {
  adminService,
  eleveurService,
  veterinaireService,
  marchandService,
  commonService
};
