const https = require('https');
const http = require('http');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const urlObj = new URL(url);

    const reqOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    if (options.body) {
      reqOptions.headers['Content-Type'] = 'application/json';
      reqOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
    }

    const req = protocol.request(reqOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testNavigationFlow() {
  console.log('=== Test du Flow de Navigation ===\n');

  const baseURL = 'http://localhost:3003';  try {
    // 1. Test de connexion
    console.log('1. 🔐 Test de connexion...');
    const loginResponse = await makeRequest(`${baseURL}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${loginResponse.status} - ${JSON.stringify(loginResponse.data)}`);
    }

    console.log('✅ Connexion réussie');
    console.log('📊 Données utilisateur:', {
      id: loginResponse.data.user.id,
      email: loginResponse.data.user.email,
      role: loginResponse.data.user.role,
      hasToken: !!loginResponse.data.token
    });

    const token = loginResponse.data.token;
    const user = loginResponse.data.user;

    // 2. Test de navigation logic
    console.log('\n2. 🧭 Test logique de navigation...');

    function getRedirectPath(role) {
      const rolePathMap = {
        'admin': '/admin/dashboard',
        'eleveur': '/eleveur/dashboard',
        'veterinaire': '/veterinaire/dashboard',
        'marchand': '/marchand/dashboard'
      };
      return rolePathMap[role] || '/dashboard';
    }

    const targetPath = getRedirectPath(user.role);
    console.log(`🎯 Rôle: ${user.role} → Navigation vers: ${targetPath}`);    // 3. Test de vérification utilisateur
    console.log('\n3. 👤 Test vérification utilisateur...');
    const userCheckResponse = await makeRequest(`${baseURL}/api/auth/user`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (userCheckResponse.status !== 200) {
      throw new Error(`User check failed: ${userCheckResponse.status}`);
    }

    console.log('✅ Vérification utilisateur réussie');
    console.log('📊 Données vérifiées:', {
      id: userCheckResponse.data.id,
      email: userCheckResponse.data.email,
      role: userCheckResponse.data.role
    });

    // 4. Test DashboardLayout logic
    console.log('\n4. 🏠 Test logique DashboardLayout...');

    function testDashboardAccess(userRole, requiredRole) {
      const isAuthenticated = !!user;

      if (!isAuthenticated) {
        return { allowed: false, redirect: '/login', reason: 'Non authentifié' };
      }

      if (requiredRole && userRole !== requiredRole) {
        return {
          allowed: false,
          redirect: `/${userRole}/dashboard`,
          reason: `Rôle incorrect. Requis: ${requiredRole}, Actuel: ${userRole}`
        };
      }

      return { allowed: true, reason: 'Accès autorisé' };
    }

    // Test différents accès
    const tests = [
      { route: '/admin/dashboard', requiredRole: 'admin' },
      { route: '/eleveur/dashboard', requiredRole: 'eleveur' },
      { route: '/veterinaire/dashboard', requiredRole: 'veterinaire' },
      { route: '/marchand/dashboard', requiredRole: 'marchand' }
    ];

    tests.forEach(test => {
      const result = testDashboardAccess(user.role, test.requiredRole);
      const status = result.allowed ? '✅' : '❌';
      console.log(`${status} ${test.route}: ${result.reason}`);
      if (result.redirect) {
        console.log(`   📍 Redirection vers: ${result.redirect}`);
      }
    });

    // 5. Test avec un timing simulé
    console.log('\n5. ⏱️ Test avec délai (simulation React state update)...');

    function simulateReactLogin() {
      return new Promise((resolve) => {
        console.log('🔄 Simulation: setUser() appelé...');
        setTimeout(() => {
          console.log('✅ Simulation: State React mis à jour');
          console.log('🎯 Simulation: Navigation déclenchée');
          resolve(user);
        }, 100); // Simule le délai de mise à jour React
      });
    }

    const simulatedUser = await simulateReactLogin();
    console.log(`📍 Navigation simulée vers: ${getRedirectPath(simulatedUser.role)}`);

    console.log('\n=== Résumé ===');
    console.log('✅ Authentification: OK');
    console.log('✅ Données utilisateur: OK');
    console.log('✅ Logique navigation: OK');
    console.log('✅ Vérification token: OK');
    console.log('✅ Logique DashboardLayout: OK');

    console.log('\n💡 Recommandations:');
    console.log('1. Vérifier que React.Navigate utilise { replace: true }');
    console.log('2. Ajouter des logs dans le navigateur pour tracer les redirections');
    console.log('3. Vérifier que AuthContext.user est bien synchronisé');
    console.log('4. S\'assurer qu\'il n\'y a pas de boucles de redirection');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
    if (error.status) {
      console.error('📊 Status:', error.status);
    }
  }
}

// Exécuter le test
testNavigationFlow().then(() => {
  console.log('\n🏁 Test terminé');
}).catch(console.error);
