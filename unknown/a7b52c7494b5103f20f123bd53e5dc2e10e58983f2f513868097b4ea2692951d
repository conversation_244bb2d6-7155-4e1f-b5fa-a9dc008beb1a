/**
 * Service d'optimisation des requêtes de base de données
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
const performanceMonitoringService = require('./performanceMonitoringService');

class QueryOptimizationService {
  constructor() {
    this.pool = new Pool();
    this.queryCache = new Map();
    this.queryStats = new Map();
    this.optimizationRules = this.initializeOptimizationRules();
    this.indexSuggestions = new Map();
  }

  /**
   * Initialiser les règles d'optimisation
   */
  initializeOptimizationRules() {
    return [
      {
        name: 'missing_limit',
        pattern: /SELECT.*FROM.*WHERE.*(?!LIMIT)/i,
        severity: 'medium',
        suggestion: 'Ajouter une clause LIMIT pour éviter de récupérer trop de données',
        fix: (query) => query.includes('LIMIT') ? query : `${query} LIMIT 1000`
      },
      {
        name: 'select_star',
        pattern: /SELECT\s+\*\s+FROM/i,
        severity: 'low',
        suggestion: 'Éviter SELECT * et spécifier les colonnes nécessaires',
        fix: (query, tableSchema) => this.replaceSelectStar(query, tableSchema)
      },
      {
        name: 'missing_index_where',
        pattern: /WHERE\s+(\w+)\s*[=<>]/i,
        severity: 'high',
        suggestion: 'Créer un index sur les colonnes utilisées dans WHERE',
        fix: (query, analysis) => this.suggestIndex(query, analysis)
      },
      {
        name: 'inefficient_join',
        pattern: /JOIN.*ON.*=.*AND/i,
        severity: 'medium',
        suggestion: 'Optimiser les conditions de jointure',
        fix: (query) => this.optimizeJoinConditions(query)
      },
      {
        name: 'subquery_in_select',
        pattern: /SELECT.*\(SELECT.*FROM.*\)/i,
        severity: 'high',
        suggestion: 'Remplacer les sous-requêtes par des JOINs quand possible',
        fix: (query) => this.convertSubqueryToJoin(query)
      },
      {
        name: 'order_without_limit',
        pattern: /ORDER\s+BY.*(?!LIMIT)/i,
        severity: 'medium',
        suggestion: 'Ajouter LIMIT avec ORDER BY pour éviter le tri complet',
        fix: (query) => `${query} LIMIT 100`
      },
      {
        name: 'like_leading_wildcard',
        pattern: /LIKE\s+['"]%/i,
        severity: 'high',
        suggestion: 'Éviter LIKE avec wildcard en début (empêche l\'utilisation d\'index)',
        fix: (query) => this.suggestFullTextSearch(query)
      }
    ];
  }

  /**
   * Analyser et optimiser une requête
   */
  async analyzeQuery(query, params = []) {
    try {
      const startTime = process.hrtime();
      
      // Obtenir le plan d'exécution
      const explainResult = await this.pool.query(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`, params);
      const executionPlan = explainResult.rows[0]['QUERY PLAN'][0];

      // Analyser la performance
      const duration = process.hrtime(startTime);
      const durationMs = duration[0] * 1000 + duration[1] / 1000000;

      // Détecter les problèmes
      const issues = this.detectPerformanceIssues(executionPlan, query);
      
      // Appliquer les règles d'optimisation
      const optimizationSuggestions = this.applyOptimizationRules(query, executionPlan);

      // Suggérer des index
      const indexSuggestions = await this.suggestIndexes(query, executionPlan);

      const analysis = {
        query: query,
        executionTime: durationMs,
        executionPlan: executionPlan,
        issues: issues,
        optimizationSuggestions: optimizationSuggestions,
        indexSuggestions: indexSuggestions,
        optimizedQuery: this.generateOptimizedQuery(query, optimizationSuggestions),
        performance: this.calculatePerformanceScore(executionPlan, durationMs)
      };

      // Stocker les statistiques
      this.updateQueryStats(query, analysis);

      return analysis;

    } catch (error) {
      logger.error('Erreur lors de l\'analyse de la requête:', error);
      throw error;
    }
  }

  /**
   * Détecter les problèmes de performance dans le plan d'exécution
   */
  detectPerformanceIssues(executionPlan, query) {
    const issues = [];

    // Analyser récursivement le plan
    const analyzePlanNode = (node) => {
      // Scan séquentiel sur une grande table
      if (node['Node Type'] === 'Seq Scan' && node['Plan Rows'] > 10000) {
        issues.push({
          type: 'sequential_scan',
          severity: 'high',
          message: `Scan séquentiel sur ${node['Plan Rows']} lignes dans la table ${node['Relation Name']}`,
          suggestion: 'Créer un index approprié pour éviter le scan séquentiel'
        });
      }

      // Tri coûteux
      if (node['Node Type'] === 'Sort' && node['Sort Method'] === 'external merge') {
        issues.push({
          type: 'expensive_sort',
          severity: 'medium',
          message: 'Tri externe utilisé (débordement sur disque)',
          suggestion: 'Augmenter work_mem ou optimiser la requête pour réduire les données à trier'
        });
      }

      // Jointure par boucles imbriquées sur de gros volumes
      if (node['Node Type'] === 'Nested Loop' && node['Plan Rows'] > 1000) {
        issues.push({
          type: 'inefficient_join',
          severity: 'high',
          message: 'Jointure par boucles imbriquées sur un gros volume de données',
          suggestion: 'Créer des index sur les clés de jointure ou forcer un hash join'
        });
      }

      // Temps d'exécution élevé
      if (node['Actual Total Time'] > 1000) {
        issues.push({
          type: 'slow_operation',
          severity: 'medium',
          message: `Opération lente: ${node['Node Type']} (${node['Actual Total Time']}ms)`,
          suggestion: 'Analyser et optimiser cette opération spécifique'
        });
      }

      // Analyser les nœuds enfants
      if (node['Plans']) {
        node['Plans'].forEach(childNode => analyzePlanNode(childNode));
      }
    };

    analyzePlanNode(executionPlan['Plan']);

    return issues;
  }

  /**
   * Appliquer les règles d'optimisation
   */
  applyOptimizationRules(query, executionPlan) {
    const suggestions = [];

    this.optimizationRules.forEach(rule => {
      if (rule.pattern.test(query)) {
        suggestions.push({
          rule: rule.name,
          severity: rule.severity,
          suggestion: rule.suggestion,
          optimizedQuery: rule.fix(query, executionPlan)
        });
      }
    });

    return suggestions;
  }

  /**
   * Suggérer des index basés sur l'analyse
   */
  async suggestIndexes(query, executionPlan) {
    const suggestions = [];

    // Analyser les colonnes utilisées dans WHERE, JOIN, ORDER BY
    const columnsAnalysis = this.analyzeColumnUsage(query);
    
    // Vérifier les index existants
    const existingIndexes = await this.getExistingIndexes(columnsAnalysis.tables);

    columnsAnalysis.whereColumns.forEach(column => {
      if (!this.hasIndex(existingIndexes, column.table, column.column)) {
        suggestions.push({
          type: 'btree',
          table: column.table,
          columns: [column.column],
          reason: 'Utilisé dans clause WHERE',
          sql: `CREATE INDEX idx_${column.table}_${column.column} ON ${column.table} (${column.column});`,
          estimatedImprovement: 'Élevé'
        });
      }
    });

    columnsAnalysis.joinColumns.forEach(join => {
      if (!this.hasIndex(existingIndexes, join.table, join.column)) {
        suggestions.push({
          type: 'btree',
          table: join.table,
          columns: [join.column],
          reason: 'Utilisé dans jointure',
          sql: `CREATE INDEX idx_${join.table}_${join.column} ON ${join.table} (${join.column});`,
          estimatedImprovement: 'Élevé'
        });
      }
    });

    // Index composites pour ORDER BY
    if (columnsAnalysis.orderByColumns.length > 1) {
      const table = columnsAnalysis.orderByColumns[0].table;
      const columns = columnsAnalysis.orderByColumns.map(c => c.column);
      
      if (!this.hasCompositeIndex(existingIndexes, table, columns)) {
        suggestions.push({
          type: 'btree',
          table: table,
          columns: columns,
          reason: 'Optimiser ORDER BY',
          sql: `CREATE INDEX idx_${table}_${columns.join('_')} ON ${table} (${columns.join(', ')});`,
          estimatedImprovement: 'Moyen'
        });
      }
    }

    return suggestions;
  }

  /**
   * Analyser l'utilisation des colonnes dans la requête
   */
  analyzeColumnUsage(query) {
    const analysis = {
      tables: new Set(),
      whereColumns: [],
      joinColumns: [],
      orderByColumns: []
    };

    // Extraire les tables
    const tableMatches = query.match(/FROM\s+(\w+)|JOIN\s+(\w+)/gi);
    if (tableMatches) {
      tableMatches.forEach(match => {
        const table = match.replace(/FROM\s+|JOIN\s+/i, '');
        analysis.tables.add(table);
      });
    }

    // Extraire les colonnes WHERE
    const whereMatches = query.match(/WHERE\s+.*?(?=ORDER|GROUP|LIMIT|$)/i);
    if (whereMatches) {
      const whereClause = whereMatches[0];
      const columnMatches = whereClause.match(/(\w+)\.(\w+)\s*[=<>]/g);
      if (columnMatches) {
        columnMatches.forEach(match => {
          const [, table, column] = match.match(/(\w+)\.(\w+)/);
          analysis.whereColumns.push({ table, column });
        });
      }
    }

    // Extraire les colonnes JOIN
    const joinMatches = query.match(/JOIN\s+\w+\s+ON\s+(\w+)\.(\w+)\s*=\s*(\w+)\.(\w+)/gi);
    if (joinMatches) {
      joinMatches.forEach(match => {
        const parts = match.match(/JOIN\s+\w+\s+ON\s+(\w+)\.(\w+)\s*=\s*(\w+)\.(\w+)/i);
        if (parts) {
          analysis.joinColumns.push({ table: parts[1], column: parts[2] });
          analysis.joinColumns.push({ table: parts[3], column: parts[4] });
        }
      });
    }

    // Extraire les colonnes ORDER BY
    const orderByMatches = query.match(/ORDER\s+BY\s+(.*?)(?=LIMIT|$)/i);
    if (orderByMatches) {
      const orderByClause = orderByMatches[1];
      const columns = orderByClause.split(',').map(col => {
        const trimmed = col.trim();
        const match = trimmed.match(/(\w+)\.(\w+)|(\w+)/);
        if (match) {
          return match[1] ? { table: match[1], column: match[2] } : { table: null, column: match[3] };
        }
        return null;
      }).filter(Boolean);
      
      analysis.orderByColumns = columns;
    }

    analysis.tables = Array.from(analysis.tables);
    return analysis;
  }

  /**
   * Obtenir les index existants
   */
  async getExistingIndexes(tables) {
    try {
      const result = await this.pool.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE tablename = ANY($1)
      `, [tables]);

      return result.rows;
    } catch (error) {
      logger.error('Erreur lors de la récupération des index:', error);
      return [];
    }
  }

  /**
   * Vérifier si un index existe
   */
  hasIndex(existingIndexes, table, column) {
    return existingIndexes.some(index => 
      index.tablename === table && 
      index.indexdef.includes(`(${column})`)
    );
  }

  /**
   * Vérifier si un index composite existe
   */
  hasCompositeIndex(existingIndexes, table, columns) {
    const columnPattern = `(${columns.join(', ')})`;
    return existingIndexes.some(index => 
      index.tablename === table && 
      index.indexdef.includes(columnPattern)
    );
  }

  /**
   * Générer une requête optimisée
   */
  generateOptimizedQuery(originalQuery, suggestions) {
    let optimizedQuery = originalQuery;

    suggestions.forEach(suggestion => {
      if (suggestion.optimizedQuery && suggestion.severity === 'high') {
        optimizedQuery = suggestion.optimizedQuery;
      }
    });

    return optimizedQuery;
  }

  /**
   * Calculer un score de performance
   */
  calculatePerformanceScore(executionPlan, executionTime) {
    let score = 100;

    // Pénaliser le temps d'exécution
    if (executionTime > 1000) score -= 30;
    else if (executionTime > 500) score -= 20;
    else if (executionTime > 100) score -= 10;

    // Pénaliser les scans séquentiels
    const hasSeqScan = this.hasSequentialScan(executionPlan.Plan);
    if (hasSeqScan) score -= 25;

    // Pénaliser les tris externes
    const hasExternalSort = this.hasExternalSort(executionPlan.Plan);
    if (hasExternalSort) score -= 15;

    // Pénaliser les jointures inefficaces
    const hasInefficinetJoin = this.hasInefficientJoin(executionPlan.Plan);
    if (hasInefficinetJoin) score -= 20;

    return Math.max(0, score);
  }

  /**
   * Vérifier la présence de scans séquentiels
   */
  hasSequentialScan(planNode) {
    if (planNode['Node Type'] === 'Seq Scan' && planNode['Plan Rows'] > 1000) {
      return true;
    }

    if (planNode['Plans']) {
      return planNode['Plans'].some(child => this.hasSequentialScan(child));
    }

    return false;
  }

  /**
   * Vérifier la présence de tris externes
   */
  hasExternalSort(planNode) {
    if (planNode['Node Type'] === 'Sort' && planNode['Sort Method'] === 'external merge') {
      return true;
    }

    if (planNode['Plans']) {
      return planNode['Plans'].some(child => this.hasExternalSort(child));
    }

    return false;
  }

  /**
   * Vérifier la présence de jointures inefficaces
   */
  hasInefficientJoin(planNode) {
    if (planNode['Node Type'] === 'Nested Loop' && planNode['Plan Rows'] > 1000) {
      return true;
    }

    if (planNode['Plans']) {
      return planNode['Plans'].some(child => this.hasInefficientJoin(child));
    }

    return false;
  }

  /**
   * Mettre à jour les statistiques de requête
   */
  updateQueryStats(query, analysis) {
    const queryHash = this.hashQuery(query);
    
    if (!this.queryStats.has(queryHash)) {
      this.queryStats.set(queryHash, {
        query: query,
        executions: 0,
        totalTime: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0,
        lastExecution: null,
        issues: []
      });
    }

    const stats = this.queryStats.get(queryHash);
    stats.executions++;
    stats.totalTime += analysis.executionTime;
    stats.avgTime = stats.totalTime / stats.executions;
    stats.minTime = Math.min(stats.minTime, analysis.executionTime);
    stats.maxTime = Math.max(stats.maxTime, analysis.executionTime);
    stats.lastExecution = new Date();
    stats.issues = analysis.issues;
  }

  /**
   * Obtenir les statistiques des requêtes
   */
  getQueryStats() {
    return Array.from(this.queryStats.values())
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 50); // Top 50 des requêtes les plus lentes
  }

  /**
   * Créer automatiquement les index suggérés
   */
  async createSuggestedIndexes(suggestions, confirm = false) {
    if (!confirm) {
      throw new Error('Confirmation requise pour créer les index');
    }

    const results = [];

    for (const suggestion of suggestions) {
      try {
        await this.pool.query(suggestion.sql);
        results.push({
          success: true,
          index: suggestion,
          message: 'Index créé avec succès'
        });
        
        logger.info(`Index créé: ${suggestion.sql}`);
      } catch (error) {
        results.push({
          success: false,
          index: suggestion,
          error: error.message
        });
        
        logger.error(`Erreur création index: ${suggestion.sql}`, error);
      }
    }

    return results;
  }

  /**
   * Générer un hash pour une requête
   */
  hashQuery(query) {
    // Normaliser la requête (supprimer les espaces, convertir en minuscules)
    const normalized = query.replace(/\s+/g, ' ').trim().toLowerCase();
    
    // Générer un hash simple
    let hash = 0;
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir en 32-bit integer
    }
    
    return hash.toString();
  }

  /**
   * Méthodes d'optimisation spécifiques
   */

  replaceSelectStar(query, tableSchema) {
    // Remplacer SELECT * par les colonnes spécifiques
    // Cette implémentation nécessiterait le schéma de la table
    return query; // Placeholder
  }

  suggestIndex(query, analysis) {
    // Suggérer la création d'index basé sur l'analyse
    return query; // Placeholder
  }

  optimizeJoinConditions(query) {
    // Optimiser les conditions de jointure
    return query; // Placeholder
  }

  convertSubqueryToJoin(query) {
    // Convertir les sous-requêtes en jointures
    return query; // Placeholder
  }

  suggestFullTextSearch(query) {
    // Suggérer l'utilisation de la recherche full-text
    return query.replace(/LIKE\s+['"]%([^%]+)%['"]/, "@@to_tsquery('$1')");
  }
}

module.exports = new QueryOptimizationService();
