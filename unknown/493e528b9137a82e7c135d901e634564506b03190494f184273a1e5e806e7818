/**
 * Routes pour les métriques et optimisations de performance
 */

const express = require('express');
const router = express.Router();
const { query, param } = require('express-validator');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const performanceMonitoringService = require('../services/performanceMonitoringService');
const queryOptimizationService = require('../services/queryOptimizationService');
const PerformanceTestSuite = require('../../tests/performance/performanceTestSuite');

/**
 * @swagger
 * /api/performance/metrics:
 *   get:
 *     summary: Obtenir les métriques de performance Prometheus
 *     tags: [Performance]
 *     responses:
 *       200:
 *         description: Métriques au format Prometheus
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = await performanceMonitoringService.getMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des métriques'
    });
  }
});

/**
 * @swagger
 * /api/performance/report:
 *   get:
 *     summary: Générer un rapport de performance
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           default: "24h"
 *         description: Période du rapport (1h, 24h, 7d, 30d)
 *     responses:
 *       200:
 *         description: Rapport de performance détaillé
 */
router.get('/report',
  auth,
  validate([
    query('timeRange').optional().isIn(['1h', '24h', '7d', '30d'])
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'manager') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const timeRange = req.query.timeRange || '24h';
      const report = await performanceMonitoringService.generatePerformanceReport(timeRange);

      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du rapport'
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/query/analyze:
 *   post:
 *     summary: Analyser une requête SQL pour optimisation
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *                 description: Requête SQL à analyser
 *               params:
 *                 type: array
 *                 description: Paramètres de la requête
 *     responses:
 *       200:
 *         description: Analyse de la requête avec suggestions d'optimisation
 */
router.post('/query/analyze',
  auth,
  validate([
    // body('query').isString().notEmpty()
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'developer') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const { query, params = [] } = req.body;
      const analysis = await queryOptimizationService.analyzeQuery(query, params);

      res.json({
        success: true,
        data: analysis
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse de la requête',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/query/stats:
 *   get:
 *     summary: Obtenir les statistiques des requêtes
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistiques des requêtes les plus lentes
 */
router.get('/query/stats',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'developer') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const stats = queryOptimizationService.getQueryStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques'
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/indexes/suggest:
 *   post:
 *     summary: Suggérer des index pour optimiser les performances
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *     responses:
 *       200:
 *         description: Suggestions d'index
 */
router.post('/indexes/suggest',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const { query } = req.body;
      const analysis = await queryOptimizationService.analyzeQuery(query);
      const suggestions = analysis.indexSuggestions;

      res.json({
        success: true,
        data: suggestions
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération des suggestions'
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/indexes/create:
 *   post:
 *     summary: Créer les index suggérés
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               suggestions:
 *                 type: array
 *                 items:
 *                   type: object
 *               confirm:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Résultats de la création des index
 */
router.post('/indexes/create',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const { suggestions, confirm = false } = req.body;
      
      if (!confirm) {
        return res.status(400).json({
          success: false,
          message: 'Confirmation requise pour créer les index'
        });
      }

      const results = await queryOptimizationService.createSuggestedIndexes(suggestions, confirm);

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création des index',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/test/run:
 *   post:
 *     summary: Exécuter la suite de tests de performance
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Résultats des tests de performance
 */
router.post('/test/run',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      // Exécuter les tests en arrière-plan
      const testSuite = new PerformanceTestSuite();
      
      // Démarrer les tests de manière asynchrone
      testSuite.runAllTests()
        .then(results => {
          console.log('Tests de performance terminés:', results);
        })
        .catch(error => {
          console.error('Erreur tests de performance:', error);
        });

      res.json({
        success: true,
        message: 'Tests de performance démarrés',
        data: {
          status: 'running',
          estimatedDuration: '10-15 minutes'
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors du démarrage des tests'
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/alerts:
 *   get:
 *     summary: Obtenir les alertes de performance récentes
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [info, warning, critical]
 *     responses:
 *       200:
 *         description: Liste des alertes de performance
 */
router.get('/alerts',
  auth,
  validate([
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('severity').optional().isIn(['info', 'warning', 'critical'])
  ]),
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'manager') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const limit = parseInt(req.query.limit) || 50;
      const severity = req.query.severity;

      let query = `
        SELECT * FROM performance_alerts 
        WHERE 1=1
      `;
      const params = [];

      if (severity) {
        query += ` AND severity = $${params.length + 1}`;
        params.push(severity);
      }

      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1}`;
      params.push(limit);

      const { Pool } = require('pg');
      const pool = new Pool();
      const result = await pool.query(query, params);

      res.json({
        success: true,
        data: result.rows
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des alertes'
      });
    }
  }
);

/**
 * @swagger
 * /api/performance/optimize/suggestions:
 *   get:
 *     summary: Obtenir des suggestions d'optimisation automatiques
 *     tags: [Performance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Suggestions d'optimisation basées sur l'analyse
 */
router.get('/optimize/suggestions',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions
      if (req.user.role !== 'admin' && req.user.role !== 'developer') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      // Analyser les requêtes lentes récentes
      const slowQueries = await queryOptimizationService.analyzeSlowQueries();
      
      // Générer des suggestions basées sur les métriques
      const suggestions = [];

      slowQueries.forEach(query => {
        suggestions.push(...query.optimization_suggestions.map(suggestion => ({
          type: 'query_optimization',
          priority: 'high',
          query: query.query,
          suggestion: suggestion,
          estimatedImprovement: 'Réduction de 20-50% du temps d\'exécution'
        })));
      });

      // Ajouter d'autres suggestions basées sur les métriques système
      // (à implémenter selon les besoins)

      res.json({
        success: true,
        data: suggestions
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération des suggestions'
      });
    }
  }
);

module.exports = router;
