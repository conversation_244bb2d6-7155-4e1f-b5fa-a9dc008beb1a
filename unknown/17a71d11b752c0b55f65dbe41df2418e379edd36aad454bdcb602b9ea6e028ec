/**
 * Service de prévision de production avicole
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');

class ProductionForecastService {
  constructor() {
    this.pool = new Pool();
    this.forecastCache = new Map();
    this.seasonalFactors = {
      // Facteurs saisonniers pour différents types de production
      oeufs: {
        1: 0.85,  // Janvier
        2: 0.88,  // Février
        3: 0.95,  // Mars
        4: 1.05,  // Avril
        5: 1.10,  // Mai
        6: 1.08,  // Juin
        7: 1.02,  // Juillet
        8: 0.98,  // Août
        9: 1.03,  // Septembre
        10: 1.07, // Octobre
        11: 0.92, // Novembre
        12: 0.87  // Décembre
      },
      viande: {
        1: 0.90, 2: 0.92, 3: 0.98, 4: 1.05, 5: 1.08, 6: 1.10,
        7: 1.12, 8: 1.08, 9: 1.02, 10: 0.98, 11: 0.95, 12: 0.92
      }
    };
  }

  /**
   * Prédire la production future avec multiple algorithmes
   */
  async forecastProduction(volailleId, days = 30, algorithm = 'ensemble') {
    try {
      const cacheKey = `forecast_${volailleId}_${days}_${algorithm}`;
      
      // Vérifier le cache
      if (this.forecastCache.has(cacheKey)) {
        const cached = this.forecastCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 3600000) { // 1 heure
          return cached.forecast;
        }
      }

      // Obtenir les données historiques
      const historicalData = await this.getHistoricalData(volailleId);
      
      if (historicalData.length < 14) {
        throw new Error('Données historiques insuffisantes (minimum 14 jours)');
      }

      let forecast;
      
      switch (algorithm) {
        case 'linear_regression':
          forecast = await this.linearRegressionForecast(historicalData, days);
          break;
        case 'exponential_smoothing':
          forecast = await this.exponentialSmoothingForecast(historicalData, days);
          break;
        case 'arima':
          forecast = await this.arimaForecast(historicalData, days);
          break;
        case 'seasonal_decomposition':
          forecast = await this.seasonalDecompositionForecast(historicalData, days);
          break;
        case 'ensemble':
        default:
          forecast = await this.ensembleForecast(historicalData, days);
          break;
      }

      // Ajouter des métadonnées
      forecast.metadata = {
        volailleId: volailleId,
        algorithm: algorithm,
        dataPoints: historicalData.length,
        forecastDays: days,
        confidence: this.calculateOverallConfidence(forecast, historicalData),
        factors: await this.getInfluencingFactors(volailleId),
        timestamp: new Date().toISOString()
      };

      // Mettre en cache
      this.forecastCache.set(cacheKey, {
        forecast: forecast,
        timestamp: Date.now()
      });

      // Sauvegarder la prévision
      await this.saveForecast(volailleId, forecast);

      return forecast;

    } catch (error) {
      logger.error('Erreur lors de la prévision de production:', error);
      throw error;
    }
  }

  /**
   * Prévision par régression linéaire
   */
  async linearRegressionForecast(data, days) {
    const n = data.length;
    const x = data.map((_, i) => i);
    const y = data.map(d => d.quantite_produite);

    // Calculer la régression linéaire
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Générer les prévisions
    const forecasts = [];
    for (let i = 1; i <= days; i++) {
      const futureX = n + i - 1;
      const prediction = slope * futureX + intercept;
      
      forecasts.push({
        day: i,
        date: this.addDays(new Date(), i).toISOString().split('T')[0],
        predicted: Math.max(0, prediction),
        confidence: this.calculateLinearConfidence(i, data),
        method: 'linear_regression'
      });
    }

    return {
      forecasts: forecasts,
      model: {
        type: 'linear_regression',
        slope: slope,
        intercept: intercept,
        r_squared: this.calculateRSquared(x, y, slope, intercept)
      }
    };
  }

  /**
   * Prévision par lissage exponentiel
   */
  async exponentialSmoothingForecast(data, days) {
    const alpha = 0.3; // Paramètre de lissage
    const beta = 0.1;  // Paramètre de tendance
    const gamma = 0.1; // Paramètre saisonnier

    const values = data.map(d => d.quantite_produite);
    
    // Initialisation
    let level = values[0];
    let trend = values.length > 1 ? values[1] - values[0] : 0;
    const seasonLength = 7; // Cycle hebdomadaire
    const seasonal = this.initializeSeasonalFactors(values, seasonLength);

    // Lissage des données historiques
    const smoothed = [level];
    
    for (let i = 1; i < values.length; i++) {
      const seasonalIndex = i % seasonLength;
      
      const newLevel = alpha * (values[i] / seasonal[seasonalIndex]) + 
                      (1 - alpha) * (level + trend);
      const newTrend = beta * (newLevel - level) + (1 - beta) * trend;
      const newSeasonal = gamma * (values[i] / newLevel) + 
                         (1 - gamma) * seasonal[seasonalIndex];

      level = newLevel;
      trend = newTrend;
      seasonal[seasonalIndex] = newSeasonal;
      
      smoothed.push(level + trend);
    }

    // Générer les prévisions
    const forecasts = [];
    for (let i = 1; i <= days; i++) {
      const seasonalIndex = (values.length + i - 1) % seasonLength;
      const prediction = (level + trend * i) * seasonal[seasonalIndex];
      
      forecasts.push({
        day: i,
        date: this.addDays(new Date(), i).toISOString().split('T')[0],
        predicted: Math.max(0, prediction),
        confidence: this.calculateExponentialConfidence(i, smoothed),
        method: 'exponential_smoothing'
      });
    }

    return {
      forecasts: forecasts,
      model: {
        type: 'exponential_smoothing',
        alpha: alpha,
        beta: beta,
        gamma: gamma,
        final_level: level,
        final_trend: trend,
        seasonal_factors: seasonal
      }
    };
  }

  /**
   * Prévision ARIMA simplifiée
   */
  async arimaForecast(data, days) {
    const values = data.map(d => d.quantite_produite);
    
    // Calcul de la moyenne mobile
    const windowSize = 7;
    const movingAverages = this.calculateMovingAverage(values, windowSize);
    
    // Calcul des résidus
    const residuals = values.slice(windowSize - 1).map((val, i) => val - movingAverages[i]);
    
    // Modèle AR(1) simplifié
    const arCoeff = this.calculateARCoefficient(residuals);
    
    // Générer les prévisions
    const forecasts = [];
    let lastValue = values[values.length - 1];
    let lastResidual = residuals[residuals.length - 1];
    
    for (let i = 1; i <= days; i++) {
      const trendComponent = movingAverages[movingAverages.length - 1];
      const arComponent = arCoeff * lastResidual;
      const prediction = trendComponent + arComponent;
      
      forecasts.push({
        day: i,
        date: this.addDays(new Date(), i).toISOString().split('T')[0],
        predicted: Math.max(0, prediction),
        confidence: this.calculateARIMAConfidence(i, residuals),
        method: 'arima'
      });
      
      lastResidual = arComponent;
    }

    return {
      forecasts: forecasts,
      model: {
        type: 'arima',
        ar_coefficient: arCoeff,
        window_size: windowSize,
        residual_variance: this.calculateVariance(residuals)
      }
    };
  }

  /**
   * Prévision par décomposition saisonnière
   */
  async seasonalDecompositionForecast(data, days) {
    const values = data.map(d => d.quantite_produite);
    const volailleInfo = await this.getVolailleInfo(data[0].volaille_id);
    
    // Décomposer la série temporelle
    const decomposition = this.decomposeTimeSeries(values);
    
    // Appliquer les facteurs saisonniers
    const seasonalFactors = this.seasonalFactors[volailleInfo.type_production] || 
                           this.seasonalFactors.oeufs;
    
    const forecasts = [];
    const currentMonth = new Date().getMonth() + 1;
    
    for (let i = 1; i <= days; i++) {
      const futureDate = this.addDays(new Date(), i);
      const futureMonth = futureDate.getMonth() + 1;
      
      const trendValue = decomposition.trend + decomposition.trendSlope * i;
      const seasonalValue = seasonalFactors[futureMonth] || 1;
      const cyclicalValue = this.calculateCyclicalComponent(i, decomposition.cycle);
      
      const prediction = trendValue * seasonalValue * cyclicalValue;
      
      forecasts.push({
        day: i,
        date: futureDate.toISOString().split('T')[0],
        predicted: Math.max(0, prediction),
        confidence: this.calculateSeasonalConfidence(i, decomposition),
        method: 'seasonal_decomposition',
        components: {
          trend: trendValue,
          seasonal: seasonalValue,
          cyclical: cyclicalValue
        }
      });
    }

    return {
      forecasts: forecasts,
      model: {
        type: 'seasonal_decomposition',
        decomposition: decomposition,
        seasonal_factors: seasonalFactors
      }
    };
  }

  /**
   * Prévision d'ensemble (combinaison de méthodes)
   */
  async ensembleForecast(data, days) {
    // Exécuter plusieurs méthodes
    const methods = [
      'linear_regression',
      'exponential_smoothing',
      'seasonal_decomposition'
    ];

    const forecasts = {};
    const weights = {
      linear_regression: 0.2,
      exponential_smoothing: 0.4,
      seasonal_decomposition: 0.4
    };

    // Obtenir les prévisions de chaque méthode
    for (const method of methods) {
      try {
        forecasts[method] = await this.forecastProduction(
          data[0].volaille_id, 
          days, 
          method
        );
      } catch (error) {
        logger.warn(`Erreur méthode ${method}:`, error.message);
        weights[method] = 0;
      }
    }

    // Normaliser les poids
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    Object.keys(weights).forEach(key => {
      weights[key] = weights[key] / totalWeight;
    });

    // Combiner les prévisions
    const ensembleForecasts = [];
    
    for (let day = 1; day <= days; day++) {
      let weightedSum = 0;
      let weightedConfidence = 0;
      let validMethods = 0;

      Object.entries(forecasts).forEach(([method, forecast]) => {
        if (forecast && forecast.forecasts && forecast.forecasts[day - 1]) {
          const dayForecast = forecast.forecasts[day - 1];
          weightedSum += dayForecast.predicted * weights[method];
          weightedConfidence += dayForecast.confidence * weights[method];
          validMethods++;
        }
      });

      if (validMethods > 0) {
        ensembleForecasts.push({
          day: day,
          date: this.addDays(new Date(), day).toISOString().split('T')[0],
          predicted: weightedSum,
          confidence: weightedConfidence,
          method: 'ensemble',
          components: Object.fromEntries(
            Object.entries(forecasts).map(([method, forecast]) => [
              method,
              forecast?.forecasts?.[day - 1]?.predicted || 0
            ])
          )
        });
      }
    }

    return {
      forecasts: ensembleForecasts,
      model: {
        type: 'ensemble',
        methods: methods,
        weights: weights,
        component_models: Object.fromEntries(
          Object.entries(forecasts).map(([method, forecast]) => [
            method,
            forecast?.model || null
          ])
        )
      }
    };
  }

  /**
   * Obtenir les données historiques
   */
  async getHistoricalData(volailleId) {
    const result = await this.pool.query(`
      SELECT 
        date_production,
        quantite_produite,
        unite,
        type_production,
        volaille_id,
        EXTRACT(DOW FROM date_production) as day_of_week,
        EXTRACT(MONTH FROM date_production) as month
      FROM production 
      WHERE volaille_id = $1 
        AND date_production >= NOW() - INTERVAL '90 days'
      ORDER BY date_production ASC
    `, [volailleId]);

    return result.rows;
  }

  /**
   * Obtenir les informations de la volaille
   */
  async getVolailleInfo(volailleId) {
    const result = await this.pool.query(`
      SELECT type_volaille, nombre_actuel, nombre_total, date_creation
      FROM volailles 
      WHERE id = $1
    `, [volailleId]);

    if (result.rows.length === 0) {
      throw new Error('Volaille non trouvée');
    }

    const volaille = result.rows[0];
    
    // Déterminer le type de production basé sur le type de volaille
    volaille.type_production = volaille.type_volaille === 'poule' ? 'oeufs' : 'viande';
    
    return volaille;
  }

  /**
   * Obtenir les facteurs influençant la production
   */
  async getInfluencingFactors(volailleId) {
    const factors = {};

    // Facteurs météorologiques (simulés)
    factors.weather = {
      temperature: 20 + Math.random() * 10,
      humidity: 60 + Math.random() * 20,
      impact: 'moderate'
    };

    // Facteurs sanitaires
    const consultationsResult = await this.pool.query(`
      SELECT COUNT(*) as consultation_count,
             AVG(CASE WHEN urgence THEN 1 ELSE 0 END) as urgency_rate
      FROM consultations 
      WHERE volaille_id = $1 
        AND date_consultation >= NOW() - INTERVAL '30 days'
    `, [volailleId]);

    factors.health = {
      recent_consultations: parseInt(consultationsResult.rows[0].consultation_count),
      urgency_rate: parseFloat(consultationsResult.rows[0].urgency_rate) || 0,
      impact: consultationsResult.rows[0].consultation_count > 2 ? 'negative' : 'neutral'
    };

    // Facteurs d'âge du troupeau
    const volailleInfo = await this.getVolailleInfo(volailleId);
    const ageInDays = Math.floor((Date.now() - new Date(volailleInfo.date_creation)) / (1000 * 60 * 60 * 24));
    
    factors.age = {
      days: ageInDays,
      weeks: Math.floor(ageInDays / 7),
      productivity_phase: this.getProductivityPhase(ageInDays, volailleInfo.type_volaille),
      impact: this.getAgeImpact(ageInDays, volailleInfo.type_volaille)
    };

    return factors;
  }

  /**
   * Méthodes utilitaires
   */

  addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  calculateRSquared(x, y, slope, intercept) {
    const yMean = y.reduce((sum, val) => sum + val, 0) / y.length;
    const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0);
    const residualSumSquares = y.reduce((sum, val, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(val - predicted, 2);
    }, 0);

    return 1 - (residualSumSquares / totalSumSquares);
  }

  calculateMovingAverage(values, windowSize) {
    const result = [];
    for (let i = windowSize - 1; i < values.length; i++) {
      const window = values.slice(i - windowSize + 1, i + 1);
      const average = window.reduce((sum, val) => sum + val, 0) / windowSize;
      result.push(average);
    }
    return result;
  }

  calculateARCoefficient(residuals) {
    if (residuals.length < 2) return 0;
    
    const x = residuals.slice(0, -1);
    const y = residuals.slice(1);
    
    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  calculateVariance(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  }

  initializeSeasonalFactors(values, seasonLength) {
    const factors = new Array(seasonLength).fill(1);
    
    for (let i = 0; i < seasonLength && i < values.length; i++) {
      const seasonalValues = [];
      for (let j = i; j < values.length; j += seasonLength) {
        seasonalValues.push(values[j]);
      }
      
      if (seasonalValues.length > 0) {
        const average = seasonalValues.reduce((sum, val) => sum + val, 0) / seasonalValues.length;
        const overallAverage = values.reduce((sum, val) => sum + val, 0) / values.length;
        factors[i] = overallAverage > 0 ? average / overallAverage : 1;
      }
    }
    
    return factors;
  }

  decomposeTimeSeries(values) {
    const trend = this.calculateMovingAverage(values, 7);
    const trendSlope = trend.length > 1 ? 
      (trend[trend.length - 1] - trend[0]) / (trend.length - 1) : 0;
    
    return {
      trend: trend[trend.length - 1] || values[values.length - 1],
      trendSlope: trendSlope,
      cycle: this.calculateCyclicalPattern(values),
      residuals: values.map((val, i) => val - (trend[i] || val))
    };
  }

  calculateCyclicalPattern(values) {
    // Analyse cyclique simplifiée
    return {
      amplitude: this.calculateVariance(values),
      period: 7 // Cycle hebdomadaire par défaut
    };
  }

  calculateCyclicalComponent(day, cycle) {
    // Composante cyclique basée sur une sinusoïde
    return 1 + (cycle.amplitude * 0.1) * Math.sin(2 * Math.PI * day / cycle.period);
  }

  // Méthodes de calcul de confiance
  calculateLinearConfidence(day, data) {
    return Math.max(0.5, 0.95 - (day * 0.02));
  }

  calculateExponentialConfidence(day, smoothed) {
    return Math.max(0.6, 0.9 - (day * 0.015));
  }

  calculateARIMAConfidence(day, residuals) {
    const variance = this.calculateVariance(residuals);
    return Math.max(0.5, 0.85 - (day * 0.01) - (variance * 0.1));
  }

  calculateSeasonalConfidence(day, decomposition) {
    return Math.max(0.7, 0.9 - (day * 0.01));
  }

  calculateOverallConfidence(forecast, historicalData) {
    if (!forecast.forecasts || forecast.forecasts.length === 0) return 0;
    
    const avgConfidence = forecast.forecasts.reduce((sum, f) => sum + f.confidence, 0) / forecast.forecasts.length;
    const dataQuality = Math.min(1, historicalData.length / 30); // Plus de données = meilleure qualité
    
    return avgConfidence * dataQuality;
  }

  getProductivityPhase(ageInDays, typeVolaille) {
    if (typeVolaille === 'poule') {
      if (ageInDays < 140) return 'juvenile';
      if (ageInDays < 365) return 'peak';
      if (ageInDays < 730) return 'mature';
      return 'declining';
    } else {
      if (ageInDays < 30) return 'starter';
      if (ageInDays < 60) return 'grower';
      return 'finisher';
    }
  }

  getAgeImpact(ageInDays, typeVolaille) {
    const phase = this.getProductivityPhase(ageInDays, typeVolaille);
    
    const impacts = {
      juvenile: 'increasing',
      peak: 'optimal',
      mature: 'stable',
      declining: 'decreasing',
      starter: 'rapid_growth',
      grower: 'steady_growth',
      finisher: 'weight_gain'
    };

    return impacts[phase] || 'neutral';
  }

  async saveForecast(volailleId, forecast) {
    try {
      await this.pool.query(`
        INSERT INTO production_forecasts (volaille_id, forecast_data, algorithm, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [volailleId, JSON.stringify(forecast), forecast.metadata.algorithm]);
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde de la prévision:', error);
    }
  }
}

module.exports = new ProductionForecastService();
