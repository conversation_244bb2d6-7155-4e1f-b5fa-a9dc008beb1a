issue login Marchand !
[vite] connecting... client:789:9
[vite] connected. client:912:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
⚠️ Seuil de performance dépassé: render_time (3277 > 16) <anonymous code>:1:145535
✅ AuthContext: Utilisateur chargé:
Object { id: 1, username: "admin", email: "<EMAIL>", role_id: 1, first_name: "Ad<PERSON>", last_name: "<PERSON><PERSON><PERSON>", profile_id: null, status: "active", preferences: {}, firebase_uid: "96CSpHFAoKaAvBD018vRtRe8qW93", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: admin AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:5174/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
✅ AuthContext: Utilisateur chargé:
Object { id: 1, username: "admin", email: "<EMAIL>", role_id: 1, first_name: "Admin", last_name: "Poultray", profile_id: null, status: "active", preferences: {}, firebase_uid: "96CSpHFAoKaAvBD018vRtRe8qW93", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: admin AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: admin DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 1, email: "<EMAIL>", role: "admin", requiredRole: "admin" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: admin <anonymous code>:1:148389
🔍 AuthContext: Chargement utilisateur, token: false AuthContext.jsx:74:15
❌ AuthContext: Pas de token, arrêt du chargement AuthContext.jsx:76:17
🔐 Début de la connexion avec: <EMAIL> Login.jsx:53:15
🔐 AuthContext: Début de la connexion pour: <EMAIL> AuthContext.jsx:110:15
XHRPOST
https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyBZ9KIB7945XD6qJphq7dsaMJSh8DRBuX8
[HTTP/2 400  217ms]

Erreur lors de la connexion: FirebaseError: Firebase: Error (auth/invalid-credential).
    createErrorInternal firebase_auth.js:707
    _fail firebase_auth.js:672
    _performFetchWithErrorHandling firebase_auth.js:1142
    _performApiRequest firebase_auth.js:1075
    _performSignInRequest firebase_auth.js:1153
    signInWithPassword firebase_auth.js:3447
    handleRecaptchaFlow firebase_auth.js:3200
    _getIdTokenResponse firebase_auth.js:3530
    _processCredentialSavingMfaContextIfNecessary firebase_auth.js:4554
    _signInWithCredential firebase_auth.js:4638
    signInWithCredential firebase_auth.js:4646
    signInWithEmailAndPassword firebase_auth.js:4882
    login firebaseAuth.js:71
    login AuthContext.jsx:113
    handleSubmit Login.jsx:43
    handleFormSubmit ValidatedForm.jsx:59
    handleSubmit useFormValidation.js:163
    callCallback2 chunk-KPD4VVXB.js:3680
    invokeGuardedCallbackDev chunk-KPD4VVXB.js:3705
    invokeGuardedCallback chunk-KPD4VVXB.js:3739
    invokeGuardedCallbackAndCatchFirstError chunk-KPD4VVXB.js:3742
    executeDispatch chunk-KPD4VVXB.js:7046
    processDispatchQueueItemsInOrder chunk-KPD4VVXB.js:7066
    processDispatchQueue chunk-KPD4VVXB.js:7075
    dispatchEventsForPlugins chunk-KPD4VVXB.js:7083
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7206
    batchedUpdates$1 chunk-KPD4VVXB.js:18966
    batchedUpdates chunk-KPD4VVXB.js:3585
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7205
    dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay chunk-KPD4VVXB.js:5484
    dispatchEvent chunk-KPD4VVXB.js:5478
    dispatchDiscreteEvent chunk-KPD4VVXB.js:5455
    addEventBubbleListener chunk-KPD4VVXB.js:5650
    addTrappedEventListener chunk-KPD4VVXB.js:7152
    listenToNativeEvent chunk-KPD4VVXB.js:7109
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7118
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7115
    createRoot chunk-KPD4VVXB.js:21233
    createRoot$1 chunk-KPD4VVXB.js:21580
    createRoot react-dom_client.js:21
    <anonymous> main.jsx:5
<anonymous code>:1:145535
❌ AuthContext: Erreur de connexion: Error: Une erreur est survenue
    handleFirebaseError firebaseAuth.js:179
    login firebaseAuth.js:106
    login AuthContext.jsx:113
    handleSubmit Login.jsx:43
    handleFormSubmit ValidatedForm.jsx:59
    handleSubmit useFormValidation.js:163
    callCallback2 chunk-KPD4VVXB.js:3680
    invokeGuardedCallbackDev chunk-KPD4VVXB.js:3705
    invokeGuardedCallback chunk-KPD4VVXB.js:3739
    invokeGuardedCallbackAndCatchFirstError chunk-KPD4VVXB.js:3742
    executeDispatch chunk-KPD4VVXB.js:7046
    processDispatchQueueItemsInOrder chunk-KPD4VVXB.js:7066
    processDispatchQueue chunk-KPD4VVXB.js:7075
    dispatchEventsForPlugins chunk-KPD4VVXB.js:7083
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7206
    batchedUpdates$1 chunk-KPD4VVXB.js:18966
    batchedUpdates chunk-KPD4VVXB.js:3585
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7205
    dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay chunk-KPD4VVXB.js:5484
    dispatchEvent chunk-KPD4VVXB.js:5478
    dispatchDiscreteEvent chunk-KPD4VVXB.js:5455
    addEventBubbleListener chunk-KPD4VVXB.js:5650
    addTrappedEventListener chunk-KPD4VVXB.js:7152
    listenToNativeEvent chunk-KPD4VVXB.js:7109
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7118
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7115
    createRoot chunk-KPD4VVXB.js:21233
    createRoot$1 chunk-KPD4VVXB.js:21580
    createRoot react-dom_client.js:21
    <anonymous> main.jsx:5
<anonymous code>:1:145535
❌ Erreur lors de la connexion: Error: Une erreur est survenue
    handleFirebaseError firebaseAuth.js:179
    login firebaseAuth.js:106
    login AuthContext.jsx:113
    handleSubmit Login.jsx:43
    handleFormSubmit ValidatedForm.jsx:59
    handleSubmit useFormValidation.js:163
    callCallback2 chunk-KPD4VVXB.js:3680
    invokeGuardedCallbackDev chunk-KPD4VVXB.js:3705
    invokeGuardedCallback chunk-KPD4VVXB.js:3739
    invokeGuardedCallbackAndCatchFirstError chunk-KPD4VVXB.js:3742
    executeDispatch chunk-KPD4VVXB.js:7046
    processDispatchQueueItemsInOrder chunk-KPD4VVXB.js:7066
    processDispatchQueue chunk-KPD4VVXB.js:7075
    dispatchEventsForPlugins chunk-KPD4VVXB.js:7083
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7206
    batchedUpdates$1 chunk-KPD4VVXB.js:18966
    batchedUpdates chunk-KPD4VVXB.js:3585
    dispatchEventForPluginEventSystem chunk-KPD4VVXB.js:7205
    dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay chunk-KPD4VVXB.js:5484
    dispatchEvent chunk-KPD4VVXB.js:5478
    dispatchDiscreteEvent chunk-KPD4VVXB.js:5455
    addEventBubbleListener chunk-KPD4VVXB.js:5650
    addTrappedEventListener chunk-KPD4VVXB.js:7152
    listenToNativeEvent chunk-KPD4VVXB.js:7109
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7118
    listenToAllSupportedEvents chunk-KPD4VVXB.js:7115
    createRoot chunk-KPD4VVXB.js:21233
    createRoot$1 chunk-KPD4VVXB.js:21580
    createRoot react-dom_client.js:21
    <anonymous> main.jsx:5
<anonymous code>:1:145535
Détails de l'erreur:
Object { status: undefined, statusText: undefined, data: undefined, message: "Une erreur est survenue" }
<anonymous code>:1:145535




🔐 Début de la connexion avec: <EMAIL> Login.jsx:53:15
🔐 AuthContext: Début de la connexion pour: <EMAIL> AuthContext.jsx:110:15
✅ AuthContext: Données d'authentification reçues:
Object { hasToken: true, hasUser: true, userRole: "eleveur" }
AuthContext.jsx:112:15
🎯 AuthContext: État mis à jour - utilisateur connecté avec le rôle: eleveur AuthContext.jsx:120:15
✅ Connexion réussie:
Object { id: 2, email: "<EMAIL>", username: "eleveur", first_name: "", last_name: "", status: "active", role: "eleveur" }
Login.jsx:55:15
🎯 Redirection vers le dashboard pour le rôle: eleveur Login.jsx:62:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
📍 Navigation vers: /eleveur/dashboard Login.jsx:65:15
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:5174/api/eleveurs/1/dashboard
[HTTP/1.1 403 Forbidden 149ms]

XHRGET
http://localhost:5174/api/eleveurs/1/ouvriers
[HTTP/1.1 403 Forbidden 168ms]

XHRGET
http://localhost:5174/api/eleveurs/1/dashboard
[HTTP/1.1 403 Forbidden 184ms]

XHRGET
http://localhost:5174/api/eleveurs/1/ouvriers
[HTTP/1.1 403 Forbidden 214ms]

Erreur lors de la récupération des données:
Object { message: "Request failed with status code 403", name: "AxiosError", code: "ERR_BAD_REQUEST", config: {…}, request: XMLHttpRequest, response: {…}, status: 403, stack: "", … }
<anonymous code>:1:145535
Erreur lors de la récupération des données:
Object { message: "Request failed with status code 403", name: "AxiosError", code: "ERR_BAD_REQUEST", config: {…}, request: XMLHttpRequest, response: {…}, status: 403, stack: "", … }
<anonymous code>:1:145535
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 70ms]

XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 68ms]

XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 131ms]

XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 120ms]

🔄 Tentative 1/3 pour /volailles 2 axiosConfig.js:78:15
🔄 Tentative 1/3 pour /eleveurs 2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 15ms]

🔄 Tentative 2/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 13ms]

🔄 Tentative 2/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 14ms]

🔄 Tentative 2/3 pour /eleveurs axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 18ms]

🔄 Tentative 2/3 pour /eleveurs axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 11ms]

🔄 Tentative 3/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 24ms]

🔄 Tentative 3/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 15ms]

🔄 Tentative 3/3 pour /eleveurs axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 21ms]

🔄 Tentative 3/3 pour /eleveurs axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 13ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:29.360Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des volailles", url: "/volailles", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des volailles: StandardError: Erreur lors de la récupération des volailles
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    fetchVolailles VolaillesList.jsx:49
    VolaillesList VolaillesList.jsx:43
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 35ms]

XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 59ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:29.676Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des volailles", url: "/volailles", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des volailles: StandardError: Erreur lors de la récupération des volailles
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    fetchVolailles VolaillesList.jsx:49
    VolaillesList VolaillesList.jsx:43
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 48ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:29.844Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur serveur lors de la récupération des éleveurs", url: "/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des éleveurs: StandardError: Erreur serveur lors de la récupération des éleveurs
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    fetchEleveurs VolaillesList.jsx:61
    VolaillesList VolaillesList.jsx:44
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:29.958Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur serveur lors de la récupération des éleveurs", url: "/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des éleveurs: StandardError: Erreur serveur lors de la récupération des éleveurs
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    wrap axios.js:8
    <anonymous> axiosConfig.js:131
    _request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    fetchEleveurs VolaillesList.jsx:61
    VolaillesList VolaillesList.jsx:44
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/eleveur/ventes
[HTTP/1.1 404 Not Found 8ms]

XHRGET
http://localhost:3003/api/eleveur/ventes/stats
[HTTP/1.1 404 Not Found 13ms]

XHRGET
http://localhost:3003/api/eleveur/ventes
[HTTP/1.1 404 Not Found 41ms]

XHRGET
http://localhost:3003/api/eleveur/ventes/stats
[HTTP/1.1 404 Not Found 25ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:30.573Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/ventes", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur ventes: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getVentes eleveurService.js:12
    loadVentes VentesManagement.jsx:87
    VentesManagement VentesManagement.jsx:80
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:30.705Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/ventes/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats ventes: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getVentesStats eleveurService.js:32
    loadStats VentesManagement.jsx:100
    VentesManagement VentesManagement.jsx:81
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:30.708Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/ventes/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats ventes: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getVentesStats eleveurService.js:32
    loadStats VentesManagement.jsx:100
    VentesManagement VentesManagement.jsx:81
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:30.710Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/ventes", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur ventes: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getVentes eleveurService.js:12
    loadVentes VentesManagement.jsx:87
    VentesManagement VentesManagement.jsx:80
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/eleveur/statistics/detailed?period=6months
[HTTP/1.1 404 Not Found 8ms]

XHRGET
http://localhost:3003/api/eleveur/statistics/kpis?period=6months
[HTTP/1.1 404 Not Found 10ms]

XHRGET
http://localhost:3003/api/eleveur/statistics/detailed?period=6months
[HTTP/1.1 404 Not Found 18ms]

XHRGET
http://localhost:3003/api/eleveur/statistics/kpis?period=6months
[HTTP/1.1 404 Not Found 21ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:34.715Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/statistics/detailed?period=6months", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur statistiques: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getDetailedStatistics eleveurService.js:39
    loadStatistics EleveurStatistics.jsx:85
    EleveurStatistics EleveurStatistics.jsx:78
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:34.718Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/statistics/kpis?period=6months", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:34.987Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/statistics/detailed?period=6months", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur statistiques: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getDetailedStatistics eleveurService.js:39
    loadStatistics EleveurStatistics.jsx:85
    EleveurStatistics EleveurStatistics.jsx:78
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:34.990Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/eleveur/statistics/kpis?period=6months", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/api/integrations/iot/devices
[HTTP/1.1 404 Not Found 14ms]

XHRGET
http://localhost:3003/api/api/integrations/iot/alerts
[HTTP/1.1 404 Not Found 27ms]

XHRGET
http://localhost:3003/api/api/integrations/iot/stats
[HTTP/1.1 404 Not Found 51ms]

XHRGET
http://localhost:3003/api/api/integrations/iot/devices
[HTTP/1.1 404 Not Found 16ms]

XHRGET
http://localhost:3003/api/api/integrations/iot/alerts
[HTTP/1.1 404 Not Found 6ms]

XHRGET
http://localhost:3003/api/api/integrations/iot/stats
[HTTP/1.1 404 Not Found 10ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.604Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/devices", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur dispositifs IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getDevices iotService.js:12
    loadDevices IoTDevicesManagement.jsx:98
    IoTDevicesManagement IoTDevicesManagement.jsx:82
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.734Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/alerts", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur alertes IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getAlerts iotService.js:46
    loadAlerts IoTDevicesManagement.jsx:117
    IoTDevicesManagement IoTDevicesManagement.jsx:94
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.737Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getStats iotService.js:112
    loadStats IoTDevicesManagement.jsx:120
    IoTDevicesManagement IoTDevicesManagement.jsx:84
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.738Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/devices", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur dispositifs IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getDevices iotService.js:12
    loadDevices IoTDevicesManagement.jsx:98
    IoTDevicesManagement IoTDevicesManagement.jsx:82
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.740Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/alerts", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur alertes IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getAlerts iotService.js:46
    loadAlerts IoTDevicesManagement.jsx:117
    IoTDevicesManagement IoTDevicesManagement.jsx:94
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:25:42.817Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/api/integrations/iot/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats IoT: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getStats iotService.js:112
    loadStats IoTDevicesManagement.jsx:120
    IoTDevicesManagement IoTDevicesManagement.jsx:84
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535






👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
🔍 AuthContext: Chargement utilisateur, token: false AuthContext.jsx:74:15
❌ AuthContext: Pas de token, arrêt du chargement AuthContext.jsx:76:17
🔐 Début de la connexion avec: <EMAIL> Login.jsx:53:15
🔐 AuthContext: Début de la connexion pour: <EMAIL> AuthContext.jsx:110:15
✅ AuthContext: Données d'authentification reçues:
Object { hasToken: true, hasUser: true, userRole: "veterinaire" }
AuthContext.jsx:112:15
🎯 AuthContext: État mis à jour - utilisateur connecté avec le rôle: veterinaire AuthContext.jsx:120:15
✅ Connexion réussie:
Object { id: 8, email: "<EMAIL>", username: "vetirinaire", first_name: "", last_name: "", status: "active", role: "veterinaire" }
Login.jsx:55:15
🎯 Redirection vers le dashboard pour le rôle: veterinaire Login.jsx:62:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
📍 Navigation vers: /veterinaire/dashboard Login.jsx:65:15
✅ AuthContext: Utilisateur chargé:
Object { id: 8, username: "vetirinaire", email: "<EMAIL>", role_id: 16, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "gePyjBdy0uUKiTiQmV6KxmreNqz2", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: veterinaire AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 656ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 812ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 696ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 769ms]

Erreur lors de la récupération des données:
Object { message: "Request failed with status code 500", name: "AxiosError", code: "ERR_BAD_RESPONSE", config: {…}, request: XMLHttpRequest, response: {…}, status: 500, stack: "", … }
<anonymous code>:1:145535
Erreur lors de la récupération des données:
Object { message: "Request failed with status code 500", name: "AxiosError", code: "ERR_BAD_RESPONSE", config: {…}, request: XMLHttpRequest, response: {…}, status: 500, stack: "", … }
<anonymous code>:1:145535


👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/veterinaire/prescriptions
[HTTP/1.1 404 Not Found 11ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 61ms]

XHRGET
http://localhost:3003/api/veterinaire/medicaments
[HTTP/1.1 404 Not Found 75ms]

XHRGET
http://localhost:3003/api/veterinaire/prescriptions/stats
[HTTP/1.1 404 Not Found 27ms]

XHRGET
http://localhost:3003/api/veterinaire/prescriptions
[HTTP/1.1 404 Not Found 11ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 23ms]

XHRGET
http://localhost:3003/api/veterinaire/medicaments
[HTTP/1.1 404 Not Found 82ms]

XHRGET
http://localhost:3003/api/veterinaire/prescriptions/stats
[HTTP/1.1 404 Not Found 30ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.130Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/prescriptions", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur prescriptions: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPrescriptions veterinaireService.js:12
    loadPrescriptions PrescriptionsManagement.jsx:105
    PrescriptionsManagement PrescriptionsManagement.jsx:96
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.136Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/prescriptions", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur prescriptions: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPrescriptions veterinaireService.js:12
    loadPrescriptions PrescriptionsManagement.jsx:105
    PrescriptionsManagement PrescriptionsManagement.jsx:96
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.296Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients PrescriptionsManagement.jsx:118
    PrescriptionsManagement PrescriptionsManagement.jsx:97
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.301Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/medicaments", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur médicaments: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getMedicaments veterinaireService.js:132
    loadMedicaments PrescriptionsManagement.jsx:127
    PrescriptionsManagement PrescriptionsManagement.jsx:98
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.304Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/medicaments", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur médicaments: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getMedicaments veterinaireService.js:132
    loadMedicaments PrescriptionsManagement.jsx:127
    PrescriptionsManagement PrescriptionsManagement.jsx:98
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.306Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/prescriptions/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats prescriptions: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPrescriptionsStats veterinaireService.js:39
    loadStats PrescriptionsManagement.jsx:136
    PrescriptionsManagement PrescriptionsManagement.jsx:99
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.308Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients PrescriptionsManagement.jsx:118
    PrescriptionsManagement PrescriptionsManagement.jsx:97
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:28:57.311Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/prescriptions/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats prescriptions: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPrescriptionsStats veterinaireService.js:39
    loadStats PrescriptionsManagement.jsx:136
    PrescriptionsManagement PrescriptionsManagement.jsx:99
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535


👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/veterinaire/consultations
[HTTP/1.1 404 Not Found 17ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 56ms]

XHRGET
http://localhost:3003/api/veterinaire/consultations/stats
[HTTP/1.1 404 Not Found 28ms]

XHRGET
http://localhost:3003/api/veterinaire/consultations
[HTTP/1.1 404 Not Found 53ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 41ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.632Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/consultations", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/veterinaire/consultations/stats
[HTTP/1.1 404 Not Found 55ms]

Erreur consultations: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getConsultations veterinaireService.js:46
    loadConsultations ConsultationsManagement.jsx:103
    ConsultationsManagement ConsultationsManagement.jsx:95
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.892Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/consultations", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur consultations: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getConsultations veterinaireService.js:46
    loadConsultations ConsultationsManagement.jsx:103
    ConsultationsManagement ConsultationsManagement.jsx:95
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.898Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients ConsultationsManagement.jsx:116
    ConsultationsManagement ConsultationsManagement.jsx:96
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.902Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/consultations/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats consultations: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getConsultationsStats veterinaireService.js:66
    loadStats ConsultationsManagement.jsx:125
    ConsultationsManagement ConsultationsManagement.jsx:97
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.905Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients ConsultationsManagement.jsx:116
    ConsultationsManagement ConsultationsManagement.jsx:96
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:11.918Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/consultations/stats", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur stats consultations: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getConsultationsStats veterinaireService.js:66
    loadStats ConsultationsManagement.jsx:125
    ConsultationsManagement ConsultationsManagement.jsx:97
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535


👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
MUI X: The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.
You can replace it with the `textField` component slot in most cases.
For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5). Component Stack:
    DesktopDatePicker2 @mui_x-date-pickers_DatePicker.js:561
    DatePicker2 @mui_x-date-pickers_DatePicker.js:1268
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Grid2 @mui_material.js:6234
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Grid2 @mui_material.js:6234
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    CardContent2 @mui_material.js:3506
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Paper2 chunk-6LYU7UHL.js:235
    node_modules chunk-AXQ7RD4E.js:1727
    Card2 @mui_material.js:3181
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Container3 chunk-LTCE2M3P.js:2095
    LocalizationProvider2 chunk-SRM3AYIW.js:35
    HistoriqueMedical HistoriqueMedical.jsx:64
    Suspense unknown:0
    RenderedRoute react-router-dom.js:4092
    Outlet react-router-dom.js:4495
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    main unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    DashboardLayout DashboardLayout.jsx:17
    RenderedRoute react-router-dom.js:4092
    Routes react-router-dom.js:4561
    Router react-router-dom.js:4509
    BrowserRouter react-router-dom.js:5252
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider chunk-CH6NFDZL.js:3279
    RtlProvider chunk-LTCE2M3P.js:1138
    ThemeProvider chunk-LTCE2M3P.js:1089
    ThemeProvider2 chunk-LTCE2M3P.js:1180
    ThemeProvider chunk-KFEB75RM.js:332
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 16ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 6ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:24.557Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients HistoriqueMedical.jsx:87
    HistoriqueMedical HistoriqueMedical.jsx:76
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:24.559Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients HistoriqueMedical.jsx:87
    HistoriqueMedical HistoriqueMedical.jsx:76
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535


👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 8, email: "<EMAIL>", role: "veterinaire", requiredRole: "veterinaire" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: veterinaire <anonymous code>:1:148389
Erreur stats rendez-vous: TypeError: (intermediate value).getAppointmentsStats is not a function
    loadStats AppointmentsManagement.jsx:142
    AppointmentsManagement AppointmentsManagement.jsx:93
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
 Component Stack:
    AppointmentsManagement AppointmentsManagement.jsx:59
    Suspense unknown:0
    RenderedRoute react-router-dom.js:4092
    Outlet react-router-dom.js:4495
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    main unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    div unknown:0
    node_modules chunk-AXQ7RD4E.js:1727
    Box3 chunk-LTCE2M3P.js:398
    DashboardLayout DashboardLayout.jsx:17
    RenderedRoute react-router-dom.js:4092
    Routes react-router-dom.js:4561
    Router react-router-dom.js:4509
    BrowserRouter react-router-dom.js:5252
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider chunk-CH6NFDZL.js:3279
    RtlProvider chunk-LTCE2M3P.js:1138
    ThemeProvider chunk-LTCE2M3P.js:1089
    ThemeProvider2 chunk-LTCE2M3P.js:1180
    ThemeProvider chunk-KFEB75RM.js:332
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Erreur stats rendez-vous: TypeError: (intermediate value).getAppointmentsStats is not a function
    loadStats AppointmentsManagement.jsx:142
    AppointmentsManagement AppointmentsManagement.jsx:93
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/veterinaire/appointments
[HTTP/1.1 404 Not Found 36ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 19ms]

XHRGET
http://localhost:3003/api/veterinaire/eleveurs
[HTTP/1.1 404 Not Found 25ms]

XHRGET
http://localhost:3003/api/veterinaire/appointments
[HTTP/1.1 404 Not Found 5ms]

XHRGET
http://localhost:3003/api/veterinaire/patients
[HTTP/1.1 404 Not Found 8ms]

XHRGET
http://localhost:3003/api/veterinaire/eleveurs
[HTTP/1.1 404 Not Found 36ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.868Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/appointments", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur rendez-vous: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getAppointments veterinaireService.js:73
    loadAppointments AppointmentsManagement.jsx:99
    AppointmentsManagement AppointmentsManagement.jsx:90
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.872Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients AppointmentsManagement.jsx:124
    AppointmentsManagement AppointmentsManagement.jsx:91
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.874Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur éleveurs: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getEleveurs veterinaireService.js:216
    loadEleveurs AppointmentsManagement.jsx:133
    AppointmentsManagement AppointmentsManagement.jsx:92
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    commitPassiveMountOnFiber chunk-KPD4VVXB.js:18206
    commitPassiveMountEffects_complete chunk-KPD4VVXB.js:18179
    commitPassiveMountEffects_begin chunk-KPD4VVXB.js:18169
    commitPassiveMountEffects chunk-KPD4VVXB.js:18159
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19543
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.876Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur éleveurs: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getEleveurs veterinaireService.js:216
    loadEleveurs AppointmentsManagement.jsx:133
    AppointmentsManagement AppointmentsManagement.jsx:92
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.878Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/appointments", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur rendez-vous: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getAppointments veterinaireService.js:73
    loadAppointments AppointmentsManagement.jsx:99
    AppointmentsManagement AppointmentsManagement.jsx:90
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-08T23:29:40.879Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/veterinaire/patients", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur patients: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request axios.js:2206
    request axios.js:2115
    method axios.js:2243
    wrap axios.js:8
    getPatients veterinaireService.js:105
    loadPatients AppointmentsManagement.jsx:124
    AppointmentsManagement AppointmentsManagement.jsx:91
    commitHookEffectListMount chunk-KPD4VVXB.js:16963
    invokePassiveEffectMountInDEV chunk-KPD4VVXB.js:18374
    invokeEffectsInDev chunk-KPD4VVXB.js:19754
    commitDoubleInvokeEffectsInDEV chunk-KPD4VVXB.js:19739
    flushPassiveEffectsImpl chunk-KPD4VVXB.js:19556
    flushPassiveEffects chunk-KPD4VVXB.js:19500
    performSyncWorkOnRoot chunk-KPD4VVXB.js:18921
    flushSyncCallbacks chunk-KPD4VVXB.js:9166
    commitRootImpl chunk-KPD4VVXB.js:19485
    commitRoot chunk-KPD4VVXB.js:19330
    finishConcurrentRender chunk-KPD4VVXB.js:18858
    performConcurrentWorkOnRoot chunk-KPD4VVXB.js:18768
    workLoop chunk-KPD4VVXB.js:197
    flushWork chunk-KPD4VVXB.js:176
    performWorkUntilDeadline chunk-KPD4VVXB.js:384
    js chunk-KPD4VVXB.js:405
    js chunk-KPD4VVXB.js:453
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:465
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:481
    js chunk-KPD4VVXB.js:21629
    __require2 chunk-LK32TJAX.js:18
    js chunk-KPD4VVXB.js:21641
    __require2 chunk-LK32TJAX.js:18
    js react-dom_client.js:12
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
