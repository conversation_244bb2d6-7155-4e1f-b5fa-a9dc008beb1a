/**
 * Service d'optimisation des performances frontend
 */

import { lazy } from 'react';

class PerformanceOptimizationService {
  constructor() {
    this.performanceObserver = null;
    this.resourceTimings = new Map();
    this.componentMetrics = new Map();
    this.bundleAnalytics = {
      loadTimes: new Map(),
      chunkSizes: new Map(),
      cacheHits: new Map()
    };
    this.optimizationStrategies = new Map();
    this.preloadedResources = new Set();
  }

  /**
   * Initialiser le service d'optimisation
   */
  initialize() {
    this.setupPerformanceObserver();
    this.setupResourceTimingObserver();
    this.setupLongTaskObserver();
    this.setupLayoutShiftObserver();
    this.setupNavigationObserver();
    this.enableServiceWorkerCaching();
    
    console.log('🚀 Service d\'optimisation des performances initialisé');
  }

  /**
   * Configuration du Performance Observer
   */
  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      // Observer pour les métriques de peinture
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric('paint', entry.name, entry.startTime);
          
          if (entry.name === 'first-contentful-paint') {
            this.optimizeBasedOnFCP(entry.startTime);
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });

      // Observer pour les métriques de navigation
      const navigationObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.analyzeNavigationTiming(entry);
        });
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });

      // Observer pour les métriques LCP
      const lcpObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric('lcp', 'largest-contentful-paint', entry.startTime);
          this.optimizeBasedOnLCP(entry.startTime, entry.element);
        });
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  }

  /**
   * Observer pour les timings des ressources
   */
  setupResourceTimingObserver() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.analyzeResourceTiming(entry);
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
    }
  }

  /**
   * Observer pour les tâches longues
   */
  setupLongTaskObserver() {
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.handleLongTask(entry);
        });
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
    }
  }

  /**
   * Observer pour les décalages de mise en page (CLS)
   */
  setupLayoutShiftObserver() {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
            this.recordMetric('cls', 'cumulative-layout-shift', clsValue);
            
            if (clsValue > 0.1) {
              this.optimizeLayoutStability();
            }
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  /**
   * Observer pour les métriques de navigation
   */
  setupNavigationObserver() {
    // Mesurer le temps de chargement initial
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.recordMetric('navigation', 'page-load', loadTime);
      
      if (loadTime > 3000) {
        this.implementLoadTimeOptimizations();
      }
    });

    // Mesurer le temps d'interaction
    document.addEventListener('DOMContentLoaded', () => {
      const domTime = performance.now();
      this.recordMetric('navigation', 'dom-ready', domTime);
    });
  }

  /**
   * Analyser les timings de navigation
   */
  analyzeNavigationTiming(entry) {
    const metrics = {
      dns: entry.domainLookupEnd - entry.domainLookupStart,
      tcp: entry.connectEnd - entry.connectStart,
      ssl: entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      ttfb: entry.responseStart - entry.requestStart,
      download: entry.responseEnd - entry.responseStart,
      domProcessing: entry.domContentLoadedEventStart - entry.responseEnd,
      domComplete: entry.domComplete - entry.domContentLoadedEventStart
    };

    // Identifier les goulots d'étranglement
    Object.entries(metrics).forEach(([metric, value]) => {
      this.recordMetric('navigation', metric, value);
      
      if (this.isBottleneck(metric, value)) {
        this.suggestOptimization(metric, value);
      }
    });
  }

  /**
   * Analyser les timings des ressources
   */
  analyzeResourceTiming(entry) {
    const resourceType = this.getResourceType(entry.name);
    const loadTime = entry.responseEnd - entry.startTime;
    
    this.resourceTimings.set(entry.name, {
      type: resourceType,
      loadTime: loadTime,
      size: entry.transferSize,
      cached: entry.transferSize === 0,
      timestamp: Date.now()
    });

    // Optimisations basées sur le type de ressource
    if (resourceType === 'script' && loadTime > 1000) {
      this.suggestScriptOptimization(entry.name, loadTime);
    } else if (resourceType === 'image' && entry.transferSize > 500000) {
      this.suggestImageOptimization(entry.name, entry.transferSize);
    } else if (resourceType === 'stylesheet' && loadTime > 500) {
      this.suggestCSSOptimization(entry.name, loadTime);
    }
  }

  /**
   * Gérer les tâches longues
   */
  handleLongTask(entry) {
    const duration = entry.duration;
    
    this.recordMetric('longtask', 'duration', duration);
    
    if (duration > 50) {
      console.warn(`Tâche longue détectée: ${duration}ms`);
      this.suggestTaskOptimization(entry);
    }
  }

  /**
   * Optimisations basées sur First Contentful Paint
   */
  optimizeBasedOnFCP(fcpTime) {
    if (fcpTime > 2500) {
      this.implementCriticalResourceOptimizations();
      this.enableResourceHints();
    }
  }

  /**
   * Optimisations basées sur Largest Contentful Paint
   */
  optimizeBasedOnLCP(lcpTime, element) {
    if (lcpTime > 2500) {
      this.optimizeLCPElement(element);
      this.implementImageOptimizations();
    }
  }

  /**
   * Optimiser la stabilité de la mise en page
   */
  optimizeLayoutStability() {
    // Ajouter des dimensions explicites aux images
    this.addImageDimensions();
    
    // Réserver l'espace pour le contenu dynamique
    this.reserveSpaceForDynamicContent();
    
    // Optimiser les polices web
    this.optimizeWebFonts();
  }

  /**
   * Implémenter les optimisations de temps de chargement
   */
  implementLoadTimeOptimizations() {
    // Activer le lazy loading
    this.enableLazyLoading();
    
    // Précharger les ressources critiques
    this.preloadCriticalResources();
    
    // Optimiser les bundles JavaScript
    this.optimizeJavaScriptBundles();
    
    // Activer la compression
    this.enableCompression();
  }

  /**
   * Activer le lazy loading pour les images et composants
   */
  enableLazyLoading() {
    // Lazy loading des images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));

    // Lazy loading des composants React
    this.setupComponentLazyLoading();
  }

  /**
   * Configuration du lazy loading des composants React
   */
  setupComponentLazyLoading() {
    // Créer des composants lazy pour les routes
    const lazyComponents = {
      Dashboard: lazy(() => import('../components/dashboard/Dashboard')),
      VolaillesList: lazy(() => import('../components/volailles/VolaillesList')),
      ProductionDashboard: lazy(() => import('../components/production/ProductionDashboard')),
      ConsultationsList: lazy(() => import('../components/consultations/ConsultationsList')),
      UserProfile: lazy(() => import('../components/user/UserProfile')),
      Settings: lazy(() => import('../components/settings/Settings')),
      Reports: lazy(() => import('../components/reports/Reports')),
      Analytics: lazy(() => import('../components/analytics/Analytics'))
    };

    // Précharger les composants basés sur la navigation utilisateur
    this.preloadComponentsBasedOnUserBehavior(lazyComponents);

    return lazyComponents;
  }

  /**
   * Précharger les ressources critiques
   */
  preloadCriticalResources() {
    const criticalResources = [
      { href: '/api/user/profile', as: 'fetch' },
      { href: '/fonts/roboto-v20-latin-regular.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
      { href: '/images/logo.webp', as: 'image' }
    ];

    criticalResources.forEach(resource => {
      if (!this.preloadedResources.has(resource.href)) {
        const link = document.createElement('link');
        link.rel = 'preload';
        Object.assign(link, resource);
        document.head.appendChild(link);
        this.preloadedResources.add(resource.href);
      }
    });
  }

  /**
   * Optimiser les bundles JavaScript
   */
  optimizeJavaScriptBundles() {
    // Analyser les bundles chargés
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    
    scripts.forEach(script => {
      const src = script.src;
      const timing = this.resourceTimings.get(src);
      
      if (timing && timing.loadTime > 1000) {
        // Suggérer le code splitting
        this.suggestCodeSplitting(src);
      }
    });

    // Implémenter le prefetching intelligent
    this.implementIntelligentPrefetching();
  }

  /**
   * Implémenter le prefetching intelligent
   */
  implementIntelligentPrefetching() {
    // Analyser les patterns de navigation
    const navigationPatterns = this.analyzeNavigationPatterns();
    
    // Précharger les pages probables
    navigationPatterns.forEach(pattern => {
      if (pattern.probability > 0.7) {
        this.prefetchRoute(pattern.route);
      }
    });
  }

  /**
   * Précharger une route
   */
  prefetchRoute(route) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    document.head.appendChild(link);
  }

  /**
   * Optimiser les images
   */
  implementImageOptimizations() {
    // Utiliser des formats d'image modernes
    this.enableModernImageFormats();
    
    // Implémenter le responsive images
    this.implementResponsiveImages();
    
    // Optimiser les images critiques
    this.optimizeCriticalImages();
  }

  /**
   * Activer les formats d'image modernes
   */
  enableModernImageFormats() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
      if (this.supportsWebP() && !img.src.includes('.webp')) {
        const webpSrc = img.src.replace(/\.(jpg|jpeg|png)$/, '.webp');
        
        // Vérifier si la version WebP existe
        this.checkImageExists(webpSrc).then(exists => {
          if (exists) {
            img.src = webpSrc;
          }
        });
      }
    });
  }

  /**
   * Vérifier si le navigateur supporte WebP
   */
  supportsWebP() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * Vérifier si une image existe
   */
  checkImageExists(src) {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = src;
    });
  }

  /**
   * Optimiser les polices web
   */
  optimizeWebFonts() {
    // Précharger les polices critiques
    const criticalFonts = [
      '/fonts/roboto-v20-latin-regular.woff2',
      '/fonts/roboto-v20-latin-700.woff2'
    ];

    criticalFonts.forEach(font => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = font;
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });

    // Utiliser font-display: swap
    this.enableFontDisplaySwap();
  }

  /**
   * Activer font-display: swap
   */
  enableFontDisplaySwap() {
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: 'Roboto';
        font-display: swap;
        src: url('/fonts/roboto-v20-latin-regular.woff2') format('woff2');
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Activer le Service Worker pour le cache
   */
  enableServiceWorkerCaching() {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('Service Worker enregistré:', registration);
          this.optimizeCacheStrategy(registration);
        })
        .catch(error => {
          console.error('Erreur Service Worker:', error);
        });
    }
  }

  /**
   * Optimiser la stratégie de cache
   */
  optimizeCacheStrategy(registration) {
    // Implémenter différentes stratégies selon le type de ressource
    const cacheStrategies = {
      'static': 'cache-first',
      'api': 'network-first',
      'images': 'cache-first',
      'documents': 'stale-while-revalidate'
    };

    // Configurer les stratégies via le Service Worker
    if (registration.active) {
      registration.active.postMessage({
        type: 'CONFIGURE_CACHE_STRATEGIES',
        strategies: cacheStrategies
      });
    }
  }

  /**
   * Enregistrer une métrique de performance
   */
  recordMetric(category, name, value) {
    const key = `${category}.${name}`;
    
    if (!this.componentMetrics.has(key)) {
      this.componentMetrics.set(key, []);
    }
    
    this.componentMetrics.get(key).push({
      value: value,
      timestamp: Date.now()
    });

    // Garder seulement les 100 dernières mesures
    const metrics = this.componentMetrics.get(key);
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * Obtenir les métriques de performance
   */
  getPerformanceMetrics() {
    const metrics = {};
    
    this.componentMetrics.forEach((values, key) => {
      const recentValues = values.slice(-10);
      metrics[key] = {
        current: recentValues[recentValues.length - 1]?.value || 0,
        average: recentValues.reduce((sum, m) => sum + m.value, 0) / recentValues.length,
        min: Math.min(...recentValues.map(m => m.value)),
        max: Math.max(...recentValues.map(m => m.value)),
        count: values.length
      };
    });

    return metrics;
  }

  /**
   * Générer un rapport de performance
   */
  generatePerformanceReport() {
    return {
      timestamp: new Date().toISOString(),
      metrics: this.getPerformanceMetrics(),
      resourceTimings: Object.fromEntries(this.resourceTimings),
      bundleAnalytics: this.bundleAnalytics,
      optimizationSuggestions: this.getOptimizationSuggestions(),
      webVitals: this.getWebVitals()
    };
  }

  /**
   * Obtenir les Web Vitals
   */
  getWebVitals() {
    const vitals = {};
    
    // LCP
    const lcpMetrics = this.componentMetrics.get('lcp.largest-contentful-paint');
    if (lcpMetrics && lcpMetrics.length > 0) {
      vitals.lcp = lcpMetrics[lcpMetrics.length - 1].value;
    }

    // FID (approximation via les tâches longues)
    const longTaskMetrics = this.componentMetrics.get('longtask.duration');
    if (longTaskMetrics && longTaskMetrics.length > 0) {
      vitals.fid = Math.max(...longTaskMetrics.slice(-5).map(m => m.value));
    }

    // CLS
    const clsMetrics = this.componentMetrics.get('cls.cumulative-layout-shift');
    if (clsMetrics && clsMetrics.length > 0) {
      vitals.cls = clsMetrics[clsMetrics.length - 1].value;
    }

    return vitals;
  }

  /**
   * Méthodes utilitaires
   */

  getResourceType(url) {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
    return 'other';
  }

  isBottleneck(metric, value) {
    const thresholds = {
      dns: 100,
      tcp: 100,
      ssl: 200,
      ttfb: 600,
      download: 1000,
      domProcessing: 1500
    };

    return value > (thresholds[metric] || 1000);
  }

  suggestOptimization(metric, value) {
    const suggestions = {
      dns: 'Considérer l\'utilisation d\'un CDN ou DNS plus rapide',
      tcp: 'Optimiser la configuration du serveur ou utiliser HTTP/2',
      ssl: 'Optimiser la configuration SSL/TLS',
      ttfb: 'Optimiser les performances du serveur backend',
      download: 'Activer la compression gzip/brotli',
      domProcessing: 'Optimiser le JavaScript et réduire la complexité du DOM'
    };

    console.warn(`Goulot d'étranglement détecté - ${metric}: ${value}ms`);
    console.info(`Suggestion: ${suggestions[metric]}`);
  }

  analyzeNavigationPatterns() {
    // Analyser les patterns de navigation stockés
    const patterns = JSON.parse(localStorage.getItem('navigationPatterns') || '[]');
    
    return patterns.map(pattern => ({
      ...pattern,
      probability: pattern.count / patterns.reduce((sum, p) => sum + p.count, 0)
    }));
  }

  getOptimizationSuggestions() {
    const suggestions = [];
    const metrics = this.getPerformanceMetrics();

    // Suggestions basées sur les métriques
    Object.entries(metrics).forEach(([key, metric]) => {
      if (key.includes('paint') && metric.current > 2500) {
        suggestions.push({
          type: 'performance',
          priority: 'high',
          message: 'Temps de peinture élevé - optimiser les ressources critiques'
        });
      }
    });

    return suggestions;
  }
}

// Instance singleton
const performanceOptimizationService = new PerformanceOptimizationService();

export default performanceOptimizationService;
