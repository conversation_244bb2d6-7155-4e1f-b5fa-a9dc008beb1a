/**
 * Routes pour les fonctionnalités d'intelligence artificielle
 */

const express = require('express');
const router = express.Router();
const { body, query, param } = require('express-validator');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const aiPredictionService = require('../services/aiPredictionService');
const productionForecastService = require('../services/productionForecastService');
const intelligentAlertService = require('../services/intelligentAlertService');

/**
 * @swagger
 * /api/ai/health/predict:
 *   post:
 *     summary: Prédire l'état de santé d'un troupeau
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               volailleId:
 *                 type: string
 *                 format: uuid
 *               features:
 *                 type: array
 *                 items:
 *                   type: number
 *                 description: Caractéristiques optionnelles (si non fournies, extraites automatiquement)
 *     responses:
 *       200:
 *         description: Prédiction de santé avec recommandations
 */
router.post('/health/predict',
  auth,
  validate([
    body('volailleId').isUUID(),
    body('features').optional().isArray()
  ]),
  async (req, res) => {
    try {
      const { volailleId, features } = req.body;
      
      // Vérifier les permissions
      const hasAccess = await checkVolailleAccess(req.user, volailleId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette volaille'
        });
      }

      const prediction = await aiPredictionService.predictHealth(volailleId, features);

      res.json({
        success: true,
        data: prediction
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la prédiction de santé',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/production/forecast:
 *   post:
 *     summary: Prédire la production future
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               volailleId:
 *                 type: string
 *                 format: uuid
 *               days:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 90
 *                 default: 7
 *               algorithm:
 *                 type: string
 *                 enum: [linear_regression, exponential_smoothing, arima, seasonal_decomposition, ensemble]
 *                 default: ensemble
 *     responses:
 *       200:
 *         description: Prévision de production avec métadonnées
 */
router.post('/production/forecast',
  auth,
  validate([
    body('volailleId').isUUID(),
    body('days').optional().isInt({ min: 1, max: 90 }),
    body('algorithm').optional().isIn(['linear_regression', 'exponential_smoothing', 'arima', 'seasonal_decomposition', 'ensemble'])
  ]),
  async (req, res) => {
    try {
      const { volailleId, days = 7, algorithm = 'ensemble' } = req.body;
      
      const hasAccess = await checkVolailleAccess(req.user, volailleId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette volaille'
        });
      }

      const forecast = await productionForecastService.forecastProduction(volailleId, days, algorithm);

      res.json({
        success: true,
        data: forecast
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la prévision de production',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/mortality/predict:
 *   post:
 *     summary: Prédire le risque de mortalité
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               volailleId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Prédiction du risque de mortalité avec mesures préventives
 */
router.post('/mortality/predict',
  auth,
  validate([
    body('volailleId').isUUID()
  ]),
  async (req, res) => {
    try {
      const { volailleId } = req.body;
      
      const hasAccess = await checkVolailleAccess(req.user, volailleId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette volaille'
        });
      }

      const prediction = await aiPredictionService.predictMortalityRisk(volailleId);

      res.json({
        success: true,
        data: prediction
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la prédiction de mortalité',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/feed/optimize:
 *   post:
 *     summary: Optimiser l'alimentation du troupeau
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               volailleId:
 *                 type: string
 *                 format: uuid
 *               objectives:
 *                 type: object
 *                 properties:
 *                   maxCost:
 *                     type: number
 *                   targetProduction:
 *                     type: number
 *                   prioritizeHealth:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Recommandations d'alimentation optimisées
 */
router.post('/feed/optimize',
  auth,
  validate([
    body('volailleId').isUUID(),
    body('objectives').optional().isObject()
  ]),
  async (req, res) => {
    try {
      const { volailleId, objectives = {} } = req.body;
      
      const hasAccess = await checkVolailleAccess(req.user, volailleId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette volaille'
        });
      }

      const optimization = await aiPredictionService.optimizeFeed(volailleId, objectives);

      res.json({
        success: true,
        data: optimization
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'optimisation de l\'alimentation',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/alerts/analyze:
 *   post:
 *     summary: Analyser et détecter les anomalies
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               volailleId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Analyse des anomalies avec alertes générées
 */
router.post('/alerts/analyze',
  auth,
  validate([
    body('volailleId').isUUID()
  ]),
  async (req, res) => {
    try {
      const { volailleId } = req.body;
      
      const hasAccess = await checkVolailleAccess(req.user, volailleId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette volaille'
        });
      }

      const analysis = await intelligentAlertService.analyzeAndAlert(volailleId);

      res.json({
        success: true,
        data: analysis
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des anomalies',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/predictions/history:
 *   get:
 *     summary: Obtenir l'historique des prédictions
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: volailleId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [health, production_forecast, mortality_risk, feed_optimization]
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Historique des prédictions
 */
router.get('/predictions/history',
  auth,
  validate([
    query('volailleId').optional().isUUID(),
    query('type').optional().isIn(['health', 'production_forecast', 'mortality_risk', 'feed_optimization']),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ]),
  async (req, res) => {
    try {
      const { volailleId, type, limit = 50 } = req.query;
      
      let query = `
        SELECT p.*, v.nom as volaille_nom
        FROM ai_predictions p
        JOIN volailles v ON p.volaille_id = v.id
        WHERE 1=1
      `;
      const params = [];
      let paramIndex = 1;

      // Filtrer par volaille si spécifié
      if (volailleId) {
        const hasAccess = await checkVolailleAccess(req.user, volailleId);
        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'Accès non autorisé à cette volaille'
          });
        }
        
        query += ` AND p.volaille_id = $${paramIndex}`;
        params.push(volailleId);
        paramIndex++;
      } else {
        // Filtrer par utilisateur
        if (req.user.role === 'eleveur') {
          query += ` AND v.eleveur_id = $${paramIndex}`;
          params.push(req.user.id);
          paramIndex++;
        }
      }

      // Filtrer par type si spécifié
      if (type) {
        query += ` AND p.type = $${paramIndex}`;
        params.push(type);
        paramIndex++;
      }

      query += ` ORDER BY p.created_at DESC LIMIT $${paramIndex}`;
      params.push(limit);

      const { Pool } = require('pg');
      const pool = new Pool();
      const result = await pool.query(query, params);

      res.json({
        success: true,
        data: result.rows
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/insights/dashboard:
 *   get:
 *     summary: Obtenir les insights IA pour le dashboard
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: volailleId
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Insights et recommandations IA
 */
router.get('/insights/dashboard',
  auth,
  validate([
    query('volailleId').optional().isUUID()
  ]),
  async (req, res) => {
    try {
      const { volailleId } = req.query;
      
      if (volailleId) {
        const hasAccess = await checkVolailleAccess(req.user, volailleId);
        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'Accès non autorisé à cette volaille'
          });
        }
      }

      const insights = await generateDashboardInsights(req.user, volailleId);

      res.json({
        success: true,
        data: insights
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération des insights',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/models/retrain:
 *   post:
 *     summary: Relancer l'entraînement des modèles IA (admin seulement)
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Entraînement des modèles démarré
 */
router.post('/models/retrain',
  auth,
  async (req, res) => {
    try {
      // Vérifier les permissions admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé - admin requis'
        });
      }

      // Démarrer l'entraînement en arrière-plan
      aiPredictionService.trainModels()
        .then(() => {
          console.log('Entraînement des modèles IA terminé');
        })
        .catch(error => {
          console.error('Erreur lors de l\'entraînement:', error);
        });

      res.json({
        success: true,
        message: 'Entraînement des modèles démarré',
        data: {
          status: 'training_started',
          estimatedDuration: '30-60 minutes'
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors du démarrage de l\'entraînement',
        error: error.message
      });
    }
  }
);

/**
 * Fonctions utilitaires
 */

async function checkVolailleAccess(user, volailleId) {
  const { Pool } = require('pg');
  const pool = new Pool();
  
  if (user.role === 'admin') {
    return true;
  }
  
  if (user.role === 'eleveur') {
    const result = await pool.query(
      'SELECT id FROM volailles WHERE id = $1 AND eleveur_id = $2',
      [volailleId, user.id]
    );
    return result.rows.length > 0;
  }
  
  if (user.role === 'veterinaire') {
    // Les vétérinaires peuvent accéder aux volailles de leurs consultations
    const result = await pool.query(`
      SELECT DISTINCT v.id 
      FROM volailles v
      JOIN consultations c ON v.id = c.volaille_id
      WHERE v.id = $1 AND c.veterinaire_id = $2
    `, [volailleId, user.id]);
    return result.rows.length > 0;
  }
  
  return false;
}

async function generateDashboardInsights(user, volailleId) {
  const insights = {
    summary: {},
    recommendations: [],
    alerts: [],
    trends: {},
    predictions: {}
  };

  try {
    // Obtenir les volailles accessibles
    const volailles = volailleId ? [{ id: volailleId }] : await getUserVolailles(user);
    
    for (const volaille of volailles) {
      // Prédiction de santé
      try {
        const healthPrediction = await aiPredictionService.predictHealth(volaille.id);
        insights.predictions[volaille.id] = {
          health: healthPrediction
        };
        
        // Ajouter des recommandations basées sur la santé
        if (healthPrediction.status === 'mauvais') {
          insights.recommendations.push({
            type: 'health',
            priority: 'high',
            volailleId: volaille.id,
            message: 'Consultation vétérinaire recommandée',
            confidence: healthPrediction.confidence
          });
        }
      } catch (error) {
        console.warn(`Erreur prédiction santé pour ${volaille.id}:`, error.message);
      }

      // Prévision de production
      try {
        const productionForecast = await productionForecastService.forecastProduction(volaille.id, 7);
        insights.predictions[volaille.id] = {
          ...insights.predictions[volaille.id],
          production: productionForecast
        };
        
        // Analyser les tendances
        const trend = productionForecast.metadata?.factors?.trend || 'stable';
        if (trend === 'decreasing') {
          insights.recommendations.push({
            type: 'production',
            priority: 'medium',
            volailleId: volaille.id,
            message: 'Tendance de production en baisse détectée'
          });
        }
      } catch (error) {
        console.warn(`Erreur prévision production pour ${volaille.id}:`, error.message);
      }
    }

    // Calculer le résumé global
    insights.summary = {
      totalVolailles: volailles.length,
      healthyVolailles: Object.values(insights.predictions).filter(p => 
        p.health?.status === 'excellent' || p.health?.status === 'bon'
      ).length,
      alertsCount: insights.recommendations.filter(r => r.priority === 'high').length,
      avgProductionTrend: 'stable' // Calculer la tendance moyenne
    };

  } catch (error) {
    console.error('Erreur lors de la génération des insights:', error);
  }

  return insights;
}

async function getUserVolailles(user) {
  const { Pool } = require('pg');
  const pool = new Pool();
  
  let query = 'SELECT id FROM volailles WHERE 1=1';
  const params = [];
  
  if (user.role === 'eleveur') {
    query += ' AND eleveur_id = $1';
    params.push(user.id);
  } else if (user.role === 'veterinaire') {
    query = `
      SELECT DISTINCT v.id 
      FROM volailles v
      JOIN consultations c ON v.id = c.volaille_id
      WHERE c.veterinaire_id = $1
    `;
    params.push(user.id);
  }
  
  query += ' LIMIT 10'; // Limiter pour les performances
  
  const result = await pool.query(query, params);
  return result.rows;
}

module.exports = router;
