# Documentation du Tableau de Bord Marchand - Poultray DZ

Ce document décrit l'implémentation du tableau de bord pour le rôle Marchand dans l'application Poultray DZ. Il couvre la structure de la base de données, les endpoints API, les composants frontend et l'intégration des recommandations IA.

## Table des matières

1. [Structure de la base de données](#structure-de-la-base-de-données)
2. [API Backend](#api-backend)
3. [Interface utilisateur](#interface-utilisateur)
4. [Intégration IA](#intégration-ia)
5. [Guide d'installation](#guide-dinstallation)

## Structure de la base de données

Le tableau de bord Marchand s'appuie sur plusieurs nouvelles tables dans la base de données PostgreSQL :

### Table `products`

Stocke les informations sur les produits du marchand.

```sql
CREATE TABLE IF NOT EXISTS products (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  price DECIMAL(10,2) NOT NULL,
  stock_quantity INTEGER NOT NULL DEFAULT 0,
  stock_alert_threshold INTEGER DEFAULT 10,
  unit VARCHAR(20) DEFAULT 'unité',
  image_url VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Table `clients`

Stocke les informations sur les clients du marchand.

```sql
CREATE TABLE IF NOT EXISTS clients (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  user_id INTEGER REFERENCES users(id) NULL,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Table `orders`

Stocke les informations sur les commandes passées auprès du marchand.

```sql
CREATE TABLE IF NOT EXISTS orders (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  client_id INTEGER REFERENCES clients(id),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  payment_status VARCHAR(20) DEFAULT 'unpaid',
  payment_method VARCHAR(50),
  shipping_address TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Table `order_items`

Stocke les détails des articles dans chaque commande.

```sql
CREATE TABLE IF NOT EXISTS order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id),
  product_id INTEGER REFERENCES products(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Table `product_views`

Stocke les données analytiques sur les vues des produits.

```sql
CREATE TABLE IF NOT EXISTS product_views (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id),
  user_id INTEGER REFERENCES users(id) NULL,
  ip_address VARCHAR(50),
  view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Backend

Les endpoints API suivants ont été implémentés pour le tableau de bord Marchand :

### Endpoints du tableau de bord

- `GET /api/marchand/dashboard/summary` - Récupère un résumé des statistiques du marchand
- `GET /api/marchand/dashboard/revenue` - Récupère les données de revenus pour une période spécifiée

### Endpoints de gestion des produits

- `GET /api/marchand/products` - Récupère tous les produits du marchand
- `POST /api/marchand/products` - Crée un nouveau produit
- `PATCH /api/marchand/products/:id/stock` - Met à jour le stock d'un produit

### Endpoints de gestion des commandes

- `GET /api/marchand/orders` - Récupère toutes les commandes du marchand (filtrable par statut)
- `POST /api/marchand/orders` - Crée une nouvelle commande
- `PATCH /api/marchand/orders/:id/status` - Met à jour le statut d'une commande

### Endpoints d'intelligence artificielle

- `GET /api/marchand/ai/recommendations` - Récupère des recommandations IA pour le marchand

## Interface utilisateur

L'interface utilisateur du tableau de bord Marchand est construite avec React et Material UI. Elle comprend les composants suivants :

### Composant principal

- `DashboardMarchand` - Composant principal qui intègre tous les autres composants

### Composants de statistiques et graphiques

- `StatCard` - Affiche une statistique clé avec une icône
- `RevenueChart` - Graphique d'évolution des revenus
- `TopProductsChart` - Graphique des produits les plus vendus

### Composants de gestion

- `OrdersTable` - Tableau des commandes avec actions
- `LowStockAlert` - Alertes pour les produits avec stock faible

### Composants d'intelligence artificielle

- `AIRecommendations` - Affiche les recommandations générées par l'IA

## Intégration IA

Le tableau de bord Marchand intègre des recommandations basées sur l'IA via OpenAI. Les fonctionnalités suivantes sont disponibles :

### Optimisation des prix

Analyse les données de vente et de stock pour suggérer des ajustements de prix optimaux pour maximiser les revenus.

### Prévision de la demande

Prédit la demande future pour les produits en analysant l'historique des ventes et les tendances saisonnières.

### Analyse de rentabilité

Identifie les produits non rentables ou à faible performance et suggère des actions pour améliorer la rentabilité.

## Guide d'installation

Pour intégrer le tableau de bord Marchand dans l'application existante, suivez ces étapes :

### 1. Mise à jour de la base de données

Exécutez le script de création des tables dans PostgreSQL :

```javascript
const Marchand = require("../models/marchand");

// Créer les tables nécessaires
Marchand.createTables()
  .then(() => console.log("Tables du marchand créées avec succès"))
  .catch((err) => console.error("Erreur lors de la création des tables:", err));
```

### 2. Intégration des routes backend

Ajoutez les routes du marchand dans le fichier principal de l'application :

```javascript
const express = require("express");
const app = express();
const marchandRoutes = require("./routes/marchandRoutes");

// Autres configurations...

// Routes du marchand
app.use("/api/marchand", marchandRoutes);

// Autres routes...
```

### 3. Intégration frontend

Importez le composant du tableau de bord dans votre application React :

```javascript
import DashboardMarchand from "./components/DashboardMarchand";

// Dans votre composant de routage
<Route path="/dashboard/marchand" element={<DashboardMarchand />} />;
```

### 4. Configuration de l'API OpenAI

Assurez-vous d'avoir configuré la clé API OpenAI dans votre fichier .env :

```
OPENAI_API_KEY=votre_clé_api_openai
```

## Prochaines étapes

- Implémenter la gestion des commandes en temps réel
- Optimiser les performances des requêtes SQL
- Ajouter des fonctionnalités d'export de données (CSV, PDF)
- Intégrer des notifications push pour les nouvelles commandes
- Développer un système de gestion des retours et remboursements

---

_Documentation créée le 2023-12-20_
