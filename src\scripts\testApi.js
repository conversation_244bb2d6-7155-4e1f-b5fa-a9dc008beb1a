const axios = require('axios');

async function testApi() {
  try {
    console.log('Test de l\'API...');
    
    // Test de la route de base
    console.log('Test de la route de base...');
    const baseResponse = await axios.get('http://localhost:3003/');
    console.log('Réponse de la route de base:', baseResponse.data);
    
    // Test de la route de connexion
    console.log('Test de la route de connexion...');
    const loginResponse = await axios.post('http://localhost:3003/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('Réponse de la route de connexion:', {
      status: loginResponse.status,
      hasToken: !!loginResponse.data.token,
      hasUser: !!loginResponse.data.user,
      userRole: loginResponse.data.user?.role
    });
    
    // Test de la route des éleveurs
    console.log('Test de la route des éleveurs...');
    const eleveursResponse = await axios.get('http://localhost:3003/api/eleveurs');
    console.log('Réponse de la route des éleveurs:', {
      status: eleveursResponse.status,
      count: eleveursResponse.data.length
    });
    
    // Test de la route des volailles
    console.log('Test de la route des volailles...');
    const volaillesResponse = await axios.get('http://localhost:3003/api/volailles');
    console.log('Réponse de la route des volailles:', {
      status: volaillesResponse.status,
      count: volaillesResponse.data.length
    });
    
    console.log('Tests terminés avec succès');
  } catch (error) {
    console.error('Erreur lors des tests:', error.message);
    if (error.response) {
      console.error('Détails de l\'erreur:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }
  }
}

testApi();
