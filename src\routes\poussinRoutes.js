const express = require('express');
const router = express.Router();
const { body, validationResult, param, query } = require('express-validator');
const { auth } = require('../middleware/auth');
const { hasRole } = require('../middleware/roleCheck');
const Poussin = require('../models/poussin');
const Eleveur = require('../models/eleveur');

/**
 * Routes pour la gestion des poussins
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 */

// Middleware de validation pour la création/modification d'un poussin
const validatePoussin = [
  body('lot_numero')
    .notEmpty()
    .withMessage('Le numéro de lot est requis')
    .isLength({ max: 50 })
    .withMessage('Le numéro de lot ne peut pas dépasser 50 caractères'),

  body('race')
    .notEmpty()
    .withMessage('La race est requise')
    .isLength({ max: 50 })
    .withMessage('La race ne peut pas dépasser 50 caractères'),

  body('quantite_initiale')
    .isInt({ min: 1 })
    .withMessage('La quantité initiale doit être un nombre entier positif'),

  body('date_eclosion')
    .isISO8601()
    .withMessage('La date d\'éclosion doit être une date valide'),

  body('type_elevage')
    .isIn(['chair', 'pondeuse', 'reproducteur', 'mixte'])
    .withMessage('Le type d\'élevage doit être: chair, pondeuse, reproducteur ou mixte'),

  body('poids_moyen')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le poids moyen doit être un nombre positif'),

  body('prix_achat_unitaire')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le prix d\'achat unitaire doit être un nombre positif')
];

// GET /api/poussins - Récupérer tous les poussins d'un éleveur
router.get('/',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    query('statut')
      .optional()
      .isIn(['actif', 'vendu', 'transfere', 'abattu', 'archive'])
      .withMessage('Statut invalide'),
    query('type_elevage')
      .optional()
      .isIn(['chair', 'pondeuse', 'reproducteur', 'mixte'])
      .withMessage('Type d\'élevage invalide'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('La page doit être un nombre entier positif'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('La limite doit être entre 1 et 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const { statut, type_elevage, page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      // Construire les conditions de recherche
      const whereConditions = {
        eleveur_id: req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id
      };

      if (statut) whereConditions.statut = statut;
      if (type_elevage) whereConditions.type_elevage = type_elevage;

      const { count, rows: poussins } = await Poussin.findAndCountAll({
        where: whereConditions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['date_arrivee', 'DESC']],
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          }
        ]
      });

      res.json({
        success: true,
        data: {
          poussins,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des poussins:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des poussins'
      });
    }
  }
);

// GET /api/poussins/actifs - Récupérer les poussins actifs
router.get('/actifs',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      const poussins = await Poussin.findActifs(eleveurId);

      res.json({
        success: true,
        data: poussins
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des poussins actifs:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des poussins actifs'
      });
    }
  }
);

// GET /api/poussins/statistiques - Récupérer les statistiques des poussins
router.get('/statistiques',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      const statistiques = await Poussin.getStatistiques(eleveurId);

      res.json({
        success: true,
        data: statistiques
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des statistiques'
      });
    }
  }
);

// GET /api/poussins/:id - Récupérer un poussin spécifique
router.get('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const poussin = await Poussin.findOne({
        where: whereConditions,
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          }
        ]
      });

      if (!poussin) {
        return res.status(404).json({
          success: false,
          message: 'Poussin non trouvé'
        });
      }

      // Calculer les performances
      const performances = poussin.calculerPerformances();

      res.json({
        success: true,
        data: {
          ...poussin.toJSON(),
          performances
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du poussin:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération du poussin'
      });
    }
  }
);

// POST /api/poussins - Créer un nouveau lot de poussins
router.post('/',
  auth,
  hasRole(['eleveur', 'admin']),
  validatePoussin,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const eleveurId = req.user.role === 'admin' ? req.body.eleveur_id || req.user.id : req.user.id;

      // Vérifier que l'éleveur existe
      const eleveur = await Eleveur.findByPk(eleveurId);
      if (!eleveur) {
        return res.status(404).json({
          success: false,
          message: 'Éleveur non trouvé'
        });
      }

      // Vérifier l'unicité du numéro de lot pour cet éleveur
      const lotExistant = await Poussin.findOne({
        where: {
          eleveur_id: eleveurId,
          lot_numero: req.body.lot_numero
        }
      });

      if (lotExistant) {
        return res.status(400).json({
          success: false,
          message: 'Un lot avec ce numéro existe déjà pour cet éleveur'
        });
      }

      const poussinData = {
        ...req.body,
        eleveur_id: eleveurId,
        quantite_actuelle: req.body.quantite_initiale
      };

      const poussin = await Poussin.create(poussinData);

      res.status(201).json({
        success: true,
        message: 'Lot de poussins créé avec succès',
        data: poussin
      });
    } catch (error) {
      console.error('Erreur lors de la création du lot de poussins:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la création du lot de poussins'
      });
    }
  }
);

// PUT /api/poussins/:id - Mettre à jour un lot de poussins
router.put('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    ...validatePoussin
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const poussin = await Poussin.findOne({ where: whereConditions });

      if (!poussin) {
        return res.status(404).json({
          success: false,
          message: 'Poussin non trouvé'
        });
      }

      // Vérifier l'unicité du numéro de lot si modifié
      if (req.body.lot_numero && req.body.lot_numero !== poussin.lot_numero) {
        const lotExistant = await Poussin.findOne({
          where: {
            eleveur_id: poussin.eleveur_id,
            lot_numero: req.body.lot_numero,
            id: { [require('sequelize').Op.ne]: poussin.id }
          }
        });

        if (lotExistant) {
          return res.status(400).json({
            success: false,
            message: 'Un lot avec ce numéro existe déjà pour cet éleveur'
          });
        }
      }

      await poussin.update(req.body);

      res.json({
        success: true,
        message: 'Lot de poussins mis à jour avec succès',
        data: poussin
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du lot de poussins:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la mise à jour du lot de poussins'
      });
    }
  }
);

// POST /api/poussins/:id/vaccination - Ajouter une vaccination
router.post('/:id/vaccination',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('nom_vaccin')
      .notEmpty()
      .withMessage('Le nom du vaccin est requis'),
    body('date_vaccination')
      .isISO8601()
      .withMessage('La date de vaccination doit être une date valide'),
    body('veterinaire')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Le nom du vétérinaire ne peut pas dépasser 100 caractères')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      }

      const poussin = await Poussin.findOne({ where: whereConditions });

      if (!poussin) {
        return res.status(404).json({
          success: false,
          message: 'Poussin non trouvé'
        });
      }

      await poussin.ajouterVaccination(req.body);

      res.json({
        success: true,
        message: 'Vaccination ajoutée avec succès',
        data: poussin
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la vaccination:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'ajout de la vaccination'
      });
    }
  }
);

// POST /api/poussins/:id/traitement - Ajouter un traitement
router.post('/:id/traitement',
  auth,
  hasRole(['eleveur', 'veterinaire', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('nom_traitement')
      .notEmpty()
      .withMessage('Le nom du traitement est requis'),
    body('date_debut')
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    body('duree_jours')
      .optional()
      .isInt({ min: 1 })
      .withMessage('La durée doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role === 'eleveur') {
        whereConditions.eleveur_id = req.user.id;
      }

      const poussin = await Poussin.findOne({ where: whereConditions });

      if (!poussin) {
        return res.status(404).json({
          success: false,
          message: 'Poussin non trouvé'
        });
      }

      await poussin.ajouterTraitement(req.body);

      res.json({
        success: true,
        message: 'Traitement ajouté avec succès',
        data: poussin
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout du traitement:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'ajout du traitement'
      });
    }
  }
);

// DELETE /api/poussins/:id - Supprimer un lot de poussins
router.delete('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const poussin = await Poussin.findOne({ where: whereConditions });

      if (!poussin) {
        return res.status(404).json({
          success: false,
          message: 'Poussin non trouvé'
        });
      }

      await poussin.destroy();

      res.json({
        success: true,
        message: 'Lot de poussins supprimé avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la suppression du lot de poussins:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la suppression du lot de poussins'
      });
    }
  }
);

module.exports = router;
