import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { PoussinManagement } from '../index';
import testDbService from '../../../../services/testDbService';

// Test data
const testLots = [
  {
    eleveur_id: 1,
    lot_numero: 'TEST_LOT001',
    race: 'Leghorn',
    souche: 'Blanche',
    quantite_initiale: 100,
    quantite_actuelle: 95,
    date_eclosion: new Date('2024-01-01'),
    date_arrivee: new Date('2024-01-05'),
    poids_moyen: 42.5,
    poids_objectif: 45,
    gain_quotidien_moyen: 2.1,
    taux_mortalite: 5,
    mortalite_cumulative: 5,
    statut: 'actif',
    type_elevage: 'chair',
    batiment: 'Bâtiment A'
  },
  {
    eleveur_id: 1,
    lot_numero: 'TEST_LOT002',
    race: 'Rhode Island',
    souche: 'Rouge',
    quantite_initiale: 150,
    quantite_actuelle: 145,
    date_eclosion: new Date('2024-01-05'),
    date_arrivee: new Date('2024-01-10'),
    poids_moyen: 38.2,
    poids_objectif: 50,
    gain_quotidien_moyen: 1.9,
    taux_mortalite: 3.3,
    mortalite_cumulative: 5,
    statut: 'actif',
    type_elevage: 'chair',
    batiment: 'Bâtiment B'
  }
];

describe('PoussinManagement Component', () => {
  // Insérer les données de test avant tous les tests
  beforeAll(async () => {
    // Nettoyer les données de test existantes
    await testDbService.cleanTestData();
    
    // Insérer les données de test
    for (const lot of testLots) {
      await testDbService.insertTestPoussins(lot);
    }
  });
  
  // Nettoyer les données de test après tous les tests
  afterAll(async () => {
    await testDbService.cleanTestData();
  });
  
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };
  
  test('renders loading state initially', () => {
    renderWithI18n(<PoussinManagement />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  test('renders lots table after loading', async () => {
    renderWithI18n(<PoussinManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Vérifier que les données de test sont affichées
    expect(screen.getByText('TEST_LOT001')).toBeInTheDocument();
    expect(screen.getByText('Leghorn')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    
    expect(screen.getByText('TEST_LOT002')).toBeInTheDocument();
    expect(screen.getByText('Rhode Island')).toBeInTheDocument();
    expect(screen.getByText('150')).toBeInTheDocument();
  });
  
  test('opens add dialog when clicking add button', async () => {
    renderWithI18n(<PoussinManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue d'ajout
    fireEvent.click(screen.getByText('poussin.addLot'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('poussin.lotNumber')).toBeInTheDocument();
  });
  
  test('handles lot creation successfully', async () => {
    renderWithI18n(<PoussinManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue d'ajout
    fireEvent.click(screen.getByText('poussin.addLot'));
    
    // Remplir le formulaire
    fireEvent.change(screen.getByLabelText('poussin.lotNumber'), {
      target: { value: 'TEST_LOT003' }
    });
    
    fireEvent.change(screen.getByLabelText('poussin.race'), {
      target: { value: 'Plymouth Rock' }
    });
    
    fireEvent.change(screen.getByLabelText('poussin.souche'), {
      target: { value: 'Barrée' }
    });
    
    fireEvent.change(screen.getByLabelText('poussin.quantity'), {
      target: { value: '120' }
    });
    
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    
    fireEvent.change(screen.getByLabelText('poussin.eclosionDate'), {
      target: { value: dateString }
    });
    
    fireEvent.change(screen.getByLabelText('poussin.targetWeight'), {
      target: { value: '48' }
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('common.add'));
    
    // Vérifier que le nouveau lot est ajouté (la vérification réelle se fait via la base de données)
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });
  
  // Note: Le test de suppression est commenté car il nécessiterait de modifier la base de données réelle
  // test('handles lot deletion', async () => {
  //   renderWithI18n(<PoussinManagement />);
  //   
  //   // Attendre que les données soient chargées
  //   await waitFor(() => {
  //     expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  //   
  //   // Simuler la confirmation de suppression
  //   window.confirm = jest.fn(() => true);
  //   
  //   // Cliquer sur le bouton de suppression du premier lot
  //   const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
  //   fireEvent.click(deleteButtons[0]);
  //   
  //   // Vérifier que le lot est supprimé (la vérification réelle se fait via la base de données)
  //   await waitFor(() => {
  //     expect(screen.queryByText('TEST_LOT001')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  // });
  
  // Note: Le test d'erreur est commenté car il nécessiterait de simuler une erreur de base de données
  // test('handles error state', async () => {
  //   // Simuler une erreur de base de données
  //   jest.spyOn(testDbService, 'getPoussins').mockRejectedValueOnce(new Error('Failed to fetch'));
  //   
  //   renderWithI18n(<PoussinManagement />);
  //   
  //   // Attendre que l'erreur soit affichée
  //   await waitFor(() => {
  //     expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  //   
  //   expect(screen.getByText('errors.fetchFailed')).toBeInTheDocument();
  //   
  //   // Restaurer le mock
  //   jest.restoreAllMocks();
  // });
});
