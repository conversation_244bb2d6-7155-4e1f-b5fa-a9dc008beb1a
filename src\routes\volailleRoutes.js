const express = require('express');
const router = express.Router();
const Volaille = require('../models/volaille');
const { auth, checkRole } = require('../middleware/auth');
const sequelize = require('../config/database');

// Créer une nouvelle volaille
router.post('/', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    const volaille = await Volaille.create(req.body);
    res.status(201).json(volaille);
  } catch (error) {
    console.error('Erreur lors de la création de la volaille:', error);
    res.status(400).json({
      message: 'Erreur lors de la création de la volaille',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Récupérer toutes les volailles
router.get('/', auth, async (req, res) => {
  try {
    console.log('Recherche des volailles avec l\'éleveur associé');
const volailles = await Volaille.findAll({
      include: [{ model: require('../models/eleveur'), as: 'Eleveur' }]
    });
    res.json(volailles);
  } catch (error) {
    console.error('Erreur lors de la récupération des volailles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des volailles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Récupérer une volaille par son ID
router.get('/:id', auth, async (req, res) => {
  try {
    const volaille = await Volaille.findByPk(req.params.id, {
      include: [{ model: require('../models/eleveur'), as: 'Eleveur' }]
    });
    if (volaille) {
      res.json(volaille);
    } else {
      res.status(404).json({ message: 'Volaille non trouvée' });
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des volailles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des volailles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Récupérer les volailles d'un éleveur
router.get('/eleveur/:eleveurId', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    console.log('Recherche des volailles avec l\'éleveur associé');
const volailles = await Volaille.findAll({
      where: { eleveur_id: req.params.eleveurId },
      include: [{ model: require('../models/eleveur'), as: 'Eleveur' }]
    });
    res.json(volailles);
  } catch (error) {
    console.error('Erreur lors de la récupération des volailles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des volailles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Mettre à jour une volaille
router.put('/:id', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    const [updated] = await Volaille.update(req.body, {
      where: { id: req.params.id }
    });
    if (updated) {
      const volaille = await Volaille.findByPk(req.params.id, {
        include: [{ model: require('../models/eleveur'), as: 'Eleveur' }]
      });
      if (volaille) {
        res.json(volaille);
      } else {
        res.status(404).json({ message: 'Volaille non trouvée' });
      }
    } else {
      res.status(404).json({ message: 'Volaille non trouvée' });
    }
  } catch (error) {
    console.error('Erreur lors de la création de la volaille:', error);
    res.status(400).json({
      message: 'Erreur lors de la création de la volaille',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Supprimer une volaille
router.delete('/:id', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    const success = await Volaille.destroy({
      where: { id: req.params.id }
    });
    if (success) {
      res.json({ message: 'Volaille supprimée avec succès' });
    } else {
      res.status(404).json({ message: 'Volaille non trouvée' });
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des volailles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des volailles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
