const jwt = require('jsonwebtoken');

/**
 * Middleware pour vérifier les rôles spécifiques des utilisateurs
 * Utilisé dans les routes pour contrôler l'accès basé sur les rôles
 */

// Middleware principal pour vérifier les rôles
const checkRoles = (allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Accès refusé. Utilisateur non authentifié.'
        });
      }

      // Admin a toujours accès
      if (req.user.role === 'admin') {
        return next();
      }

      // Vérifier si le rôle de l'utilisateur est dans la liste des rôles autorisés
      if (!allowedRoles.includes(req.user.role)) {
        return res.status(403).json({
          status: 'error',
          message: 'Accès refusé. Droits insuffisants.',
          userRole: req.user.role,
          requiredRoles: allowedRoles
        });
      }

      next();
    } catch (error) {
      console.error('Erreur dans le middleware checkRoles:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la vérification des droits d\'accès'
      });
    }
  };
};

// Middleware pour vérifier si l'utilisateur est un éleveur
const isEleveur = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    if (req.user.role !== 'eleveur' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé. Droits d\'éleveur requis.',
        userRole: req.user.role
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware isEleveur:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification des droits d\'éleveur',
      error: error.message
    });
  }
};

// Middleware pour vérifier si l'utilisateur est un vétérinaire
const isVeterinaire = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    if (req.user.role !== 'veterinaire' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé. Droits de vétérinaire requis.',
        userRole: req.user.role
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware isVeterinaire:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification des droits de vétérinaire',
      error: error.message
    });
  }
};

// Middleware pour vérifier si l'utilisateur est un marchand
const isMarchand = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    if (req.user.role !== 'marchand' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé. Droits de marchand requis.',
        userRole: req.user.role
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware isMarchand:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification des droits de marchand',
      error: error.message
    });
  }
};

// Middleware pour vérifier si l'utilisateur est un admin
const isAdmin = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    if (req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé. Droits d\'administrateur requis.',
        userRole: req.user.role
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware isAdmin:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification des droits d\'administrateur',
      error: error.message
    });
  }
};

// Middleware générique pour vérifier plusieurs rôles
const hasRole = (allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Accès refusé. Utilisateur non authentifié.'
        });
      }

      if (!allowedRoles.includes(req.user.role)) {
        return res.status(403).json({
          status: 'error',
          message: `Accès refusé. Rôles autorisés: ${allowedRoles.join(', ')}`,
          userRole: req.user.role,
          allowedRoles
        });
      }

      next();
    } catch (error) {
      console.error('Erreur dans le middleware hasRole:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la vérification des rôles',
        error: error.message
      });
    }
  };
};

// Middleware pour vérifier si l'utilisateur peut accéder aux données d'un éleveur spécifique
const canAccessEleveurData = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Accès refusé. Utilisateur non authentifié.'
      });
    }

    // Les admins peuvent accéder à toutes les données
    if (req.user.role === 'admin') {
      return next();
    }

    // Les éleveurs ne peuvent accéder qu'à leurs propres données
    if (req.user.role === 'eleveur') {
      const eleveurId = req.params.eleveurId || req.query.eleveur_id || req.body.eleveur_id;
      if (eleveurId && eleveurId !== req.user.id.toString()) {
        return res.status(403).json({
          status: 'error',
          message: 'Accès refusé. Vous ne pouvez accéder qu\'à vos propres données.',
          userRole: req.user.role
        });
      }
    }

    next();
  } catch (error) {
    console.error('Erreur dans le middleware canAccessEleveurData:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification des droits d\'accès aux données',
      error: error.message
    });
  }
};

module.exports = {
  isEleveur,
  isMarchand,
  isVeterinaire,
  isAdmin,
  hasRole,
  canAccessEleveurData,
  checkRoles
};
