/**
 * Service d'alertes intelligentes avec détection d'anomalies
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
const notificationService = require('./notificationService');
const aiPredictionService = require('./aiPredictionService');

class IntelligentAlertService {
  constructor() {
    this.pool = new Pool();
    this.alertRules = new Map();
    this.anomalyDetectors = new Map();
    this.alertHistory = new Map();
    this.suppressionRules = new Map();
    this.escalationRules = new Map();
    
    this.initializeAlertRules();
    this.initializeAnomalyDetectors();
  }

  /**
   * Initialiser les règles d'alerte
   */
  initializeAlertRules() {
    // Règles de production
    this.alertRules.set('production_drop', {
      type: 'production',
      severity: 'high',
      threshold: 0.3, // Baisse de 30%
      timeWindow: '7d',
      condition: (current, baseline) => (baseline - current) / baseline > 0.3,
      message: 'Baisse significative de production détectée',
      actions: ['notify_veterinaire', 'schedule_inspection']
    });

    this.alertRules.set('mortality_spike', {
      type: 'health',
      severity: 'critical',
      threshold: 0.05, // 5% de mortalité
      timeWindow: '24h',
      condition: (mortalityRate) => mortalityRate > 0.05,
      message: 'Pic de mortalité anormal détecté',
      actions: ['immediate_veterinary_call', 'quarantine_alert']
    });

    this.alertRules.set('feed_consumption_anomaly', {
      type: 'nutrition',
      severity: 'medium',
      threshold: 0.25, // Variation de 25%
      timeWindow: '3d',
      condition: (current, expected) => Math.abs(current - expected) / expected > 0.25,
      message: 'Consommation d\'aliment anormale',
      actions: ['check_feed_quality', 'monitor_health']
    });

    this.alertRules.set('temperature_extreme', {
      type: 'environment',
      severity: 'high',
      threshold: { min: 15, max: 30 },
      timeWindow: '1h',
      condition: (temp, thresholds) => temp < thresholds.min || temp > thresholds.max,
      message: 'Température extrême dans le poulailler',
      actions: ['adjust_ventilation', 'emergency_cooling']
    });

    this.alertRules.set('egg_quality_decline', {
      type: 'quality',
      severity: 'medium',
      threshold: 0.15, // 15% d'œufs défectueux
      timeWindow: '7d',
      condition: (defectRate) => defectRate > 0.15,
      message: 'Dégradation de la qualité des œufs',
      actions: ['nutrition_review', 'stress_assessment']
    });

    this.alertRules.set('growth_rate_slow', {
      type: 'growth',
      severity: 'medium',
      threshold: 0.8, // 80% du taux attendu
      timeWindow: '14d',
      condition: (actualRate, expectedRate) => actualRate < expectedRate * 0.8,
      message: 'Croissance plus lente que prévu',
      actions: ['nutrition_optimization', 'health_check']
    });
  }

  /**
   * Initialiser les détecteurs d'anomalies
   */
  initializeAnomalyDetectors() {
    // Détecteur basé sur l'écart-type
    this.anomalyDetectors.set('statistical', {
      name: 'Statistical Anomaly Detection',
      detect: (values, threshold = 2) => {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        const stdDev = Math.sqrt(variance);
        
        return values.map((value, index) => ({
          index: index,
          value: value,
          isAnomaly: Math.abs(value - mean) > threshold * stdDev,
          score: Math.abs(value - mean) / stdDev,
          type: 'statistical'
        }));
      }
    });

    // Détecteur basé sur les quartiles (IQR)
    this.anomalyDetectors.set('iqr', {
      name: 'Interquartile Range Anomaly Detection',
      detect: (values, multiplier = 1.5) => {
        const sorted = [...values].sort((a, b) => a - b);
        const q1 = sorted[Math.floor(sorted.length * 0.25)];
        const q3 = sorted[Math.floor(sorted.length * 0.75)];
        const iqr = q3 - q1;
        const lowerBound = q1 - multiplier * iqr;
        const upperBound = q3 + multiplier * iqr;
        
        return values.map((value, index) => ({
          index: index,
          value: value,
          isAnomaly: value < lowerBound || value > upperBound,
          score: Math.max(
            (lowerBound - value) / iqr,
            (value - upperBound) / iqr,
            0
          ),
          type: 'iqr'
        }));
      }
    });

    // Détecteur de tendances
    this.anomalyDetectors.set('trend', {
      name: 'Trend Anomaly Detection',
      detect: (values, windowSize = 7) => {
        const anomalies = [];
        
        for (let i = windowSize; i < values.length; i++) {
          const recentWindow = values.slice(i - windowSize, i);
          const previousWindow = values.slice(i - windowSize * 2, i - windowSize);
          
          if (previousWindow.length === windowSize) {
            const recentAvg = recentWindow.reduce((sum, val) => sum + val, 0) / windowSize;
            const previousAvg = previousWindow.reduce((sum, val) => sum + val, 0) / windowSize;
            
            const changeRate = previousAvg > 0 ? (recentAvg - previousAvg) / previousAvg : 0;
            
            anomalies.push({
              index: i,
              value: values[i],
              isAnomaly: Math.abs(changeRate) > 0.3, // 30% de changement
              score: Math.abs(changeRate),
              type: 'trend',
              changeRate: changeRate
            });
          }
        }
        
        return anomalies;
      }
    });

    // Détecteur saisonnier
    this.anomalyDetectors.set('seasonal', {
      name: 'Seasonal Anomaly Detection',
      detect: (values, period = 7) => {
        const seasonalAverages = new Array(period).fill(0);
        const seasonalCounts = new Array(period).fill(0);
        
        // Calculer les moyennes saisonnières
        values.forEach((value, index) => {
          const seasonIndex = index % period;
          seasonalAverages[seasonIndex] += value;
          seasonalCounts[seasonIndex]++;
        });
        
        seasonalAverages.forEach((sum, index) => {
          seasonalAverages[index] = seasonalCounts[index] > 0 ? sum / seasonalCounts[index] : 0;
        });
        
        return values.map((value, index) => {
          const seasonIndex = index % period;
          const expected = seasonalAverages[seasonIndex];
          const deviation = expected > 0 ? Math.abs(value - expected) / expected : 0;
          
          return {
            index: index,
            value: value,
            expected: expected,
            isAnomaly: deviation > 0.4, // 40% de déviation
            score: deviation,
            type: 'seasonal'
          };
        });
      }
    });
  }

  /**
   * Analyser et détecter les anomalies
   */
  async analyzeAndAlert(volailleId) {
    try {
      logger.info(`Analyse des anomalies pour la volaille ${volailleId}`);
      
      // Obtenir les données récentes
      const data = await this.getRecentData(volailleId);
      
      // Analyser chaque type de données
      const analyses = {
        production: await this.analyzeProduction(volailleId, data.production),
        health: await this.analyzeHealth(volailleId, data.health),
        environment: await this.analyzeEnvironment(volailleId, data.environment),
        nutrition: await this.analyzeNutrition(volailleId, data.nutrition)
      };

      // Détecter les anomalies
      const anomalies = await this.detectAnomalies(analyses);
      
      // Générer les alertes
      const alerts = await this.generateAlerts(volailleId, anomalies);
      
      // Traiter les alertes
      await this.processAlerts(alerts);
      
      return {
        volailleId: volailleId,
        analyses: analyses,
        anomalies: anomalies,
        alerts: alerts,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Erreur lors de l\'analyse des anomalies:', error);
      throw error;
    }
  }

  /**
   * Analyser la production
   */
  async analyzeProduction(volailleId, productionData) {
    if (!productionData || productionData.length === 0) {
      return { status: 'no_data', anomalies: [] };
    }

    const values = productionData.map(d => d.quantite_produite);
    
    // Détecter les anomalies avec différents algorithmes
    const statisticalAnomalies = this.anomalyDetectors.get('statistical').detect(values);
    const trendAnomalies = this.anomalyDetectors.get('trend').detect(values);
    const seasonalAnomalies = this.anomalyDetectors.get('seasonal').detect(values);

    // Calculer les métriques de base
    const currentProduction = values[values.length - 1];
    const averageProduction = values.reduce((sum, val) => sum + val, 0) / values.length;
    const productionTrend = this.calculateTrend(values);

    // Prédire la production attendue
    const expectedProduction = await this.predictExpectedProduction(volailleId);

    return {
      status: 'analyzed',
      metrics: {
        current: currentProduction,
        average: averageProduction,
        expected: expectedProduction,
        trend: productionTrend,
        deviation: expectedProduction > 0 ? 
          Math.abs(currentProduction - expectedProduction) / expectedProduction : 0
      },
      anomalies: {
        statistical: statisticalAnomalies.filter(a => a.isAnomaly),
        trend: trendAnomalies.filter(a => a.isAnomaly),
        seasonal: seasonalAnomalies.filter(a => a.isAnomaly)
      }
    };
  }

  /**
   * Analyser la santé
   */
  async analyzeHealth(volailleId, healthData) {
    // Obtenir les prédictions de santé IA
    const healthPrediction = await aiPredictionService.predictHealth(volailleId);
    
    // Analyser les consultations récentes
    const consultations = await this.getRecentConsultations(volailleId);
    
    // Calculer le taux de mortalité
    const mortalityRate = await this.calculateMortalityRate(volailleId);
    
    // Analyser les symptômes rapportés
    const symptoms = await this.analyzeSymptoms(volailleId);

    return {
      status: 'analyzed',
      prediction: healthPrediction,
      metrics: {
        mortalityRate: mortalityRate,
        consultationFrequency: consultations.length,
        urgentConsultations: consultations.filter(c => c.urgence).length,
        symptomSeverity: symptoms.averageSeverity
      },
      riskFactors: this.identifyHealthRiskFactors(healthPrediction, consultations, symptoms),
      anomalies: this.detectHealthAnomalies(healthPrediction, mortalityRate, consultations)
    };
  }

  /**
   * Analyser l'environnement
   */
  async analyzeEnvironment(volailleId, environmentData) {
    // Simuler des données environnementales
    const temperature = 20 + Math.random() * 15;
    const humidity = 50 + Math.random() * 30;
    const airQuality = Math.random();

    const anomalies = [];

    // Vérifier les seuils de température
    if (temperature < 15 || temperature > 30) {
      anomalies.push({
        type: 'temperature',
        value: temperature,
        severity: temperature < 10 || temperature > 35 ? 'critical' : 'high',
        message: `Température ${temperature < 15 ? 'trop basse' : 'trop élevée'}: ${temperature.toFixed(1)}°C`
      });
    }

    // Vérifier l'humidité
    if (humidity < 40 || humidity > 80) {
      anomalies.push({
        type: 'humidity',
        value: humidity,
        severity: 'medium',
        message: `Humidité ${humidity < 40 ? 'trop basse' : 'trop élevée'}: ${humidity.toFixed(1)}%`
      });
    }

    return {
      status: 'analyzed',
      metrics: {
        temperature: temperature,
        humidity: humidity,
        airQuality: airQuality
      },
      anomalies: anomalies
    };
  }

  /**
   * Analyser la nutrition
   */
  async analyzeNutrition(volailleId, nutritionData) {
    // Obtenir les données de consommation d'aliment
    const feedConsumption = await this.getFeedConsumption(volailleId);
    
    // Calculer la consommation attendue
    const expectedConsumption = await this.calculateExpectedFeedConsumption(volailleId);
    
    const deviation = expectedConsumption > 0 ? 
      Math.abs(feedConsumption - expectedConsumption) / expectedConsumption : 0;

    const anomalies = [];
    
    if (deviation > 0.25) {
      anomalies.push({
        type: 'feed_consumption',
        value: feedConsumption,
        expected: expectedConsumption,
        deviation: deviation,
        severity: deviation > 0.5 ? 'high' : 'medium',
        message: `Consommation d'aliment ${feedConsumption < expectedConsumption ? 'insuffisante' : 'excessive'}`
      });
    }

    return {
      status: 'analyzed',
      metrics: {
        currentConsumption: feedConsumption,
        expectedConsumption: expectedConsumption,
        deviation: deviation
      },
      anomalies: anomalies
    };
  }

  /**
   * Détecter les anomalies globales
   */
  async detectAnomalies(analyses) {
    const allAnomalies = [];

    // Collecter toutes les anomalies
    Object.entries(analyses).forEach(([category, analysis]) => {
      if (analysis.anomalies) {
        if (Array.isArray(analysis.anomalies)) {
          analysis.anomalies.forEach(anomaly => {
            allAnomalies.push({
              ...anomaly,
              category: category,
              timestamp: new Date().toISOString()
            });
          });
        } else {
          Object.entries(analysis.anomalies).forEach(([type, anomalies]) => {
            anomalies.forEach(anomaly => {
              allAnomalies.push({
                ...anomaly,
                category: category,
                detectionType: type,
                timestamp: new Date().toISOString()
              });
            });
          });
        }
      }
    });

    // Classer par sévérité
    allAnomalies.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);
    });

    return allAnomalies;
  }

  /**
   * Générer les alertes
   */
  async generateAlerts(volailleId, anomalies) {
    const alerts = [];

    for (const anomaly of anomalies) {
      // Vérifier les règles de suppression
      if (await this.shouldSuppressAlert(volailleId, anomaly)) {
        continue;
      }

      // Créer l'alerte
      const alert = {
        id: this.generateAlertId(),
        volailleId: volailleId,
        type: anomaly.type || anomaly.category,
        severity: anomaly.severity || 'medium',
        message: anomaly.message || `Anomalie détectée: ${anomaly.type}`,
        data: anomaly,
        actions: this.getRecommendedActions(anomaly),
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      alerts.push(alert);
    }

    return alerts;
  }

  /**
   * Traiter les alertes
   */
  async processAlerts(alerts) {
    for (const alert of alerts) {
      try {
        // Sauvegarder l'alerte
        await this.saveAlert(alert);
        
        // Envoyer les notifications
        await this.sendAlertNotifications(alert);
        
        // Exécuter les actions automatiques
        await this.executeAutomaticActions(alert);
        
        // Planifier l'escalade si nécessaire
        await this.scheduleEscalation(alert);

      } catch (error) {
        logger.error(`Erreur lors du traitement de l'alerte ${alert.id}:`, error);
      }
    }
  }

  /**
   * Méthodes utilitaires
   */

  async getRecentData(volailleId) {
    const data = {};

    // Données de production
    const productionResult = await this.pool.query(`
      SELECT * FROM production 
      WHERE volaille_id = $1 
        AND date_production >= NOW() - INTERVAL '30 days'
      ORDER BY date_production DESC
    `, [volailleId]);
    data.production = productionResult.rows;

    // Données de santé (consultations)
    const healthResult = await this.pool.query(`
      SELECT * FROM consultations 
      WHERE volaille_id = $1 
        AND date_consultation >= NOW() - INTERVAL '30 days'
      ORDER BY date_consultation DESC
    `, [volailleId]);
    data.health = healthResult.rows;

    // Données environnementales (simulées)
    data.environment = [];

    // Données nutritionnelles (simulées)
    data.nutrition = [];

    return data;
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  async predictExpectedProduction(volailleId) {
    // Utiliser le service de prévision de production
    try {
      const forecast = await require('./productionForecastService').forecastProduction(volailleId, 1);
      return forecast.forecasts[0]?.predicted || 0;
    } catch (error) {
      logger.warn('Impossible de prédire la production attendue:', error.message);
      return 0;
    }
  }

  async getRecentConsultations(volailleId) {
    const result = await this.pool.query(`
      SELECT * FROM consultations 
      WHERE volaille_id = $1 
        AND date_consultation >= NOW() - INTERVAL '7 days'
      ORDER BY date_consultation DESC
    `, [volailleId]);
    
    return result.rows;
  }

  async calculateMortalityRate(volailleId) {
    const result = await this.pool.query(`
      SELECT 
        nombre_total,
        nombre_actuel,
        (nombre_total - nombre_actuel)::float / nombre_total as mortality_rate
      FROM volailles 
      WHERE id = $1
    `, [volailleId]);
    
    return result.rows[0]?.mortality_rate || 0;
  }

  async analyzeSymptoms(volailleId) {
    // Analyser les symptômes des consultations récentes
    const result = await this.pool.query(`
      SELECT symptomes, diagnostic, urgence
      FROM consultations 
      WHERE volaille_id = $1 
        AND date_consultation >= NOW() - INTERVAL '14 days'
    `, [volailleId]);
    
    const symptoms = result.rows;
    const averageSeverity = symptoms.length > 0 ? 
      symptoms.filter(s => s.urgence).length / symptoms.length : 0;
    
    return {
      count: symptoms.length,
      averageSeverity: averageSeverity,
      urgentSymptoms: symptoms.filter(s => s.urgence)
    };
  }

  identifyHealthRiskFactors(prediction, consultations, symptoms) {
    const riskFactors = [];
    
    if (prediction.status === 'mauvais') {
      riskFactors.push({
        factor: 'État de santé critique',
        severity: 'high',
        confidence: prediction.confidence
      });
    }
    
    if (consultations.length > 3) {
      riskFactors.push({
        factor: 'Consultations fréquentes',
        severity: 'medium',
        count: consultations.length
      });
    }
    
    return riskFactors;
  }

  detectHealthAnomalies(prediction, mortalityRate, consultations) {
    const anomalies = [];
    
    if (mortalityRate > 0.05) {
      anomalies.push({
        type: 'high_mortality',
        value: mortalityRate,
        severity: 'critical',
        message: `Taux de mortalité élevé: ${(mortalityRate * 100).toFixed(1)}%`
      });
    }
    
    return anomalies;
  }

  async getFeedConsumption(volailleId) {
    // Simuler la consommation d'aliment
    return 50 + Math.random() * 20;
  }

  async calculateExpectedFeedConsumption(volailleId) {
    // Calculer la consommation attendue basée sur le nombre d'animaux
    const result = await this.pool.query(`
      SELECT nombre_actuel, type_volaille 
      FROM volailles 
      WHERE id = $1
    `, [volailleId]);
    
    if (result.rows.length === 0) return 0;
    
    const { nombre_actuel, type_volaille } = result.rows[0];
    const consumptionPerAnimal = type_volaille === 'poule' ? 0.12 : 0.15; // kg par jour
    
    return nombre_actuel * consumptionPerAnimal;
  }

  async shouldSuppressAlert(volailleId, anomaly) {
    // Vérifier si une alerte similaire a été envoyée récemment
    const recentAlerts = await this.pool.query(`
      SELECT COUNT(*) as count
      FROM alerts 
      WHERE volaille_id = $1 
        AND type = $2 
        AND created_at >= NOW() - INTERVAL '1 hour'
    `, [volailleId, anomaly.type]);
    
    return parseInt(recentAlerts.rows[0].count) > 0;
  }

  getRecommendedActions(anomaly) {
    const actionMap = {
      production_drop: ['check_feed_quality', 'veterinary_consultation', 'stress_assessment'],
      high_mortality: ['immediate_isolation', 'veterinary_emergency', 'disinfection'],
      temperature: ['adjust_ventilation', 'heating_cooling_system'],
      feed_consumption: ['feed_quality_check', 'health_monitoring']
    };
    
    return actionMap[anomaly.type] || ['general_monitoring'];
  }

  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async saveAlert(alert) {
    await this.pool.query(`
      INSERT INTO alerts (id, volaille_id, type, severity, message, data, status, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
    `, [
      alert.id,
      alert.volailleId,
      alert.type,
      alert.severity,
      alert.message,
      JSON.stringify(alert.data),
      alert.status
    ]);
  }

  async sendAlertNotifications(alert) {
    // Envoyer les notifications selon la sévérité
    const recipients = await this.getAlertRecipients(alert);
    
    for (const recipient of recipients) {
      await notificationService.sendAlert(recipient, alert);
    }
  }

  async getAlertRecipients(alert) {
    // Déterminer les destinataires selon le type et la sévérité de l'alerte
    const result = await this.pool.query(`
      SELECT u.id, u.email, u.role
      FROM users u
      JOIN volailles v ON u.id = v.eleveur_id OR u.role IN ('veterinaire', 'admin')
      WHERE v.id = $1
    `, [alert.volailleId]);
    
    return result.rows;
  }

  async executeAutomaticActions(alert) {
    // Exécuter les actions automatiques selon le type d'alerte
    for (const action of alert.actions) {
      try {
        await this.executeAction(action, alert);
      } catch (error) {
        logger.error(`Erreur lors de l'exécution de l'action ${action}:`, error);
      }
    }
  }

  async executeAction(action, alert) {
    switch (action) {
      case 'schedule_inspection':
        await this.scheduleInspection(alert.volailleId);
        break;
      case 'adjust_ventilation':
        await this.adjustVentilation(alert.volailleId);
        break;
      // Ajouter d'autres actions selon les besoins
    }
  }

  async scheduleInspection(volailleId) {
    // Programmer une inspection automatique
    logger.info(`Inspection programmée pour la volaille ${volailleId}`);
  }

  async adjustVentilation(volailleId) {
    // Ajuster automatiquement la ventilation
    logger.info(`Ajustement de la ventilation pour la volaille ${volailleId}`);
  }

  async scheduleEscalation(alert) {
    if (alert.severity === 'critical') {
      // Programmer une escalade après 30 minutes si non résolue
      setTimeout(async () => {
        await this.escalateAlert(alert.id);
      }, 30 * 60 * 1000);
    }
  }

  async escalateAlert(alertId) {
    // Escalader l'alerte vers un niveau supérieur
    logger.info(`Escalade de l'alerte ${alertId}`);
  }

  /**
   * Démarrer l'analyse périodique
   */
  startPeriodicAnalysis() {
    // Analyser toutes les volailles toutes les heures
    setInterval(async () => {
      try {
        const volailles = await this.getAllVolailles();
        
        for (const volaille of volailles) {
          await this.analyzeAndAlert(volaille.id);
        }
      } catch (error) {
        logger.error('Erreur lors de l\'analyse périodique:', error);
      }
    }, 60 * 60 * 1000); // 1 heure
  }

  async getAllVolailles() {
    const result = await this.pool.query('SELECT id FROM volailles WHERE nombre_actuel > 0');
    return result.rows;
  }
}

module.exports = new IntelligentAlertService();
