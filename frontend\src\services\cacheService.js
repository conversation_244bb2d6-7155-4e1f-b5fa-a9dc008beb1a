/**
 * Service de mise en cache pour optimiser les performances
 * Gère la mise en cache des données API et des ressources
 */

// Types de cache
export const CACHE_TYPES = {
  MEMORY: 'memory',
  SESSION: 'session',
  LOCAL: 'local',
  INDEXED_DB: 'indexed_db'
};

// Stratégies de cache
export const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache_first',
  NETWORK_FIRST: 'network_first',
  CACHE_ONLY: 'cache_only',
  NETWORK_ONLY: 'network_only',
  STALE_WHILE_REVALIDATE: 'stale_while_revalidate'
};

/**
 * Classe pour gérer le cache en mémoire
 */
class MemoryCache {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessTimes = new Map();
  }

  set(key, value, ttl = 300000) { // TTL par défaut: 5 minutes
    // Nettoyer le cache si nécessaire
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    const expiresAt = Date.now() + ttl;
    this.cache.set(key, { value, expiresAt });
    this.accessTimes.set(key, Date.now());
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    // Vérifier l'expiration
    if (Date.now() > item.expiresAt) {
      this.delete(key);
      return null;
    }

    // Mettre à jour le temps d'accès
    this.accessTimes.set(key, Date.now());
    return item.value;
  }

  delete(key) {
    this.cache.delete(key);
    this.accessTimes.delete(key);
  }

  clear() {
    this.cache.clear();
    this.accessTimes.clear();
  }

  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (Date.now() > item.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  // Éviction LRU (Least Recently Used)
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

/**
 * Service principal de cache
 */
class CacheService {
  constructor() {
    this.memoryCache = new MemoryCache();
    this.requestCache = new Map(); // Cache pour les requêtes en cours
  }

  /**
   * Générer une clé de cache
   */
  generateKey(url, params = {}, method = 'GET') {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});

    return `${method}:${url}:${JSON.stringify(sortedParams)}`;
  }

  /**
   * Mettre en cache une valeur
   */
  set(key, value, options = {}) {
    const {
      type = CACHE_TYPES.MEMORY,
      ttl = 300000, // 5 minutes
      compress = false
    } = options;

    try {
      let serializedValue = value;
      
      if (compress && typeof value === 'object') {
        // Compression simple pour les objets volumineux
        serializedValue = JSON.stringify(value);
      }

      switch (type) {
        case CACHE_TYPES.MEMORY:
          this.memoryCache.set(key, serializedValue, ttl);
          break;

        case CACHE_TYPES.SESSION:
          sessionStorage.setItem(key, JSON.stringify({
            value: serializedValue,
            expiresAt: Date.now() + ttl
          }));
          break;

        case CACHE_TYPES.LOCAL:
          localStorage.setItem(key, JSON.stringify({
            value: serializedValue,
            expiresAt: Date.now() + ttl
          }));
          break;

        default:
          this.memoryCache.set(key, serializedValue, ttl);
      }

      console.log(`📦 Mise en cache: ${key} (${type})`);
    } catch (error) {
      console.warn('Erreur lors de la mise en cache:', error);
    }
  }

  /**
   * Récupérer une valeur du cache
   */
  get(key, type = CACHE_TYPES.MEMORY) {
    try {
      switch (type) {
        case CACHE_TYPES.MEMORY:
          return this.memoryCache.get(key);

        case CACHE_TYPES.SESSION:
        case CACHE_TYPES.LOCAL:
          const storage = type === CACHE_TYPES.SESSION ? sessionStorage : localStorage;
          const item = storage.getItem(key);
          
          if (!item) return null;
          
          const parsed = JSON.parse(item);
          if (Date.now() > parsed.expiresAt) {
            storage.removeItem(key);
            return null;
          }
          
          return parsed.value;

        default:
          return this.memoryCache.get(key);
      }
    } catch (error) {
      console.warn('Erreur lors de la récupération du cache:', error);
      return null;
    }
  }

  /**
   * Vérifier si une clé existe dans le cache
   */
  has(key, type = CACHE_TYPES.MEMORY) {
    return this.get(key, type) !== null;
  }

  /**
   * Supprimer une entrée du cache
   */
  delete(key, type = CACHE_TYPES.MEMORY) {
    switch (type) {
      case CACHE_TYPES.MEMORY:
        this.memoryCache.delete(key);
        break;
      case CACHE_TYPES.SESSION:
        sessionStorage.removeItem(key);
        break;
      case CACHE_TYPES.LOCAL:
        localStorage.removeItem(key);
        break;
    }
  }

  /**
   * Nettoyer tout le cache
   */
  clear(type = null) {
    if (type) {
      switch (type) {
        case CACHE_TYPES.MEMORY:
          this.memoryCache.clear();
          break;
        case CACHE_TYPES.SESSION:
          sessionStorage.clear();
          break;
        case CACHE_TYPES.LOCAL:
          localStorage.clear();
          break;
      }
    } else {
      // Nettoyer tous les types
      this.memoryCache.clear();
      this.requestCache.clear();
    }
  }

  /**
   * Wrapper pour les requêtes avec cache
   */
  async cachedRequest(requestFunction, cacheKey, options = {}) {
    const {
      strategy = CACHE_STRATEGIES.CACHE_FIRST,
      cacheType = CACHE_TYPES.MEMORY,
      ttl = 300000,
      forceRefresh = false
    } = options;

    // Vérifier si la requête est déjà en cours
    if (this.requestCache.has(cacheKey)) {
      console.log(`⏳ Requête en cours pour: ${cacheKey}`);
      return this.requestCache.get(cacheKey);
    }

    switch (strategy) {
      case CACHE_STRATEGIES.CACHE_FIRST:
        return this.cacheFirstStrategy(requestFunction, cacheKey, { cacheType, ttl, forceRefresh });

      case CACHE_STRATEGIES.NETWORK_FIRST:
        return this.networkFirstStrategy(requestFunction, cacheKey, { cacheType, ttl });

      case CACHE_STRATEGIES.CACHE_ONLY:
        return this.get(cacheKey, cacheType);

      case CACHE_STRATEGIES.NETWORK_ONLY:
        return this.executeRequest(requestFunction, cacheKey);

      case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
        return this.staleWhileRevalidateStrategy(requestFunction, cacheKey, { cacheType, ttl });

      default:
        return this.cacheFirstStrategy(requestFunction, cacheKey, { cacheType, ttl, forceRefresh });
    }
  }

  /**
   * Stratégie Cache First
   */
  async cacheFirstStrategy(requestFunction, cacheKey, options) {
    const { cacheType, ttl, forceRefresh } = options;

    // Vérifier le cache d'abord
    if (!forceRefresh) {
      const cachedData = this.get(cacheKey, cacheType);
      if (cachedData) {
        console.log(`🎯 Cache hit: ${cacheKey}`);
        return cachedData;
      }
    }

    // Exécuter la requête si pas en cache
    console.log(`🌐 Cache miss: ${cacheKey}`);
    const data = await this.executeRequest(requestFunction, cacheKey);
    
    // Mettre en cache le résultat
    this.set(cacheKey, data, { type: cacheType, ttl });
    
    return data;
  }

  /**
   * Stratégie Network First
   */
  async networkFirstStrategy(requestFunction, cacheKey, options) {
    const { cacheType, ttl } = options;

    try {
      // Essayer le réseau d'abord
      const data = await this.executeRequest(requestFunction, cacheKey);
      this.set(cacheKey, data, { type: cacheType, ttl });
      return data;
    } catch (error) {
      // Fallback vers le cache en cas d'erreur réseau
      console.warn('Erreur réseau, fallback vers le cache:', error);
      const cachedData = this.get(cacheKey, cacheType);
      if (cachedData) {
        return cachedData;
      }
      throw error;
    }
  }

  /**
   * Stratégie Stale While Revalidate
   */
  async staleWhileRevalidateStrategy(requestFunction, cacheKey, options) {
    const { cacheType, ttl } = options;

    // Retourner les données en cache immédiatement
    const cachedData = this.get(cacheKey, cacheType);
    
    // Lancer la revalidation en arrière-plan
    this.executeRequest(requestFunction, cacheKey)
      .then(freshData => {
        this.set(cacheKey, freshData, { type: cacheType, ttl });
      })
      .catch(error => {
        console.warn('Erreur lors de la revalidation:', error);
      });

    return cachedData;
  }

  /**
   * Exécuter une requête avec gestion des doublons
   */
  async executeRequest(requestFunction, cacheKey) {
    // Créer une promesse pour cette requête
    const requestPromise = requestFunction();
    
    // Stocker la promesse pour éviter les doublons
    this.requestCache.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Nettoyer la requête en cours
      this.requestCache.delete(cacheKey);
    }
  }

  /**
   * Invalider le cache par pattern
   */
  invalidatePattern(pattern, cacheType = CACHE_TYPES.MEMORY) {
    const regex = new RegExp(pattern);
    
    if (cacheType === CACHE_TYPES.MEMORY) {
      const keysToDelete = [];
      for (const key of this.memoryCache.cache.keys()) {
        if (regex.test(key)) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => this.memoryCache.delete(key));
    }
    
    // Pour localStorage et sessionStorage, il faudrait itérer sur toutes les clés
    // Ce qui peut être coûteux, donc on évite pour l'instant
  }

  /**
   * Obtenir les statistiques du cache
   */
  getStats() {
    return {
      memory: this.memoryCache.getStats(),
      activeRequests: this.requestCache.size,
      sessionStorageSize: this.getStorageSize(sessionStorage),
      localStorageSize: this.getStorageSize(localStorage)
    };
  }

  /**
   * Calculer la taille d'un storage
   */
  getStorageSize(storage) {
    let size = 0;
    try {
      for (const key in storage) {
        if (storage.hasOwnProperty(key)) {
          size += storage[key].length + key.length;
        }
      }
    } catch (error) {
      console.warn('Erreur lors du calcul de la taille du storage:', error);
    }
    return size;
  }
}

// Instance singleton
const cacheService = new CacheService();

export default cacheService;
