/**
 * Composant de gestion des produits pour les marchands
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  InputAdornment,
  Switch,
  FormControlLabel,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  PhotoCamera as PhotoIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Store as StoreIcon,
  AttachMoney as MoneyIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { marchandAPI } from '../../services/marchandService';

const ProduitsManagement = () => {
  const { user } = useAuth();
  const [produits, setProduits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduit, setSelectedProduit] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    categorie: '',
    prix_unitaire: '',
    unite_mesure: '',
    stock_actuel: '',
    stock_minimum: '',
    stock_maximum: '',
    fournisseur: '',
    code_produit: '',
    statut: 'actif',
    image_url: '',
    notes: ''
  });

  const [stats, setStats] = useState({
    totalProduits: 0,
    produitsActifs: 0,
    produitsRupture: 0,
    valeurStock: 0
  });

  const categories = [
    'Volailles vivantes',
    'Œufs',
    'Viande de volaille',
    'Aliments pour volailles',
    'Équipements',
    'Médicaments',
    'Accessoires',
    'Autres'
  ];

  const unitesMesure = [
    'Pièce',
    'Kg',
    'Gramme',
    'Litre',
    'Sac',
    'Boîte',
    'Carton',
    'Palette'
  ];

  useEffect(() => {
    loadProduits();
    loadStats();
  }, []);

  const loadProduits = async () => {
    try {
      setLoading(true);
      const response = await marchandAPI.getProduits();
      setProduits(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des produits');
      console.error('Erreur produits:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await marchandAPI.getProduitsStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats produits:', err);
    }
  };

  const handleOpenDialog = (produit = null) => {
    if (produit) {
      setSelectedProduit(produit);
      setFormData({
        nom: produit.nom || '',
        description: produit.description || '',
        categorie: produit.categorie || '',
        prix_unitaire: produit.prix_unitaire || '',
        unite_mesure: produit.unite_mesure || '',
        stock_actuel: produit.stock_actuel || '',
        stock_minimum: produit.stock_minimum || '',
        stock_maximum: produit.stock_maximum || '',
        fournisseur: produit.fournisseur || '',
        code_produit: produit.code_produit || '',
        statut: produit.statut || 'actif',
        image_url: produit.image_url || '',
        notes: produit.notes || ''
      });
    } else {
      setSelectedProduit(null);
      setFormData({
        nom: '',
        description: '',
        categorie: '',
        prix_unitaire: '',
        unite_mesure: '',
        stock_actuel: '',
        stock_minimum: '',
        stock_maximum: '',
        fournisseur: '',
        code_produit: '',
        statut: 'actif',
        image_url: '',
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduit(null);
  };

  const handleSubmit = async () => {
    try {
      const produitData = {
        ...formData,
        marchand_id: user.profile_id
      };

      if (selectedProduit) {
        await marchandAPI.updateProduit(selectedProduit.id, produitData);
      } else {
        await marchandAPI.createProduit(produitData);
      }

      handleCloseDialog();
      loadProduits();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde du produit');
      console.error('Erreur sauvegarde produit:', err);
    }
  };

  const handleDelete = async (produitId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        await marchandAPI.deleteProduit(produitId);
        loadProduits();
        loadStats();
      } catch (err) {
        setError('Erreur lors de la suppression du produit');
        console.error('Erreur suppression produit:', err);
      }
    }
  };

  const getStatutColor = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'actif': return 'success';
      case 'inactif': return 'default';
      case 'rupture': return 'error';
      case 'commande': return 'warning';
      default: return 'default';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'actif': return 'Actif';
      case 'inactif': return 'Inactif';
      case 'rupture': return 'Rupture de stock';
      case 'commande': return 'En commande';
      default: return statut;
    }
  };

  const getStockStatus = (produit) => {
    if (produit.stock_actuel <= 0) return 'rupture';
    if (produit.stock_actuel <= produit.stock_minimum) return 'faible';
    return 'normal';
  };

  const getStockColor = (status) => {
    switch (status) {
      case 'rupture': return 'error';
      case 'faible': return 'warning';
      case 'normal': return 'success';
      default: return 'default';
    }
  };

  const filteredProduits = produits.filter(produit => {
    const matchesSearch = produit.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         produit.code_produit.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || produit.categorie === filterCategory;
    const matchesStatus = !filterStatus || produit.statut === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const StatCard = ({ title, value, icon, color = 'primary', subtitle }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ 
            p: 1, 
            borderRadius: 1, 
            bgcolor: `${color}.light`, 
            color: `${color}.contrastText`,
            mr: 2 
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des produits...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Gestion des Produits
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Nouveau Produit
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Produits"
            value={stats.totalProduits}
            icon={<StoreIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Produits Actifs"
            value={stats.produitsActifs}
            icon={<CheckIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Ruptures de Stock"
            value={stats.produitsRupture}
            icon={<WarningIcon />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Valeur du Stock"
            value={`${stats.valeurStock.toLocaleString()} DA`}
            icon={<MoneyIcon />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Filtres et recherche */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Rechercher un produit..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  <MenuItem value="">Toutes les catégories</MenuItem>
                  {categories.map(cat => (
                    <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Statut</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="">Tous les statuts</MenuItem>
                  <MenuItem value="actif">Actif</MenuItem>
                  <MenuItem value="inactif">Inactif</MenuItem>
                  <MenuItem value="rupture">Rupture</MenuItem>
                  <MenuItem value="commande">En commande</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Typography variant="body2" color="text.secondary">
                {filteredProduits.length} produit(s) trouvé(s)
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Table des produits */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Liste des Produits
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Produit</TableCell>
                  <TableCell>Catégorie</TableCell>
                  <TableCell>Prix</TableCell>
                  <TableCell>Stock</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredProduits.map((produit) => {
                  const stockStatus = getStockStatus(produit);
                  return (
                    <TableRow key={produit.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {produit.image_url ? (
                            <img
                              src={produit.image_url}
                              alt={produit.nom}
                              style={{ width: 40, height: 40, marginRight: 8, borderRadius: 4 }}
                            />
                          ) : (
                            <Box sx={{ 
                              width: 40, 
                              height: 40, 
                              bgcolor: 'grey.200', 
                              borderRadius: 1, 
                              mr: 1,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <PhotoIcon color="disabled" />
                            </Box>
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {produit.nom}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Code: {produit.code_produit}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>{produit.categorie}</TableCell>
                      <TableCell>
                        {produit.prix_unitaire} DA/{produit.unite_mesure}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Badge
                            badgeContent={stockStatus === 'rupture' ? '!' : stockStatus === 'faible' ? '⚠' : ''}
                            color={getStockColor(stockStatus)}
                          >
                            <Typography variant="body2">
                              {produit.stock_actuel} {produit.unite_mesure}
                            </Typography>
                          </Badge>
                          {stockStatus === 'faible' && (
                            <Tooltip title="Stock faible">
                              <WarningIcon color="warning" sx={{ ml: 1, fontSize: 16 }} />
                            </Tooltip>
                          )}
                          {stockStatus === 'rupture' && (
                            <Tooltip title="Rupture de stock">
                              <WarningIcon color="error" sx={{ ml: 1, fontSize: 16 }} />
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatutLabel(produit.statut)}
                          color={getStatutColor(produit.statut)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Modifier">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(produit)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Voir détails">
                          <IconButton
                            size="small"
                            onClick={() => {/* Voir détails */}}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(produit.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Dialog pour ajouter/modifier un produit */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProduit ? 'Modifier le Produit' : 'Nouveau Produit'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nom du produit"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Code produit"
                value={formData.code_produit}
                onChange={(e) => setFormData({ ...formData, code_produit: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={formData.categorie}
                  onChange={(e) => setFormData({ ...formData, categorie: e.target.value })}
                >
                  {categories.map(cat => (
                    <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Unité de mesure</InputLabel>
                <Select
                  value={formData.unite_mesure}
                  onChange={(e) => setFormData({ ...formData, unite_mesure: e.target.value })}
                >
                  {unitesMesure.map(unite => (
                    <MenuItem key={unite} value={unite}>{unite}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Prix unitaire (DA)"
                type="number"
                value={formData.prix_unitaire}
                onChange={(e) => setFormData({ ...formData, prix_unitaire: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Fournisseur"
                value={formData.fournisseur}
                onChange={(e) => setFormData({ ...formData, fournisseur: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Stock actuel"
                type="number"
                value={formData.stock_actuel}
                onChange={(e) => setFormData({ ...formData, stock_actuel: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Stock minimum"
                type="number"
                value={formData.stock_minimum}
                onChange={(e) => setFormData({ ...formData, stock_minimum: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Stock maximum"
                type="number"
                value={formData.stock_maximum}
                onChange={(e) => setFormData({ ...formData, stock_maximum: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="URL de l'image"
                value={formData.image_url}
                onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Statut</InputLabel>
                <Select
                  value={formData.statut}
                  onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                >
                  <MenuItem value="actif">Actif</MenuItem>
                  <MenuItem value="inactif">Inactif</MenuItem>
                  <MenuItem value="rupture">Rupture de stock</MenuItem>
                  <MenuItem value="commande">En commande</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={2}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedProduit ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ProduitsManagement;
