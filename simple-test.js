const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware de base
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur simple...');

// Route de base
app.get('/', (req, res) => {
  console.log('📡 Requête reçue sur /');
  res.json({ 
    message: 'Serveur simple Poultray DZ',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route de connexion simulée
app.post('/api/auth/login', (req, res) => {
  console.log('📡 Tentative de connexion:', req.body);
  
  const { email } = req.body;
  
  if (email === '<EMAIL>') {
    res.json({
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      firebase_uid: '96CSpHFAoKaAvBD018vRtRe8qW93'
    });
  } else {
    res.status(401).json({ message: 'Identifiants invalides' });
  }
});

// Route utilisateur
app.get('/api/auth/user', (req, res) => {
  console.log('📡 Récupération utilisateur');
  res.json({
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    first_name: 'Admin',
    last_name: 'Poultray'
  });
});

// Route statistiques
app.get('/api/admin/stats', (req, res) => {
  console.log('📡 Récupération statistiques');
  res.json({
    totalUsers: 3,
    totalAdmins: 1,
    totalEleveurs: 2,
    totalVeterinaires: 0,
    totalMarchands: 0,
    users: [
      { role: 'admin', count: '1' },
      { role: 'eleveur', count: '2' }
    ],
    volailles: {
      total: 150,
      disponibles: 120,
      vendues: 30
    },
    ventes: {
      total: 25,
      montantTotal: 125000,
      moyenneParVente: 5000
    }
  });
});

// Route utilisateurs
app.get('/api/admin/users', (req, res) => {
  console.log('📡 Récupération utilisateurs');
  res.json({
    users: [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        first_name: 'Admin',
        last_name: 'Poultray',
        phone: null,
        address: null
      },
      {
        id: 2,
        username: 'eleveur1',
        email: '<EMAIL>',
        role: 'eleveur',
        first_name: 'Jean',
        last_name: 'Dupont',
        phone: '0123456789',
        address: 'Alger'
      }
    ],
    total: 2,
    page: 1,
    limit: 10,
    totalPages: 1
  });
});

// Route volailles
app.get('/api/volailles', (req, res) => {
  console.log('📡 Récupération volailles');
  res.json([
    {
      id: 1,
      espece: 'Poulet',
      race: 'Broiler',
      age: 45,
      poids: 2.5,
      quantite: 100,
      prix_unitaire: 500,
      eleveur_nom: 'Jean',
      eleveur_prenom: 'Dupont'
    }
  ]);
});

// Route éleveurs
app.get('/api/eleveurs', (req, res) => {
  console.log('📡 Récupération éleveurs');
  res.json([
    {
      id: 2,
      nom: 'Dupont',
      prenom: 'Jean',
      email: '<EMAIL>',
      telephone: '0123456789',
      adresse: 'Alger'
    }
  ]);
});

// Route 404
app.use('*', (req, res) => {
  console.log('❌ Route non trouvée:', req.originalUrl);
  res.status(404).json({ message: 'Route non trouvée' });
});

// Démarrer le serveur
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Serveur simple démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
