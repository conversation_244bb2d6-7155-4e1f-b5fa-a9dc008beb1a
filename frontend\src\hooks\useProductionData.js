import { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { useAuthToken } from './useAuthToken';
import { format, subDays, parseISO, isValid } from 'date-fns';

/**
 * Hook personnalisé pour récupérer et gérer les données de production
 * @param {Object} options - Options de configuration
 * @param {string} options.startDate - Date de début (format YYYY-MM-DD)
 * @param {string} options.endDate - Date de fin (format YYYY-MM-DD)
 * @param {string} options.type - Type de production (oeufs, chair, etc.)
 * @returns {Object} Données et fonctions pour gérer les données de production
 */
export const useProductionData = (options = {}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token, isAuthenticated } = useAuthToken();

  // Valeurs par défaut pour les options
  const defaultOptions = {
    startDate: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    endDate: format(new Date(), 'yyyy-MM-dd'),
    type: 'all'
  };

  // Fusionner les options par défaut avec les options fournies
  const mergedOptions = { ...defaultOptions, ...options };

  // Récupérer les données de production
  const fetchProductionData = async (queryOptions = {}) => {
    if (!isAuthenticated) {
      setError(new Error('Non authentifié'));
      setLoading(false);
      return;
    }

    const opts = { ...mergedOptions, ...queryOptions };
    
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (opts.startDate) params.append('startDate', opts.startDate);
      if (opts.endDate) params.append('endDate', opts.endDate);
      if (opts.type !== 'all') params.append('type', opts.type);

      const response = await axios.get(`/api/production?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setData(response.data);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des données de production:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  // Ajouter une nouvelle entrée de production
  const addProductionEntry = async (productionData) => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const response = await axios.post('/api/production', productionData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Mettre à jour les données locales
      setData(prevData => [...prevData, response.data]);
      return response.data;
    } catch (err) {
      console.error('Erreur lors de l\'ajout d\'une entrée de production:', err);
      throw err;
    }
  };

  // Mettre à jour une entrée de production
  const updateProductionEntry = async (id, productionData) => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      const response = await axios.put(`/api/production/${id}`, productionData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Mettre à jour les données locales
      setData(prevData => 
        prevData.map(item => item.id === id ? response.data : item)
      );
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la mise à jour d\'une entrée de production:', err);
      throw err;
    }
  };

  // Supprimer une entrée de production
  const deleteProductionEntry = async (id) => {
    if (!isAuthenticated) {
      throw new Error('Non authentifié');
    }

    try {
      await axios.delete(`/api/production/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Mettre à jour les données locales
      setData(prevData => prevData.filter(item => item.id !== id));
      return true;
    } catch (err) {
      console.error('Erreur lors de la suppression d\'une entrée de production:', err);
      throw err;
    }
  };

  // Calculer les statistiques de production
  const stats = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        average: 0,
        min: 0,
        max: 0,
        trend: 'stable'
      };
    }

    // Trier les données par date
    const sortedData = [...data].sort((a, b) => {
      const dateA = parseISO(a.date);
      const dateB = parseISO(b.date);
      return isValid(dateA) && isValid(dateB) ? dateA - dateB : 0;
    });

    // Calculer les statistiques
    const total = sortedData.reduce((sum, item) => sum + (item.quantite || 0), 0);
    const average = total / sortedData.length;
    const quantities = sortedData.map(item => item.quantite || 0);
    const min = Math.min(...quantities);
    const max = Math.max(...quantities);

    // Calculer la tendance
    let trend = 'stable';
    if (sortedData.length > 1) {
      const firstHalf = sortedData.slice(0, Math.floor(sortedData.length / 2));
      const secondHalf = sortedData.slice(Math.floor(sortedData.length / 2));
      
      const firstHalfAvg = firstHalf.reduce((sum, item) => sum + (item.quantite || 0), 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, item) => sum + (item.quantite || 0), 0) / secondHalf.length;
      
      const percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
      
      if (percentChange > 5) trend = 'up';
      else if (percentChange < -5) trend = 'down';
    }

    return {
      total,
      average,
      min,
      max,
      trend
    };
  }, [data]);

  // Charger les données au montage du composant ou lorsque les options changent
  useEffect(() => {
    fetchProductionData();
  }, [mergedOptions.startDate, mergedOptions.endDate, mergedOptions.type, isAuthenticated]);

  return {
    data,
    loading,
    error,
    stats,
    fetchProductionData,
    addProductionEntry,
    updateProductionEntry,
    deleteProductionEntry
  };
};

export default useProductionData;

