const sequelize = require('../src/config/database');
const { QueryTypes } = require('sequelize');

async function checkVolaillesTable() {
  try {
    console.log('Checking volailles table structure...');

    // Check if the volailles table exists
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'volailles'
      );
    `;

    const [tableExists] = await sequelize.query(tableExistsQuery, {
      type: QueryTypes.SELECT
    });

    if (!tableExists.exists) {
      console.log('❌ The volailles table does not exist');
      return;
    }

    console.log('✅ The volailles table exists');

    // Get table structure
    const tableStructureQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'volailles'
      ORDER BY ordinal_position;
    `;

    const columns = await sequelize.query(tableStructureQuery, {
      type: QueryTypes.SELECT
    });

    console.log('✅ Table structure:');
    console.table(columns);

    // Get sample data
    const sampleDataQuery = `SELECT * FROM volailles LIMIT 1;`;

    const sampleData = await sequelize.query(sampleDataQuery, {
      type: QueryTypes.SELECT
    });

    if (sampleData.length > 0) {
      console.log('✅ Sample data:');
      console.log(sampleData[0]);
    } else {
      console.log('⚠️ No data found in the table');
    }

  } catch (error) {
    console.error('❌ Error checking volailles table:', error);
  } finally {
    await sequelize.close();
  }
}

checkVolaillesTable();
