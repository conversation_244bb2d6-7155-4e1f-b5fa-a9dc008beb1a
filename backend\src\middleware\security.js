/**
 * Middleware de sécurité et rate limiting
 */

const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const helmet = require('helmet');
const cors = require('cors');
const logger = require('../utils/logger');

/**
 * Configuration CORS
 */
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5174',
      'https://poultraydz.com',
      'https://app.poultraydz.com'
    ];

    // Permettre les requêtes sans origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      logger.warn(`Origine CORS non autorisée: ${origin}`);
      callback(new Error('Non autorisé par la politique CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

/**
 * Rate limiting général
 */
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requêtes par IP par fenêtre
  message: {
    success: false,
    message: 'Trop de requêtes, veuillez réessayer plus tard',
    errors: [],
    meta: {
      timestamp: new Date().toISOString(),
      retryAfter: '15 minutes'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Rate limit dépassé pour IP: ${req.ip}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url
    });
    res.status(429).json(res.locals.rateLimitMessage || {
      success: false,
      message: 'Trop de requêtes, veuillez réessayer plus tard'
    });
  }
});

/**
 * Rate limiting strict pour l'authentification
 */
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 tentatives de connexion par IP
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Trop de tentatives de connexion, compte temporairement bloqué',
    errors: [],
    meta: {
      timestamp: new Date().toISOString(),
      retryAfter: '15 minutes'
    }
  },
  handler: (req, res) => {
    logger.warn(`Tentatives de connexion excessives pour IP: ${req.ip}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body.email
    });
    res.status(429).json({
      success: false,
      message: 'Trop de tentatives de connexion, compte temporairement bloqué'
    });
  }
});

/**
 * Rate limiting pour les API sensibles
 */
const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requêtes par minute
  message: {
    success: false,
    message: 'Limite de requêtes API dépassée'
  }
});

/**
 * Slow down pour ralentir les requêtes répétées
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 100, // Commencer à ralentir après 100 requêtes
  delayMs: 500, // Ajouter 500ms de délai par requête
  maxDelayMs: 20000, // Délai maximum de 20 secondes
  onLimitReached: (req, res, options) => {
    logger.warn(`Speed limit atteint pour IP: ${req.ip}`, {
      ip: req.ip,
      delay: options.delay
    });
  }
});

/**
 * Configuration Helmet pour la sécurité des headers
 */
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.poultraydz.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * Middleware de validation d'IP
 */
const ipWhitelist = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  
  // Liste des IPs bloquées (à implémenter avec une base de données)
  const blockedIPs = process.env.BLOCKED_IPS ? process.env.BLOCKED_IPS.split(',') : [];
  
  if (blockedIPs.includes(clientIP)) {
    logger.warn(`Tentative d'accès depuis une IP bloquée: ${clientIP}`);
    return res.status(403).json({
      success: false,
      message: 'Accès interdit'
    });
  }
  
  next();
};

/**
 * Middleware de détection d'attaques
 */
const attackDetection = (req, res, next) => {
  const suspiciousPatterns = [
    /(\<script\>|\<\/script\>)/gi, // XSS
    /(union|select|insert|delete|update|drop|create|alter)/gi, // SQL Injection
    /(\.\.\/)|(\.\.\\)/gi, // Path Traversal
    /(eval\(|javascript:)/gi // Code Injection
  ];

  const checkString = JSON.stringify(req.body) + req.url + JSON.stringify(req.query);
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(checkString)) {
      logger.warn(`Tentative d'attaque détectée depuis IP: ${req.ip}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        pattern: pattern.toString(),
        payload: checkString.substring(0, 200)
      });
      
      return res.status(400).json({
        success: false,
        message: 'Requête invalide détectée'
      });
    }
  }
  
  next();
};

/**
 * Middleware de logging de sécurité
 */
const securityLogger = (req, res, next) => {
  // Logger les tentatives d'accès aux endpoints sensibles
  const sensitiveEndpoints = ['/api/admin', '/api/users', '/api/auth'];
  
  if (sensitiveEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
    logger.info('Accès à un endpoint sensible:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      userId: req.user?.id,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

/**
 * Middleware de validation des tokens
 */
const tokenValidation = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (token) {
    // Vérifier si le token est dans une liste noire (à implémenter)
    const blacklistedTokens = []; // À remplacer par une vérification en base
    
    if (blacklistedTokens.includes(token)) {
      logger.warn(`Tentative d'utilisation d'un token blacklisté`, {
        ip: req.ip,
        token: token.substring(0, 20) + '...'
      });
      
      return res.status(401).json({
        success: false,
        message: 'Token invalide'
      });
    }
  }
  
  next();
};

/**
 * Middleware de protection contre les attaques par force brute
 */
const bruteForceProtection = (req, res, next) => {
  const key = `bf_${req.ip}_${req.body.email || req.body.username || 'unknown'}`;
  
  // À implémenter avec Redis pour un stockage persistant
  // Pour l'instant, utilisation d'un Map simple
  if (!global.bruteForceAttempts) {
    global.bruteForceAttempts = new Map();
  }
  
  const attempts = global.bruteForceAttempts.get(key) || { count: 0, lastAttempt: Date.now() };
  
  // Réinitialiser si plus de 1 heure s'est écoulée
  if (Date.now() - attempts.lastAttempt > 60 * 60 * 1000) {
    attempts.count = 0;
  }
  
  if (attempts.count >= 10) {
    logger.warn(`Protection contre force brute activée pour: ${key}`);
    return res.status(429).json({
      success: false,
      message: 'Trop de tentatives, compte temporairement bloqué'
    });
  }
  
  // Incrémenter le compteur en cas d'échec (à gérer dans le contrôleur)
  req.incrementBruteForce = () => {
    attempts.count++;
    attempts.lastAttempt = Date.now();
    global.bruteForceAttempts.set(key, attempts);
  };
  
  req.resetBruteForce = () => {
    global.bruteForceAttempts.delete(key);
  };
  
  next();
};

module.exports = {
  corsOptions,
  generalLimiter,
  authLimiter,
  apiLimiter,
  speedLimiter,
  helmetConfig,
  ipWhitelist,
  attackDetection,
  securityLogger,
  tokenValidation,
  bruteForceProtection
};
