/**
 * Service d'optimisation des requêtes SQL
 * Optimise les performances des requêtes et surveille les performances
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');

class QueryOptimizer {
  constructor(pool) {
    this.pool = pool;
    this.queryStats = new Map();
    this.slowQueryThreshold = 1000; // 1 seconde
  }

  /**
   * Exécuter une requête avec monitoring des performances
   */
  async executeQuery(query, params = [], options = {}) {
    const { 
      name = 'unnamed_query',
      timeout = 30000,
      cache = false,
      cacheKey = null,
      cacheTTL = 300000 // 5 minutes
    } = options;

    const startTime = Date.now();
    const queryId = `${name}_${Date.now()}`;

    try {
      // Vérifier le cache si activé
      if (cache && cacheKey) {
        const cachedResult = await this.getCachedResult(cacheKey);
        if (cachedResult) {
          logger.info(`Cache hit pour la requête: ${name}`);
          return cachedResult;
        }
      }

      // Exécuter la requête avec timeout
      const client = await this.pool.connect();
      
      try {
        // Définir un timeout pour la requête
        await client.query('SET statement_timeout = $1', [timeout]);
        
        const result = await client.query(query, params);
        const duration = Date.now() - startTime;

        // Enregistrer les statistiques
        this.recordQueryStats(name, duration, result.rowCount);

        // Alerter si la requête est lente
        if (duration > this.slowQueryThreshold) {
          logger.warn(`Requête lente détectée: ${name} (${duration}ms)`, {
            query: query.substring(0, 200),
            duration,
            rowCount: result.rowCount
          });
        }

        // Mettre en cache si demandé
        if (cache && cacheKey) {
          await this.setCachedResult(cacheKey, result.rows, cacheTTL);
        }

        return result.rows;
      } finally {
        client.release();
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error(`Erreur dans la requête ${name}:`, {
        error: error.message,
        query: query.substring(0, 200),
        params,
        duration
      });

      // Enregistrer l'erreur dans les stats
      this.recordQueryError(name, error, duration);
      
      throw error;
    }
  }

  /**
   * Requêtes optimisées pour les dashboards
   */
  async getDashboardData(userId, userRole) {
    const queries = {
      eleveur: `
        WITH recent_data AS (
          SELECT 
            v.id,
            v.type_volaille,
            v.nombre_total,
            v.nombre_actuel,
            v.date_creation,
            p.quantite_produite,
            p.date_production
          FROM volailles v
          LEFT JOIN production p ON v.id = p.volaille_id 
            AND p.date_production >= CURRENT_DATE - INTERVAL '30 days'
          WHERE v.eleveur_id = $1
            AND v.statut = 'actif'
        )
        SELECT 
          COUNT(DISTINCT id) as total_volailles,
          SUM(nombre_actuel) as total_animaux,
          COALESCE(SUM(quantite_produite), 0) as production_mensuelle,
          COUNT(DISTINCT CASE WHEN date_creation >= CURRENT_DATE - INTERVAL '7 days' THEN id END) as nouvelles_volailles
        FROM recent_data
      `,
      
      veterinaire: `
        SELECT 
          COUNT(DISTINCT c.id) as total_consultations,
          COUNT(DISTINCT c.eleveur_id) as eleveurs_suivis,
          COUNT(DISTINCT CASE WHEN c.date_consultation >= CURRENT_DATE - INTERVAL '7 days' THEN c.id END) as consultations_semaine,
          COUNT(DISTINCT CASE WHEN c.urgence = true THEN c.id END) as consultations_urgentes
        FROM consultations c
        WHERE c.veterinaire_id = $1
          AND c.date_consultation >= CURRENT_DATE - INTERVAL '30 days'
      `,
      
      marchand: `
        SELECT 
          COUNT(DISTINCT co.id) as total_commandes,
          COALESCE(SUM(co.montant_total), 0) as chiffre_affaires,
          COUNT(DISTINCT co.eleveur_id) as clients_actifs,
          COUNT(DISTINCT CASE WHEN co.statut = 'en_attente' THEN co.id END) as commandes_en_attente
        FROM commandes co
        WHERE co.marchand_id = $1
          AND co.date_commande >= CURRENT_DATE - INTERVAL '30 days'
      `
    };

    const query = queries[userRole];
    if (!query) {
      throw new Error(`Rôle non supporté: ${userRole}`);
    }

    return this.executeQuery(query, [userId], {
      name: `dashboard_${userRole}`,
      cache: true,
      cacheKey: `dashboard_${userRole}_${userId}`,
      cacheTTL: 300000 // 5 minutes
    });
  }

  /**
   * Requête optimisée pour la liste des volailles avec pagination
   */
  async getVolaillesPaginated(eleveurId, page = 1, limit = 20, filters = {}) {
    const offset = (page - 1) * limit;
    const conditions = ['v.eleveur_id = $1'];
    const params = [eleveurId];
    let paramIndex = 2;

    // Ajouter les filtres
    if (filters.type_volaille) {
      conditions.push(`v.type_volaille = $${paramIndex}`);
      params.push(filters.type_volaille);
      paramIndex++;
    }

    if (filters.statut) {
      conditions.push(`v.statut = $${paramIndex}`);
      params.push(filters.statut);
      paramIndex++;
    }

    if (filters.search) {
      conditions.push(`(v.nom ILIKE $${paramIndex} OR v.description ILIKE $${paramIndex})`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = conditions.join(' AND ');

    // Requête principale avec jointures optimisées
    const dataQuery = `
      SELECT 
        v.id,
        v.nom,
        v.type_volaille,
        v.nombre_total,
        v.nombre_actuel,
        v.statut,
        v.date_creation,
        COALESCE(latest_prod.quantite_produite, 0) as derniere_production,
        latest_prod.date_production as date_derniere_production,
        COALESCE(health_stats.consultations_count, 0) as nombre_consultations
      FROM volailles v
      LEFT JOIN LATERAL (
        SELECT quantite_produite, date_production
        FROM production p
        WHERE p.volaille_id = v.id
        ORDER BY p.date_production DESC
        LIMIT 1
      ) latest_prod ON true
      LEFT JOIN LATERAL (
        SELECT COUNT(*) as consultations_count
        FROM consultations c
        WHERE c.volaille_id = v.id
          AND c.date_consultation >= CURRENT_DATE - INTERVAL '30 days'
      ) health_stats ON true
      WHERE ${whereClause}
      ORDER BY v.date_creation DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Requête pour le total
    const countQuery = `
      SELECT COUNT(*) as total
      FROM volailles v
      WHERE ${whereClause}
    `;

    params.push(limit, offset);

    const [data, countResult] = await Promise.all([
      this.executeQuery(dataQuery, params, {
        name: 'volailles_paginated',
        cache: true,
        cacheKey: `volailles_${eleveurId}_${page}_${JSON.stringify(filters)}`,
        cacheTTL: 180000 // 3 minutes
      }),
      this.executeQuery(countQuery, params.slice(0, -2), {
        name: 'volailles_count'
      })
    ]);

    const total = parseInt(countResult[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Enregistrer les statistiques de requête
   */
  recordQueryStats(name, duration, rowCount) {
    if (!this.queryStats.has(name)) {
      this.queryStats.set(name, {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        totalRows: 0,
        errors: 0
      });
    }

    const stats = this.queryStats.get(name);
    stats.count++;
    stats.totalDuration += duration;
    stats.avgDuration = stats.totalDuration / stats.count;
    stats.maxDuration = Math.max(stats.maxDuration, duration);
    stats.minDuration = Math.min(stats.minDuration, duration);
    stats.totalRows += rowCount || 0;

    this.queryStats.set(name, stats);
  }

  /**
   * Enregistrer une erreur de requête
   */
  recordQueryError(name, error, duration) {
    if (!this.queryStats.has(name)) {
      this.recordQueryStats(name, duration, 0);
    }

    const stats = this.queryStats.get(name);
    stats.errors++;
    this.queryStats.set(name, stats);
  }

  /**
   * Obtenir les statistiques de performance
   */
  getPerformanceStats() {
    const stats = {};
    
    for (const [name, data] of this.queryStats) {
      stats[name] = {
        ...data,
        errorRate: data.count > 0 ? (data.errors / data.count) * 100 : 0,
        avgRowsPerQuery: data.count > 0 ? data.totalRows / data.count : 0
      };
    }

    return stats;
  }

  /**
   * Cache simple en mémoire (à remplacer par Redis en production)
   */
  cache = new Map();

  async getCachedResult(key) {
    const cached = this.cache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  async setCachedResult(key, data, ttl) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  /**
   * Nettoyer le cache expiré
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache) {
      if (value.expires <= now) {
        this.cache.delete(key);
      }
    }
  }
}

module.exports = QueryOptimizer;
