-- Migration pour le tableau de bord Marchand
-- Ce script crée les tables nécessaires pour le tableau de bord Marchand
-- Table des produits
CREATE TABLE IF NOT EXISTS products (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  price DECIMAL(10, 2) NOT NULL,
  stock_quantity INTEGER NOT NULL DEFAULT 0,
  stock_alert_threshold INTEGER DEFAULT 10,
  unit VARCHAR(20) DEFAULT 'unité',
  image_url VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Table des clients
CREATE TABLE IF NOT EXISTS clients (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  user_id INTEGER REFERENCES users(id) NULL,
  name VA<PERSON>HAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Table des commandes
CREATE TABLE IF NOT EXISTS orders (
  id SERIAL PRIMARY KEY,
  marchand_id INTEGER REFERENCES users(id),
  client_id INTEGER REFERENCES clients(id),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  total_amount DECIMAL(10, 2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  payment_status VARCHAR(20) DEFAULT 'unpaid',
  payment_method VARCHAR(50),
  shipping_address TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Table des articles de commande
CREATE TABLE IF NOT EXISTS order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id),
  product_id INTEGER REFERENCES products(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Table des vues de produits (pour analytics)
CREATE TABLE IF NOT EXISTS product_views (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id),
  user_id INTEGER REFERENCES users(id) NULL,
  ip_address VARCHAR(50),
  view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_products_marchand_id ON products(marchand_id);
CREATE INDEX IF NOT EXISTS idx_clients_marchand_id ON clients(marchand_id);
CREATE INDEX IF NOT EXISTS idx_orders_marchand_id ON orders(marchand_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_product_views_product_id ON product_views(product_id);
-- Fonction pour mettre à jour le timestamp 'updated_at'
CREATE OR REPLACE FUNCTION update_modified_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = now();
RETURN NEW;
END;
$$ language 'plpgsql';
-- Triggers pour mettre à jour automatiquement 'updated_at'
CREATE TRIGGER update_products_modtime BEFORE
UPDATE ON products FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
CREATE TRIGGER update_clients_modtime BEFORE
UPDATE ON clients FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
CREATE TRIGGER update_orders_modtime BEFORE
UPDATE ON orders FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
-- Commentaires sur les tables et colonnes pour la documentation
COMMENT ON TABLE products IS 'Produits proposés par les marchands';
COMMENT ON TABLE clients IS 'Clients des marchands';
COMMENT ON TABLE orders IS 'Commandes passées auprès des marchands';
COMMENT ON TABLE order_items IS 'Articles inclus dans les commandes';
COMMENT ON TABLE product_views IS 'Statistiques de consultation des produits';
