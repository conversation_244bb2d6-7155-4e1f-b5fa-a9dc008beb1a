#!/usr/bin/env node

/**
 * <PERSON>ript to test the settings API endpoints
 */

require('dotenv').config();
const axios = require('axios');
const colors = require('colors/safe');

// Configuration
const API_URL = `http://localhost:${process.env.PORT || 3003}/api`;
let authToken = null;

// Helper function to print colored output
const log = {
  info: (msg) => console.log(colors.cyan(msg)),
  success: (msg) => console.log(colors.green(`✅ ${msg}`)),
  error: (msg, err) => {
    console.error(colors.red(`❌ ${msg}`));
    if (err) {
      console.error(colors.red('Error details:'));
      console.error(err.response?.data || err.message || err);
    }
  },
  separator: () => console.log(colors.gray('------------------------------------------------------'))
};

// Setup axios instance with auth token
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add interceptor to handle errors consistently
api.interceptors.response.use(
  response => response,
  error => {
    log.error(`Request failed: ${error.config.method.toUpperCase()} ${error.config.url}`, error);
    return Promise.reject(error);
  }
);

// Function to login and get auth token
async function login() {
  log.info('Logging in as admin...');
  try {
    const response = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    authToken = response.data.token;
    api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
    log.success('Login successful');
    return true;
  } catch (error) {
    log.error('Login failed', error);
    return false;
  }
}

// Test general settings endpoints
async function testGeneralSettings() {
  log.separator();
  log.info('Testing General Settings Endpoints');

  try {
    // Get general settings
    const getResponse = await api.get('/admin/settings/general');
    log.success('Retrieved general settings');
    console.log(JSON.stringify(getResponse.data, null, 2));

    // Update general settings
    const updateData = {
      siteName: 'Poultray DZ Test',
      siteDescription: 'Testing the settings API',
      contactEmail: '<EMAIL>'
    };

    const updateResponse = await api.post('/admin/settings/general', updateData);
    log.success('Updated general settings');
    console.log(JSON.stringify(updateResponse.data, null, 2));

    // Verify update worked
    const verifyResponse = await api.get('/admin/settings/general');
    const updatedSettings = verifyResponse.data;

    if (updatedSettings.siteName === updateData.siteName &&
        updatedSettings.siteDescription === updateData.siteDescription) {
      log.success('Settings were correctly updated in the database');
    } else {
      log.error('Settings were not correctly updated');
    }

    return true;
  } catch (error) {
    log.error('General settings test failed', error);
    return false;
  }
}

// Test SMTP settings endpoints
async function testSmtpSettings() {
  log.separator();
  log.info('Testing SMTP Settings Endpoints');

  try {
    // Get SMTP settings
    const getResponse = await api.get('/admin/settings/smtp');
    log.success('Retrieved SMTP settings');
    console.log(JSON.stringify(getResponse.data, null, 2));

    // Update SMTP settings
    const updateData = {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      user: '<EMAIL>',
      pass: 'testpassword',
      fromName: 'Poultray DZ Test',
      fromEmail: '<EMAIL>',
      testEmailRecipient: '<EMAIL>'
    };

    const updateResponse = await api.post('/admin/settings/smtp', updateData);
    log.success('Updated SMTP settings');
    console.log(JSON.stringify(updateResponse.data, null, 2));

    // Verify update worked
    const verifyResponse = await api.get('/admin/settings/smtp');
    const updatedSettings = verifyResponse.data;

    if (updatedSettings.host === updateData.host &&
        updatedSettings.port === updateData.port) {
      log.success('SMTP settings were correctly updated in the database');
    } else {
      log.error('SMTP settings were not correctly updated');
    }

    // Skipping the actual SMTP test to avoid sending emails
    log.info('Skipping SMTP test functionality to avoid sending actual emails');

    return true;
  } catch (error) {
    log.error('SMTP settings test failed', error);
    return false;
  }
}

// Test security settings endpoints
async function testSecuritySettings() {
  log.separator();
  log.info('Testing Security Settings Endpoints');

  try {
    // Get security settings
    const getResponse = await api.get('/admin/settings/security');
    log.success('Retrieved security settings');
    console.log(JSON.stringify(getResponse.data, null, 2));

    // Update security settings
    const updateData = {
      sessionTimeout: 60,
      maxLoginAttempts: 3,
      lockoutDuration: 30,
      passwordComplexityRegex: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[A-Za-z\\d]{10,}$',
      passwordExpiryDays: 60,
      apiRateLimitingEnabled: true,
      apiRateLimitRequests: 200
    };

    const updateResponse = await api.post('/admin/settings/security', updateData);
    log.success('Updated security settings');
    console.log(JSON.stringify(updateResponse.data, null, 2));

    // Verify update worked
    const verifyResponse = await api.get('/admin/settings/security');
    const updatedSettings = verifyResponse.data;

    if (updatedSettings.sessionTimeout === updateData.sessionTimeout &&
        updatedSettings.maxLoginAttempts === updateData.maxLoginAttempts) {
      log.success('Security settings were correctly updated in the database');
    } else {
      log.error('Security settings were not correctly updated');
    }

    return true;
  } catch (error) {
    log.error('Security settings test failed', error);
    return false;
  }
}

// Main test function
async function runTests() {
  log.info('Starting settings API tests...');

  // Login first
  const loggedIn = await login();
  if (!loggedIn) {
    log.error('Cannot continue without login, exiting tests');
    process.exit(1);
  }

  // Run all tests
  let success = true;

  success = await testGeneralSettings() && success;
  success = await testSmtpSettings() && success;
  success = await testSecuritySettings() && success;

  log.separator();
  if (success) {
    log.success('All settings API tests completed successfully');
  } else {
    log.error('Some settings API tests failed');
    process.exit(1);
  }
}

// Run the tests
runTests();
