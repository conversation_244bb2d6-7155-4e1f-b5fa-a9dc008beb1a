/**
 * Utilitaire de vérification et correction des problèmes d'authentification
 * Pour le tableau de bord administrateur de Poultray DZ
 */

// Fonction pour décoder un token JWT sans vérification
export function decodeJWT(token) {
  if (!token) return null;
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error('Erreur lors du décodage du token:', e);
    return null;
  }
}

// Vérifier si le token est expiré
export function isTokenExpired(token) {
  const decodedToken = decodeJWT(token);
  if (!decodedToken || !decodedToken.exp) return true;
  
  const expirationDate = new Date(decodedToken.exp * 1000);
  const now = new Date();
  return expirationDate < now;
}

// Vérifier si l'utilisateur a le rôle admin
export function isUserAdmin(token) {
  const decodedToken = decodeJWT(token);
  if (!decodedToken || !decodedToken.user || !decodedToken.user.role) return false;
  
  return decodedToken.user.role === 'admin';
}

// Vérifier l'état de l'authentification
export function checkAuthStatus() {
  const token = localStorage.getItem('token');
  
  if (!token) {
    return {
      isAuthenticated: false,
      isAdmin: false,
      isExpired: true,
      message: 'Aucun token trouvé. Veuillez vous connecter.'
    };
  }
  
  const expired = isTokenExpired(token);
  const admin = isUserAdmin(token);
  
  if (expired) {
    return {
      isAuthenticated: false,
      isAdmin: admin,
      isExpired: true,
      message: 'Votre session a expiré. Veuillez vous reconnecter.'
    };
  }
  
  if (!admin) {
    return {
      isAuthenticated: true,
      isAdmin: false,
      isExpired: false,
      message: 'Vous n\'avez pas les droits d\'administrateur nécessaires.'
    };
  }
  
  return {
    isAuthenticated: true,
    isAdmin: true,
    isExpired: false,
    message: 'Authentification valide avec droits d\'administrateur.'
  };
}

// Fonction pour rediriger l'utilisateur en fonction de son statut d'authentification
export function handleAuthRedirect() {
  const status = checkAuthStatus();
  
  if (!status.isAuthenticated) {
    // Rediriger vers la page de connexion
    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
    return false;
  }
  
  if (!status.isAdmin) {
    // Rediriger vers le tableau de bord standard
    window.location.href = '/dashboard';
    return false;
  }
  
  return true;
}

// Fonction pour rafraîchir le token si nécessaire
export async function refreshTokenIfNeeded() {
  const token = localStorage.getItem('token');
  
  if (!token) return false;
  
  // Si le token est sur le point d'expirer (moins de 5 minutes restantes)
  const decodedToken = decodeJWT(token);
  if (!decodedToken || !decodedToken.exp) return false;
  
  const expirationDate = new Date(decodedToken.exp * 1000);
  const now = new Date();
  const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
  
  if (expirationDate < fiveMinutesFromNow) {
    try {
      // Appeler l'API pour rafraîchir le token
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.token) {
          localStorage.setItem('token', data.token);
          return true;
        }
      }
    } catch (error) {
      console.error('Erreur lors du rafraîchissement du token:', error);
    }
  }
  
  return false;
}
