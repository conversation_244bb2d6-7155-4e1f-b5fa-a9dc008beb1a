const { body, param, query, validationResult } = require('express-validator');
const { FeedItem, FeedSupplier, FeedStock, FeedPlan, Ferme } = require('../models');
const jwt = require('jsonwebtoken');

/**
 * Feed Management Middleware
 * Validation, authentication, and authorization for feed operations
 */

// ==================== AUTHENTICATION & AUTHORIZATION ====================

/**
 * Verify JWT token and extract user information
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Token d\'accès requis'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Token invalide ou expiré'
      });
    }
    req.user = user;
    next();
  });
};

/**
 * Check if user has required role for feed management
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Permissions insuffisantes pour cette opération'
      });
    }

    next();
  };
};

/**
 * Check if user has access to the specified farm
 */
const checkFarmAccess = async (req, res, next) => {
  try {
    const farmId = req.params.farmId || req.body.farm_id || req.query.farm_id;
    
    if (!farmId) {
      return res.status(400).json({
        success: false,
        message: 'ID de ferme requis'
      });
    }

    // Admin can access all farms
    if (req.user.role === 'admin') {
      req.farmId = farmId;
      return next();
    }

    // Check if user owns the farm or is assigned to it
    const farm = await Ferme.findOne({
      where: {
        id: farmId,
        [req.user.role === 'eleveur' ? 'user_id' : 'veterinaire_id']: req.user.id
      }
    });

    if (!farm) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé à cette ferme'
      });
    }

    req.farmId = farmId;
    next();
  } catch (error) {
    console.error('Error checking farm access:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions'
    });
  }
};

// ==================== VALIDATION RULES ====================

/**
 * Validation rules for feed items
 */
const validateFeedItem = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('brand')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('La marque ne peut pas dépasser 50 caractères'),
  
  body('category')
    .isIn(['starter', 'grower', 'finisher', 'layer', 'breeder', 'supplement', 'premix'])
    .withMessage('Catégorie invalide'),
  
  body('poultry_types')
    .isArray({ min: 1 })
    .withMessage('Au moins un type de volaille doit être spécifié')
    .custom((value) => {
      const validTypes = ['poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade'];
      return value.every(type => validTypes.includes(type));
    })
    .withMessage('Type de volaille invalide'),
  
  body('min_age_days')
    .isInt({ min: 0, max: 1000 })
    .withMessage('Âge minimum invalide (0-1000 jours)'),
  
  body('max_age_days')
    .isInt({ min: 0, max: 1000 })
    .withMessage('Âge maximum invalide (0-1000 jours)')
    .custom((value, { req }) => {
      if (value <= req.body.min_age_days) {
        throw new Error('L\'âge maximum doit être supérieur à l\'âge minimum');
      }
      return true;
    }),
  
  body('unit_of_measure')
    .isIn(['kg', 'g', 'tonne', 'sac'])
    .withMessage('Unité de mesure invalide'),
  
  body('daily_consumption_per_bird')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Consommation journalière invalide'),
  
  body('nutritional_info')
    .optional()
    .isObject()
    .withMessage('Informations nutritionnelles doivent être un objet'),
  
  body('storage_requirements')
    .optional()
    .isObject()
    .withMessage('Exigences de stockage doivent être un objet')
];

/**
 * Validation rules for feed suppliers
 */
const validateFeedSupplier = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('phone')
    .optional()
    .matches(/^[+]?[0-9\s\-\(\)]{8,20}$/)
    .withMessage('Numéro de téléphone invalide'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Adresse email invalide'),
  
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Adresse trop longue (max 200 caractères)'),
  
  body('delivery_zones')
    .optional()
    .isArray()
    .withMessage('Zones de livraison doivent être un tableau'),
  
  body('payment_terms')
    .optional()
    .isIn(['cash', 'credit_7', 'credit_15', 'credit_30', 'credit_60'])
    .withMessage('Conditions de paiement invalides'),
  
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Limite de crédit invalide'),
  
  body('rating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Note invalide (0-5)')
];

/**
 * Validation rules for feed stock
 */
const validateFeedStock = [
  body('farm_id')
    .isInt({ min: 1 })
    .withMessage('ID de ferme invalide'),
  
  body('feed_item_id')
    .isInt({ min: 1 })
    .withMessage('ID d\'aliment invalide'),
  
  body('supplier_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID de fournisseur invalide'),
  
  body('batch_number')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Numéro de lot trop long'),
  
  body('quantity_received')
    .isFloat({ min: 0.1 })
    .withMessage('Quantité reçue invalide'),
  
  body('unit_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Coût unitaire invalide'),
  
  body('purchase_date')
    .optional()
    .isISO8601()
    .withMessage('Date d\'achat invalide'),
  
  body('expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Date d\'expiration invalide')
    .custom((value, { req }) => {
      if (value && req.body.purchase_date && new Date(value) <= new Date(req.body.purchase_date)) {
        throw new Error('La date d\'expiration doit être postérieure à la date d\'achat');
      }
      return true;
    }),
  
  body('storage_location')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Emplacement de stockage trop long'),
  
  body('low_stock_threshold')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Seuil de stock bas invalide')
];

/**
 * Validation rules for feed consumption logs
 */
const validateFeedConsumption = [
  body('farm_id')
    .isInt({ min: 1 })
    .withMessage('ID de ferme invalide'),
  
  body('feed_stock_id')
    .isInt({ min: 1 })
    .withMessage('ID de stock d\'aliment invalide'),
  
  body('poultry_batch_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID de lot de volailles invalide'),
  
  body('quantity_consumed')
    .isFloat({ min: 0.1 })
    .withMessage('Quantité consommée invalide'),
  
  body('consumption_date')
    .isISO8601()
    .withMessage('Date de consommation invalide')
    .custom((value) => {
      const date = new Date(value);
      const now = new Date();
      if (date > now) {
        throw new Error('La date de consommation ne peut pas être dans le futur');
      }
      return true;
    }),
  
  body('consumption_time')
    .optional()
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Heure de consommation invalide (HH:MM)'),
  
  body('feeding_method')
    .optional()
    .isIn(['manual', 'automatic', 'semi_automatic'])
    .withMessage('Méthode d\'alimentation invalide'),
  
  body('number_of_birds')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Nombre d\'oiseaux invalide'),
  
  body('average_bird_weight')
    .optional()
    .isFloat({ min: 0.01, max: 10 })
    .withMessage('Poids moyen des oiseaux invalide'),
  
  body('weather_conditions')
    .optional()
    .isIn(['sunny', 'cloudy', 'rainy', 'windy', 'hot', 'cold'])
    .withMessage('Conditions météorologiques invalides'),
  
  body('temperature')
    .optional()
    .isFloat({ min: -10, max: 50 })
    .withMessage('Température invalide (-10°C à 50°C)'),
  
  body('humidity')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Humidité invalide (0-100%)')
];

/**
 * Validation rules for feed plans
 */
const validateFeedPlan = [
  body('farm_id')
    .isInt({ min: 1 })
    .withMessage('ID de ferme invalide'),
  
  body('poultry_batch_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID de lot de volailles invalide'),
  
  body('plan_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nom du plan invalide (2-100 caractères)'),
  
  body('plan_type')
    .isIn(['standard', 'custom', 'veterinary', 'organic'])
    .withMessage('Type de plan invalide'),
  
  body('poultry_type')
    .isIn(['poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade'])
    .withMessage('Type de volaille invalide'),
  
  body('age_range_start')
    .isInt({ min: 0, max: 1000 })
    .withMessage('Âge de début invalide'),
  
  body('age_range_end')
    .isInt({ min: 0, max: 1000 })
    .withMessage('Âge de fin invalide')
    .custom((value, { req }) => {
      if (value <= req.body.age_range_start) {
        throw new Error('L\'âge de fin doit être supérieur à l\'âge de début');
      }
      return true;
    }),
  
  body('target_weight')
    .optional()
    .isFloat({ min: 0.1, max: 20 })
    .withMessage('Poids cible invalide'),
  
  body('daily_feed_amount')
    .isFloat({ min: 1 })
    .withMessage('Quantité journalière d\'aliment invalide'),
  
  body('feeding_frequency')
    .isInt({ min: 1, max: 10 })
    .withMessage('Fréquence d\'alimentation invalide (1-10)'),
  
  body('feeding_times')
    .isArray({ min: 1 })
    .withMessage('Horaires d\'alimentation requis')
    .custom((value) => {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      return value.every(time => timeRegex.test(time));
    })
    .withMessage('Format d\'heure invalide (HH:MM)'),
  
  body('start_date')
    .isISO8601()
    .withMessage('Date de début invalide'),
  
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('Date de fin invalide')
    .custom((value, { req }) => {
      if (value && new Date(value) <= new Date(req.body.start_date)) {
        throw new Error('La date de fin doit être postérieure à la date de début');
      }
      return true;
    })
];

// ==================== VALIDATION MIDDLEWARE ====================

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Erreurs de validation',
      errors: errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

/**
 * Validate pagination parameters
 */
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Numéro de page invalide'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite invalide (1-100)'),
  
  query('sort_by')
    .optional()
    .isIn(['name', 'created_at', 'updated_at', 'quantity', 'cost', 'date'])
    .withMessage('Critère de tri invalide'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Ordre de tri invalide (ASC/DESC)')
];

/**
 * Validate date range parameters
 */
const validateDateRange = [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Date de début invalide'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Date de fin invalide')
    .custom((value, { req }) => {
      if (value && req.query.start_date && new Date(value) <= new Date(req.query.start_date)) {
        throw new Error('La date de fin doit être postérieure à la date de début');
      }
      return true;
    })
];

// ==================== BUSINESS LOGIC VALIDATION ====================

/**
 * Check if feed item exists and is active
 */
const validateFeedItemExists = async (req, res, next) => {
  try {
    const feedItemId = req.params.feedItemId || req.body.feed_item_id;
    
    if (!feedItemId) {
      return res.status(400).json({
        success: false,
        message: 'ID d\'aliment requis'
      });
    }

    const feedItem = await FeedItem.findOne({
      where: {
        id: feedItemId,
        status: 'active'
      }
    });

    if (!feedItem) {
      return res.status(404).json({
        success: false,
        message: 'Aliment non trouvé ou inactif'
      });
    }

    req.feedItem = feedItem;
    next();
  } catch (error) {
    console.error('Error validating feed item:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de l\'aliment'
    });
  }
};

/**
 * Check if feed stock exists and has sufficient quantity
 */
const validateFeedStockAvailability = async (req, res, next) => {
  try {
    const feedStockId = req.params.stockId || req.body.feed_stock_id;
    const quantityNeeded = req.body.quantity_consumed;
    
    if (!feedStockId) {
      return res.status(400).json({
        success: false,
        message: 'ID de stock d\'aliment requis'
      });
    }

    const feedStock = await FeedStock.findOne({
      where: {
        id: feedStockId,
        status: 'active'
      }
    });

    if (!feedStock) {
      return res.status(404).json({
        success: false,
        message: 'Stock d\'aliment non trouvé ou inactif'
      });
    }

    if (quantityNeeded && feedStock.quantity_current < quantityNeeded) {
      return res.status(400).json({
        success: false,
        message: `Stock insuffisant. Disponible: ${feedStock.quantity_current}, Requis: ${quantityNeeded}`
      });
    }

    req.feedStock = feedStock;
    next();
  } catch (error) {
    console.error('Error validating feed stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation du stock'
    });
  }
};

/**
 * Rate limiting for feed operations
 */
const rateLimitFeedOperations = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    const userId = req.user?.id;
    if (!userId) {
      return next();
    }
    
    const now = Date.now();
    const userRequests = requests.get(userId) || [];
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Trop de requêtes. Veuillez réessayer plus tard.'
      });
    }
    
    validRequests.push(now);
    requests.set(userId, validRequests);
    
    next();
  };
};

module.exports = {
  // Authentication & Authorization
  authenticateToken,
  requireRole,
  checkFarmAccess,
  
  // Validation Rules
  validateFeedItem,
  validateFeedSupplier,
  validateFeedStock,
  validateFeedConsumption,
  validateFeedPlan,
  
  // Validation Middleware
  handleValidationErrors,
  validatePagination,
  validateDateRange,
  
  // Business Logic Validation
  validateFeedItemExists,
  validateFeedStockAvailability,
  
  // Rate Limiting
  rateLimitFeedOperations
};