import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  CircularProgress,
  Snackbar,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Send as SendIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  NotificationsActive as PushIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';
import { useAuth } from '../../contexts/AuthContext';

const Notifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [confirmDelete, setConfirmDelete] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info',
    recipients: 'all', // 'all', 'role', 'specific'
    roleId: '',
    userId: '',
    notificationType: 'both', // 'email', 'push', 'both'
  });

  // Fetch notifications
  useEffect(() => {
    fetchNotifications();
    fetchUsers();
  }, [page]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(`/admin/notifications?page=${page}&limit=10`);
      setNotifications(response.data.notifications || []);
      setTotalPages(response.data.pagination?.totalPages || 1);
      setError('');
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Erreur lors de la récupération des notifications');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await axiosInstance.get('/admin/users');
      setUsers(response.data.users);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const notificationData = { ...formData };

      // Adjust data based on recipient type
      if (formData.recipients === 'all') {
        delete notificationData.roleId;
        delete notificationData.userId;
      } else if (formData.recipients === 'role') {
        delete notificationData.userId;
      } else if (formData.recipients === 'specific') {
        delete notificationData.roleId;
      }

      const response = await axiosInstance.post('/admin/notifications/send', notificationData);
      setSuccess('Notification envoyée avec succès');
      fetchNotifications();
      handleCloseDialog();
    } catch (err) {
      console.error('Error sending notification:', err);
      setError(err.response?.data?.message || 'Erreur lors de l\'envoi de la notification');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfirm = (notificationId) => {
    setConfirmDelete(notificationId);
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      await axiosInstance.delete(`/admin/notifications/${confirmDelete}`);
      setSuccess('Notification supprimée avec succès');
      fetchNotifications();
      setConfirmDelete(null);
    } catch (err) {
      console.error('Error deleting notification:', err);
      setError(err.response?.data?.message || 'Erreur lors de la suppression de la notification');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const handleSendTestNotification = async () => {
    try {
      setLoading(true);
      await axiosInstance.post('/admin/notifications/test');
      setSuccess('Notification de test envoyée avec succès');
    } catch (err) {
      console.error('Error sending test notification:', err);
      setError(err.response?.data?.message || 'Erreur lors de l\'envoi de la notification de test');
    } finally {
      setLoading(false);
    }
  };

  const getNotificationTypeIcon = (type) => {
    switch (type) {
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'push':
        return <PushIcon fontSize="small" />;
      case 'both':
        return (
          <Box sx={{ display: 'flex' }}>
            <EmailIcon fontSize="small" />
            <PushIcon fontSize="small" />
          </Box>
        );
      default:
        return <NotificationsIcon fontSize="small" />;
    }
  };

  const getNotificationTypeLabel = (type) => {
    switch (type) {
      case 'email':
        return 'Email';
      case 'push':
        return 'Push';
      case 'both':
        return 'Email & Push';
      default:
        return 'Inconnu';
    }
  };

  const getNotificationStatusChip = (status) => {
    switch (status) {
      case 'sent':
        return <Chip label="Envoyée" color="success" size="small" />;
      case 'failed':
        return <Chip label="Échec" color="error" size="small" />;
      case 'pending':
        return <Chip label="En attente" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationsIcon color="primary" /> Gestion des Notifications
          </Typography>
          <Box>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<SendIcon />}
              onClick={handleSendTestNotification}
              sx={{ mr: 1 }}
            >
              Notification de Test
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<NotificationsIcon />}
              onClick={handleOpenDialog}
            >
              Nouvelle Notification
            </Button>
          </Box>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab label="Historique des Notifications" />
          <Tab label="Paramètres" />
        </Tabs>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        {tabValue === 0 && (
          <>
            {loading && !notifications.length ? (
              <Box display="flex" justifyContent="center" my={4}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper} sx={{ mt: 2 }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Titre</TableCell>
                        <TableCell>Message</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Méthode</TableCell>
                        <TableCell>Destinataires</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Statut</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {notifications.length > 0 ? (
                        notifications.map((notification) => (
                          <TableRow key={notification.id}>
                            <TableCell>{notification.title}</TableCell>
                            <TableCell>{notification.message.substring(0, 50)}...</TableCell>
                            <TableCell>
                              <Chip
                                label={notification.type}
                                color={notification.type === 'info' ? 'info' : notification.type === 'warning' ? 'warning' : 'error'}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {getNotificationTypeIcon(notification.notificationType)}
                              {getNotificationTypeLabel(notification.notificationType)}
                            </TableCell>
                            <TableCell>
                              {notification.recipients === 'all' ? 'Tous les utilisateurs' :
                               notification.recipients === 'role' ? `Rôle: ${notification.roleName}` :
                               `Utilisateur: ${notification.userName}`}
                            </TableCell>
                            <TableCell>{new Date(notification.created_at).toLocaleDateString()}</TableCell>
                            <TableCell>{getNotificationStatusChip(notification.status)}</TableCell>
                            <TableCell align="right">
                              <IconButton
                                color="error"
                                onClick={() => handleDeleteConfirm(notification.id)}
                                size="small"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} align="center">
                            Aucune notification trouvée
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                {totalPages > 1 && (
                  <Box display="flex" justifyContent="center" mt={3}>
                    <Pagination
                      count={totalPages}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                    />
                  </Box>
                )}
              </>
            )}
          </>
        )}

        {tabValue === 1 && (
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SettingsIcon /> Paramètres de Notification
            </Typography>
            <Divider sx={{ my: 2 }} />
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <EmailIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Configuration Email
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Configurez les paramètres de notification par email pour la plateforme.
                    </Typography>
                    <TextField
                      label="Email expéditeur"
                      fullWidth
                      margin="normal"
                      defaultValue="<EMAIL>"
                    />
                    <TextField
                      label="Nom expéditeur"
                      fullWidth
                      margin="normal"
                      defaultValue="Poultray DZ"
                    />
                  </CardContent>
                  <CardActions>
                    <Button size="small" color="primary">
                      Enregistrer
                    </Button>
                    <Button size="small" onClick={handleSendTestNotification}>
                      Tester
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <PushIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Configuration Push
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Configurez les paramètres de notification push pour la plateforme.
                    </Typography>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Activer les notifications push</InputLabel>
                      <Select
                        value="enabled"
                        label="Activer les notifications push"
                      >
                        <MenuItem value="enabled">Activé</MenuItem>
                        <MenuItem value="disabled">Désactivé</MenuItem>
                      </Select>
                    </FormControl>
                    <TextField
                      label="Firebase Server Key"
                      fullWidth
                      margin="normal"
                      type="password"
                      defaultValue="••••••••••••••••••••••••••••••••"
                    />
                  </CardContent>
                  <CardActions>
                    <Button size="small" color="primary">
                      Enregistrer
                    </Button>
                    <Button size="small" onClick={handleSendTestNotification}>
                      Tester
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        )}
      </Paper>

      {/* Dialog for creating notifications */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>Nouvelle Notification</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="title"
            label="Titre"
            type="text"
            fullWidth
            value={formData.title}
            onChange={handleChange}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="message"
            label="Message"
            multiline
            rows={4}
            fullWidth
            value={formData.message}
            onChange={handleChange}
            required
            sx={{ mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="dense">
                <InputLabel>Type de notification</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  label="Type de notification"
                  onChange={handleChange}
                >
                  <MenuItem value="info">Information</MenuItem>
                  <MenuItem value="warning">Avertissement</MenuItem>
                  <MenuItem value="error">Erreur</MenuItem>
                  <MenuItem value="success">Succès</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="dense">
                <InputLabel>Méthode d'envoi</InputLabel>
                <Select
                  name="notificationType"
                  value={formData.notificationType}
                  label="Méthode d'envoi"
                  onChange={handleChange}
                >
                  <MenuItem value="email">Email uniquement</MenuItem>
                  <MenuItem value="push">Push uniquement</MenuItem>
                  <MenuItem value="both">Email et Push</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
            <InputLabel>Destinataires</InputLabel>
            <Select
              name="recipients"
              value={formData.recipients}
              label="Destinataires"
              onChange={handleChange}
            >
              <MenuItem value="all">Tous les utilisateurs</MenuItem>
              <MenuItem value="role">Par rôle</MenuItem>
              <MenuItem value="specific">Utilisateur spécifique</MenuItem>
            </Select>
          </FormControl>

          {formData.recipients === 'role' && (
            <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
              <InputLabel>Rôle</InputLabel>
              <Select
                name="roleId"
                value={formData.roleId}
                label="Rôle"
                onChange={handleChange}
              >
                <MenuItem value="1">Administrateur</MenuItem>
                <MenuItem value="2">Éleveur</MenuItem>
                <MenuItem value="3">Marchand</MenuItem>
                <MenuItem value="4">Vétérinaire</MenuItem>
              </Select>
            </FormControl>
          )}

          {formData.recipients === 'specific' && (
            <FormControl fullWidth margin="dense" sx={{ mt: 2 }}>
              <InputLabel>Utilisateur</InputLabel>
              <Select
                name="userId"
                value={formData.userId}
                label="Utilisateur"
                onChange={handleChange}
              >
                {users.map((user) => (
                  <MenuItem key={user.id} value={user.id}>
                    {user.username} ({user.email})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary" disabled={!formData.title || !formData.message}>
            {loading ? <CircularProgress size={24} /> : 'Envoyer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation dialog for delete */}
      <Dialog open={!!confirmDelete} onClose={() => setConfirmDelete(null)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>Êtes-vous sûr de vouloir supprimer cette notification ? Cette action est irréversible.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDelete(null)}>Annuler</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            {loading ? <CircularProgress size={24} /> : 'Supprimer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={!!error || !!success}
        autoHideDuration={6000}
        onClose={() => {
          setError('');
          setSuccess('');
        }}
      >
        <Alert
          onClose={() => {
            setError('');
            setSuccess('');
          }}
          severity={error ? 'error' : 'success'}
          sx={{ width: '100%' }}
        >
          {error || success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Notifications;
