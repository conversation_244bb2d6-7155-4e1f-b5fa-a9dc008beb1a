import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Box,
  MenuItem,
  Grid,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import axiosInstance from '../utils/axiosConfig';

function VolaillesList() {
  const [volailles, setVolailles] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [open, setOpen] = useState(false);
  const [editingVolaille, setEditingVolaille] = useState(null);
  const [formData, setFormData] = useState({
    eleveur_id: '',
    espece: '',
    race: '',
    age: '',
    poids: '',
    quantite: '',
    prix_unitaire: '',
    description: '',
  });

  useEffect(() => {
    fetchVolailles();
    fetchEleveurs();
  }, []);

  const fetchVolailles = async () => {
    try {
      const response = await axiosInstance.get('/volailles');
      // S'assurer que response.data est un tableau
      const volaillesData = Array.isArray(response.data) ? response.data : response.data.volailles || [];
      setVolailles(volaillesData);
    } catch (error) {
      console.error('Erreur lors de la récupération des volailles:', error);
      setVolailles([]); // Définir un tableau vide en cas d'erreur
    }
  };

  const fetchEleveurs = async () => {
    try {
      const response = await axiosInstance.get('/eleveurs');
      // S'assurer que response.data est un tableau
      const eleveursData = Array.isArray(response.data) ? response.data : response.data.eleveurs || [];
      setEleveurs(eleveursData);
    } catch (error) {
      console.error('Erreur lors de la récupération des éleveurs:', error);
      setEleveurs([]); // Définir un tableau vide en cas d'erreur
    }
  };

  const handleOpen = (volaille = null) => {
    if (volaille) {
      setEditingVolaille(volaille);
      setFormData({
        ...volaille,
        eleveur_id: volaille.eleveur_id?.toString() || '',
      });
    } else {
      setEditingVolaille(null);
      setFormData({
        eleveur_id: '',
        espece: '',
        race: '',
        age: '',
        poids: '',
        quantite: '',
        prix_unitaire: '',
        description: '',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingVolaille(null);
    setFormData({
      eleveur_id: '',
      espece: '',
      race: '',
      age: '',
      poids: '',
      quantite: '',
      prix_unitaire: '',
      description: '',
    });
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const submitData = {
        ...formData,
        age: parseInt(formData.age) || null,
        poids: parseFloat(formData.poids) || null,
        quantite: parseInt(formData.quantite) || 0,
        prix_unitaire: parseFloat(formData.prix_unitaire) || 0,
      };

      if (editingVolaille) {
        await axiosInstance.put(`/api/volailles/${editingVolaille.id}`, submitData);
      } else {
        await axiosInstance.post('/api/volailles', submitData);
      }
      handleClose();
      fetchVolailles();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement:', error);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette volaille ?')) {
      try {
        await axiosInstance.delete(`/api/volailles/${id}`);
        fetchVolailles();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
      }
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Liste des Volailles
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Ajouter une Volaille
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Éleveur</TableCell>
              <TableCell>Espèce</TableCell>
              <TableCell>Race</TableCell>
              <TableCell>Âge</TableCell>
              <TableCell>Poids (kg)</TableCell>
              <TableCell>Quantité</TableCell>
              <TableCell>Prix Unitaire (DA)</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.isArray(volailles) && volailles.map((volaille) => (
              <TableRow key={volaille.id}>
                <TableCell>{`${volaille.eleveur_nom || ''} ${volaille.eleveur_prenom || ''}`}</TableCell>
                <TableCell>{volaille.espece}</TableCell>
                <TableCell>{volaille.race}</TableCell>
                <TableCell>{volaille.age ? `${volaille.age} jours` : '-'}</TableCell>
                <TableCell>{volaille.poids || '-'}</TableCell>
                <TableCell>{volaille.quantite}</TableCell>
                <TableCell>{volaille.prix_unitaire}</TableCell>
                <TableCell>
                  <IconButton
                    color="primary"
                    onClick={() => handleOpen(volaille)}
                    size="small"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    color="error"
                    onClick={() => handleDelete(volaille.id)}
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingVolaille ? 'Modifier la volaille' : 'Ajouter une volaille'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 2 }} onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Éleveur"
                  name="eleveur_id"
                  value={formData.eleveur_id}
                  onChange={handleChange}
                  margin="normal"
                  required
                >
                  {Array.isArray(eleveurs) && eleveurs.map((eleveur) => (
                    <MenuItem key={eleveur.id} value={eleveur.id.toString()}>
                      {`${eleveur.nom} ${eleveur.prenom}`}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Espèce"
                  name="espece"
                  value={formData.espece}
                  onChange={handleChange}
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Race"
                  name="race"
                  value={formData.race}
                  onChange={handleChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Âge (jours)"
                  name="age"
                  type="number"
                  value={formData.age}
                  onChange={handleChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Poids (kg)"
                  name="poids"
                  type="number"
                  value={formData.poids}
                  onChange={handleChange}
                  margin="normal"
                  inputProps={{ step: '0.1' }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Quantité"
                  name="quantite"
                  type="number"
                  value={formData.quantite}
                  onChange={handleChange}
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Prix Unitaire (DA)"
                  name="prix_unitaire"
                  type="number"
                  value={formData.prix_unitaire}
                  onChange={handleChange}
                  margin="normal"
                  required
                  inputProps={{ step: '0.01' }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  margin="normal"
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editingVolaille ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}

export default VolaillesList;
