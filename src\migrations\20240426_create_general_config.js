const { DataTypes } = require('sequelize');

/**
 * Migration to create the general configuration table
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Direct creation - Sequelize handles existence gracefully
    await queryInterface.createTable('general_config', {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        siteName: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: 'Poultray DZ',
        },
        siteDescription: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        contactEmail: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        contactPhone: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        address: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        logo: {
          type: DataTypes.STRING, // URL to the logo image
          allowNull: true,
        },
        favicon: {
          type: DataTypes.STRING, // URL to the favicon
          allowNull: true,
        },
        primaryColor: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: '#2c5530', // Default green color
        },
        secondaryColor: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: '#e7eae2', // Light green/gray
        },
        defaultLanguage: {
          type: DataTypes.STRING,
          allowNull: false,
          defaultValue: 'fr', // French as default
        },
        availableLanguages: {
          type: DataTypes.TEXT, // JSON string of available languages
          allowNull: false,
          defaultValue: JSON.stringify(['fr', 'ar', 'en']),
        },
        dateFormat: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'DD/MM/YYYY',
        },
        timeFormat: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'HH:mm',
        },
        timezone: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'Africa/Algiers',
        },
        maintenanceMode: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        maintenanceMessage: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        googleAnalyticsId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        facebookPixelId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        maxUploadSize: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 5, // In MB
        },
        allowUserRegistration: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        defaultUserRole: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'user',
        },
        footerText: {
          type: DataTypes.TEXT,
          allowNull: true,
          defaultValue: '© Poultray DZ',
        },
        socialLinks: {
          type: DataTypes.TEXT, // JSON string of social media links
          allowNull: true,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
        }
      });

      // Add default general configuration
      await queryInterface.bulkInsert('general_config', [{
        siteName: 'Poultray DZ',
        siteDescription: 'Plateforme de l\'industrie avicole en Algérie',
        contactEmail: '<EMAIL>',
        contactPhone: '+213 XXXXXXXXX',
        address: 'Alger, Algérie',
        logo: '/assets/logo.png',
        favicon: '/assets/favicon.ico',
        primaryColor: '#2c5530',
        secondaryColor: '#e7eae2',
        defaultLanguage: 'fr',
        availableLanguages: JSON.stringify(['fr', 'ar', 'en']),
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        timezone: 'Africa/Algiers',
        maintenanceMode: false,
        maintenanceMessage: 'Le site est actuellement en maintenance. Merci de revenir plus tard.',
        googleAnalyticsId: null,
        facebookPixelId: null,
        maxUploadSize: 5,
        allowUserRegistration: true,
        defaultUserRole: 'user',
        footerText: '© Poultray DZ - Tous droits réservés',
        socialLinks: JSON.stringify({
          facebook: 'https://facebook.com/poultraydz',
          twitter: 'https://twitter.com/poultraydz',
          instagram: 'https://instagram.com/poultraydz'
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      }]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('general_config');
  }
};
