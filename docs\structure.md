# Poultry DZ Platform Structure

Here is the comprehensive structure of your Poultry DZ platform, organized by main components and directories:

## Overview

The platform consists of three main components:
- **Backend**: Node.js/Express API server with PostgreSQL database
- **Frontend**: React + Vite + Material-UI web application
- **Mobile App**: Flutter application with offline-first architecture

## Root Directory

Configuration, documentation, and scripts for both backend and frontend.

## Backend (Node.js/Express)

### Core Structure
- **src/**: Main backend source code
  - **controllers/**: API logic for admin, veterinary, marketplace, statistics, etc.
  - **models/**: Sequelize models for users, roles, farms, products, orders, etc.
  - **routes/**: Express route definitions for all API endpoints
  - **middleware/**: Authentication, authorization, and utility middleware
  - **services/**: Business logic, integrations (notifications, OpenAI, API configs)
  - **utils/**: Utility functions (database, cloud storage, etc.)
  - **database/**: Scripts for database setup and migrations
  - **scripts/**: Initialization and test scripts

### Additional Backend Components
- **backend/**: Extended backend services
  - **routes/**: Chat, market, ratings, verification routes
  - **services/**: Notification services
  - **utils/**: Encryption utilities
  - **websocket/**: Real-time chat server
  - **database/**: Market schema and additional database scripts

## Frontend (React + Vite + Material-UI)

### Structure
- **frontend/src/**: Main frontend source code
  - **components/**: Reusable UI components (admin, dashboards, etc.)
  - **pages/**: Main pages (admin user management, dashboards, etc.)
  - **services/**: API communication logic
  - **config/**: App configuration
  - **contexts/**: React context providers
  - **hooks/**: Custom React hooks
  - **layouts/**: Page layouts
  - **theme/**: Material-UI theming
  - **translations/**: i18n support (French/Arabic)
  - **assets/**: Static assets
  - **styles/**: CSS styles

## Mobile App (Flutter)

### Enhanced Mobile Structure
- **Mobile_App/poultry_dz_mobile/lib/**: Main Flutter source code
  - **models/**: Data models for mobile (User, FeedConsumptionLog, FeedPlan, etc.)
  - **services/**: API and business logic
    - **api/**: API service implementations (FeedApiService)
    - **database/**: Local SQLite database helper
    - **sync/**: Background synchronization services
  - **screens/**: Mobile UI screens
    - **feed/**: Feed management screens (stock list, forms, analytics)
    - **auth/**: Authentication screens
    - **dashboard/**: Role-based dashboards
  - **providers/**: State management (FeedProvider, AuthProvider)
  - **widgets/**: Reusable UI components (LoadingWidget, EmptyStateWidget)
  - **utils/**: Utility functions and constants
  - **l10n/**: Internationalization (French/Arabic)

### Mobile Testing Structure
- **test/**: Comprehensive testing suite
  - **providers/**: Unit tests for state management
  - **widgets/**: Widget tests for UI components
  - **integration/**: End-to-end workflow tests

## Documentation & Utilities

- **docs/**: Database schema, API docs, dashboard plans, troubleshooting guides
- **.env files**: Environment variables for backend and frontend
- **package.json**: Dependency management for backend and frontend
- **scripts/**: Various utility and test scripts
- **migrations/**: Database migration files

## Key Features Supported

### Multi-Role Management
- **Éleveur** (Farmer): Farm management, livestock tracking
- **Vétérinaire** (Veterinarian): Consultations, prescriptions, availability
- **Marchand** (Merchant): Product sales, inventory management
- **Acheteur** (Buyer): Product purchasing, order management
- **Admin**: System administration, user management

### Core Functionalities
- Farm/veterinary/marketplace features
- Real-time chat and notifications
- Analytics and reporting
- Multi-language support (French/Arabic)
- Mobile offline-first architecture
- Feed management system with analytics
- Background synchronization
- Secure authentication and authorization

### Recent Mobile Enhancements (v1.3.0+4)
- Complete feed management system
- Offline synchronization with SQLite
- Comprehensive testing suite
- Analytics and forecasting
- Background task management
- Network-aware data synchronization


Poultray-dz-TraeDev/Web_App/Poultraydz-Trae/
├── README.md
├── package.json
├── .env
├── .gitignore
├── docs/
│   ├── structure.md
│   ├── api-docs.md
│   ├── database-schema.md
│   └── troubleshooting.md
├── src/                           # Backend (Node.js/Express)
│   ├── index.js                   # Point d'entrée principal
│   ├── app.js                     # Configuration Express
│   ├── controllers/
│   │   ├── adminController.js
│   │   ├── authController.js
│   │   ├── veterinaireController.js
│   │   ├── iaAnalysisController.js
│   │   ├── disponibiliteController.js
│   │   ├── marketplaceController.js
│   │   ├── farmController.js
│   │   ├── orderController.js
│   │   └── statisticsController.js
│   ├── models/
│   │   ├── index.js
│   │   ├── user.js
│   │   ├── role.js
│   │   ├── veterinaire.js
│   │   ├── disponibilite.js
│   │   ├── annonce.js
│   │   ├── farm.js
│   │   ├── product.js
│   │   ├── order.js
│   │   └── consultation.js
│   ├── routes/
│   │   ├── adminRoutes.js
│   │   ├── authRoutes.js
│   │   ├── veterinaireRoutes.js
│   │   ├── iaAnalysisRoutes.js
│   │   ├── disponibiliteRoutes.js
│   │   ├── marketplaceRoutes.js
│   │   ├── farmRoutes.js
│   │   └── orderRoutes.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── roleCheck.js
│   │   ├── validation.js
│   │   └── errorHandler.js
│   ├── services/
│   │   ├── notificationService.js
│   │   ├── openaiService.js
│   │   ├── emailService.js
│   │   └── apiConfig.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── cloudStorage.js
│   │   ├── helpers.js
│   │   └── constants.js
│   ├── database/
│   │   ├── migrations/
│   │   ├── seeders/
│   │   └── config.js
│   └── scripts/
│       ├── init.js
│       └── test.js
├── frontend/                      # Frontend (React + Vite)
│   ├── package.json
│   ├── vite.config.js
│   ├── index.html
│   ├── .env
│   ├── public/
│   │   ├── favicon.ico
│   │   └── assets/
│   └── src/
│       ├── main.jsx
│       ├── App.jsx
│       ├── components/
│       │   ├── admin/
│       │   │   ├── AdminDashboard.jsx
│       │   │   ├── UserManagement.jsx
│       │   │   └── SystemSettings.jsx
│       │   ├── veterinaire/
│       │   │   ├── VeterinaireDashboard.jsx
│       │   │   ├── ProfileManagement.jsx
│       │   │   ├── CalendarManagement.jsx
│       │   │   └── IAAnalysis.jsx
│       │   ├── marketplace/
│       │   │   ├── ProductList.jsx
│       │   │   ├── ProductDetail.jsx
│       │   │   └── OrderManagement.jsx
│       │   ├── common/
│       │   │   ├── Header.jsx
│       │   │   ├── Sidebar.jsx
│       │   │   ├── Footer.jsx
│       │   │   └── LoadingSpinner.jsx
│       │   └── forms/
│       │       ├── LoginForm.jsx
│       │       ├── RegisterForm.jsx
│       │       └── ContactForm.jsx
│       ├── pages/
│       │   ├── Home.jsx
│       │   ├── Login.jsx
│       │   ├── Register.jsx
│       │   ├── Dashboard.jsx
│       │   ├── Profile.jsx
│       │   └── NotFound.jsx
│       ├── services/
│       │   ├── api.js
│       │   ├── authService.js
│       │   ├── veterinaireService.js
│       │   └── marketplaceService.js
│       ├── config/
│       │   ├── constants.js
│       │   └── apiConfig.js
│       ├── contexts/
│       │   ├── AuthContext.jsx
│       │   ├── ThemeContext.jsx
│       │   └── LanguageContext.jsx
│       ├── hooks/
│       │   ├── useAuth.js
│       │   ├── useApi.js
│       │   └── useLocalStorage.js
│       ├── layouts/
│       │   ├── MainLayout.jsx
│       │   ├── AuthLayout.jsx
│       │   └── DashboardLayout.jsx
│       ├── theme/
│       │   ├── index.js
│       │   ├── colors.js
│       │   └── typography.js
│       ├── translations/
│       │   ├── fr.json
│       │   └── ar.json
│       ├── assets/
│       │   ├── images/
│       │   ├── icons/
│       │   └── fonts/
│       └── styles/
│           ├── global.css
│           ├── components.css
│           └── utilities.css
├── Mobile_App/                    # Application Mobile (Flutter)
│   └── poultry_dz_mobile/
│       ├── pubspec.yaml
│       ├── CHANGELOG-mobile.md
│       ├── README.md
│       ├── analysis_options.yaml
│       ├── l10n.yaml
│       ├── android/
│       ├── ios/
│       ├── linux/
│       ├── macos/
│       ├── web/
│       ├── windows/
│       ├── assets/
│       ├── lib/
│       │   ├── main.dart
│       │   ├── models/
│       │   │   ├── user.dart
│       │   │   ├── farm_role.dart
│       │   │   ├── feed_consumption_log.dart
│       │   │   ├── feed_plan.dart
│       │   │   ├── product.dart
│       │   │   └── order.dart
│       │   ├── services/
│       │   │   ├── api/
│       │   │   │   ├── feed_api_service.dart
│       │   │   │   ├── api_service.dart
│       │   │   │   └── auth_service.dart
│       │   │   ├── database/
│       │   │   │   └── database_helper.dart
│       │   │   ├── sync/
│       │   │   │   └── sync_service.dart
│       │   │   └── storage_service.dart
│       │   ├── providers/
│       │   │   ├── feed_provider.dart
│       │   │   ├── auth_provider.dart
│       │   │   └── theme_provider.dart
│       │   ├── screens/
│       │   │   ├── auth/
│       │   │   │   ├── login_screen.dart
│       │   │   │   └── register_screen.dart
│       │   │   ├── feed/
│       │   │   │   ├── feed_stock_list_screen.dart
│       │   │   │   ├── feed_consumption_form_screen.dart
│       │   │   │   ├── feed_plan_form_screen.dart
│       │   │   │   └── feed_analytics_screen.dart
│       │   │   ├── dashboard/
│       │   │   │   ├── eleveur_dashboard.dart
│       │   │   │   ├── veterinaire_dashboard.dart
│       │   │   │   ├── marchand_dashboard.dart
│       │   │   │   └── admin_dashboard.dart
│       │   │   ├── home_screen.dart
│       │   │   ├── profile_screen.dart
│       │   │   └── marketplace_screen.dart
│       │   ├── widgets/
│       │   │   ├── common/
│       │   │   │   ├── loading_widget.dart
│       │   │   │   ├── empty_state_widget.dart
│       │   │   │   └── error_widget.dart
│       │   │   ├── feed/
│       │   │   │   ├── consumption_forecast_widget.dart
│       │   │   │   ├── feed_card_widget.dart
│       │   │   │   └── stock_level_indicator.dart
│       │   │   ├── custom_button.dart
│       │   │   └── product_card.dart
│       │   ├── utils/
│       │   │   ├── constants.dart
│       │   │   ├── helpers.dart
│       │   │   └── validators.dart
│       │   └── l10n/
│       │       ├── app_localizations.dart
│       │       ├── app_fr.arb
│       │       └── app_ar.arb
│       └── test/
│           ├── providers/
│           │   └── feed_provider_test.dart
│           ├── widgets/
│           │   └── common_widgets_test.dart
│           ├── integration/
│           │   └── feed_workflow_test.dart
│           └── unit/
│               ├── models/
│               └── services/
├── backend/                       # Extended Backend Services
│   ├── database/
│   │   └── market-schema.sql
│   ├── routes/
│   │   ├── chat.js
│   │   ├── market.js
│   │   ├── ratings.js
│   │   └── verification.js
│   ├── services/
│   │   └── notification-service.js
│   ├── utils/
│   │   └── encryption.js
│   ├── websocket/
│   │   └── chat-server.js
│   └── server-integration.js
├── migrations/
│   ├── 20231220_create_veterinaires.js
│   └── add_dashboard_tables.sql
├── scripts/                       # Utility Scripts
│   ├── debug-smtp-model.js
│   ├── run-migrations.js
│   ├── setup-database.js
│   ├── test-database-tables.js
│   ├── test-db-connection.js
│   ├── test-general-endpoint.js
│   ├── test-model-direct.js
│   ├── test-models.js
│   ├── test-routes.js
│   ├── test-security-endpoint.js
│   ├── test-security-service.js
│   ├── test-settings-api.js
│   ├── test-smtp-endpoint.js
│   ├── test-update-security-settings.js
│   └── test-user-update.js
├── config/
│   └── config.json
├── .cursor/
│   └── rules/
│       └── poultray.mdc
├── .trae/
│   └── rules/
│       └── project_rules.md
├── .vscode/
│   ├── extensions.json
│   └── settings.json
├── auth-fix.js
├── backend-endpoints-analysis.md
├── check-auth.js
├── dashboards.md
├── debug-navigation.js
├── How-to-use.md
├── server-minimal.js
├── setup-production-db.js
├── simple-server.js
├── simple-test.js
├── start-production-backend.js
├── test-*.js                     # Various test files
├── todo.md
└── query