import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { Editor } from '@tinymce/tinymce-react';

const PageManager = () => {
  const [pages, setPages] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingPage, setEditingPage] = useState(null);
  const [page, setPage] = useState({
    title: '',
    slug: '',
    content: '',
    status: 'draft',
    isPublished: false,
    metaTitle: '',
    metaDescription: '',
    layout: 'default'
  });

  useEffect(() => {
    loadPages();
  }, []);

  const loadPages = async () => {
    try {
      // Implémenter le chargement des pages depuis l'API
      const mockPages = [
        {
          id: 1,
          title: 'À propos',
          slug: 'about',
          status: 'published',
          isPublished: true
        },
        {
          id: 2,
          title: 'Contact',
          slug: 'contact',
          status: 'published',
          isPublished: true
        }
      ];
      setPages(mockPages);
    } catch (error) {
      console.error('Erreur lors du chargement des pages:', error);
    }
  };

  const handleOpenDialog = (page = null) => {
    if (page) {
      setEditingPage(page);
      setPage(page);
    } else {
      setEditingPage(null);
      setPage({
        title: '',
        slug: '',
        content: '',
        status: 'draft',
        isPublished: false,
        metaTitle: '',
        metaDescription: '',
        layout: 'default'
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingPage(null);
    setPage({
      title: '',
      slug: '',
      content: '',
      status: 'draft',
      isPublished: false,
      metaTitle: '',
      metaDescription: '',
      layout: 'default'
    });
  };

  const handleSavePage = async () => {
    try {
      const updatedPages = editingPage
        ? pages.map((p) => (p.id === editingPage.id ? { ...p, ...page } : p))
        : [...pages, { ...page, id: Date.now() }];

      setPages(updatedPages);
      handleCloseDialog();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la page:', error);
    }
  };

  const handleDeletePage = async (pageId) => {
    try {
      const updatedPages = pages.filter((p) => p.id !== pageId);
      setPages(updatedPages);
    } catch (error) {
      console.error('Erreur lors de la suppression de la page:', error);
    }
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)+/g, '');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Gestion des Pages
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Créer une nouvelle page
        </Button>
      </Box>

      <Card>
        <CardContent>
          <List>
            {pages.map((page) => (
              <ListItem key={page.id}>
                <ListItemText
                  primary={page.title}
                  secondary={`/${page.slug} - ${page.status}`}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    aria-label="preview"
                    onClick={() => window.open(`/${page.slug}`, '_blank')}
                  >
                    <VisibilityIcon />
                  </IconButton>
                  <IconButton
                    edge="end"
                    aria-label="edit"
                    onClick={() => handleOpenDialog(page)}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => handleDeletePage(page.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingPage ? 'Modifier la page' : 'Créer une nouvelle page'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Titre"
                value={page.title}
                onChange={(e) => {
                  const title = e.target.value;
                  setPage({
                    ...page,
                    title,
                    slug: generateSlug(title)
                  });
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Slug"
                value={page.slug}
                onChange={(e) =>
                  setPage({ ...page, slug: generateSlug(e.target.value) })
                }
              />
            </Grid>

            <Grid item xs={12}>
              <Editor
                apiKey="votre-clé-api-tinymce"
                value={page.content}
                init={{
                  height: 400,
                  menubar: true,
                  plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                  ],
                  toolbar:
                    'undo redo | formatselect | bold italic backcolor | \
                    alignleft aligncenter alignright alignjustify | \
                    bullist numlist outdent indent | removeformat | help'
                }}
                onEditorChange={(content) => setPage({ ...page, content })}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Layout</InputLabel>
                <Select
                  value={page.layout}
                  label="Layout"
                  onChange={(e) => setPage({ ...page, layout: e.target.value })}
                >
                  <MenuItem value="default">Par défaut</MenuItem>
                  <MenuItem value="full-width">Pleine largeur</MenuItem>
                  <MenuItem value="sidebar">Avec barre latérale</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Statut</InputLabel>
                <Select
                  value={page.status}
                  label="Statut"
                  onChange={(e) => setPage({ ...page, status: e.target.value })}
                >
                  <MenuItem value="draft">Brouillon</MenuItem>
                  <MenuItem value="published">Publié</MenuItem>
                  <MenuItem value="archived">Archivé</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={page.isPublished}
                    onChange={(e) =>
                      setPage({ ...page, isPublished: e.target.checked })
                    }
                  />
                }
                label="Publier"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meta Title"
                value={page.metaTitle}
                onChange={(e) => setPage({ ...page, metaTitle: e.target.value })}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Meta Description"
                value={page.metaDescription}
                onChange={(e) =>
                  setPage({ ...page, metaDescription: e.target.value })
                }
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSavePage} variant="contained" color="primary">
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PageManager;
