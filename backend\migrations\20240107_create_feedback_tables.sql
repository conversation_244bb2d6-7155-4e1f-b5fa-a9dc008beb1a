-- Migration pour créer les tables de feedback et analytics

-- Table des enquêtes
CREATE TABLE surveys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('feedback', 'nps', 'satisfaction', 'feature_request', 'bug_report')),
    target_audience JSONB, -- {roles: [], segments: [], conditions: {}}
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    trigger_conditions JSONB, -- Conditions pour déclencher l'enquête
    display_settings JSONB, -- Paramètres d'affichage
    response_count INTEGER DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0
);

-- Table des questions d'enquête
CREATE TABLE survey_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    survey_id UUID REFERENCES surveys(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL CHECK (question_type IN ('text', 'textarea', 'radio', 'checkbox', 'rating', 'scale', 'dropdown')),
    options JSONB, -- Options pour les questions à choix multiples
    is_required BOOLEAN DEFAULT false,
    order_index INTEGER NOT NULL,
    validation_rules JSONB, -- Règles de validation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des réponses aux enquêtes
CREATE TABLE survey_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    survey_id UUID REFERENCES surveys(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submitted_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB -- Données supplémentaires (user agent, etc.)
);

-- Table des réponses aux questions
CREATE TABLE survey_question_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    response_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    question_id UUID REFERENCES survey_questions(id) ON DELETE CASCADE,
    answer_value JSONB, -- Valeur de la réponse
    answer_text TEXT, -- Texte libre si applicable
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des feedbacks rapides
CREATE TABLE quick_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(50) NOT NULL CHECK (type IN ('bug', 'feature', 'improvement', 'complaint', 'praise')),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    page_url VARCHAR(500),
    feature_name VARCHAR(255),
    category VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    assigned_to UUID REFERENCES users(id),
    resolution_notes TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Table des événements analytics
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL,
    user_id UUID REFERENCES users(id),
    event_type VARCHAR(100) NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    data JSONB,
    page_name VARCHAR(255),
    url VARCHAR(500),
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des sessions utilisateur
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- en secondes
    page_views INTEGER DEFAULT 0,
    interactions INTEGER DEFAULT 0,
    errors INTEGER DEFAULT 0,
    user_agent TEXT,
    ip_address INET,
    referrer VARCHAR(500),
    exit_page VARCHAR(255),
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des tests A/B
CREATE TABLE ab_tests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    hypothesis TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT false,
    traffic_allocation DECIMAL(3,2) DEFAULT 1.0, -- Pourcentage du trafic inclus
    variants JSONB NOT NULL, -- Configuration des variantes
    goals JSONB, -- Objectifs à mesurer
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des assignations aux tests A/B
CREATE TABLE ab_test_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_id UUID REFERENCES ab_tests(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    variant_id VARCHAR(100) NOT NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(test_id, user_id)
);

-- Table des événements de tests A/B
CREATE TABLE ab_test_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_id UUID REFERENCES ab_tests(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    variant_id VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    session_id UUID,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des activités utilisateur pour les conditions de déclenchement
CREATE TABLE user_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    activity_type VARCHAR(100) NOT NULL,
    activity_data JSONB,
    page_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des catégories de feedback
CREATE TABLE feedback_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7), -- Code couleur hex
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des tags pour les feedbacks
CREATE TABLE feedback_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    color VARCHAR(7),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table de liaison feedback-tags
CREATE TABLE feedback_tag_relations (
    feedback_id UUID REFERENCES quick_feedback(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES feedback_tags(id) ON DELETE CASCADE,
    PRIMARY KEY (feedback_id, tag_id)
);

-- Index pour améliorer les performances
CREATE INDEX idx_surveys_active ON surveys(is_active, start_date, end_date);
CREATE INDEX idx_survey_responses_user ON survey_responses(user_id);
CREATE INDEX idx_survey_responses_survey ON survey_responses(survey_id);
CREATE INDEX idx_quick_feedback_user ON quick_feedback(user_id);
CREATE INDEX idx_quick_feedback_status ON quick_feedback(status);
CREATE INDEX idx_quick_feedback_category ON quick_feedback(category);
CREATE INDEX idx_quick_feedback_created ON quick_feedback(created_at);
CREATE INDEX idx_analytics_events_session ON analytics_events(session_id);
CREATE INDEX idx_analytics_events_user ON analytics_events(user_id);
CREATE INDEX idx_analytics_events_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session ON user_sessions(session_id);
CREATE INDEX idx_ab_tests_active ON ab_tests(is_active);
CREATE INDEX idx_ab_test_assignments_test ON ab_test_assignments(test_id);
CREATE INDEX idx_ab_test_assignments_user ON ab_test_assignments(user_id);
CREATE INDEX idx_ab_test_events_test ON ab_test_events(test_id);
CREATE INDEX idx_ab_test_events_user ON ab_test_events(user_id);
CREATE INDEX idx_user_activities_user ON user_activities(user_id);
CREATE INDEX idx_user_activities_type ON user_activities(activity_type);

-- Triggers pour mettre à jour les timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_surveys_updated_at BEFORE UPDATE ON surveys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quick_feedback_updated_at BEFORE UPDATE ON quick_feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ab_tests_updated_at BEFORE UPDATE ON ab_tests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insérer les catégories de feedback par défaut
INSERT INTO feedback_categories (name, description, color, icon) VALUES
('Interface', 'Problèmes liés à l''interface utilisateur', '#2196F3', 'design_services'),
('Performance', 'Problèmes de performance et vitesse', '#FF9800', 'speed'),
('Fonctionnalité', 'Demandes de nouvelles fonctionnalités', '#4CAF50', 'add_circle'),
('Bug', 'Rapports de bugs et erreurs', '#F44336', 'bug_report'),
('Contenu', 'Feedback sur le contenu et les textes', '#9C27B0', 'article'),
('Navigation', 'Problèmes de navigation et ergonomie', '#00BCD4', 'navigation'),
('Mobile', 'Problèmes spécifiques à l''application mobile', '#795548', 'phone_android'),
('Sécurité', 'Problèmes de sécurité et confidentialité', '#607D8B', 'security'),
('Intégration', 'Problèmes d''intégration avec d''autres systèmes', '#E91E63', 'integration_instructions'),
('Documentation', 'Feedback sur la documentation et l''aide', '#3F51B5', 'help_outline');

-- Insérer les tags par défaut
INSERT INTO feedback_tags (name, color) VALUES
('urgent', '#F44336'),
('facile', '#4CAF50'),
('difficile', '#FF9800'),
('populaire', '#2196F3'),
('technique', '#9C27B0'),
('design', '#E91E63'),
('mobile', '#795548'),
('web', '#00BCD4'),
('api', '#607D8B'),
('performance', '#FF5722');

-- Fonction pour calculer les statistiques de feedback
CREATE OR REPLACE FUNCTION calculate_feedback_stats()
RETURNS TABLE (
    category VARCHAR(100),
    total_count BIGINT,
    avg_rating DECIMAL(3,2),
    open_count BIGINT,
    resolved_count BIGINT,
    response_time_avg DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        qf.category,
        COUNT(*) as total_count,
        ROUND(AVG(qf.rating), 2) as avg_rating,
        COUNT(CASE WHEN qf.status = 'open' THEN 1 END) as open_count,
        COUNT(CASE WHEN qf.status = 'resolved' THEN 1 END) as resolved_count,
        ROUND(AVG(EXTRACT(EPOCH FROM (qf.resolved_at - qf.created_at))/3600), 2) as response_time_avg
    FROM quick_feedback qf
    WHERE qf.created_at >= NOW() - INTERVAL '30 days'
    GROUP BY qf.category
    ORDER BY total_count DESC;
END;
$$ LANGUAGE plpgsql;
