import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Tooltip,
  IconButton,
  Collapse,
  Typography,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge
} from '@mui/material';
import {
  Sync,
  SyncProblem,
  CloudDone,
  CloudOff,
  Refresh,
  ExpandMore,
  ExpandLess,
  Schedule,
  Error as ErrorIcon,
  CheckCircle,
  Pending
} from '@mui/icons-material';
import syncService, { SYNC_EVENTS } from '../../services/syncService';

/**
 * Composant pour afficher le statut de synchronisation
 */
const SyncStatus = ({ 
  variant = 'chip', // 'chip' | 'detailed' | 'minimal'
  dataTypes = [], // Types de données spécifiques à surveiller
  showLastSync = true,
  showPendingUpdates = true,
  position = 'relative' // 'fixed' | 'relative'
}) => {
  const [syncStatus, setSyncStatus] = useState({});
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [expanded, setExpanded] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mettre à jour le statut de synchronisation
  useEffect(() => {
    const updateStatus = () => {
      const globalStatus = syncService.getSyncStatus();
      setSyncStatus(globalStatus);
      setIsOnline(globalStatus.isOnline);
    };

    // Mise à jour initiale
    updateStatus();

    // Écouter les événements de synchronisation
    const handleDataUpdate = () => updateStatus();
    const handleSyncError = () => updateStatus();
    const handleConnectionStatus = (event) => {
      setIsOnline(event.online);
      updateStatus();
    };

    syncService.on(SYNC_EVENTS.DATA_UPDATED, handleDataUpdate);
    syncService.on(SYNC_EVENTS.SYNC_ERROR, handleSyncError);
    syncService.on(SYNC_EVENTS.CONNECTION_STATUS, handleConnectionStatus);

    // Mise à jour périodique
    const interval = setInterval(updateStatus, 5000);

    return () => {
      syncService.off(SYNC_EVENTS.DATA_UPDATED, handleDataUpdate);
      syncService.off(SYNC_EVENTS.SYNC_ERROR, handleSyncError);
      syncService.off(SYNC_EVENTS.CONNECTION_STATUS, handleConnectionStatus);
      clearInterval(interval);
    };
  }, []);

  const getSyncIcon = () => {
    if (!isOnline) return <CloudOff />;
    if (syncStatus.totalPending > 0) return <SyncProblem />;
    if (syncStatus.activeTypes?.length > 0) return <CloudDone />;
    return <Sync />;
  };

  const getSyncColor = () => {
    if (!isOnline) return 'error';
    if (syncStatus.totalPending > 0) return 'warning';
    if (syncStatus.activeTypes?.length > 0) return 'success';
    return 'default';
  };

  const getSyncLabel = () => {
    if (!isOnline) return 'Hors ligne';
    if (syncStatus.totalPending > 0) return `${syncStatus.totalPending} en attente`;
    if (syncStatus.activeTypes?.length > 0) return 'Synchronisé';
    return 'Inactif';
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // Forcer la synchronisation de tous les types actifs
    for (const dataType of syncStatus.activeTypes || []) {
      try {
        // Note: Nous aurions besoin d'une méthode pour forcer la sync de tous les types
        console.log(`Rafraîchissement forcé pour ${dataType}`);
      } catch (error) {
        console.error(`Erreur lors du rafraîchissement de ${dataType}:`, error);
      }
    }
    
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const formatLastSync = (timestamp) => {
    if (!timestamp) return 'Jamais';
    const now = new Date();
    const diff = now - new Date(timestamp);
    
    if (diff < 60000) return 'À l\'instant';
    if (diff < 3600000) return `Il y a ${Math.floor(diff / 60000)} min`;
    if (diff < 86400000) return `Il y a ${Math.floor(diff / 3600000)} h`;
    return new Date(timestamp).toLocaleDateString();
  };

  const containerProps = position === 'fixed' ? {
    position: 'fixed',
    top: 16,
    right: 16,
    zIndex: 1000
  } : {};

  if (variant === 'minimal') {
    return (
      <Box {...containerProps}>
        <Tooltip title={`Synchronisation: ${getSyncLabel()}`}>
          <IconButton size="small" color={getSyncColor()}>
            <Badge badgeContent={syncStatus.totalPending || 0} color="error">
              {getSyncIcon()}
            </Badge>
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  if (variant === 'chip') {
    return (
      <Box {...containerProps}>
        <Tooltip title={`Types actifs: ${syncStatus.activeTypes?.length || 0}`}>
          <Chip
            icon={getSyncIcon()}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                {getSyncLabel()}
                {syncStatus.totalPending > 0 && (
                  <Badge badgeContent={syncStatus.totalPending} color="error" />
                )}
              </Box>
            }
            color={getSyncColor()}
            variant="outlined"
            size="small"
            onClick={() => setExpanded(!expanded)}
            onDelete={handleRefresh}
            deleteIcon={
              <Refresh 
                sx={{ 
                  animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }} 
              />
            }
          />
        </Tooltip>

        <Collapse in={expanded}>
          <Box mt={1} p={2} bgcolor="background.paper" borderRadius={1} boxShadow={1} minWidth={300}>
            <Typography variant="subtitle2" gutterBottom>
              État de la Synchronisation
            </Typography>
            
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Connexion:</Typography>
              <Typography variant="body2" color={isOnline ? 'success.main' : 'error.main'}>
                {isOnline ? 'En ligne' : 'Hors ligne'}
              </Typography>
            </Box>

            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Types actifs:</Typography>
              <Typography variant="body2">
                {syncStatus.activeTypes?.length || 0}
              </Typography>
            </Box>

            {syncStatus.totalPending > 0 && (
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">En attente:</Typography>
                <Typography variant="body2" color="warning.main">
                  {syncStatus.totalPending}
                </Typography>
              </Box>
            )}

            {syncStatus.activeTypes?.length > 0 && (
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Types synchronisés:
                </Typography>
                <List dense>
                  {syncStatus.activeTypes.map(type => (
                    <ListItem key={type} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckCircle color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={type}
                        secondary={showLastSync ? formatLastSync(syncStatus.lastSyncTimes?.[type]) : null}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        </Collapse>
      </Box>
    );
  }

  // Variant 'detailed'
  return (
    <Box {...containerProps}>
      <Alert
        severity={getSyncColor()}
        icon={getSyncIcon()}
        action={
          <IconButton
            size="small"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <Refresh 
              sx={{ 
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} 
            />
          </IconButton>
        }
      >
        <Box>
          <Typography variant="subtitle2">
            Synchronisation: {getSyncLabel()}
          </Typography>
          
          <Box mt={1}>
            <Typography variant="body2">
              Connexion: {isOnline ? '🟢 En ligne' : '🔴 Hors ligne'}
            </Typography>
            
            <Typography variant="body2">
              Types actifs: {syncStatus.activeTypes?.length || 0}
            </Typography>
            
            {syncStatus.totalPending > 0 && (
              <Typography variant="body2" color="warning.main">
                Mises à jour en attente: {syncStatus.totalPending}
              </Typography>
            )}
          </Box>

          {syncStatus.activeTypes?.length > 0 && (
            <Box mt={2}>
              <Typography variant="caption" display="block" gutterBottom>
                Dernières synchronisations:
              </Typography>
              {syncStatus.activeTypes.slice(0, 3).map(type => (
                <Typography key={type} variant="caption" display="block">
                  {type}: {formatLastSync(syncStatus.lastSyncTimes?.[type])}
                </Typography>
              ))}
            </Box>
          )}
        </Box>
      </Alert>
    </Box>
  );
};

export default SyncStatus;
