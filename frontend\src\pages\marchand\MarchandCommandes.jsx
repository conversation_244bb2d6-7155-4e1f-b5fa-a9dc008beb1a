import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import axios from 'axios';

const MarchandCommandes = () => {
  const [commandes, setCommandes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedCommande, setSelectedCommande] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    fetchCommandes();
  }, []);

  const fetchCommandes = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/marchand/commandes');
      setCommandes(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des commandes');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOpenDialog = (commande) => {
    setSelectedCommande(commande);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCommande(null);
  };

  const handleUpdateStatus = async (commandeId, newStatus) => {
    try {
      await axios.patch(`/api/marchand/commandes/${commandeId}/status`, { status: newStatus });
      setSnackbar({
        open: true,
        message: 'Statut de la commande mis à jour avec succès',
        severity: 'success'
      });
      fetchCommandes();
      handleCloseDialog();
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur lors de la mise à jour du statut',
        severity: 'error'
      });
      console.error('Erreur:', err);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'en_attente':
        return 'warning';
      case 'acceptee':
        return 'success';
      case 'refusee':
        return 'error';
      case 'livree':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'en_attente':
        return 'En attente';
      case 'acceptee':
        return 'Acceptée';
      case 'refusee':
        return 'Refusée';
      case 'livree':
        return 'Livrée';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Commandes Reçues
      </Typography>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID Commande</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Total</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {commandes
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((commande) => (
                <TableRow key={commande.id}>
                  <TableCell>{commande.id}</TableCell>
                  <TableCell>
                    {new Date(commande.date_commande).toLocaleDateString('fr-FR')}
                  </TableCell>
                  <TableCell>{commande.client_nom}</TableCell>
                  <TableCell>
                    {commande.total.toLocaleString('fr-FR', {
                      style: 'currency',
                      currency: 'DZD'
                    })}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(commande.status)}
                      color={getStatusColor(commande.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(commande)}
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={commandes.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page"
        />
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        {selectedCommande && (
          <>
            <DialogTitle>
              Détails de la commande #{selectedCommande.id}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Client: {selectedCommande.client_nom}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  Date: {new Date(selectedCommande.date_commande).toLocaleDateString('fr-FR')}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  Statut: <Chip
                    label={getStatusLabel(selectedCommande.status)}
                    color={getStatusColor(selectedCommande.status)}
                    size="small"
                  />
                </Typography>

                <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                  Produits commandés
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Produit</TableCell>
                        <TableCell align="right">Quantité</TableCell>
                        <TableCell align="right">Prix unitaire</TableCell>
                        <TableCell align="right">Total</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {selectedCommande.produits.map((produit) => (
                        <TableRow key={produit.id}>
                          <TableCell>{produit.nom}</TableCell>
                          <TableCell align="right">{produit.quantite}</TableCell>
                          <TableCell align="right">
                            {produit.prix_unitaire.toLocaleString('fr-FR', {
                              style: 'currency',
                              currency: 'DZD'
                            })}
                          </TableCell>
                          <TableCell align="right">
                            {(produit.quantite * produit.prix_unitaire).toLocaleString('fr-FR', {
                              style: 'currency',
                              currency: 'DZD'
                            })}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3} align="right">
                          <strong>Total</strong>
                        </TableCell>
                        <TableCell align="right">
                          <strong>
                            {selectedCommande.total.toLocaleString('fr-FR', {
                              style: 'currency',
                              currency: 'DZD'
                            })}
                          </strong>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>

                {selectedCommande.commentaire && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Commentaire:
                    </Typography>
                    <Typography variant="body1">
                      {selectedCommande.commentaire}
                    </Typography>
                  </Box>
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              {selectedCommande.status === 'en_attente' && (
                <>
                  <Button
                    onClick={() => handleUpdateStatus(selectedCommande.id, 'refusee')}
                    color="error"
                    startIcon={<CloseIcon />}
                  >
                    Refuser
                  </Button>
                  <Button
                    onClick={() => handleUpdateStatus(selectedCommande.id, 'acceptee')}
                    color="success"
                    startIcon={<CheckIcon />}
                  >
                    Accepter
                  </Button>
                </>
              )}
              {selectedCommande.status === 'acceptee' && (
                <Button
                  onClick={() => handleUpdateStatus(selectedCommande.id, 'livree')}
                  color="primary"
                  variant="contained"
                >
                  Marquer comme livrée
                </Button>
              )}
              <Button onClick={handleCloseDialog}>Fermer</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MarchandCommandes;
