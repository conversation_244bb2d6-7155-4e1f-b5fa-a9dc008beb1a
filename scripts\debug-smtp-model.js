require('dotenv').config();
const db = require('../src/models');
const colors = require('colors');

async function debugSmtpModel() {
  try {
    console.log('Debugging SMTP model'.yellow.bold);

    // Check what models are available
    console.log('Available models:'.cyan, Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));

    // Check if SmtpConfig model exists
    if (db.SmtpConfig) {
      console.log('✅ SmtpConfig model found'.green);
      console.log('Model details:'.cyan, db.SmtpConfig.name);
      console.log('Table name:'.cyan, db.SmtpConfig.tableName);

      // Try to find a record
      try {
        const smtpConfig = await db.SmtpConfig.findOne();
        if (smtpConfig) {
          console.log('✅ SMTP configuration found:'.green);
          console.log(JSON.stringify(smtpConfig, null, 2));
        } else {
          console.log('❌ No SMTP configuration found in database'.red);
        }
      } catch (error) {
        console.log('❌ Error finding SMTP configuration:'.red, error.message);
        console.log('Error details:'.red, error);
      }
    } else {
      console.log('❌ SmtpConfig model NOT found'.red);

      // Check for similar named models
      const possibleModels = Object.keys(db).filter(k =>
        k.toLowerCase().includes('smtp') ||
        k.toLowerCase().includes('mail') ||
        k.toLowerCase().includes('email')
      );

      if (possibleModels.length > 0) {
        console.log('Possible related models:'.yellow, possibleModels);
      }
    }

    // Try direct SQL query to see what's in the database
    try {
      console.log('\nTrying direct SQL query to check database table...'.yellow);
      const [results] = await db.sequelize.query('SELECT * FROM smtp_configurations LIMIT 1');
      if (results && results.length > 0) {
        console.log('✅ Found record in smtp_configurations table:'.green);
        console.log(JSON.stringify(results[0], null, 2));
      } else {
        console.log('❌ No records found in smtp_configurations table'.red);
      }
    } catch (error) {
      console.log('❌ Error executing SQL query:'.red, error.message);
    }

  } catch (error) {
    console.error('❌ Error debugging SMTP model:'.red, error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
  }
}

// Run the debug function
debugSmtpModel();
