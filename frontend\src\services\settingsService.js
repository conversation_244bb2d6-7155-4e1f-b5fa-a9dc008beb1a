import axiosInstance from '../utils/axiosConfig';

const BASE_URL = '/admin/settings';

// SMTP Settings
const fetchSmtpSettings = async () => {
  try {
    const response = await axiosInstance.get(`${BASE_URL}/smtp`);
    return response.data;
  } catch (error) {
    console.error('Error fetching SMTP settings:', error);
    throw error;
  }
};

// Alias for fetchSmtpSettings
const getSmtpConfig = async () => {
  return fetchSmtpSettings();
};

const updateSmtpSettings = async (settings) => {
  try {
    const response = await axiosInstance.post(`${BASE_URL}/smtp`, settings);
    return response.data;
  } catch (error) {
    console.error('Error updating SMTP settings:', error);
    throw error;
  }
};

// Security Settings
const fetchSecuritySettings = async () => {
  try {
    const response = await axiosInstance.get(`${BASE_URL}/security`);
    return response.data;
  } catch (error) {
    console.error('Error fetching security settings:', error);
    throw error;
  }
};

// Alias for fetchSecuritySettings for new components
const getSecuritySettings = async () => {
  return fetchSecuritySettings();
};

const updateSecuritySettings = async (settings) => {
  try {
    const response = await axiosInstance.post(`${BASE_URL}/security`, settings);
    return response.data;
  } catch (error) {
    console.error('Error updating security settings:', error);
    throw error;
  }
};

// General Settings
const fetchGeneralSettings = async () => {
  try {
    const response = await axiosInstance.get(`${BASE_URL}/general`);
    return response.data;
  } catch (error) {
    console.error('Error fetching general settings:', error);
    throw error;
  }
};

const updateGeneralSettings = async (settings) => {
  try {
    const response = await axiosInstance.post(`${BASE_URL}/general`, settings);
    return response.data;
  } catch (error) {
    console.error('Error updating general settings:', error);
    throw error;
  }
};

// API Keys
const fetchApiKeys = async () => {
  try {
    const response = await axiosInstance.get(`${BASE_URL}/api-keys`);
    return response.data;
  } catch (error) {
    console.error('Error fetching API keys:', error);
    throw error;
  }
};

const updateApiKeys = async (apiKeys) => {
  try {
    const response = await axiosInstance.post(`${BASE_URL}/api-keys`, { apiKeys });
    return response.data;
  } catch (error) {
    console.error('Error updating API keys:', error);
    throw error;
  }
};

// Test SMTP Configuration
const testSmtpConfig = async (config) => {
  try {
    const response = await axiosInstance.post(`${BASE_URL}/smtp/test`, config);
    return response.data;
  } catch (error) {
    console.error('Error testing SMTP configuration:', error);
    throw error;
  }
};

const settingsService = {
  fetchSmtpSettings,
  updateSmtpSettings,
  fetchSecuritySettings,
  getSecuritySettings,
  updateSecuritySettings,
  fetchGeneralSettings,
  updateGeneralSettings,
  fetchApiKeys,
  updateApiKeys,
  testSmtpConfig,
  getSmtpConfig
};

export default settingsService;
