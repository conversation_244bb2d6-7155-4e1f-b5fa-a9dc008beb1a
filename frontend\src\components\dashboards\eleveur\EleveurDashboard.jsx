import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  IconButton,
  Tab,
  Tabs
} from '@mui/material';
import {
  Timeline,
  Assessment,
  Notifications,
  Settings
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import ChairManagement from './ChairManagement';
import DindeManagement from './DindeManagement';
import PondeuseManagement from './PondeuseManagement';
import DailyCollection from './DailyCollection';

function EleveurDashboard() {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(0);
  const [stats, setStats] = useState({
    totalVolailles: 0,
    productionJournaliere: 0,
    tauxMortalite: 0,
    revenuMensuel: 0
  });

  useEffect(() => {
    // Fetch dashboard statistics
    const fetchStats = async () => {
      try {
        // const response = await api.get('/api/eleveur/statistics');
        // setStats(response.data);
        setStats({
          totalVolailles: 1250,
          productionJournaliere: 850,
          tauxMortalite: 2.5,
          revenuMensuel: 15000
        }); // Données de test
      } catch (err) {
        console.error('Erreur chargement statistiques:', err);
      }
    };

    fetchStats();
  }, []);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: // Vue d'ensemble
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    {t('Total Volailles')}
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalVolailles}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    {t('Production Journalière')}
                  </Typography>
                  <Typography variant="h5">
                    {stats.productionJournaliere}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    {t('Taux de Mortalité')}
                  </Typography>
                  <Typography variant="h5">
                    {stats.tauxMortalite}%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    {t('Revenu Mensuel')}
                  </Typography>
                  <Typography variant="h5">
                    {stats.revenuMensuel} DA
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );
      case 1: // Volailles de Chair
        return <ChairManagement />;
      case 2: // Dindes
        return <DindeManagement />;
      case 3: // Pondeuses
        return <PondeuseManagement />;
      case 4: // Collecte Journalière
        return <DailyCollection />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          {t('Tableau de Bord Éleveur')}
        </Typography>

        <Tabs
          value={selectedTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{ mb: 3 }}
        >
          <Tab label={t('Vue d\'ensemble')} />
          <Tab label={t('Volailles de Chair')} />
          <Tab label={t('Dindes')} />
          <Tab label={t('Pondeuses')} />
          <Tab label={t('Collecte Journalière')} />
        </Tabs>

        {renderTabContent()}
      </Container>
    </Box>
  );
}

export default EleveurDashboard;