# 📱 Prompt de Démarrage - Développement Mobile Poultry DZ

## Instruction Simple pour l'Agent Mobile

**Commence immédiatement le développement de l'application mobile Flutter Poultry DZ sans t'arrêter. Développe une application complète et fonctionnelle connectée au backend existant.**

### 🎯 Objectif

Créer une application mobile Flutter complète pour Poultry DZ qui se connecte au backend Node.js/Express existant sur `http://localhost:3003`.

### ⚡ Actions à Effectuer Immédiatement

1. **Scaffolder l'app Flutter** dans `/Mobile_App/poultry_dz_mobile/`
2. **Implémenter l'authentification** avec connexion au backend `/api/auth`
3. **Créer les dashboards** pour tous les rôles (Éleveur, Vétérinaire, Marchand, Admin)
4. **Connecter toutes les APIs** existantes du backend
5. **Tester la connexion** avec le serveur backend sur port 3003
6. **Implémenter la gestion d'état** avec Provider ou Riverpod
7. **Ajouter le support bilingue** Français/Arabe
8. **Optimiser pour l'usage terrain** (gros boutons, offline, etc.)

### 🔗 Backend Disponible

- **URL**: `http://localhost:3003`
- **APIs**: `/api/auth`, `/api/admin`, `/api/prescriptions`, `/api/produits`, `/api/ventes`
- **Auth**: JWT tokens
- **Base de données**: PostgreSQL avec 25+ tables

### 📋 Structure à Créer

```
Mobile_App/poultry_dz_mobile/
├── lib/
│   ├── main.dart
│   ├── models/
│   ├── services/
│   ├── screens/
│   ├── widgets/
│   └── providers/
└── pubspec.yaml
```

### 🚀 Commande de Démarrage

**COMMENCE MAINTENANT** - Ne demande rien, ne planifie pas, développe directement l'application mobile Flutter complète et connectée au backend existant.

---

_Utilise les outils disponibles pour créer les fichiers, installer les dépendances et tester la connexion au backend immédiatement._
