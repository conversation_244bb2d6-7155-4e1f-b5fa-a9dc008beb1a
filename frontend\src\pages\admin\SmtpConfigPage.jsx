import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  FormControlLabel,
  Checkbox,
  Tooltip,
  IconButton,
  Snackbar,
  InputAdornment // Added InputAdornment
} from '@mui/material';
import {
  Save as SaveIcon,
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Cable as TestConnectionIcon
} from '@mui/icons-material';
import settingsService from '../../services/settingsService';
import { useLanguage } from '../../contexts/LanguageContext';

const SmtpConfigPage = () => {
  const { t } = useLanguage();

  const [smtpSettings, setSmtpSettings] = useState({
    host: '',
    port: 587,
    secure: false,
    user: '',
    pass: '',
    fromName: '',
    fromEmail: '',
    replyTo: '',
    testEmailRecipient: '',
    isEnabled: true
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    const fetchSmtpSettings = async () => {
      try {
        setLoading(true);
        const data = await settingsService.getSmtpConfig();
        console.log('Fetched SMTP settings:', data);
        setSmtpSettings(data);
        setError('');
      } catch (err) {
        console.error('Error fetching SMTP settings:', err);
        setError(t('settings.smtp.fetchError') || 'Failed to load SMTP settings');
        showToast(t('settings.smtp.fetchError') || 'Failed to load SMTP settings', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchSmtpSettings();
  }, [t]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSmtpSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError('');
      await settingsService.updateSmtpConfig(smtpSettings);
      setSuccess(t('settings.smtp.saveSuccess') || 'SMTP settings updated successfully');
      showToast(t('settings.smtp.saveSuccess') || 'SMTP settings updated successfully', 'success');
    } catch (err) {
      console.error('Error updating SMTP settings:', err);
      setError(t('settings.smtp.saveError') || 'Failed to update SMTP settings');
      showToast(t('settings.smtp.saveError') || 'Failed to update SMTP settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTesting(true);
      setError('');
      setSuccess('');

      const result = await settingsService.testSmtpConfig({
        testEmail: smtpSettings.testEmailRecipient
      });

      console.log('SMTP test result:', result);
      setSuccess(t('settings.smtp.testSuccess') || 'Test email sent successfully');
      showToast(t('settings.smtp.testSuccess') || 'Test email sent successfully', 'success');
    } catch (err) {
      console.error('Error testing SMTP connection:', err);
      setError(t('settings.smtp.testError') || 'Failed to send test email');
      showToast(t('settings.smtp.testError') || 'Failed to send test email', 'error');
    } finally {
      setTesting(false);
    }
  };

  const showToast = (message, severity) => {
    setToast({ open: true, message, severity });
  };

  const handleCloseToast = () => {
    setToast({ ...toast, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        <EmailIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        {t('settings.smtp.title') || 'SMTP Configuration'}
      </Typography>

      <Paper sx={{ p: 3, mt: 2 }}>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={smtpSettings.isEnabled}
                    onChange={handleChange}
                    name="isEnabled"
                    color="primary"
                  />
                }
                label={t('settings.smtp.isEnabled') || 'Enable Email Sending'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={t('settings.smtp.host') || 'SMTP Host'}
                name="host"
                value={smtpSettings.host}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={t('settings.smtp.port') || 'SMTP Port'}
                name="port"
                type="number"
                value={smtpSettings.port}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
                helperText={t('settings.smtp.portHelp') || 'Common ports: 25, 465 (SSL), 587 (TLS)'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={t('settings.smtp.user') || 'SMTP Username'}
                name="user"
                value={smtpSettings.user}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={t('settings.smtp.pass') || 'SMTP Password'}
                name="pass"
                type={showPassword ? 'text' : 'password'}
                value={smtpSettings.pass}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={smtpSettings.secure}
                    onChange={handleChange}
                    name="secure"
                    color="primary"
                    disabled={!smtpSettings.isEnabled}
                  />
                }
                label={t('settings.smtp.secure') || 'Use Secure Connection (SSL/TLS)'}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={t('settings.smtp.fromEmail') || 'From Email Address'}
                name="fromEmail"
                type="email"
                value={smtpSettings.fromEmail}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.smtp.fromName') || 'From Name'}
                name="fromName"
                value={smtpSettings.fromName}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.smtp.replyTo') || 'Reply-To Email Address'}
                name="replyTo"
                type="email"
                value={smtpSettings.replyTo}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                {t('settings.smtp.testConnection') || 'Test Email Configuration'}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('settings.smtp.testEmailRecipient') || 'Test Email Recipient'}
                name="testEmailRecipient"
                type="email"
                value={smtpSettings.testEmailRecipient}
                onChange={handleChange}
                disabled={!smtpSettings.isEnabled}
                helperText={t('settings.smtp.testEmailRecipientHelp') || 'Email address to receive the test message'}
              />
            </Grid>

            <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<TestConnectionIcon />}
                onClick={handleTestConnection}
                disabled={testing || !smtpSettings.isEnabled || !smtpSettings.testEmailRecipient}
                sx={{ mt: 1 }}
              >
                {testing ? (
                  <>
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    {t('settings.smtp.testing') || 'Testing...'}
                  </>
                ) : (
                  t('settings.smtp.testButton') || 'Send Test Email'
                )}
              </Button>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={saving || !smtpSettings.isEnabled}
                >
                  {saving ? (
                    <>
                      <CircularProgress size={24} sx={{ mr: 1 }} />
                      {t('common.saving') || 'Saving...'}
                    </>
                  ) : (
                    t('common.save') || 'Save Settings'
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}>
          {toast.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SmtpConfigPage;
