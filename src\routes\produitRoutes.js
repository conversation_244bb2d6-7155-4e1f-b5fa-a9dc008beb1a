const express = require('express');
const router = express.Router();
const {
  getProduitsByMarchand,
  createProduit,
  updateProduit,
  deleteProduit,
  getStatistiquesProduits,
  getProduitById,
  rechercherProduits,
  updateStock,
  togglePromotion
} = require('../controllers/produitController');
const { auth, checkRole } = require('../middleware/auth');

// Middleware d'authentification pour toutes les routes
router.use(auth);

// Routes pour les produits

// GET /api/produits/recherche - Rechercher des produits (route publique pour les clients)
router.get('/recherche', rechercherProduits);

// GET /api/produits/marchand/:marchandId - Obtenir tous les produits d'un marchand
router.get('/marchand/:marchandId', checkRole(['admin', 'marchand']), getProduitsByMarchand);

// GET /api/produits/marchand/:marchandId/statistiques - Obtenir les statistiques de produits
router.get('/marchand/:marchandId/statistiques', checkRole(['admin', 'marchand']), getStatistiquesProduits);

// GET /api/produits/:id - Obtenir un produit par ID
router.get('/:id', getProduitById);

// POST /api/produits - Créer un nouveau produit
router.post('/', checkRole(['admin', 'marchand']), createProduit);

// PUT /api/produits/:id - Mettre à jour un produit
router.put('/:id', checkRole(['admin', 'marchand']), updateProduit);

// PUT /api/produits/:id/stock - Mettre à jour le stock d'un produit
router.put('/:id/stock', checkRole(['admin', 'marchand']), updateStock);

// PUT /api/produits/:id/promotion - Activer/désactiver une promotion
router.put('/:id/promotion', checkRole(['admin', 'marchand']), togglePromotion);

// DELETE /api/produits/:id - Supprimer un produit
router.delete('/:id', checkRole(['admin', 'marchand']), deleteProduit);

module.exports = router;