const express = require('express');
const app = express();
const PORT = 3003;

// Middleware de base
app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur de test Poultray DZ',
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// Route de test API
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API de test fonctionnelle',
    routes: [
      '/api/translations',
      '/api/admin/stats',
      '/api/auth/login'
    ]
  });
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur de test démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
  process.exit(1);
});
