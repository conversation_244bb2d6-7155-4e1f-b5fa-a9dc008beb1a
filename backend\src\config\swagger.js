/**
 * Configuration Swagger/OpenAPI pour la documentation de l'API
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Poultray DZ API',
      version: '1.0.0',
      description: 'API pour la plateforme de gestion avicole Poultray DZ',
      contact: {
        name: 'Équipe Poultray DZ',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3000',
        description: 'Serveur de développement'
      },
      {
        url: 'https://api.poultraydz.com',
        description: 'Serveur de production'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Token JWT pour l\'authentification'
        }
      },
      schemas: {
        // Schéma de réponse standard
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: 'Indique si l\'opération a réussi'
            },
            data: {
              description: 'Données de la réponse'
            },
            message: {
              type: 'string',
              description: 'Message descriptif'
            },
            errors: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/ValidationError'
              },
              description: 'Liste des erreurs'
            },
            meta: {
              type: 'object',
              properties: {
                timestamp: {
                  type: 'string',
                  format: 'date-time'
                },
                version: {
                  type: 'string'
                },
                pagination: {
                  $ref: '#/components/schemas/Pagination'
                }
              }
            }
          }
        },
        
        // Schéma d'erreur de validation
        ValidationError: {
          type: 'object',
          properties: {
            field: {
              type: 'string',
              description: 'Nom du champ en erreur'
            },
            message: {
              type: 'string',
              description: 'Message d\'erreur'
            },
            value: {
              description: 'Valeur qui a causé l\'erreur'
            }
          }
        },
        
        // Schéma de pagination
        Pagination: {
          type: 'object',
          properties: {
            page: {
              type: 'integer',
              description: 'Numéro de page actuelle'
            },
            limit: {
              type: 'integer',
              description: 'Nombre d\'éléments par page'
            },
            total: {
              type: 'integer',
              description: 'Nombre total d\'éléments'
            },
            totalPages: {
              type: 'integer',
              description: 'Nombre total de pages'
            },
            hasNext: {
              type: 'boolean',
              description: 'Indique s\'il y a une page suivante'
            },
            hasPrev: {
              type: 'boolean',
              description: 'Indique s\'il y a une page précédente'
            }
          }
        },
        
        // Schéma utilisateur
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'Identifiant unique'
            },
            username: {
              type: 'string',
              description: 'Nom d\'utilisateur'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Adresse email'
            },
            first_name: {
              type: 'string',
              description: 'Prénom'
            },
            last_name: {
              type: 'string',
              description: 'Nom de famille'
            },
            role: {
              type: 'string',
              enum: ['admin', 'eleveur', 'veterinaire', 'marchand'],
              description: 'Rôle de l\'utilisateur'
            },
            created_at: {
              type: 'string',
              format: 'date-time'
            },
            updated_at: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        
        // Schéma volaille
        Volaille: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'Identifiant unique'
            },
            nom: {
              type: 'string',
              description: 'Nom de la volaille'
            },
            type_volaille: {
              type: 'string',
              enum: ['poule', 'coq', 'poussin', 'canard', 'oie', 'dinde'],
              description: 'Type de volaille'
            },
            nombre_total: {
              type: 'integer',
              description: 'Nombre total d\'animaux'
            },
            nombre_actuel: {
              type: 'integer',
              description: 'Nombre actuel d\'animaux'
            },
            statut: {
              type: 'string',
              enum: ['actif', 'inactif', 'vendu', 'mort'],
              description: 'Statut de la volaille'
            },
            eleveur_id: {
              type: 'integer',
              description: 'ID de l\'éleveur propriétaire'
            },
            date_creation: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        
        // Schéma consultation
        Consultation: {
          type: 'object',
          properties: {
            id: {
              type: 'integer'
            },
            eleveur_id: {
              type: 'integer'
            },
            veterinaire_id: {
              type: 'integer'
            },
            volaille_id: {
              type: 'integer'
            },
            date_consultation: {
              type: 'string',
              format: 'date-time'
            },
            diagnostic: {
              type: 'string'
            },
            traitement: {
              type: 'string'
            },
            urgence: {
              type: 'boolean'
            },
            statut: {
              type: 'string',
              enum: ['programmee', 'en_cours', 'terminee', 'annulee']
            }
          }
        },
        
        // Schéma commande
        Commande: {
          type: 'object',
          properties: {
            id: {
              type: 'integer'
            },
            eleveur_id: {
              type: 'integer'
            },
            marchand_id: {
              type: 'integer'
            },
            date_commande: {
              type: 'string',
              format: 'date-time'
            },
            montant_total: {
              type: 'number',
              format: 'decimal'
            },
            statut: {
              type: 'string',
              enum: ['en_attente', 'confirmee', 'en_preparation', 'expediee', 'livree', 'annulee']
            },
            items: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/CommandeItem'
              }
            }
          }
        },
        
        // Schéma item de commande
        CommandeItem: {
          type: 'object',
          properties: {
            id: {
              type: 'integer'
            },
            produit_nom: {
              type: 'string'
            },
            quantite: {
              type: 'integer'
            },
            prix_unitaire: {
              type: 'number',
              format: 'decimal'
            },
            sous_total: {
              type: 'number',
              format: 'decimal'
            }
          }
        }
      },
      
      responses: {
        Success: {
          description: 'Opération réussie',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ApiResponse'
              }
            }
          }
        },
        ValidationError: {
          description: 'Erreurs de validation',
          content: {
            'application/json': {
              schema: {
                allOf: [
                  { $ref: '#/components/schemas/ApiResponse' },
                  {
                    properties: {
                      success: { example: false },
                      message: { example: 'Erreurs de validation' }
                    }
                  }
                ]
              }
            }
          }
        },
        NotFound: {
          description: 'Ressource non trouvée',
          content: {
            'application/json': {
              schema: {
                allOf: [
                  { $ref: '#/components/schemas/ApiResponse' },
                  {
                    properties: {
                      success: { example: false },
                      message: { example: 'Ressource introuvable' }
                    }
                  }
                ]
              }
            }
          }
        },
        Unauthorized: {
          description: 'Non autorisé',
          content: {
            'application/json': {
              schema: {
                allOf: [
                  { $ref: '#/components/schemas/ApiResponse' },
                  {
                    properties: {
                      success: { example: false },
                      message: { example: 'Accès non autorisé' }
                    }
                  }
                ]
              }
            }
          }
        },
        ServerError: {
          description: 'Erreur serveur',
          content: {
            'application/json': {
              schema: {
                allOf: [
                  { $ref: '#/components/schemas/ApiResponse' },
                  {
                    properties: {
                      success: { example: false },
                      message: { example: 'Erreur interne du serveur' }
                    }
                  }
                ]
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js'
  ]
};

const specs = swaggerJsdoc(options);

const swaggerOptions = {
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #2c5aa0 }
  `,
  customSiteTitle: 'Poultray DZ API Documentation',
  customfavIcon: '/favicon.ico'
};

module.exports = {
  specs,
  swaggerUi,
  swaggerOptions
};
