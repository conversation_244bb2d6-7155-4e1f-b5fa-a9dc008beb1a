const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const sequelize = require('../config/database');

const db = {};

// Load all model files and initialize them
fs.readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      file.slice(-3) === '.js' &&
      file.indexOf('.test.js') === -1
    );
  })
  .forEach(file => {
    try {
      const modelDefiner = require(path.join(__dirname, file));

      // Check if the model definer is a function (typical for Sequelize models)
      if (typeof modelDefiner === 'function') {
        const model = modelDefiner(sequelize, Sequelize.DataTypes);
        if (model && model.name) {
          db[model.name] = model;
          console.log(`Model ${model.name} loaded successfully`);
        }
      }
    } catch (error) {
      console.error(`Error loading model from file ${file}:`, error.message);
    }
  });

// Set up associations after all models are loaded
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    try {
      db[modelName].associate(db);
      console.log(`Associations set for ${modelName}`);
    } catch (error) {
      console.error(`Error setting associations for ${modelName}:`, error.message);
    }
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Add models to sequelize.models for easy access
sequelize.models = db;

console.log('All models loaded:', Object.keys(db).filter(key => key !== 'sequelize' && key !== 'Sequelize'));

module.exports = db;
