import React, { memo } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Pets,
  TrendingUp,
  Warning,
  Egg,
  LocalHospital,
  ShoppingCart,
  Add,
  Visibility,
  Edit
} from '@mui/icons-material';
import { useOptimizedMemo } from '../../hooks/usePerformance';

/**
 * Carte de statistiques pour éleveur
 */
const EleveurStatsCard = memo(({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  color = 'primary',
  trend = null,
  loading = false 
}) => {
  const trendColor = useOptimizedMemo(() => {
    if (!trend) return 'text.secondary';
    return trend > 0 ? 'success.main' : trend < 0 ? 'error.main' : 'text.secondary';
  }, [trend], 'EleveurStatsCard.trendColor');

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h4" color={`${color}.main`} fontWeight="bold">
              {loading ? '-' : value}
            </Typography>
            <Typography variant="h6" color="text.primary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
            {trend !== null && (
              <Box display="flex" alignItems="center" mt={1}>
                <TrendingUp 
                  fontSize="small" 
                  sx={{ 
                    color: trendColor,
                    transform: trend < 0 ? 'rotate(180deg)' : 'none'
                  }} 
                />
                <Typography variant="caption" color={trendColor} sx={{ ml: 0.5 }}>
                  {Math.abs(trend)}% ce mois
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light`, width: 56, height: 56 }}>
            <Icon fontSize="large" />
          </Avatar>
        </Box>
        {loading && <LinearProgress sx={{ mt: 2 }} />}
      </CardContent>
    </Card>
  );
});

/**
 * Carte de volaille individuelle
 */
const VolailleCard = memo(({ 
  volaille, 
  onView, 
  onEdit, 
  showActions = true 
}) => {
  const healthStatus = useOptimizedMemo(() => {
    const ratio = volaille.nombre_actuel / volaille.nombre_total;
    if (ratio >= 0.95) return { color: 'success', label: 'Excellent' };
    if (ratio >= 0.85) return { color: 'warning', label: 'Bon' };
    return { color: 'error', label: 'Attention' };
  }, [volaille.nombre_actuel, volaille.nombre_total], 'VolailleCard.healthStatus');

  const productionRate = useOptimizedMemo(() => {
    return volaille.derniere_production || 0;
  }, [volaille.derniere_production], 'VolailleCard.productionRate');

  return (
    <Card elevation={1} sx={{ height: '100%', position: 'relative' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Typography variant="h6" gutterBottom>
              {volaille.nom}
            </Typography>
            <Chip 
              label={volaille.type_volaille} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
          </Box>
          {showActions && (
            <Box>
              <Tooltip title="Voir détails">
                <IconButton size="small" onClick={() => onView?.(volaille)}>
                  <Visibility fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Modifier">
                <IconButton size="small" onClick={() => onEdit?.(volaille)}>
                  <Edit fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Animaux
            </Typography>
            <Typography variant="h6">
              {volaille.nombre_actuel}/{volaille.nombre_total}
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={(volaille.nombre_actuel / volaille.nombre_total) * 100}
              color={healthStatus.color}
              sx={{ mt: 1 }}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Production
            </Typography>
            <Typography variant="h6">
              {productionRate}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              unités/jour
            </Typography>
          </Grid>
        </Grid>

        <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
          <Chip 
            label={healthStatus.label}
            color={healthStatus.color}
            size="small"
          />
          <Typography variant="caption" color="text.secondary">
            Créé le {new Date(volaille.date_creation).toLocaleDateString()}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
});

/**
 * Widget de production récente
 */
const ProductionWidget = memo(({ productions = [], loading = false }) => {
  const totalProduction = useOptimizedMemo(() => {
    return productions.reduce((sum, prod) => sum + prod.quantite_produite, 0);
  }, [productions], 'ProductionWidget.totalProduction');

  const productionByType = useOptimizedMemo(() => {
    return productions.reduce((acc, prod) => {
      acc[prod.type_production] = (acc[prod.type_production] || 0) + prod.quantite_produite;
      return acc;
    }, {});
  }, [productions], 'ProductionWidget.productionByType');

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Egg color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">
            Production Récente
          </Typography>
        </Box>

        {loading ? (
          <LinearProgress />
        ) : (
          <>
            <Typography variant="h4" color="primary.main" gutterBottom>
              {totalProduction}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              unités cette semaine
            </Typography>

            <Box mt={2}>
              {Object.entries(productionByType).map(([type, quantity]) => (
                <Box key={type} display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    {type}
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {quantity}
                  </Typography>
                </Box>
              ))}
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
});

/**
 * Widget d'alertes et notifications
 */
const AlertesWidget = memo(({ alertes = [] }) => {
  const alertesByPriority = useOptimizedMemo(() => {
    return alertes.reduce((acc, alerte) => {
      acc[alerte.priorite] = (acc[alerte.priorite] || 0) + 1;
      return acc;
    }, {});
  }, [alertes], 'AlertesWidget.alertesByPriority');

  const criticalAlertes = useOptimizedMemo(() => {
    return alertes.filter(alerte => alerte.priorite === 'critique');
  }, [alertes], 'AlertesWidget.criticalAlertes');

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Warning color="warning" sx={{ mr: 1 }} />
          <Typography variant="h6">
            Alertes
          </Typography>
          {alertes.length > 0 && (
            <Chip 
              label={alertes.length} 
              color="warning" 
              size="small" 
              sx={{ ml: 1 }} 
            />
          )}
        </Box>

        {alertes.length === 0 ? (
          <Alert severity="success" variant="outlined">
            Aucune alerte active
          </Alert>
        ) : (
          <>
            {criticalAlertes.length > 0 && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {criticalAlertes.length} alerte(s) critique(s) nécessitent votre attention
              </Alert>
            )}

            <Box>
              {Object.entries(alertesByPriority).map(([priorite, count]) => (
                <Box key={priorite} display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    {priorite.charAt(0).toUpperCase() + priorite.slice(1)}
                  </Typography>
                  <Chip 
                    label={count} 
                    size="small" 
                    color={priorite === 'critique' ? 'error' : priorite === 'haute' ? 'warning' : 'default'}
                  />
                </Box>
              ))}
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
});

/**
 * Widget de consultations vétérinaires
 */
const ConsultationsWidget = memo(({ consultations = [], onSchedule }) => {
  const prochaines = useOptimizedMemo(() => {
    return consultations
      .filter(c => new Date(c.date_consultation) > new Date())
      .sort((a, b) => new Date(a.date_consultation) - new Date(b.date_consultation))
      .slice(0, 3);
  }, [consultations], 'ConsultationsWidget.prochaines');

  const urgentes = useOptimizedMemo(() => {
    return consultations.filter(c => c.urgence);
  }, [consultations], 'ConsultationsWidget.urgentes');

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <LocalHospital color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">
              Consultations
            </Typography>
          </Box>
          <Tooltip title="Programmer une consultation">
            <IconButton size="small" onClick={onSchedule}>
              <Add />
            </IconButton>
          </Tooltip>
        </Box>

        {urgentes.length > 0 && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            {urgentes.length} consultation(s) urgente(s)
          </Alert>
        )}

        {prochaines.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            Aucune consultation programmée
          </Typography>
        ) : (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Prochaines consultations
            </Typography>
            {prochaines.map((consultation) => (
              <Box key={consultation.id} mb={1} p={1} bgcolor="grey.50" borderRadius={1}>
                <Typography variant="body2" fontWeight="medium">
                  Dr. {consultation.veterinaire_nom}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {new Date(consultation.date_consultation).toLocaleDateString()} à{' '}
                  {new Date(consultation.date_consultation).toLocaleTimeString()}
                </Typography>
                {consultation.urgence && (
                  <Chip label="Urgent" color="error" size="small" sx={{ ml: 1 }} />
                )}
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
});

/**
 * Widget de commandes récentes
 */
const CommandesWidget = memo(({ commandes = [], onNewOrder }) => {
  const commandesRecentes = useOptimizedMemo(() => {
    return commandes
      .sort((a, b) => new Date(b.date_commande) - new Date(a.date_commande))
      .slice(0, 5);
  }, [commandes], 'CommandesWidget.commandesRecentes');

  const totalCA = useOptimizedMemo(() => {
    return commandes.reduce((sum, cmd) => sum + cmd.montant_total, 0);
  }, [commandes], 'CommandesWidget.totalCA');

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <ShoppingCart color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">
              Commandes
            </Typography>
          </Box>
          <Tooltip title="Nouvelle commande">
            <IconButton size="small" onClick={onNewOrder}>
              <Add />
            </IconButton>
          </Tooltip>
        </Box>

        <Typography variant="h5" color="primary.main" gutterBottom>
          {totalCA.toLocaleString()} DA
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Chiffre d'affaires ce mois
        </Typography>

        {commandesRecentes.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            Aucune commande récente
          </Typography>
        ) : (
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Commandes récentes
            </Typography>
            {commandesRecentes.map((commande) => (
              <Box key={commande.id} display="flex" justifyContent="space-between" mb={1}>
                <Box>
                  <Typography variant="body2">
                    Commande #{commande.id}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {new Date(commande.date_commande).toLocaleDateString()}
                  </Typography>
                </Box>
                <Box textAlign="right">
                  <Typography variant="body2" fontWeight="medium">
                    {commande.montant_total.toLocaleString()} DA
                  </Typography>
                  <Chip 
                    label={commande.statut} 
                    size="small" 
                    color={commande.statut === 'livree' ? 'success' : 'default'}
                  />
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
});

EleveurStatsCard.displayName = 'EleveurStatsCard';
VolailleCard.displayName = 'VolailleCard';
ProductionWidget.displayName = 'ProductionWidget';
AlertesWidget.displayName = 'AlertesWidget';
ConsultationsWidget.displayName = 'ConsultationsWidget';
CommandesWidget.displayName = 'CommandesWidget';

export {
  EleveurStatsCard,
  VolailleCard,
  ProductionWidget,
  AlertesWidget,
  ConsultationsWidget,
  CommandesWidget
};
