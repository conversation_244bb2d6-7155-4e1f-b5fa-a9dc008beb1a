const { Op } = require('sequelize');
const { Order, OrderItem, Product, User } = require('../models');

class AIRecommendationService {
  // Calculate product performance metrics
  async calculateProductMetrics(productId, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const sales = await OrderItem.findAll({
      where: {
        product_id: productId,
        created_at: { [Op.gte]: startDate }
      },
      include: [{
        model: Order,
        as: 'order',
        where: { status: 'delivered' }
      }]
    });

    // Calculate key metrics
    const totalSales = sales.length;
    const totalQuantity = sales.reduce((sum, sale) => sum + sale.quantity, 0);
    const totalRevenue = sales.reduce((sum, sale) => sum + parseFloat(sale.total_price), 0);
    const averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0;

    return {
      totalSales,
      totalQuantity,
      totalRevenue,
      averageOrderValue,
      dailyAverage: totalQuantity / days
    };
  }

  // Generate inventory recommendations
  async generateInventoryRecommendations(marchandId) {
    const products = await Product.findAll({
      where: { marchand_id: marchandId }
    });

    const recommendations = [];

    for (const product of products) {
      const metrics = await this.calculateProductMetrics(product.id);
      const turnoverRate = metrics.totalQuantity / (product.stock_quantity || 1);
      const daysUntilStockout = product.stock_quantity / (metrics.dailyAverage || 0.1);

      // Generate recommendations based on metrics
      if (daysUntilStockout < 7) {
        recommendations.push({
          type: 'URGENT_RESTOCK',
          product: product.name,
          currentStock: product.stock_quantity,
          suggestedOrder: Math.ceil(metrics.dailyAverage * 30), // 30 days supply
          reason: 'Critical inventory level - Less than 7 days of stock remaining'
        });
      } else if (daysUntilStockout < 14) {
        recommendations.push({
          type: 'RESTOCK_SOON',
          product: product.name,
          currentStock: product.stock_quantity,
          suggestedOrder: Math.ceil(metrics.dailyAverage * 20), // 20 days supply
          reason: 'Low inventory level - Less than 14 days of stock remaining'
        });
      } else if (turnoverRate > 2) {
        recommendations.push({
          type: 'HIGH_DEMAND',
          product: product.name,
          currentStock: product.stock_quantity,
          suggestedOrder: Math.ceil(metrics.dailyAverage * 45), // 45 days supply for high-demand items
          reason: 'High turnover rate - Consider increasing base stock level'
        });
      } else if (turnoverRate < 0.2) {
        recommendations.push({
          type: 'SLOW_MOVING',
          product: product.name,
          currentStock: product.stock_quantity,
          suggestedAction: 'Consider promotional pricing or bundle offers',
          reason: 'Low turnover rate - Stock moving slower than expected'
        });
      }
    }

    return recommendations;
  }

  // Generate pricing recommendations
  async generatePricingRecommendations(marchandId) {
    const products = await Product.findAll({
      where: { marchand_id: marchandId },
      include: [{
        model: OrderItem,
        as: 'orderItems',
        include: [{
          model: Order,
          as: 'order',
          where: {
            status: 'delivered',
            created_at: { [Op.gte]: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days
          }
        }]
      }]
    });

    const recommendations = [];

    for (const product of products) {
      const sales = product.orderItems || [];
      const totalSales = sales.length;
      const averagePrice = sales.reduce((sum, sale) => sum + parseFloat(sale.unit_price), 0) / (totalSales || 1);

      // Price elasticity analysis (simplified)
      const highPriceSales = sales.filter(sale => parseFloat(sale.unit_price) > averagePrice).length;
      const lowPriceSales = sales.filter(sale => parseFloat(sale.unit_price) < averagePrice).length;

      if (totalSales > 0) {
        if (highPriceSales > lowPriceSales * 1.5) {
          recommendations.push({
            type: 'PRICE_INCREASE',
            product: product.name,
            currentPrice: product.price,
            suggestedPrice: parseFloat(product.price) * 1.1, // 10% increase
            reason: 'Strong sales at higher price points indicate room for price increase'
          });
        } else if (lowPriceSales > highPriceSales * 1.5) {
          recommendations.push({
            type: 'PRICE_SENSITIVE',
            product: product.name,
            currentPrice: product.price,
            suggestedPrice: parseFloat(product.price) * 0.95, // 5% decrease
            reason: 'Sales concentrate at lower price points - consider slight price reduction'
          });
        }
      } else {
        recommendations.push({
          type: 'NO_SALES',
          product: product.name,
          currentPrice: product.price,
          suggestedAction: 'Consider promotional pricing or marketing campaign',
          reason: 'No recent sales - product may be priced out of market'
        });
      }
    }

    return recommendations;
  }

  // Generate marketing recommendations
  async generateMarketingRecommendations(marchandId) {
    const orders = await Order.findAll({
      where: {
        marchand_id: marchandId,
        status: 'delivered',
        created_at: { [Op.gte]: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000) } // Last 180 days
      },
      include: [
        { model: OrderItem, as: 'items' },
        { model: User, as: 'client' }
      ]
    });

    // Analyze purchase patterns
    const customerSegments = {};
    const productAffinities = {};

    orders.forEach(order => {
      const clientId = order.client_id;
      if (!customerSegments[clientId]) {
        customerSegments[clientId] = {
          orderCount: 0,
          totalSpent: 0,
          products: new Set(),
          lastOrder: order.created_at
        };
      }

      const segment = customerSegments[clientId];
      segment.orderCount++;
      segment.totalSpent += parseFloat(order.total_amount);

      // Track product affinities
      const items = order.items || [];
      items.forEach(item1 => {
        segment.products.add(item1.product_id);
        items.forEach(item2 => {
          if (item1.product_id !== item2.product_id) {
            const key = [item1.product_id, item2.product_id].sort().join('-');
            productAffinities[key] = (productAffinities[key] || 0) + 1;
          }
        });
      });
    });

    const recommendations = [];

    // Customer segment recommendations
    const customers = Object.entries(customerSegments).map(([id, data]) => ({
      id,
      ...data,
      averageOrderValue: data.totalSpent / data.orderCount,
      productDiversity: data.products.size,
      daysSinceLastOrder: Math.floor((new Date() - new Date(data.lastOrder)) / (1000 * 60 * 60 * 24))
    }));

    // High-value customer retention
    const highValueCustomers = customers
      .filter(c => c.averageOrderValue > 1000 && c.daysSinceLastOrder > 30)
      .slice(0, 10);

    if (highValueCustomers.length > 0) {
      recommendations.push({
        type: 'HIGH_VALUE_RETENTION',
        customers: highValueCustomers,
        suggestedAction: 'Send personalized offers to re-engage high-value customers',
        reason: 'High-value customers inactive for over 30 days'
      });
    }

    // Product bundle suggestions
    const topAffinities = Object.entries(productAffinities)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([pair, count]) => {
        const [product1, product2] = pair.split('-');
        return { product1, product2, count };
      });

    if (topAffinities.length > 0) {
      recommendations.push({
        type: 'BUNDLE_OPPORTUNITY',
        affinities: topAffinities,
        suggestedAction: 'Create product bundles for frequently co-purchased items',
        reason: 'Strong product affinities detected'
      });
    }

    // Seasonal trend analysis
    const monthlyRevenue = Array(6).fill(0);
    orders.forEach(order => {
      const monthIndex = 5 - Math.floor((Date.now() - new Date(order.created_at)) / (30 * 24 * 60 * 60 * 1000));
      if (monthIndex >= 0) {
        monthlyRevenue[monthIndex] += parseFloat(order.total_amount);
      }
    });

    const trend = monthlyRevenue.slice(1).reduce((acc, cur, i) => {
      return acc + (cur - monthlyRevenue[i]);
    }, 0) / 5;

    if (Math.abs(trend) > 100) {
      recommendations.push({
        type: 'REVENUE_TREND',
        trend: trend > 0 ? 'POSITIVE' : 'NEGATIVE',
        suggestedAction: trend > 0
          ? 'Capitalize on growth with expanded inventory and marketing'
          : 'Consider promotional campaign to reverse negative trend',
        reason: `Revenue showing ${trend > 0 ? 'positive' : 'negative'} trend over past 6 months`
      });
    }

    return recommendations;
  }

  // Get comprehensive AI recommendations
  async getComprehensiveRecommendations(marchandId) {
    const [inventory, pricing, marketing] = await Promise.all([
      this.generateInventoryRecommendations(marchandId),
      this.generatePricingRecommendations(marchandId),
      this.generateMarketingRecommendations(marchandId)
    ]);

    return {
      inventory,
      pricing,
      marketing,
      generated_at: new Date(),
      summary: {
        urgent_actions: [
          ...inventory.filter(r => r.type === 'URGENT_RESTOCK'),
          ...pricing.filter(r => r.type === 'NO_SALES'),
          ...marketing.filter(r => r.type === 'REVENUE_TREND' && r.trend === 'NEGATIVE')
        ],
        opportunities: [
          ...inventory.filter(r => r.type === 'HIGH_DEMAND'),
          ...pricing.filter(r => r.type === 'PRICE_INCREASE'),
          ...marketing.filter(r => r.type === 'BUNDLE_OPPORTUNITY')
        ]
      }
    };
  }
}

module.exports = AIRecommendationService;
