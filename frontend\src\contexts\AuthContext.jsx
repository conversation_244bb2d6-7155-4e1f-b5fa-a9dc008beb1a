import React, { createContext, useState, useEffect, useContext } from 'react';
import api from '../services/api';
import { firebaseAuth } from '../services/firebaseAuth';
import { auth } from '../services/firebaseConfig';
import tokenService from '../services/tokenService';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sessionExpired, setSessionExpired] = useState(false);

  // Gestionnaire d'événements pour les sessions expirées
  useEffect(() => {
    const handleSessionExpired = (event) => {
      console.warn('🔒 Session expirée détectée:', event.detail);
      setSessionExpired(true);
      setError(event.detail.message || 'Votre session a expiré. Veuillez vous reconnecter.');
      setToken(null);
      setUser(null);
    };

    const handleTokenExpired = (event) => {
      console.warn('🔒 Token expiré détecté:', event.detail);
      setSessionExpired(true);
      setError('Votre session a expiré. Veuillez vous reconnecter.');
      setToken(null);
      setUser(null);
      tokenService.stopTokenCheck(); // Arrêter la vérification périodique
    };

    const handleTokenExpiringSoon = (event) => {
      console.warn('⚠️ Token expire bientôt:', event.detail);
      setError(`Votre session expire dans ${event.detail.expiresInMinutes} minute(s). Veuillez sauvegarder votre travail.`);
    };

    // Écouter les événements personnalisés
    window.addEventListener('auth:session-expired', handleSessionExpired);
    window.addEventListener('auth:token-expired', handleTokenExpired);
    window.addEventListener('auth:token-expiring-soon', handleTokenExpiringSoon);

    return () => {
      window.removeEventListener('auth:session-expired', handleSessionExpired);
      window.removeEventListener('auth:token-expired', handleTokenExpired);
      window.removeEventListener('auth:token-expiring-soon', handleTokenExpiringSoon);
      tokenService.stopTokenCheck(); // Nettoyer lors du démontage
    };
  }, []);

  // L'instance api gère automatiquement les tokens via les intercepteurs
  useEffect(() => {
    if (!token) {
      setLoading(false);
      return;
    }
  }, [token]);

  // Load user data if token exists
  useEffect(() => {
    const loadUser = async () => {
      console.log('🔍 AuthContext: Chargement utilisateur, token:', !!token);
      if (!token) {
        console.log('❌ AuthContext: Pas de token, arrêt du chargement');
        setLoading(false);
        return;
      }

      try {
        console.log('📡 AuthContext: Appel API /auth/user');
        const res = await api.get('/auth/user');
        console.log('✅ AuthContext: Utilisateur chargé:', res.data);

        // Ensure role is properly formatted as string
        const userData = res.data;
        if (userData.role && typeof userData.role === 'object') {
          userData.role = userData.role.name;
        }

        console.log('🔧 AuthContext: Rôle formaté:', userData.role);
        setUser(userData);
        setError(null);

        // Démarrer la vérification périodique des tokens si l'utilisateur est chargé avec succès
        tokenService.startTokenCheck();
      } catch (err) {
        console.error('❌ AuthContext: Erreur chargement utilisateur:', err);
        setError('Session expirée. Veuillez vous reconnecter.');
        localStorage.removeItem('token');
        setToken(null);
        setUser(null);
      } finally {
        console.log('🏁 AuthContext: Fin du chargement');
        setLoading(false);
      }
    };

    loadUser();
  }, [token]);

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      setSessionExpired(false); // Réinitialiser l'état de session expirée

      console.log('🔐 AuthContext: Début de la connexion pour:', email);
      const authData = await firebaseAuth.login(email, password);
      console.log('✅ AuthContext: Données d\'authentification reçues:', {
        hasToken: !!authData.token,
        hasUser: !!authData.user,
        userRole: authData.user?.role
      });

      // Le token est déjà stocké dans localStorage par firebaseAuth.login
      setToken(authData.token);
      setUser(authData.user);

      // Démarrer la vérification périodique des tokens
      tokenService.startTokenCheck();

      console.log('🎯 AuthContext: État mis à jour - utilisateur connecté avec le rôle:', authData.user?.role);
      return authData.user;
    } catch (err) {
      console.error('❌ AuthContext: Erreur de connexion:', err);
      setError(err.message || 'Erreur de connexion');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Register user
  const register = async (userData) => {
    try {
      setLoading(true);
      // Utiliser Firebase Auth pour l'inscription
      const authData = await firebaseAuth.register(userData);
      return authData;
    } catch (err) {
      setError(err.message || 'Erreur d\'inscription');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  // --- Impersonnalisation Admin ---
  const loginAs = (impersonatedUser, impersonationToken, originalAdminToken, impersonationExpiresAt) => {
    localStorage.setItem('token', impersonationToken);
    localStorage.setItem('originalAdminToken', originalAdminToken);
    localStorage.setItem('impersonationExpiresAt', impersonationExpiresAt);
    setToken(impersonationToken);
    setUser({ ...impersonatedUser, isImpersonating: true });
  };

  const logoutAs = async () => {
    const adminToken = localStorage.getItem('originalAdminToken');
    if (adminToken) {
      localStorage.setItem('token', adminToken);
      localStorage.removeItem('originalAdminToken');
      localStorage.removeItem('impersonationExpiresAt');
      setToken(adminToken);
      await loadUserFromToken(adminToken);
    }
  };

  // Helper pour recharger l'utilisateur à partir d'un token donné
  const loadUserFromToken = async (tokenToUse) => {
    try {
      setLoading(true);
      const res = await api.get('/auth/user', { headers: { Authorization: `Bearer ${tokenToUse}` } });
      setUser(res.data);
      setError(null);
    } catch (err) {
      setError('Session expirée. Veuillez vous reconnecter.');
      setToken(null);
      setUser(null);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
    }
  };

  // Mise à jour de logout pour nettoyer l'impersonnalisation
  const logout = async () => {
    try {
      await firebaseAuth.logout();
      setToken(null);
      setUser(null);
      setSessionExpired(false);
      localStorage.removeItem('originalAdminToken');
      localStorage.removeItem('impersonationExpiresAt');

      // Arrêter la vérification périodique des tokens
      tokenService.stopTokenCheck();
    } catch (err) {
      console.error('Erreur lors de la déconnexion:', err);
      setError(err.message || 'Erreur lors de la déconnexion');
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/profile', profileData);
      setUser(res.data);
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de mise à jour du profil');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const changePassword = async (oldPassword, newPassword) => {
    try {
      setLoading(true);
      const res = await api.put('/api/auth/change-password', { oldPassword, newPassword });
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur de changement de mot de passe');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    return user && user.role === role;
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user && !!token && !sessionExpired;
  };

  // Réinitialiser l'état de session expirée
  const clearSessionExpired = () => {
    setSessionExpired(false);
    setError(null);
  };

  const value = {
    user,
    token,
    loading,
    error,
    sessionExpired,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    hasRole,
    isAuthenticated,
    setError,
    clearSessionExpired,
    loginAs,
    logoutAs
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
