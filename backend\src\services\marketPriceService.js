/**
 * Service d'intégration des prix de marché agricole
 */

const axios = require('axios');
const { Pool } = require('pg');
const logger = require('../utils/logger');

class MarketPriceService {
  constructor() {
    this.pool = new Pool();
    this.apiKey = process.env.COMMODITIES_API_KEY;
    this.baseUrl = 'https://api.commodities-api.com/v1';
    this.cache = new Map();
    this.cacheTimeout = 4 * 60 * 60 * 1000; // 4 heures
    
    // Mapping des commodités importantes pour l'aviculture
    this.commodities = {
      'CORN': { name: '<PERSON><PERSON><PERSON>', unit: 'USD/bushel', category: 'cereales' },
      'WHEAT': { name: 'Blé', unit: 'USD/bushel', category: 'cereales' },
      'SOYBEAN': { name: 'Soja', unit: 'USD/bushel', category: 'proteines' },
      'RICE': { name: '<PERSON><PERSON>', unit: 'USD/cwt', category: 'cereales' },
      'SUGAR': { name: 'Sucre', unit: 'USD/lb', category: 'additifs' },
      'CRUDE_OIL': { name: 'Pétrole brut', unit: 'USD/barrel', category: 'energie' }
    };

    // Prix locaux simulés pour l'Algérie (en DZD)
    this.localPrices = {
      'aliment_poussin': { name: 'Aliment poussin', unit: 'DZD/kg', category: 'aliments' },
      'aliment_pondeuse': { name: 'Aliment pondeuse', unit: 'DZD/kg', category: 'aliments' },
      'aliment_chair': { name: 'Aliment chair', unit: 'DZD/kg', category: 'aliments' },
      'mais_local': { name: 'Maïs local', unit: 'DZD/kg', category: 'cereales' },
      'son_ble': { name: 'Son de blé', unit: 'DZD/kg', category: 'cereales' },
      'tourteau_soja': { name: 'Tourteau de soja', unit: 'DZD/kg', category: 'proteines' }
    };
  }

  /**
   * Obtenir les prix actuels des commodités
   */
  async getCurrentPrices() {
    try {
      const cacheKey = 'current_prices';
      
      // Vérifier le cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      // Appel API pour les prix internationaux
      const internationalPrices = await this.fetchInternationalPrices();
      
      // Générer les prix locaux (simulation)
      const localPrices = await this.generateLocalPrices();
      
      const allPrices = {
        international: internationalPrices,
        local: localPrices,
        lastUpdate: new Date().toISOString(),
        exchangeRate: await this.getExchangeRate()
      };

      // Sauvegarder en base
      await this.savePrices(allPrices);
      
      // Mettre en cache
      this.cache.set(cacheKey, {
        data: allPrices,
        timestamp: Date.now()
      });

      return allPrices;

    } catch (error) {
      logger.error('Erreur lors de la récupération des prix:', error);
      
      // Retourner les derniers prix en base en cas d'erreur
      return await this.getLatestPricesFromDB();
    }
  }

  /**
   * Récupérer les prix internationaux via API
   */
  async fetchInternationalPrices() {
    try {
      const symbols = Object.keys(this.commodities).join(',');
      
      const response = await axios.get(`${this.baseUrl}/latest`, {
        params: {
          access_key: this.apiKey,
          base: 'USD',
          symbols: symbols
        }
      });

      if (!response.data.success) {
        throw new Error('Erreur API commodities');
      }

      const prices = {};
      Object.entries(response.data.rates).forEach(([symbol, rate]) => {
        if (this.commodities[symbol]) {
          prices[symbol] = {
            ...this.commodities[symbol],
            price: rate,
            change24h: this.calculatePriceChange(symbol, rate),
            trend: this.calculateTrend(symbol, rate)
          };
        }
      });

      return prices;

    } catch (error) {
      logger.error('Erreur lors de la récupération des prix internationaux:', error);
      
      // Retourner des prix simulés en cas d'erreur
      return this.getSimulatedInternationalPrices();
    }
  }

  /**
   * Générer les prix locaux (simulation basée sur les prix internationaux)
   */
  async generateLocalPrices() {
    const localPrices = {};
    const exchangeRate = await this.getExchangeRate();
    
    // Prix de base simulés en DZD
    const basePrices = {
      'aliment_poussin': 45 + Math.random() * 10,
      'aliment_pondeuse': 42 + Math.random() * 8,
      'aliment_chair': 40 + Math.random() * 8,
      'mais_local': 35 + Math.random() * 5,
      'son_ble': 25 + Math.random() * 5,
      'tourteau_soja': 55 + Math.random() * 10
    };

    Object.entries(this.localPrices).forEach(([key, commodity]) => {
      const basePrice = basePrices[key];
      const variation = (Math.random() - 0.5) * 0.1; // ±5% de variation
      const currentPrice = basePrice * (1 + variation);
      
      localPrices[key] = {
        ...commodity,
        price: Math.round(currentPrice * 100) / 100,
        change24h: variation * 100,
        trend: variation > 0.02 ? 'up' : variation < -0.02 ? 'down' : 'stable',
        availability: this.getAvailabilityStatus(key)
      };
    });

    return localPrices;
  }

  /**
   * Obtenir le taux de change USD/DZD
   */
  async getExchangeRate() {
    try {
      // Utiliser une API de taux de change ou valeur fixe
      const response = await axios.get('https://api.exchangerate-api.com/v4/latest/USD');
      return response.data.rates.DZD || 135; // Valeur par défaut
    } catch (error) {
      logger.warn('Impossible de récupérer le taux de change, utilisation de la valeur par défaut');
      return 135; // Taux approximatif USD/DZD
    }
  }

  /**
   * Calculer le changement de prix sur 24h
   */
  calculatePriceChange(symbol, currentPrice) {
    // Simulation du changement (en production, comparer avec le prix précédent)
    return (Math.random() - 0.5) * 10; // ±5% de changement
  }

  /**
   * Calculer la tendance du prix
   */
  calculateTrend(symbol, currentPrice) {
    const change = this.calculatePriceChange(symbol, currentPrice);
    if (change > 2) return 'up';
    if (change < -2) return 'down';
    return 'stable';
  }

  /**
   * Obtenir le statut de disponibilité
   */
  getAvailabilityStatus(commodity) {
    const statuses = ['disponible', 'stock_limite', 'rupture'];
    const weights = [0.7, 0.2, 0.1]; // 70% disponible, 20% stock limité, 10% rupture
    
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < statuses.length; i++) {
      cumulative += weights[i];
      if (random <= cumulative) {
        return statuses[i];
      }
    }
    
    return 'disponible';
  }

  /**
   * Obtenir l'historique des prix
   */
  async getPriceHistory(commodity, days = 30) {
    try {
      const result = await this.pool.query(`
        SELECT * FROM market_prices 
        WHERE commodity = $1 
          AND recorded_at >= NOW() - INTERVAL '${days} days'
        ORDER BY recorded_at ASC
      `, [commodity]);

      return result.rows.map(row => ({
        date: row.recorded_at,
        price: parseFloat(row.price),
        unit: row.unit,
        data: typeof row.price_data === 'string' ? 
          JSON.parse(row.price_data) : row.price_data
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique des prix:', error);
      return [];
    }
  }

  /**
   * Analyser les tendances de prix
   */
  async analyzePriceTrends(commodity) {
    try {
      const history = await this.getPriceHistory(commodity, 90);
      
      if (history.length < 7) {
        return { trend: 'insufficient_data', analysis: null };
      }

      const prices = history.map(h => h.price);
      const dates = history.map(h => new Date(h.date));
      
      // Calculer la tendance linéaire
      const trend = this.calculateLinearTrend(prices);
      
      // Calculer la volatilité
      const volatility = this.calculateVolatility(prices);
      
      // Détecter les patterns saisonniers
      const seasonality = this.detectSeasonality(history);
      
      // Prédire le prix futur (simple)
      const prediction = this.predictFuturePrice(prices, trend);

      return {
        trend: trend > 0.1 ? 'increasing' : trend < -0.1 ? 'decreasing' : 'stable',
        trendValue: trend,
        volatility: volatility,
        seasonality: seasonality,
        prediction: prediction,
        analysis: this.generatePriceAnalysis(trend, volatility, seasonality)
      };

    } catch (error) {
      logger.error('Erreur lors de l\'analyse des tendances:', error);
      return { trend: 'error', analysis: null };
    }
  }

  /**
   * Calculer la tendance linéaire
   */
  calculateLinearTrend(prices) {
    const n = prices.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = prices;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  /**
   * Calculer la volatilité
   */
  calculateVolatility(prices) {
    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    return Math.sqrt(variance) / mean; // Coefficient de variation
  }

  /**
   * Détecter la saisonnalité
   */
  detectSeasonality(history) {
    // Analyse simplifiée par mois
    const monthlyPrices = {};
    
    history.forEach(item => {
      const month = new Date(item.date).getMonth();
      if (!monthlyPrices[month]) {
        monthlyPrices[month] = [];
      }
      monthlyPrices[month].push(item.price);
    });

    const monthlyAverages = {};
    Object.entries(monthlyPrices).forEach(([month, prices]) => {
      monthlyAverages[month] = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    });

    return monthlyAverages;
  }

  /**
   * Prédire le prix futur
   */
  predictFuturePrice(prices, trend) {
    const lastPrice = prices[prices.length - 1];
    const daysAhead = 7; // Prédiction à 7 jours
    
    return {
      price: lastPrice + (trend * daysAhead),
      confidence: this.calculatePredictionConfidence(prices),
      daysAhead: daysAhead
    };
  }

  /**
   * Calculer la confiance de la prédiction
   */
  calculatePredictionConfidence(prices) {
    const volatility = this.calculateVolatility(prices);
    
    // Plus la volatilité est faible, plus la confiance est élevée
    if (volatility < 0.05) return 'high';
    if (volatility < 0.15) return 'medium';
    return 'low';
  }

  /**
   * Générer une analyse textuelle des prix
   */
  generatePriceAnalysis(trend, volatility, seasonality) {
    const analysis = [];

    if (trend > 0.1) {
      analysis.push('Tendance haussière observée sur la période');
    } else if (trend < -0.1) {
      analysis.push('Tendance baissière observée sur la période');
    } else {
      analysis.push('Prix relativement stable sur la période');
    }

    if (volatility > 0.2) {
      analysis.push('Forte volatilité - prix imprévisibles');
    } else if (volatility > 0.1) {
      analysis.push('Volatilité modérée');
    } else {
      analysis.push('Faible volatilité - prix stables');
    }

    // Analyser la saisonnalité
    const months = Object.keys(seasonality);
    if (months.length >= 6) {
      const prices = Object.values(seasonality);
      const maxMonth = months[prices.indexOf(Math.max(...prices))];
      const minMonth = months[prices.indexOf(Math.min(...prices))];
      
      analysis.push(`Prix généralement plus élevés en ${this.getMonthName(maxMonth)}`);
      analysis.push(`Prix généralement plus bas en ${this.getMonthName(minMonth)}`);
    }

    return analysis;
  }

  /**
   * Obtenir le nom du mois
   */
  getMonthName(monthIndex) {
    const months = [
      'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
      'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
    ];
    return months[parseInt(monthIndex)];
  }

  /**
   * Sauvegarder les prix en base
   */
  async savePrices(pricesData) {
    try {
      // Sauvegarder les prix internationaux
      for (const [symbol, data] of Object.entries(pricesData.international)) {
        await this.pool.query(`
          INSERT INTO market_prices (commodity, price, unit, category, price_data, recorded_at)
          VALUES ($1, $2, $3, $4, $5, NOW())
        `, [
          symbol,
          data.price,
          data.unit,
          data.category,
          JSON.stringify(data)
        ]);
      }

      // Sauvegarder les prix locaux
      for (const [symbol, data] of Object.entries(pricesData.local)) {
        await this.pool.query(`
          INSERT INTO market_prices (commodity, price, unit, category, price_data, recorded_at)
          VALUES ($1, $2, $3, $4, $5, NOW())
        `, [
          symbol,
          data.price,
          data.unit,
          data.category,
          JSON.stringify(data)
        ]);
      }

    } catch (error) {
      logger.error('Erreur lors de la sauvegarde des prix:', error);
    }
  }

  /**
   * Obtenir les derniers prix depuis la base
   */
  async getLatestPricesFromDB() {
    try {
      const result = await this.pool.query(`
        SELECT DISTINCT ON (commodity) 
          commodity, price, unit, category, price_data, recorded_at
        FROM market_prices 
        ORDER BY commodity, recorded_at DESC
      `);

      const prices = { international: {}, local: {}, lastUpdate: null };
      
      result.rows.forEach(row => {
        const data = typeof row.price_data === 'string' ? 
          JSON.parse(row.price_data) : row.price_data;
        
        if (this.commodities[row.commodity]) {
          prices.international[row.commodity] = data;
        } else if (this.localPrices[row.commodity]) {
          prices.local[row.commodity] = data;
        }
        
        if (!prices.lastUpdate || new Date(row.recorded_at) > new Date(prices.lastUpdate)) {
          prices.lastUpdate = row.recorded_at;
        }
      });

      return prices;

    } catch (error) {
      logger.error('Erreur lors de la récupération des prix depuis la base:', error);
      return { international: {}, local: {}, lastUpdate: null };
    }
  }

  /**
   * Obtenir des prix internationaux simulés
   */
  getSimulatedInternationalPrices() {
    const prices = {};
    
    Object.entries(this.commodities).forEach(([symbol, commodity]) => {
      const basePrice = this.getBasePriceForCommodity(symbol);
      const variation = (Math.random() - 0.5) * 0.2; // ±10% de variation
      
      prices[symbol] = {
        ...commodity,
        price: basePrice * (1 + variation),
        change24h: variation * 100,
        trend: variation > 0.05 ? 'up' : variation < -0.05 ? 'down' : 'stable'
      };
    });

    return prices;
  }

  /**
   * Obtenir le prix de base pour une commodité
   */
  getBasePriceForCommodity(symbol) {
    const basePrices = {
      'CORN': 6.50,
      'WHEAT': 8.20,
      'SOYBEAN': 14.80,
      'RICE': 16.50,
      'SUGAR': 0.18,
      'CRUDE_OIL': 75.00
    };
    
    return basePrices[symbol] || 10.00;
  }

  /**
   * Démarrer la collecte automatique des prix
   */
  startAutomaticPriceCollection() {
    // Collecter les prix toutes les 4 heures
    setInterval(async () => {
      try {
        await this.getCurrentPrices();
        logger.info('Collecte automatique des prix effectuée');
      } catch (error) {
        logger.error('Erreur lors de la collecte automatique des prix:', error);
      }
    }, 4 * 60 * 60 * 1000); // 4 heures
  }

  /**
   * Obtenir les alertes de prix
   */
  async getPriceAlerts() {
    try {
      const currentPrices = await this.getCurrentPrices();
      const alerts = [];

      // Analyser les changements significatifs
      Object.entries(currentPrices.local).forEach(([commodity, data]) => {
        if (Math.abs(data.change24h) > 5) {
          alerts.push({
            type: 'price_change',
            commodity: commodity,
            change: data.change24h,
            severity: Math.abs(data.change24h) > 10 ? 'high' : 'medium',
            message: `${data.name}: ${data.change24h > 0 ? 'hausse' : 'baisse'} de ${Math.abs(data.change24h).toFixed(1)}%`
          });
        }

        if (data.availability === 'rupture') {
          alerts.push({
            type: 'availability',
            commodity: commodity,
            severity: 'high',
            message: `${data.name}: rupture de stock signalée`
          });
        }
      });

      return alerts;

    } catch (error) {
      logger.error('Erreur lors de la génération des alertes de prix:', error);
      return [];
    }
  }
}

module.exports = new MarketPriceService();
