# Tableau de Bord Éleveur - Poultray DZ

Ce module implémente le tableau de bord pour les éleveurs de la plateforme Poultray DZ. Il fournit une interface complète pour visualiser les statistiques, suivre la production, gérer les alertes et consulter l'activité récente.

## Structure des Composants

```
DashboardEleveur/
├── DashboardEleveur.jsx       # Composant principal
├── DashboardEleveur.css       # Styles spécifiques
├── components/                # Sous-composants
│   ├── Overview/              # Vue d'ensemble
│   ├── ProductionTracking/    # Suivi de production
│   ├── AlertsActions/         # Alertes et actions
│   └── RecentActivity/        # Activité récente
└── hooks/                     # Hooks personnalisés
    ├── useEleveurData.js      # Gestion des données éleveur
    ├── useProductionData.js   # Gestion des données de production
    └── useAuthToken.js        # Gestion des tokens d'authentification
```

## Fonctionnalités Principales

### Vue d'Ensemble (Overview)
- Affichage des statistiques clés (total volailles, production journalière, taux de mortalité, revenu mensuel)
- Graphiques de tendance de production et de santé

### Suivi de Production (ProductionTracking)
- Visualisation détaillée de la production par période (jour, semaine, mois)
- Filtrage par type de volaille
- Comparaison avec les périodes précédentes

### Alertes et Actions (AlertsActions)
- Affichage des alertes importantes classées par priorité
- Accès rapide aux actions fréquentes

### Activité Récente (RecentActivity)
- Historique chronologique des activités
- Détails sur chaque action effectuée

## Gestion de l'Authentification

Le hook `useAuthToken` implémente une solution complète pour gérer l'authentification JWT et résoudre les problèmes d'erreurs 401 :

- Ajout automatique du token à toutes les requêtes API
- Rafraîchissement automatique du token expiré
- Redirection vers la page de connexion en cas d'échec d'authentification

## Intégration dans l'Application

Pour intégrer ce tableau de bord dans l'application, suivez ces étapes :

1. Assurez-vous que les dépendances sont installées :
   ```bash
   npm install recharts @mui/material @mui/icons-material
   ```

2. Importez le composant dans votre routeur :
   ```jsx
   import DashboardEleveur from './components/DashboardEleveur/DashboardEleveur';

   // Dans votre configuration de routes
   <Route path="/eleveur/dashboard" element={<DashboardEleveur />} />
   ```

3. Configurez les endpoints API dans les hooks personnalisés selon votre backend.

## Personnalisation

Le tableau de bord est conçu pour être facilement personnalisable :

- Modifiez les couleurs dans `DashboardEleveur.css`
- Ajustez la mise en page dans `DashboardEleveur.jsx`
- Personnalisez les graphiques dans les composants individuels

## Gestion des Données

Les hooks personnalisés (`useEleveurData` et `useProductionData`) gèrent la récupération et le traitement des données. Ils incluent :

- Chargement initial des données
- Rafraîchissement automatique à intervalles réguliers
- Gestion des erreurs avec fallback sur des données fictives pour la démonstration
- Fonctions utilitaires pour le calcul de statistiques

## Responsive Design

Le tableau de bord est entièrement responsive et s'adapte à tous les appareils :

- Desktop : Affichage complet avec tous les widgets
- Tablette : Réorganisation des widgets pour optimiser l'espace
- Mobile : Affichage vertical avec taille adaptée des graphiques

---

*Pour toute question ou suggestion d'amélioration, veuillez contacter l'équipe de développement de Poultray DZ.*
