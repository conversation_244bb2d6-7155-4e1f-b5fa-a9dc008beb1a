/**
 * Routes pour les intégrations externes
 */

const express = require('express');
const router = express.Router();
const { body, query, param } = require('express-validator');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const weatherService = require('../services/weatherService');
const marketPriceService = require('../services/marketPriceService');
const iotIntegrationService = require('../services/iotIntegrationService');

/**
 * @swagger
 * /api/integrations/weather/current:
 *   get:
 *     summary: Obtenir les données météo actuelles
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: farmId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Données météo actuelles
 */
router.get('/weather/current',
  auth,
  validate([
    query('farmId').isInt()
  ]),
  async (req, res) => {
    try {
      const { farmId } = req.query;
      
      // Vérifier l'accès à la ferme
      const hasAccess = await checkFarmAccess(req.user, farmId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette ferme'
        });
      }

      // Obtenir les coordonnées de la ferme
      const coordinates = await weatherService.getFarmCoordinates(farmId);
      
      // Récupérer les données météo
      const weatherData = await weatherService.getCurrentWeather(
        farmId, 
        coordinates.lat, 
        coordinates.lon
      );

      res.json({
        success: true,
        data: weatherData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des données météo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/weather/forecast:
 *   get:
 *     summary: Obtenir les prévisions météo
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: farmId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Prévisions météo sur 5 jours
 */
router.get('/weather/forecast',
  auth,
  validate([
    query('farmId').isInt()
  ]),
  async (req, res) => {
    try {
      const { farmId } = req.query;
      
      const hasAccess = await checkFarmAccess(req.user, farmId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette ferme'
        });
      }

      const coordinates = await weatherService.getFarmCoordinates(farmId);
      const forecastData = await weatherService.getWeatherForecast(
        farmId, 
        coordinates.lat, 
        coordinates.lon
      );

      res.json({
        success: true,
        data: forecastData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des prévisions météo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/weather/alerts:
 *   get:
 *     summary: Obtenir les alertes météo
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: farmId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Alertes météo et recommandations
 */
router.get('/weather/alerts',
  auth,
  validate([
    query('farmId').isInt()
  ]),
  async (req, res) => {
    try {
      const { farmId } = req.query;
      
      const hasAccess = await checkFarmAccess(req.user, farmId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à cette ferme'
        });
      }

      const coordinates = await weatherService.getFarmCoordinates(farmId);
      const alertsData = await weatherService.getWeatherAlerts(
        farmId, 
        coordinates.lat, 
        coordinates.lon
      );

      res.json({
        success: true,
        data: alertsData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des alertes météo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/market/prices:
 *   get:
 *     summary: Obtenir les prix de marché actuels
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Prix de marché internationaux et locaux
 */
router.get('/market/prices',
  auth,
  async (req, res) => {
    try {
      const pricesData = await marketPriceService.getCurrentPrices();

      res.json({
        success: true,
        data: pricesData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des prix de marché',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/market/history:
 *   get:
 *     summary: Obtenir l'historique des prix
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: commodity
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *     responses:
 *       200:
 *         description: Historique des prix
 */
router.get('/market/history',
  auth,
  validate([
    query('commodity').isString().notEmpty(),
    query('days').optional().isInt({ min: 1, max: 365 })
  ]),
  async (req, res) => {
    try {
      const { commodity, days = 30 } = req.query;
      
      const historyData = await marketPriceService.getPriceHistory(commodity, days);

      res.json({
        success: true,
        data: historyData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique des prix',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/market/trends:
 *   get:
 *     summary: Analyser les tendances de prix
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: commodity
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Analyse des tendances
 */
router.get('/market/trends',
  auth,
  validate([
    query('commodity').isString().notEmpty()
  ]),
  async (req, res) => {
    try {
      const { commodity } = req.query;
      
      const trendsData = await marketPriceService.analyzePriceTrends(commodity);

      res.json({
        success: true,
        data: trendsData
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des tendances',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/iot/devices:
 *   get:
 *     summary: Obtenir la liste des dispositifs IoT
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: farmId
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des dispositifs IoT
 */
router.get('/iot/devices',
  auth,
  validate([
    query('farmId').optional().isInt()
  ]),
  async (req, res) => {
    try {
      const { farmId } = req.query;
      
      if (farmId) {
        const hasAccess = await checkFarmAccess(req.user, farmId);
        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'Accès non autorisé à cette ferme'
          });
        }
      }

      const devicesStatus = iotIntegrationService.getDevicesStatus();

      res.json({
        success: true,
        data: devicesStatus
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des dispositifs IoT',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/iot/data:
 *   post:
 *     summary: Recevoir des données IoT (webhook)
 *     tags: [Integrations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: string
 *               sensorType:
 *                 type: string
 *               value:
 *                 type: any
 *               unit:
 *                 type: string
 *               timestamp:
 *                 type: string
 *     responses:
 *       200:
 *         description: Données reçues et traitées
 */
router.post('/iot/data',
  validate([
    body('deviceId').isString().notEmpty(),
    body('sensorType').isString().notEmpty(),
    body('value').exists()
  ]),
  async (req, res) => {
    try {
      const deviceData = {
        deviceId: req.body.deviceId,
        sensorType: req.body.sensorType,
        value: req.body.value,
        unit: req.body.unit,
        timestamp: req.body.timestamp || new Date().toISOString(),
        metadata: req.body.metadata || {}
      };

      await iotIntegrationService.handleDeviceDataFromWebSocket(deviceData);

      res.json({
        success: true,
        message: 'Données IoT reçues et traitées'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors du traitement des données IoT',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/iot/command:
 *   post:
 *     summary: Envoyer une commande à un dispositif IoT
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: string
 *               command:
 *                 type: string
 *               parameters:
 *                 type: object
 *     responses:
 *       200:
 *         description: Commande envoyée
 */
router.post('/iot/command',
  auth,
  validate([
    body('deviceId').isString().notEmpty(),
    body('command').isString().notEmpty(),
    body('parameters').optional().isObject()
  ]),
  async (req, res) => {
    try {
      const { deviceId, command, parameters = {} } = req.body;
      
      // Vérifier l'accès au dispositif
      const hasAccess = await checkDeviceAccess(req.user, deviceId);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé à ce dispositif'
        });
      }

      const result = await iotIntegrationService.sendDeviceCommand(
        deviceId, 
        command, 
        parameters
      );

      res.json({
        success: result.success,
        message: result.message || result.error
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'envoi de la commande',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/iot/alerts:
 *   get:
 *     summary: Obtenir les alertes IoT récentes
 *     tags: [Integrations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: farmId
 *         schema:
 *           type: integer
 *       - in: query
 *         name: hours
 *         schema:
 *           type: integer
 *           default: 24
 *     responses:
 *       200:
 *         description: Alertes IoT récentes
 */
router.get('/iot/alerts',
  auth,
  validate([
    query('farmId').optional().isInt(),
    query('hours').optional().isInt({ min: 1, max: 168 })
  ]),
  async (req, res) => {
    try {
      const { farmId, hours = 24 } = req.query;
      
      if (farmId) {
        const hasAccess = await checkFarmAccess(req.user, farmId);
        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: 'Accès non autorisé à cette ferme'
          });
        }
      }

      const alerts = await iotIntegrationService.getRecentAlerts(farmId, hours);

      res.json({
        success: true,
        data: alerts
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des alertes IoT',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/integrations/webhooks:
 *   post:
 *     summary: Recevoir des webhooks génériques
 *     tags: [Integrations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook traité
 */
router.post('/webhooks',
  async (req, res) => {
    try {
      const webhookData = {
        headers: req.headers,
        body: req.body,
        timestamp: new Date().toISOString()
      };

      // Traiter le webhook selon le type
      await processWebhook(webhookData);

      res.json({
        success: true,
        message: 'Webhook traité avec succès'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors du traitement du webhook',
        error: error.message
      });
    }
  }
);

/**
 * Fonctions utilitaires
 */

async function checkFarmAccess(user, farmId) {
  const { Pool } = require('pg');
  const pool = new Pool();
  
  if (user.role === 'admin') {
    return true;
  }
  
  if (user.role === 'eleveur') {
    const result = await pool.query(
      'SELECT id FROM fermes WHERE id = $1 AND proprietaire_id = $2',
      [farmId, user.id]
    );
    return result.rows.length > 0;
  }
  
  if (user.role === 'veterinaire') {
    // Les vétérinaires peuvent accéder aux fermes de leurs consultations
    const result = await pool.query(`
      SELECT DISTINCT f.id 
      FROM fermes f
      JOIN volailles v ON f.id = v.ferme_id
      JOIN consultations c ON v.id = c.volaille_id
      WHERE f.id = $1 AND c.veterinaire_id = $2
    `, [farmId, user.id]);
    return result.rows.length > 0;
  }
  
  return false;
}

async function checkDeviceAccess(user, deviceId) {
  const { Pool } = require('pg');
  const pool = new Pool();
  
  if (user.role === 'admin') {
    return true;
  }
  
  // Vérifier l'accès via la ferme du dispositif
  const result = await pool.query(`
    SELECT d.farm_id 
    FROM iot_devices d
    WHERE d.device_id = $1
  `, [deviceId]);
  
  if (result.rows.length === 0) {
    return false;
  }
  
  const farmId = result.rows[0].farm_id;
  return await checkFarmAccess(user, farmId);
}

async function processWebhook(webhookData) {
  // Traitement générique des webhooks
  // À personnaliser selon les besoins spécifiques
  
  const { Pool } = require('pg');
  const pool = new Pool();
  
  try {
    // Enregistrer le webhook
    await pool.query(`
      INSERT INTO webhook_logs (
        event_type, payload, response_status, success, triggered_at
      ) VALUES ($1, $2, $3, $4, NOW())
    `, [
      webhookData.headers['x-event-type'] || 'unknown',
      JSON.stringify(webhookData.body),
      200,
      true
    ]);
    
    // Traiter selon le type d'événement
    const eventType = webhookData.headers['x-event-type'];
    
    switch (eventType) {
      case 'weather_alert':
        await processWeatherAlert(webhookData.body);
        break;
      case 'price_update':
        await processPriceUpdate(webhookData.body);
        break;
      case 'device_status':
        await processDeviceStatus(webhookData.body);
        break;
      default:
        logger.info('Webhook reçu avec type inconnu:', eventType);
    }
    
  } catch (error) {
    logger.error('Erreur lors du traitement du webhook:', error);
    throw error;
  }
}

async function processWeatherAlert(data) {
  // Traiter les alertes météo
  logger.info('Alerte météo reçue:', data);
}

async function processPriceUpdate(data) {
  // Traiter les mises à jour de prix
  logger.info('Mise à jour de prix reçue:', data);
}

async function processDeviceStatus(data) {
  // Traiter les changements de statut de dispositif
  logger.info('Changement de statut de dispositif:', data);
}

module.exports = router;
