import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  IconButton,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Notifications as NotificationsIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

const AlerteStock = () => {
  const { t } = useTranslation();
  const [alertes, setAlertes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAlerte, setSelectedAlerte] = useState(null);
  const [formData, setFormData] = useState({
    type: 'stock',
    priorite: 'normale',
    message: '',
    message_ar: '',
    seuil: '',
    source_donnees: '',
    action_recommandee: '',
    date_expiration: '',
    frequence_rappel: 'quotidien',
    canaux_notification: ['email'],
    conditions_resolution: '',
    contacts_urgence: ''
  });

  useEffect(() => {
    fetchAlertes();
  }, []);

  const fetchAlertes = async () => {
    try {
      const response = await axios.get('/api/alerte-stock');
      setAlertes(response.data);
      setLoading(false);
    } catch (err) {
      setError(t('errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleOpenDialog = (alerte = null) => {
    if (alerte) {
      setSelectedAlerte(alerte);
      setFormData({
        type: alerte.type,
        priorite: alerte.priorite,
        message: alerte.message,
        message_ar: alerte.message_ar || '',
        seuil: alerte.seuil || '',
        source_donnees: alerte.source_donnees || '',
        action_recommandee: alerte.action_recommandee || '',
        date_expiration: alerte.date_expiration?.split('T')[0] || '',
        frequence_rappel: alerte.frequence_rappel || 'quotidien',
        canaux_notification: alerte.canaux_notification || ['email'],
        conditions_resolution: alerte.conditions_resolution || '',
        contacts_urgence: alerte.contacts_urgence || ''
      });
    } else {
      setSelectedAlerte(null);
      setFormData({
        type: 'stock',
        priorite: 'normale',
        message: '',
        message_ar: '',
        seuil: '',
        source_donnees: '',
        action_recommandee: '',
        date_expiration: '',
        frequence_rappel: 'quotidien',
        canaux_notification: ['email'],
        conditions_resolution: '',
        contacts_urgence: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAlerte(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChannelsChange = (e) => {
    setFormData(prev => ({
      ...prev,
      canaux_notification: e.target.value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (selectedAlerte) {
        await axios.put(`/api/alerte-stock/${selectedAlerte.id}`, formData);
      } else {
        await axios.post('/api/alerte-stock', formData);
      }
      fetchAlertes();
      handleCloseDialog();
    } catch (err) {
      setError(t('errors.saveFailed'));
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('confirmations.deleteAlert'))) {
      try {
        await axios.delete(`/api/alerte-stock/${id}`);
        fetchAlertes();
      } catch (err) {
        setError(t('errors.deleteFailed'));
      }
    }
  };

  const handleMarkAsRead = async (id) => {
    try {
      await axios.put(`/api/alerte-stock/${id}/view`);
      fetchAlertes();
    } catch (err) {
      setError(t('errors.updateFailed'));
    }
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'haute': return <ErrorIcon color="error" />;
      case 'moyenne': return <WarningIcon color="warning" />;
      case 'normale': return <InfoIcon color="info" />;
      case 'basse': return <InfoIcon color="action" />;
      default: return <InfoIcon />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'haute': return 'error';
      case 'moyenne': return 'warning';
      case 'normale': return 'info';
      case 'basse': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          {t('alerts.title')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('alerts.addAlert')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={2}>
        {alertes.map((alerte) => (
          <Grid item xs={12} key={alerte.id}>
            <Card
              sx={{
                borderLeft: 6,
                borderColor: `${getPriorityColor(alerte.priorite)}.main`,
                opacity: alerte.vue ? 0.7 : 1
              }}
            >
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item>
                    {getPriorityIcon(alerte.priorite)}
                  </Grid>
                  <Grid item xs>
                    <Typography variant="h6" component="div">
                      {alerte.message}
                    </Typography>
                    {alerte.message_ar && (
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        dir="rtl"
                        sx={{ mt: 1 }}
                      >
                        {alerte.message_ar}
                      </Typography>
                    )}
                    <Box sx={{ mt: 1 }}>
                      <Chip
                        size="small"
                        label={t(`alerts.types.${alerte.type}`)}
                        sx={{ mr: 1 }}
                      />
                      <Chip
                        size="small"
                        label={t(`alerts.priorities.${alerte.priorite}`)}
                        color={getPriorityColor(alerte.priorite)}
                        sx={{ mr: 1 }}
                      />
                      {alerte.seuil && (
                        <Chip
                          size="small"
                          label={`${t('alerts.threshold')}: ${alerte.seuil}`}
                          sx={{ mr: 1 }}
                        />
                      )}
                    </Box>
                    {alerte.action_recommandee && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        <strong>{t('alerts.recommendedAction')}:</strong> {alerte.action_recommandee}
                      </Typography>
                    )}
                  </Grid>
                  <Grid item>
                    <IconButton
                      onClick={() => handleMarkAsRead(alerte.id)}
                      color="primary"
                      disabled={alerte.vue}
                    >
                      <CheckCircleIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleOpenDialog(alerte)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleDelete(alerte.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedAlerte ? t('alerts.editAlert') : t('alerts.addAlert')}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('alerts.type')}</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  label={t('alerts.type')}
                >
                  <MenuItem value="stock">{t('alerts.types.stock')}</MenuItem>
                  <MenuItem value="sante">{t('alerts.types.sante')}</MenuItem>
                  <MenuItem value="production">{t('alerts.types.production')}</MenuItem>
                  <MenuItem value="maintenance">{t('alerts.types.maintenance')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('alerts.priority')}</InputLabel>
                <Select
                  name="priorite"
                  value={formData.priorite}
                  onChange={handleInputChange}
                  label={t('alerts.priority')}
                >
                  <MenuItem value="haute">{t('alerts.priorities.haute')}</MenuItem>
                  <MenuItem value="moyenne">{t('alerts.priorities.moyenne')}</MenuItem>
                  <MenuItem value="normale">{t('alerts.priorities.normale')}</MenuItem>
                  <MenuItem value="basse">{t('alerts.priorities.basse')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="message"
                label={t('alerts.message')}
                multiline
                rows={2}
                value={formData.message}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="message_ar"
                label={t('alerts.messageAr')}
                multiline
                rows={2}
                value={formData.message_ar}
                onChange={handleInputChange}
                fullWidth
                dir="rtl"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="seuil"
                label={t('alerts.threshold')}
                value={formData.seuil}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="source_donnees"
                label={t('alerts.dataSource')}
                value={formData.source_donnees}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="action_recommandee"
                label={t('alerts.recommendedAction')}
                multiline
                rows={2}
                value={formData.action_recommandee}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="date_expiration"
                label={t('alerts.expirationDate')}
                type="date"
                value={formData.date_expiration}
                onChange={handleInputChange}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('alerts.reminderFrequency')}</InputLabel>
                <Select
                  name="frequence_rappel"
                  value={formData.frequence_rappel}
                  onChange={handleInputChange}
                  label={t('alerts.reminderFrequency')}
                >
                  <MenuItem value="quotidien">{t('alerts.frequencies.daily')}</MenuItem>
                  <MenuItem value="hebdomadaire">{t('alerts.frequencies.weekly')}</MenuItem>
                  <MenuItem value="mensuel">{t('alerts.frequencies.monthly')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>{t('alerts.notificationChannels')}</InputLabel>
                <Select
                  multiple
                  name="canaux_notification"
                  value={formData.canaux_notification}
                  onChange={handleNotificationChannelsChange}
                  label={t('alerts.notificationChannels')}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={t(`alerts.channels.${value}`)}
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="email">{t('alerts.channels.email')}</MenuItem>
                  <MenuItem value="sms">{t('alerts.channels.sms')}</MenuItem>
                  <MenuItem value="app">{t('alerts.channels.app')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="conditions_resolution"
                label={t('alerts.resolutionConditions')}
                multiline
                rows={2}
                value={formData.conditions_resolution}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="contacts_urgence"
                label={t('alerts.emergencyContacts')}
                multiline
                rows={2}
                value={formData.contacts_urgence}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {selectedAlerte ? t('common.save') : t('common.add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AlerteStock;