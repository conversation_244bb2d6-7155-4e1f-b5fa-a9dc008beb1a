import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Card, 
  CardHeader, 
  CardContent, 
  Divider, 
  Grid,
  Skeleton,
  Tooltip
} from '@mui/material';
import { 
  WbSunny, 
  Cloud, 
  Opacity, 
  Air, 
  Thermostat,
  WaterDrop,
  Thunderstorm,
  AcUnit,
  Grain
} from '@mui/icons-material';
import axios from 'axios';

/**
 * Composant Widget Météo pour le tableau de bord éleveur
 * Affiche les conditions météorologiques actuelles et prévues qui peuvent affecter la production
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {string} props.location - Localisation pour laquelle afficher la météo (ville ou coordonnées)
 * @returns {JSX.Element} - Le composant widget météo
 */
const WeatherWidget = ({ location = 'Alger, Algérie' }) => {
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fonction pour récupérer les données météo
    const fetchWeatherData = async () => {
      try {
        setLoading(true);
        
        // Dans une application réelle, vous feriez un appel à une API météo comme OpenWeatherMap
        // const response = await axios.get(`https://api.openweathermap.org/data/2.5/weather?q=${location}&appid=YOUR_API_KEY&units=metric`);
        // setWeatherData(response.data);
        
        // Pour la démonstration, nous utilisons des données simulées
        setTimeout(() => {
          setWeatherData({
            current: {
              temp: 24,
              humidity: 65,
              wind_speed: 12,
              weather: 'sunny', // sunny, cloudy, rainy, stormy, snowy
              description: 'Ensoleillé'
            },
            forecast: [
              { day: 'Aujourd\'hui', temp: 24, weather: 'sunny', description: 'Ensoleillé' },
              { day: 'Demain', temp: 22, weather: 'cloudy', description: 'Nuageux' },
              { day: 'Après-demain', temp: 20, weather: 'rainy', description: 'Pluvieux' }
            ],
            alerts: [
              { type: 'heat', message: 'Températures élevées prévues pour les 3 prochains jours' }
            ],
            impact: {
              production: 'Positif - Conditions idéales pour la croissance',
              health: 'Attention - Surveillez l\'hydratation des volailles'
            }
          });
          setLoading(false);
        }, 1000);
        
      } catch (err) {
        console.error('Erreur lors de la récupération des données météo:', err);
        setError('Impossible de charger les données météo');
        setLoading(false);
      }
    };

    fetchWeatherData();
    
    // Rafraîchir les données toutes les heures
    const intervalId = setInterval(fetchWeatherData, 60 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, [location]);

  // Fonction pour obtenir l'icône en fonction des conditions météo
  const getWeatherIcon = (weather) => {
    switch (weather) {
      case 'sunny':
        return <WbSunny sx={{ color: '#F59E0B' }} />;
      case 'cloudy':
        return <Cloud sx={{ color: '#94A3B8' }} />;
      case 'rainy':
        return <Opacity sx={{ color: '#3B82F6' }} />;
      case 'stormy':
        return <Thunderstorm sx={{ color: '#6366F1' }} />;
      case 'snowy':
        return <AcUnit sx={{ color: '#E5E7EB' }} />;
      default:
        return <WbSunny sx={{ color: '#F59E0B' }} />;
    }
  };

  // Rendu pendant le chargement
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardHeader title="Météo" />
        <Divider />
        <CardContent>
          <Skeleton variant="rectangular" height={120} />
        </CardContent>
      </Card>
    );
  }

  // Rendu en cas d'erreur
  if (error) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardHeader title="Météo" />
        <Divider />
        <CardContent>
          <Typography color="error">{error}</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader title={
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {getWeatherIcon(weatherData.current.weather)}
          <Typography variant="h6" sx={{ ml: 1 }}>Météo - {location}</Typography>
        </Box>
      } />
      <Divider />
      <CardContent>
        {/* Conditions actuelles */}
        <Box sx={{ mb: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Thermostat sx={{ mr: 1, color: weatherData.current.temp > 25 ? '#EF4444' : '#3B82F6' }} />
                <Typography variant="h3" fontWeight="bold">
                  {weatherData.current.temp}°C
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {weatherData.current.description}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <WaterDrop sx={{ mr: 1, color: '#3B82F6' }} />
                <Typography variant="body2">
                  Humidité: {weatherData.current.humidity}%
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Air sx={{ mr: 1, color: '#94A3B8' }} />
                <Typography variant="body2">
                  Vent: {weatherData.current.wind_speed} km/h
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Prévisions */}
        <Typography variant="subtitle2" gutterBottom>Prévisions</Typography>
        <Grid container spacing={1}>
          {weatherData.forecast.map((day, index) => (
            <Grid item xs={4} key={index}>
              <Box 
                sx={{ 
                  textAlign: 'center',
                  p: 1,
                  borderRadius: 1,
                  bgcolor: 'background.paper',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                }}
              >
                <Typography variant="caption" display="block">
                  {day.day}
                </Typography>
                {getWeatherIcon(day.weather)}
                <Typography variant="body2" fontWeight="bold">
                  {day.temp}°C
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Impact sur l'élevage */}
        <Box sx={{ mt: 2, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Impact sur votre élevage
          </Typography>
          <Tooltip title="Basé sur les conditions météorologiques et les caractéristiques de votre élevage">
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Grain sx={{ mr: 1, color: '#10B981' }} />
              <Typography variant="body2">
                {weatherData.impact.production}
              </Typography>
            </Box>
          </Tooltip>
          <Tooltip title="Recommandations pour la santé de vos volailles">
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Thermostat sx={{ mr: 1, color: '#F59E0B' }} />
              <Typography variant="body2">
                {weatherData.impact.health}
              </Typography>
            </Box>
          </Tooltip>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WeatherWidget;
