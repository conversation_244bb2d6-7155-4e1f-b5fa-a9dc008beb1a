/**
 * Service de gestion des tokens JWT
 * Gère la vérification périodique des tokens et leur renouvellement
 */

class TokenService {
  constructor() {
    this.checkInterval = null;
    this.CHECK_INTERVAL_MS = 60000; // Vérifier toutes les minutes
    this.WARNING_THRESHOLD_MS = 5 * 60 * 1000; // Avertir 5 minutes avant expiration
  }

  /**
   * Démarre la vérification périodique des tokens
   */
  startTokenCheck() {
    if (this.checkInterval) {
      this.stopTokenCheck();
    }

    this.checkInterval = setInterval(() => {
      this.checkTokenExpiration();
    }, this.CHECK_INTERVAL_MS);

    // Vérification immédiate
    this.checkTokenExpiration();
  }

  /**
   * Arrête la vérification périodique des tokens
   */
  stopTokenCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Vérifie l'expiration du token actuel
   */
  checkTokenExpiration() {
    const token = localStorage.getItem('token');
    
    if (!token) {
      return;
    }

    try {
      const tokenData = this.parseToken(token);
      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiration = tokenData.exp - now;

      // Token déjà expiré
      if (timeUntilExpiration <= 0) {
        console.warn('🔒 Token expiré détecté par le service de vérification');
        this.handleTokenExpired();
        return;
      }

      // Token expire bientôt (moins de 5 minutes)
      if (timeUntilExpiration * 1000 <= this.WARNING_THRESHOLD_MS) {
        console.warn('⚠️ Token expire bientôt:', {
          expiresIn: Math.floor(timeUntilExpiration / 60) + ' minutes',
          expirationTime: new Date(tokenData.exp * 1000)
        });
        
        this.handleTokenExpiringSoon(timeUntilExpiration);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du token:', error);
      this.handleTokenExpired();
    }
  }

  /**
   * Parse un token JWT et retourne ses données
   */
  parseToken(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      
      return JSON.parse(jsonPayload);
    } catch (error) {
      throw new Error('Token malformé');
    }
  }

  /**
   * Gère un token expiré
   */
  handleTokenExpired() {
    localStorage.removeItem('token');
    localStorage.removeItem('originalAdminToken');
    localStorage.removeItem('impersonationExpiresAt');
    
    // Émettre un événement pour notifier l'application
    window.dispatchEvent(new CustomEvent('auth:token-expired', {
      detail: { 
        reason: 'Token expiré (vérification périodique)',
        timestamp: new Date().toISOString()
      }
    }));
    
    this.stopTokenCheck();
  }

  /**
   * Gère un token qui expire bientôt
   */
  handleTokenExpiringSoon(timeUntilExpiration) {
    // Émettre un événement pour avertir l'utilisateur
    window.dispatchEvent(new CustomEvent('auth:token-expiring-soon', {
      detail: { 
        timeUntilExpiration,
        expiresInMinutes: Math.floor(timeUntilExpiration / 60),
        timestamp: new Date().toISOString()
      }
    }));
  }

  /**
   * Vérifie si un token est valide (non expiré et bien formé)
   */
  isTokenValid(token = null) {
    const tokenToCheck = token || localStorage.getItem('token');
    
    if (!tokenToCheck) {
      return false;
    }

    try {
      const tokenData = this.parseToken(tokenToCheck);
      const now = Math.floor(Date.now() / 1000);
      
      return tokenData.exp > now;
    } catch (error) {
      return false;
    }
  }

  /**
   * Obtient les informations d'expiration d'un token
   */
  getTokenExpirationInfo(token = null) {
    const tokenToCheck = token || localStorage.getItem('token');
    
    if (!tokenToCheck) {
      return null;
    }

    try {
      const tokenData = this.parseToken(tokenToCheck);
      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiration = tokenData.exp - now;
      
      return {
        isValid: timeUntilExpiration > 0,
        expiresAt: new Date(tokenData.exp * 1000),
        timeUntilExpiration,
        expiresInMinutes: Math.floor(timeUntilExpiration / 60),
        isExpiringSoon: timeUntilExpiration * 1000 <= this.WARNING_THRESHOLD_MS
      };
    } catch (error) {
      return null;
    }
  }
}

// Instance singleton
const tokenService = new TokenService();

export default tokenService;
