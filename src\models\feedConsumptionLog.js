const { DataTypes } = require('sequelize');

/**
 * FeedConsumptionLog model - Tracks feed consumption at farms
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedConsumptionLog = sequelize.define('FeedConsumptionLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    farm_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'fermes',
        key: 'id'
      }
    },
    feed_stock_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'feed_stock',
        key: 'id'
      }
    },
    poultry_batch_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'poussins',
        key: 'id'
      },
      comment: 'specific poultry batch that consumed the feed'
    },
    quantity_consumed: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      validate: {
        min: 0.001
      },
      comment: 'quantity of feed consumed'
    },
    consumption_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true,
        isBefore: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // Allow today + 1 day
      }
    },
    consumption_time: {
      type: DataTypes.TIME,
      allowNull: true,
      comment: 'time of day when feed was given'
    },
    feeding_method: {
      type: DataTypes.STRING(30),
      allowNull: true,
      validate: {
        isIn: [['manual', 'automatic', 'scheduled', 'ad_libitum']]
      },
      comment: 'manual, automatic, scheduled, ad_libitum'
    },
    number_of_birds: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1
      },
      comment: 'number of birds that consumed the feed'
    },
    average_bird_weight: {
      type: DataTypes.DECIMAL(6, 3),
      allowNull: true,
      validate: {
        min: 0.001
      },
      comment: 'average weight of birds in kg'
    },
    feed_conversion_ratio: {
      type: DataTypes.DECIMAL(5, 3),
      allowNull: true,
      validate: {
        min: 0.001
      },
      comment: 'calculated FCR for this feeding'
    },
    cost_per_kg: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'cost per kg of feed at time of consumption'
    },
    total_cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'total cost of this feeding'
    },
    weather_conditions: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [0, 50]
      },
      comment: 'weather during feeding time'
    },
    temperature: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: true,
      validate: {
        min: -50,
        max: 60
      },
      comment: 'temperature in Celsius'
    },
    humidity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'humidity percentage'
    },
    recorded_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'user who recorded this consumption'
    },
    recording_method: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'manual',
      validate: {
        isIn: [['manual', 'automatic', 'mobile_app', 'iot_sensor']]
      },
      comment: 'how this consumption was recorded'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'additional notes about this feeding'
    },
    is_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'whether this record has been verified'
    },
    verified_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'user who verified this record'
    },
    verified_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'when this record was verified'
    },
    sync_status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'synced',
      validate: {
        isIn: [['pending', 'synced', 'conflict', 'error']]
      },
      comment: 'synchronization status for mobile app'
    },
    mobile_id: {
      type: DataTypes.STRING(50),
      allowNull: true,
      unique: true,
      comment: 'unique ID from mobile app for sync'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_consumption_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['farm_id', 'consumption_date']
      },
      {
        fields: ['feed_stock_id', 'consumption_date']
      },
      {
        fields: ['poultry_batch_id', 'consumption_date']
      },
      {
        fields: ['consumption_date', 'farm_id']
      },
      {
        fields: ['sync_status']
      },
      {
        fields: ['mobile_id'],
        unique: true,
        where: {
          mobile_id: { [sequelize.Sequelize.Op.ne]: null }
        }
      }
    ],
    hooks: {
      beforeValidate: (log) => {
        // Auto-calculate total cost if not provided
        if (log.quantity_consumed && log.cost_per_kg && !log.total_cost) {
          log.total_cost = log.quantity_consumed * log.cost_per_kg;
        }
        
        // Set consumption time to current time if not provided
        if (!log.consumption_time && log.consumption_date) {
          const now = new Date();
          log.consumption_time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:00`;
        }
      },
      
      afterCreate: async (log) => {
        // Update feed stock quantity
        if (log.feed_stock_id && log.quantity_consumed) {
          const feedStock = await sequelize.models.FeedStock.findByPk(log.feed_stock_id);
          if (feedStock) {
            await feedStock.consumeFeed(log.quantity_consumed, log.consumption_date);
          }
        }
      },
      
      afterUpdate: async (log, options) => {
        // If quantity changed, update feed stock accordingly
        if (options.fields && options.fields.includes('quantity_consumed')) {
          const previousLog = log._previousDataValues;
          const quantityDiff = log.quantity_consumed - (previousLog.quantity_consumed || 0);
          
          if (quantityDiff !== 0 && log.feed_stock_id) {
            const feedStock = await sequelize.models.FeedStock.findByPk(log.feed_stock_id);
            if (feedStock) {
              if (quantityDiff > 0) {
                await feedStock.consumeFeed(quantityDiff, log.consumption_date);
              } else {
                await feedStock.addStock(Math.abs(quantityDiff));
              }
            }
          }
        }
      }
    }
  });

  // Instance methods
  FeedConsumptionLog.prototype.calculateFCR = function(weightGain) {
    if (!weightGain || weightGain <= 0) return null;
    return this.quantity_consumed / weightGain;
  };

  FeedConsumptionLog.prototype.getFeedCostPerBird = function() {
    if (!this.number_of_birds || this.number_of_birds <= 0) return null;
    return this.total_cost / this.number_of_birds;
  };

  FeedConsumptionLog.prototype.getFeedPerBird = function() {
    if (!this.number_of_birds || this.number_of_birds <= 0) return null;
    return this.quantity_consumed / this.number_of_birds;
  };

  FeedConsumptionLog.prototype.verify = async function(verifiedBy) {
    this.is_verified = true;
    this.verified_by = verifiedBy;
    this.verified_at = new Date();
    return this.save();
  };

  FeedConsumptionLog.prototype.markSynced = function() {
    this.sync_status = 'synced';
    return this.save();
  };

  FeedConsumptionLog.prototype.markSyncError = function(error) {
    this.sync_status = 'error';
    this.notes = this.notes ? `${this.notes}\n\nSync Error: ${error}` : `Sync Error: ${error}`;
    return this.save();
  };

  // Class methods
  FeedConsumptionLog.findByFarm = function(farmId, startDate = null, endDate = null, options = {}) {
    const whereClause = { farm_id: farmId };
    
    if (startDate || endDate) {
      whereClause.consumption_date = {};
      if (startDate) whereClause.consumption_date[sequelize.Sequelize.Op.gte] = startDate;
      if (endDate) whereClause.consumption_date[sequelize.Sequelize.Op.lte] = endDate;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { 
          model: sequelize.models.FeedStock, 
          as: 'feed_stock',
          include: [{ model: sequelize.models.FeedItem, as: 'feed_item' }]
        },
        { model: sequelize.models.Poussin, as: 'poultry_batch' },
        { model: sequelize.models.User, as: 'recorder' }
      ],
      order: [['consumption_date', 'DESC'], ['consumption_time', 'DESC']],
      ...options
    });
  };

  FeedConsumptionLog.findByFeedStock = function(feedStockId, options = {}) {
    return this.findAll({
      where: { feed_stock_id: feedStockId },
      include: [
        { model: sequelize.models.Poussin, as: 'poultry_batch' },
        { model: sequelize.models.User, as: 'recorder' }
      ],
      order: [['consumption_date', 'DESC'], ['consumption_time', 'DESC']],
      ...options
    });
  };

  FeedConsumptionLog.findByPoultryBatch = function(poultryBatchId, options = {}) {
    return this.findAll({
      where: { poultry_batch_id: poultryBatchId },
      include: [
        { 
          model: sequelize.models.FeedStock, 
          as: 'feed_stock',
          include: [{ model: sequelize.models.FeedItem, as: 'feed_item' }]
        },
        { model: sequelize.models.User, as: 'recorder' }
      ],
      order: [['consumption_date', 'DESC'], ['consumption_time', 'DESC']],
      ...options
    });
  };

  FeedConsumptionLog.getTotalConsumptionByFarm = function(farmId, startDate = null, endDate = null) {
    const whereClause = { farm_id: farmId };
    
    if (startDate || endDate) {
      whereClause.consumption_date = {};
      if (startDate) whereClause.consumption_date[sequelize.Sequelize.Op.gte] = startDate;
      if (endDate) whereClause.consumption_date[sequelize.Sequelize.Op.lte] = endDate;
    }
    
    return this.findAll({
      where: whereClause,
      attributes: [
        [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_consumed')), 'total_quantity'],
        [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('total_cost')), 'total_cost'],
        [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'feeding_count']
      ],
      raw: true
    }).then(result => ({
      total_quantity: parseFloat(result[0]?.total_quantity || 0),
      total_cost: parseFloat(result[0]?.total_cost || 0),
      feeding_count: parseInt(result[0]?.feeding_count || 0)
    }));
  };

  FeedConsumptionLog.getAverageFCR = function(farmId, startDate = null, endDate = null) {
    const whereClause = { 
      farm_id: farmId,
      feed_conversion_ratio: { [sequelize.Sequelize.Op.ne]: null }
    };
    
    if (startDate || endDate) {
      whereClause.consumption_date = {};
      if (startDate) whereClause.consumption_date[sequelize.Sequelize.Op.gte] = startDate;
      if (endDate) whereClause.consumption_date[sequelize.Sequelize.Op.lte] = endDate;
    }
    
    return this.findAll({
      where: whereClause,
      attributes: [
        [sequelize.Sequelize.fn('AVG', sequelize.Sequelize.col('feed_conversion_ratio')), 'average_fcr']
      ],
      raw: true
    }).then(result => parseFloat(result[0]?.average_fcr || 0));
  };

  FeedConsumptionLog.getDailyConsumption = function(farmId, date) {
    return this.findAll({
      where: {
        farm_id: farmId,
        consumption_date: date
      },
      include: [
        { 
          model: sequelize.models.FeedStock, 
          as: 'feed_stock',
          include: [{ model: sequelize.models.FeedItem, as: 'feed_item' }]
        }
      ],
      attributes: [
        'feed_stock_id',
        [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_consumed')), 'daily_quantity'],
        [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('total_cost')), 'daily_cost'],
        [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'feeding_sessions']
      ],
      group: ['feed_stock_id', 'feed_stock.id', 'feed_stock.feed_item.id'],
      order: [[sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_consumed')), 'DESC']]
    });
  };

  FeedConsumptionLog.findPendingSync = function(limit = 100) {
    return this.findAll({
      where: {
        sync_status: { [sequelize.Sequelize.Op.in]: ['pending', 'error'] }
      },
      limit,
      order: [['created_at', 'ASC']]
    });
  };

  FeedConsumptionLog.findUnverified = function(farmId = null, options = {}) {
    const whereClause = { is_verified: false };
    if (farmId) whereClause.farm_id = farmId;
    
    return this.findAll({
      where: whereClause,
      include: [
        { 
          model: sequelize.models.FeedStock, 
          as: 'feed_stock',
          include: [{ model: sequelize.models.FeedItem, as: 'feed_item' }]
        },
        { model: sequelize.models.User, as: 'recorder' }
      ],
      order: [['consumption_date', 'DESC']],
      ...options
    });
  };

  // Associations
  FeedConsumptionLog.associate = function(models) {
    // FeedConsumptionLog belongs to Farm (Eleveur)
    FeedConsumptionLog.belongsTo(models.Eleveur, {
      foreignKey: 'farm_id',
      as: 'farm'
    });

    // FeedConsumptionLog belongs to FeedStock
    FeedConsumptionLog.belongsTo(models.FeedStock, {
      foreignKey: 'feed_stock_id',
      as: 'feed_stock'
    });

    // FeedConsumptionLog belongs to Poultry Batch
    FeedConsumptionLog.belongsTo(models.Poussin, {
      foreignKey: 'poultry_batch_id',
      as: 'poultry_batch'
    });

    // FeedConsumptionLog belongs to User (recorder)
    FeedConsumptionLog.belongsTo(models.User, {
      foreignKey: 'recorded_by',
      as: 'recorder'
    });

    // FeedConsumptionLog belongs to User (verifier)
    FeedConsumptionLog.belongsTo(models.User, {
      foreignKey: 'verified_by',
      as: 'verifier'
    });
  };

  return FeedConsumptionLog;
};