import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab, List, ListItem, ListItemText, ListItemIcon, Chip, Divider, Button } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import PriceChangeIcon from '@mui/icons-material/PriceChange';
import InventoryIcon from '@mui/icons-material/Inventory';
import InsightsIcon from '@mui/icons-material/Insights';
import { useTheme } from '@mui/material/styles';

/**
 * Composant pour afficher les recommandations IA
 * @param {Object} props - Propriétés du composant
 * @param {Object} props.data - Données de recommandations IA
 */
const AIRecommendations = ({ data }) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);

  // G<PERSON>rer le changement d'onglet
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Si aucune donnée, afficher un message
  if (!data) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          Chargement des recommandations...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
        variant="fullWidth"
      >
        <Tab label="Prix" icon={<PriceChangeIcon />} iconPosition="start" />
        <Tab label="Stock" icon={<InventoryIcon />} iconPosition="start" />
        <Tab label="Tendances" icon={<InsightsIcon />} iconPosition="start" />
      </Tabs>

      {/* Onglet Optimisation des prix */}
      {tabValue === 0 && (
        <Box sx={{ mt: 2 }}>
          <List>
            {data.price_optimization?.map((item, index) => (
              <React.Fragment key={`price-${item.product_id}`}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    {parseFloat(item.suggested_price) > parseFloat(item.current_price) ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2">
                        {item.product_id}
                      </Typography>
                    }
                    secondary={
                      <React.Fragment>
                        <Typography variant="body2" color="text.primary" component="span">
                          Prix actuel: {parseFloat(item.current_price).toFixed(2)} DA
                        </Typography>
                        <Typography
                          variant="body2"
                          color={parseFloat(item.suggested_price) > parseFloat(item.current_price) ? "success.main" : "error.main"}
                          sx={{ display: 'block' }}
                        >
                          Prix suggéré: {parseFloat(item.suggested_price).toFixed(2)} DA
                          {parseFloat(item.suggested_price) > parseFloat(item.current_price) ? (
                            <Chip
                              label={`+${(((parseFloat(item.suggested_price) - parseFloat(item.current_price)) / parseFloat(item.current_price)) * 100).toFixed(1)}%`}
                              color="success"
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          ) : (
                            <Chip
                              label={`${(((parseFloat(item.suggested_price) - parseFloat(item.current_price)) / parseFloat(item.current_price)) * 100).toFixed(1)}%`}
                              color="error"
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                          {item.reason}
                        </Typography>
                      </React.Fragment>
                    }
                  />
                  <Button variant="outlined" size="small" sx={{ alignSelf: 'center' }}>
                    Appliquer
                  </Button>
                </ListItem>
                {index < data.price_optimization.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      )}

      {/* Onglet Suggestions d'inventaire */}
      {tabValue === 1 && (
        <Box sx={{ mt: 2 }}>
          <List>
            {data.inventory_suggestions?.map((item, index) => (
              <React.Fragment key={`inventory-${item.product_id}`}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    <InventoryIcon color={item.action === 'restock' ? 'warning' : 'info'} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2">
                        {item.product_id}
                      </Typography>
                    }
                    secondary={
                      <React.Fragment>
                        <Typography variant="body2" color="text.primary" component="span">
                          Action recommandée:
                          <Chip
                            label={item.action === 'restock' ? 'Réapprovisionner' : 'Réduire le stock'}
                            color={item.action === 'restock' ? 'warning' : 'info'}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                          {item.reason}
                        </Typography>
                      </React.Fragment>
                    }
                  />
                  <Button
                    variant="outlined"
                    color={item.action === 'restock' ? 'warning' : 'info'}
                    size="small"
                    sx={{ alignSelf: 'center' }}
                  >
                    {item.action === 'restock' ? 'Commander' : 'Ajuster'}
                  </Button>
                </ListItem>
                {index < data.inventory_suggestions.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      )}

      {/* Onglet Tendances du marché */}
      {tabValue === 2 && (
        <Box sx={{ mt: 2 }}>
          <List>
            {data.market_trends?.map((item, index) => (
              <React.Fragment key={`trend-${index}`}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    <InsightsIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center' }}>
                        Tendance
                        <Chip
                          label={`Confiance: ${(parseFloat(item.confidence) * 100).toFixed(0)}%`}
                          color={parseFloat(item.confidence) > 0.8 ? "success" : "info"}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.primary" sx={{ mt: 0.5 }}>
                        {item.trend}
                      </Typography>
                    }
                  />
                </ListItem>
                {index < data.market_trends.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      )}

      <Box sx={{ textAlign: 'center', mt: 2 }}>
        <Button variant="text" color="primary" startIcon={<InsightsIcon />}>
          Analyse complète
        </Button>
      </Box>
    </Box>
  );
};

export default AIRecommendations;
