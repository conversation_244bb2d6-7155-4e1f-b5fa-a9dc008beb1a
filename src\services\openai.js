const OpenAI = require('openai');

// Initialize OpenAI client if API key is available
let openai;
let openaiInitialized = false;

try {
  if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key') {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    openaiInitialized = true;
    console.log('OpenAI initialized successfully');
  } else {
    console.log('OpenAI API key not provided, AI features will be disabled');
  }
} catch (error) {
  console.error('Error initializing OpenAI:', error);
  console.log('AI features will be disabled');
}

/**
 * Generate blog content using OpenAI
 * @param {Object} options - Options for blog generation
 * @param {string} options.title - Blog post title
 * @param {string} options.topic - Blog post topic
 * @param {string} options.keywords - Keywords to include
 * @param {string} options.language - Language for the blog post (fr or ar)
 * @param {number} options.maxLength - Maximum length in words
 * @returns {Promise<string>} - Generated blog content
 */
async function generateBlogPost(options) {
  if (!openaiInitialized) {
    return "OpenAI API not initialized. Please provide a valid API key.";
  }

  const { title, topic, keywords, language = 'fr', maxLength = 500 } = options;

  const languagePrompt = language === 'ar'
    ? 'Write the response in Arabic.'
    : 'Write the response in French.';

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a professional blog writer specializing in poultry farming in Algeria. ${languagePrompt}`
        },
        {
          role: "user",
          content: `Write a blog post with the title "${title}" about ${topic}.
          Include the following keywords: ${keywords}.
          The blog post should be informative, engaging, and well-structured with headings.
          Maximum length: ${maxLength} words.`
        }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating blog post with OpenAI:', error);
    throw new Error('Failed to generate blog post');
  }
}

/**
 * Analyze platform data using OpenAI
 * @param {Object} data - Platform data to analyze
 * @param {string} language - Language for the analysis (fr or ar)
 * @returns {Promise<Object>} - Analysis results
 */
async function analyzeData(data, language = 'fr') {
  if (!openaiInitialized) {
    return {
      analysis: "OpenAI API not initialized. Please provide a valid API key.",
      timestamp: new Date().toISOString()
    };
  }

  const languagePrompt = language === 'ar'
    ? 'Write the response in Arabic.'
    : 'Write the response in French.';

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a data analyst specializing in poultry farming statistics. ${languagePrompt}`
        },
        {
          role: "user",
          content: `Analyze the following data from our poultry farming platform and provide insights:
          ${JSON.stringify(data, null, 2)}

          Please provide:
          1. Key trends and patterns
          2. Potential areas of concern
          3. Recommendations for improvement
          4. Market opportunities based on the data`
        }
      ],
      temperature: 0.5,
      max_tokens: 1000,
    });

    return {
      analysis: response.choices[0].message.content,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error analyzing data with OpenAI:', error);
    throw new Error('Failed to analyze data');
  }
}

/**
 * Generate static page content using OpenAI
 * @param {Object} options - Options for page content generation
 * @param {string} options.pageType - Type of page (about, faq, help, etc.)
 * @param {string} options.keywords - Keywords to include
 * @param {string} options.language - Language for the content (fr or ar)
 * @returns {Promise<string>} - Generated page content
 */
async function generatePageContent(options) {
  if (!openaiInitialized) {
    return "OpenAI API not initialized. Please provide a valid API key.";
  }

  const { pageType, keywords, language = 'fr' } = options;

  const languagePrompt = language === 'ar'
    ? 'Write the response in Arabic.'
    : 'Write the response in French.';

  const pageTypePrompts = {
    about: 'Write an "About Us" page for a poultry farming platform in Algeria.',
    faq: 'Create a FAQ page with common questions and answers about poultry farming.',
    help: 'Create a Help/Support page for users of a poultry farming platform.',
    terms: 'Write Terms and Conditions for a poultry farming platform.',
    privacy: 'Write a Privacy Policy for a poultry farming platform.',
  };

  const promptForPageType = pageTypePrompts[pageType] ||
    `Write content for a ${pageType} page for a poultry farming platform.`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a professional content writer specializing in agricultural technology. ${languagePrompt}`
        },
        {
          role: "user",
          content: `${promptForPageType}
          Include the following keywords: ${keywords}.
          The content should be well-structured with appropriate headings and sections.`
        }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating page content with OpenAI:', error);
    throw new Error('Failed to generate page content');
  }
}

module.exports = {
  generateBlogPost,
  analyzeData,
  generatePageContent
};
