// Configuration de l'environnement de test pour Jest
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

// Configuration de react-testing-library
configure({
  testIdAttribute: 'data-testid',
});

// Configuration des variables d'environnement
process.env.REACT_APP_API_URL = 'http://localhost:3000';

// Mock pour IntersectionObserver (utilisé par Material-UI)
class MockIntersectionObserver {
  constructor() {
    this.observe = jest.fn();
    this.unobserve = jest.fn();
    this.disconnect = jest.fn();
  }
}

global.IntersectionObserver = MockIntersectionObserver;

// Mock pour ResizeObserver
class ResizeObserverMock {
  observe() {}
  unobserve() {}
  disconnect() {}
}

global.ResizeObserver = ResizeObserverMock;

// Suppression des avertissements de console pendant les tests
global.console = {
  ...console,
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Mock pour window.matchMedia (utilisé par Material-UI)
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock pour les animations du navigateur
global.requestAnimationFrame = callback => setTimeout(callback, 0);
global.cancelAnimationFrame = id => clearTimeout(id);

// Mock pour localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock pour les traductions i18n
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: {
      changeLanguage: jest.fn(),
    },
  }),
  initReactI18next: {
    type: '3rdParty',
    init: jest.fn(),
  },
}));

// Mock pour les hooks personnalisés
jest.mock('../src/hooks/usePoussins', () => ({
  usePoussins: () => ({
    lots: [],
    loading: false,
    error: null,
    addLot: jest.fn(),
    updateLot: jest.fn(),
    deleteLot: jest.fn(),
  }),
}));

jest.mock('../src/hooks/useEggProduction', () => ({
  useEggProduction: () => ({
    productions: [],
    loading: false,
    error: null,
    addProduction: jest.fn(),
    updateProduction: jest.fn(),
    deleteProduction: jest.fn(),
  }),
}));

jest.mock('../src/hooks/useVetConsultations', () => ({
  useVetConsultations: () => ({
    consultations: [],
    loading: false,
    error: null,
    addConsultation: jest.fn(),
    updateConsultation: jest.fn(),
    deleteConsultation: jest.fn(),
  }),
}));

jest.mock('../src/hooks/useStockAlerts', () => ({
  useStockAlerts: () => ({
    alerts: [],
    loading: false,
    error: null,
    addAlert: jest.fn(),
    updateAlert: jest.fn(),
    resolveAlert: jest.fn(),
    updateNotificationSettings: jest.fn(),
  }),
}));

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks();
});
