/**
 * Middleware pour standardiser les réponses API
 * Assure un format cohérent pour toutes les réponses
 */

const logger = require('../utils/logger');

/**
 * Format standard de réponse API
 */
class ApiResponse {
  constructor(success = true, data = null, message = '', errors = [], meta = {}) {
    this.success = success;
    this.data = data;
    this.message = message;
    this.errors = errors;
    this.meta = {
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0',
      ...meta
    };
  }

  static success(data, message = 'Opération réussie', meta = {}) {
    return new ApiResponse(true, data, message, [], meta);
  }

  static error(message = 'Une erreur est survenue', errors = [], meta = {}) {
    return new ApiResponse(false, null, message, errors, meta);
  }

  static validationError(errors, message = 'Erreurs de validation') {
    const formattedErrors = Array.isArray(errors) ? errors : [errors];
    return new ApiResponse(false, null, message, formattedErrors, {});
  }

  static notFound(resource = 'Ressource', message = null) {
    const defaultMessage = `${resource} introuvable`;
    return new ApiResponse(false, null, message || defaultMessage, [], {});
  }

  static unauthorized(message = 'Accès non autorisé') {
    return new ApiResponse(false, null, message, [], {});
  }

  static forbidden(message = 'Accès interdit') {
    return new ApiResponse(false, null, message, [], {});
  }

  static serverError(message = 'Erreur interne du serveur') {
    return new ApiResponse(false, null, message, [], {});
  }

  static paginated(data, pagination, message = 'Données récupérées avec succès') {
    return new ApiResponse(true, data, message, [], { pagination });
  }
}

/**
 * Middleware pour formater les réponses
 */
const responseFormatter = (req, res, next) => {
  // Méthode pour envoyer une réponse de succès
  res.success = (data, message, meta = {}) => {
    const response = ApiResponse.success(data, message, meta);
    return res.status(200).json(response);
  };

  // Méthode pour envoyer une réponse de succès avec création
  res.created = (data, message = 'Ressource créée avec succès', meta = {}) => {
    const response = ApiResponse.success(data, message, meta);
    return res.status(201).json(response);
  };

  // Méthode pour envoyer une réponse paginée
  res.paginated = (data, pagination, message = 'Données récupérées avec succès') => {
    const response = ApiResponse.paginated(data, pagination, message);
    return res.status(200).json(response);
  };

  // Méthode pour envoyer une erreur de validation
  res.validationError = (errors, message = 'Erreurs de validation') => {
    const response = ApiResponse.validationError(errors, message);
    return res.status(422).json(response);
  };

  // Méthode pour envoyer une erreur 404
  res.notFound = (resource, message) => {
    const response = ApiResponse.notFound(resource, message);
    return res.status(404).json(response);
  };

  // Méthode pour envoyer une erreur 401
  res.unauthorized = (message) => {
    const response = ApiResponse.unauthorized(message);
    return res.status(401).json(response);
  };

  // Méthode pour envoyer une erreur 403
  res.forbidden = (message) => {
    const response = ApiResponse.forbidden(message);
    return res.status(403).json(response);
  };

  // Méthode pour envoyer une erreur serveur
  res.serverError = (message, error = null) => {
    if (error) {
      logger.error('Erreur serveur:', error);
    }
    const response = ApiResponse.serverError(message);
    return res.status(500).json(response);
  };

  // Méthode pour envoyer une erreur personnalisée
  res.customError = (statusCode, message, errors = []) => {
    const response = ApiResponse.error(message, errors);
    return res.status(statusCode).json(response);
  };

  next();
};

/**
 * Middleware de gestion d'erreurs globales
 */
const errorHandler = (error, req, res, next) => {
  logger.error('Erreur non gérée:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Erreurs de validation Joi
  if (error.isJoi) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    return res.validationError(errors);
  }

  // Erreurs de base de données PostgreSQL
  if (error.code) {
    switch (error.code) {
      case '23505': // Violation de contrainte unique
        return res.customError(409, 'Cette ressource existe déjà', [{
          field: error.constraint,
          message: 'Valeur déjà utilisée'
        }]);
      
      case '23503': // Violation de clé étrangère
        return res.customError(400, 'Référence invalide', [{
          field: error.constraint,
          message: 'La ressource référencée n\'existe pas'
        }]);
      
      case '23502': // Violation de contrainte NOT NULL
        return res.customError(400, 'Champ requis manquant', [{
          field: error.column,
          message: 'Ce champ est obligatoire'
        }]);
      
      default:
        return res.serverError('Erreur de base de données');
    }
  }

  // Erreurs JWT
  if (error.name === 'JsonWebTokenError') {
    return res.unauthorized('Token invalide');
  }

  if (error.name === 'TokenExpiredError') {
    return res.unauthorized('Token expiré');
  }

  // Erreurs de validation personnalisées
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));
    return res.validationError(errors);
  }

  // Erreur 404 pour les routes non trouvées
  if (error.status === 404) {
    return res.notFound('Route');
  }

  // Erreur par défaut
  const isDevelopment = process.env.NODE_ENV === 'development';
  const message = isDevelopment ? error.message : 'Erreur interne du serveur';
  
  return res.serverError(message);
};

/**
 * Middleware pour les routes non trouvées
 */
const notFoundHandler = (req, res) => {
  res.notFound('Route', `La route ${req.method} ${req.path} n'existe pas`);
};

/**
 * Middleware de logging des requêtes
 */
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log de la requête entrante
  logger.info('Requête entrante:', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  // Override de res.json pour logger la réponse
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - start;
    
    logger.info('Réponse envoyée:', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      success: data.success,
      userId: req.user?.id
    });

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Middleware de validation des paramètres de pagination
 */
const paginationValidator = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  
  // Limites de sécurité
  const maxLimit = 100;
  const minLimit = 1;
  
  req.pagination = {
    page: Math.max(1, page),
    limit: Math.min(maxLimit, Math.max(minLimit, limit))
  };
  
  next();
};

module.exports = {
  ApiResponse,
  responseFormatter,
  errorHandler,
  notFoundHandler,
  requestLogger,
  paginationValidator
};
