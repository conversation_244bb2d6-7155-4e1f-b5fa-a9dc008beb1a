const request = require('supertest');
const app = require('../index');
const { 
  FeedItem, 
  FeedSupplier, 
  FeedStock, 
  FeedConsumptionLog, 
  FeedPlan, 
  FeedComposition, 
  Feed<PERSON><PERSON>t,
  <PERSON><PERSON><PERSON>,
  User,
  sequelize
} = require('../models');
const jwt = require('jsonwebtoken');

/**
 * Feed Management System API Tests
 * Tests all feed-related endpoints for the Poultry DZ platform
 */

describe('Feed Management API Tests', () => {
  let authToken;
  let testUser;
  let testFarm;
  let testFeedItem;
  let testSupplier;
  let testStock;
  
  beforeAll(async () => {
    // Sync database
    await sequelize.sync({ force: true });
    
    // Create test user
    testUser = await User.create({
      nom: 'Test',
      prenom: 'User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'eleveur',
      status: 'active'
    });
    
    // Create test farm
    testFarm = await Ferme.create({
      eleveur_id: testUser.id,
      nom: 'Ferme Test',
      adresse: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>',
      superficie: 1000,
      capacite_maximale: 5000,
      type_elevage: 'poulet_chair',
      status: 'active'
    });
    
    // Generate auth token
    authToken = jwt.sign(
      { 
        id: testUser.id, 
        email: testUser.email, 
        role: testUser.role,
        current_farm_id: testFarm.id
      },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );
  });
  
  afterAll(async () => {
    await sequelize.close();
  });
  
  beforeEach(async () => {
    // Clean up test data before each test
    await FeedAlert.destroy({ where: {} });
    await FeedConsumptionLog.destroy({ where: {} });
    await FeedComposition.destroy({ where: {} });
    await FeedPlan.destroy({ where: {} });
    await FeedStock.destroy({ where: {} });
    await FeedSupplier.destroy({ where: {} });
    await FeedItem.destroy({ where: {} });
  });
  
  // ==================== FEED ITEMS TESTS ====================
  
  describe('Feed Items API', () => {
    
    test('GET /api/feed/items - should get all feed items', async () => {
      // Create test feed item
      await FeedItem.create({
        name: 'Starter Feed',
        brand: 'Test Brand',
        category: 'starter',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair'],
        min_age_days: 0,
        max_age_days: 21,
        daily_consumption_per_bird: 50,
        status: 'active'
      });
      
      const response = await request(app)
        .get('/api/feed/items')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].name).toBe('Starter Feed');
    });
    
    test('POST /api/feed/items - should create new feed item', async () => {
      const feedItemData = {
        name: 'Grower Feed',
        brand: 'Premium Brand',
        category: 'grower',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair', 'pondeuse'],
        min_age_days: 22,
        max_age_days: 42,
        daily_consumption_per_bird: 120,
        nutritional_info: {
          protein: 18,
          fat: 4,
          fiber: 5,
          energy: 2900
        },
        status: 'active'
      };
      
      const response = await request(app)
        .post('/api/feed/items')
        .set('Authorization', `Bearer ${authToken}`)
        .send(feedItemData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Grower Feed');
      expect(response.body.data.category).toBe('grower');
    });
    
    test('GET /api/feed/items/:id - should get specific feed item', async () => {
      const feedItem = await FeedItem.create({
        name: 'Test Feed',
        category: 'finisher',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair'],
        status: 'active'
      });
      
      const response = await request(app)
        .get(`/api/feed/items/${feedItem.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(feedItem.id);
    });
    
    test('PUT /api/feed/items/:id - should update feed item', async () => {
      const feedItem = await FeedItem.create({
        name: 'Original Feed',
        category: 'starter',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair'],
        status: 'active'
      });
      
      const updateData = {
        name: 'Updated Feed',
        category: 'grower'
      };
      
      const response = await request(app)
        .put(`/api/feed/items/${feedItem.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Updated Feed');
      expect(response.body.data.category).toBe('grower');
    });
  });
  
  // ==================== FEED SUPPLIERS TESTS ====================
  
  describe('Feed Suppliers API', () => {
    
    test('GET /api/feed/suppliers - should get all suppliers', async () => {
      await FeedSupplier.create({
        name: 'Test Supplier',
        contact_person: 'Ahmed Benali',
        phone: '+213555123456',
        email: '<EMAIL>',
        address: 'Alger, Algérie',
        delivery_zones: ['Alger', 'Blida'],
        status: 'active'
      });
      
      const response = await request(app)
        .get('/api/feed/suppliers')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suppliers).toHaveLength(1);
    });
    
    test('POST /api/feed/suppliers - should create new supplier', async () => {
      const supplierData = {
        name: 'Premium Feed Supplier',
        contact_person: 'Fatima Zohra',
        phone: '+213661234567',
        email: '<EMAIL>',
        address: 'Oran, Algérie',
        delivery_zones: ['Oran', 'Tlemcen', 'Sidi Bel Abbès'],
        payment_terms: '30 jours',
        credit_limit: 50000,
        status: 'active'
      };
      
      const response = await request(app)
        .post('/api/feed/suppliers')
        .set('Authorization', `Bearer ${authToken}`)
        .send(supplierData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Premium Feed Supplier');
    });
  });
  
  // ==================== FEED STOCK TESTS ====================
  
  describe('Feed Stock API', () => {
    
    beforeEach(async () => {
      // Create test feed item and supplier for stock tests
      testFeedItem = await FeedItem.create({
        name: 'Test Feed Item',
        category: 'starter',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair'],
        status: 'active'
      });
      
      testSupplier = await FeedSupplier.create({
        name: 'Test Supplier',
        status: 'active'
      });
    });
    
    test('GET /api/feed/stock/farm/:farm_id - should get farm stock', async () => {
      await FeedStock.create({
        farm_id: testFarm.id,
        feed_item_id: testFeedItem.id,
        supplier_id: testSupplier.id,
        batch_number: 'BATCH001',
        quantity_received: 1000,
        quantity_current: 800,
        unit_cost: 45.50,
        purchase_date: new Date(),
        status: 'active'
      });
      
      const response = await request(app)
        .get(`/api/feed/stock/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
    
    test('POST /api/feed/stock - should add new stock entry', async () => {
      const stockData = {
        farm_id: testFarm.id,
        feed_item_id: testFeedItem.id,
        supplier_id: testSupplier.id,
        batch_number: 'BATCH002',
        quantity_received: 500,
        unit_cost: 48.00,
        purchase_date: new Date().toISOString(),
        expiry_date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(), // 6 months
        storage_location: 'Entrepôt A',
        low_stock_threshold: 50
      };
      
      const response = await request(app)
        .post('/api/feed/stock')
        .set('Authorization', `Bearer ${authToken}`)
        .send(stockData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.batch_number).toBe('BATCH002');
      expect(response.body.data.quantity_current).toBe(500);
    });
    
    test('GET /api/feed/stock/farm/:farm_id?low_stock=true - should get low stock items', async () => {
      // Create stock with low quantity
      await FeedStock.create({
        farm_id: testFarm.id,
        feed_item_id: testFeedItem.id,
        quantity_received: 100,
        quantity_current: 10, // Low stock
        low_stock_threshold: 50,
        status: 'active'
      });
      
      const response = await request(app)
        .get(`/api/feed/stock/farm/${testFarm.id}?low_stock=true`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
  });
  
  // ==================== FEED CONSUMPTION TESTS ====================
  
  describe('Feed Consumption API', () => {
    
    beforeEach(async () => {
      testFeedItem = await FeedItem.create({
        name: 'Test Feed',
        category: 'starter',
        unit_of_measure: 'kg',
        poultry_types: ['poulet_chair'],
        status: 'active'
      });
      
      testStock = await FeedStock.create({
        farm_id: testFarm.id,
        feed_item_id: testFeedItem.id,
        quantity_received: 1000,
        quantity_current: 800,
        unit_cost: 45.50,
        status: 'active'
      });
    });
    
    test('POST /api/feed/consumption - should record consumption', async () => {
      const consumptionData = {
        farm_id: testFarm.id,
        feed_stock_id: testStock.id,
        quantity_consumed: 25.5,
        consumption_date: new Date().toISOString(),
        consumption_time: '08:30',
        feeding_method: 'manual',
        number_of_birds: 500,
        average_bird_weight: 1.2,
        temperature: 22,
        humidity: 65,
        notes: 'Consommation matinale normale'
      };
      
      const response = await request(app)
        .post('/api/feed/consumption')
        .set('Authorization', `Bearer ${authToken}`)
        .send(consumptionData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.quantity_consumed).toBe(25.5);
      expect(response.body.data.recorded_by).toBe(testUser.id);
    });
    
    test('GET /api/feed/consumption/farm/:farm_id - should get consumption logs', async () => {
      await FeedConsumptionLog.create({
        farm_id: testFarm.id,
        feed_stock_id: testStock.id,
        quantity_consumed: 30,
        consumption_date: new Date(),
        recorded_by: testUser.id,
        recording_method: 'manual'
      });
      
      const response = await request(app)
        .get(`/api/feed/consumption/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
    
    test('GET /api/feed/consumption/stats/farm/:farm_id - should get consumption stats', async () => {
      // Create multiple consumption logs
      await FeedConsumptionLog.bulkCreate([
        {
          farm_id: testFarm.id,
          feed_stock_id: testStock.id,
          quantity_consumed: 25,
          consumption_date: new Date(),
          recorded_by: testUser.id,
          recording_method: 'manual'
        },
        {
          farm_id: testFarm.id,
          feed_stock_id: testStock.id,
          quantity_consumed: 30,
          consumption_date: new Date(),
          recorded_by: testUser.id,
          recording_method: 'manual'
        }
      ]);
      
      const response = await request(app)
        .get(`/api/feed/consumption/stats/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('total_consumption');
    });
  });
  
  // ==================== FEED PLANS TESTS ====================
  
  describe('Feed Plans API', () => {
    
    test('POST /api/feed/plans - should create feed plan', async () => {
      const planData = {
        farm_id: testFarm.id,
        plan_name: 'Plan Poulet Chair Standard',
        plan_type: 'standard',
        poultry_type: 'poulet_chair',
        start_age_days: 0,
        end_age_days: 42,
        target_weight: 2.5,
        daily_feed_amount: 120,
        feeding_frequency: 3,
        feeding_times: ['07:00', '13:00', '19:00'],
        estimated_fcr: 1.8,
        estimated_cost_per_bird: 15.50,
        nutritional_requirements: {
          protein_min: 18,
          energy_min: 2900,
          fiber_max: 5
        }
      };
      
      const response = await request(app)
        .post('/api/feed/plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send(planData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.plan_name).toBe('Plan Poulet Chair Standard');
      expect(response.body.data.created_by).toBe(testUser.id);
    });
    
    test('GET /api/feed/plans/farm/:farm_id - should get farm plans', async () => {
      await FeedPlan.create({
        farm_id: testFarm.id,
        plan_name: 'Test Plan',
        plan_type: 'custom',
        poultry_type: 'poulet_chair',
        start_age_days: 0,
        end_age_days: 35,
        daily_feed_amount: 100,
        feeding_frequency: 2,
        created_by: testUser.id,
        status: 'draft'
      });
      
      const response = await request(app)
        .get(`/api/feed/plans/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
  });
  
  // ==================== FEED ALERTS TESTS ====================
  
  describe('Feed Alerts API', () => {
    
    beforeEach(async () => {
      testStock = await FeedStock.create({
        farm_id: testFarm.id,
        feed_item_id: (await FeedItem.create({
          name: 'Test Feed',
          category: 'starter',
          unit_of_measure: 'kg',
          poultry_types: ['poulet_chair'],
          status: 'active'
        })).id,
        quantity_current: 10,
        low_stock_threshold: 50,
        status: 'active'
      });
    });
    
    test('GET /api/feed/alerts/farm/:farm_id - should get farm alerts', async () => {
      await FeedAlert.create({
        farm_id: testFarm.id,
        feed_stock_id: testStock.id,
        alert_type: 'low_stock',
        severity: 'medium',
        title: 'Stock bas détecté',
        message: 'Le stock de cet aliment est en dessous du seuil',
        threshold_value: 50,
        current_value: 10,
        status: 'active'
      });
      
      const response = await request(app)
        .get(`/api/feed/alerts/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });
    
    test('PUT /api/feed/alerts/:id/resolve - should resolve alert', async () => {
      const alert = await FeedAlert.create({
        farm_id: testFarm.id,
        alert_type: 'low_stock',
        severity: 'medium',
        title: 'Test Alert',
        message: 'Test message',
        status: 'active'
      });
      
      const response = await request(app)
        .put(`/api/feed/alerts/${alert.id}/resolve`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ resolution_notes: 'Stock réapprovisionné' });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
  
  // ==================== ANALYTICS TESTS ====================
  
  describe('Feed Analytics API', () => {
    
    test('GET /api/feed/analytics/farm/:farm_id - should get analytics', async () => {
      const response = await request(app)
        .get(`/api/feed/analytics/farm/${testFarm.id}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('period_days');
      expect(response.body.data).toHaveProperty('consumption');
      expect(response.body.data).toHaveProperty('stock_value');
      expect(response.body.data).toHaveProperty('active_alerts');
    });
    
    test('GET /api/feed/recommendations/farm/:farm_id - should get recommendations', async () => {
      const response = await request(app)
        .get(`/api/feed/recommendations/farm/${testFarm.id}?poultry_type=poulet_chair&age_in_days=21`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('recommended_plan');
      expect(response.body.data).toHaveProperty('suitable_feed_items');
    });
  });
  
  // ==================== ERROR HANDLING TESTS ====================
  
  describe('Error Handling', () => {
    
    test('should return 401 for unauthenticated requests', async () => {
      const response = await request(app)
        .get('/api/feed/items');
      
      expect(response.status).toBe(401);
    });
    
    test('should return 404 for non-existent feed item', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-426614174000';
      
      const response = await request(app)
        .get(`/api/feed/items/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
    
    test('should return 400 for invalid feed item data', async () => {
      const invalidData = {
        name: '', // Empty name should fail validation
        category: 'invalid_category'
      };
      
      const response = await request(app)
        .post('/api/feed/items')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});