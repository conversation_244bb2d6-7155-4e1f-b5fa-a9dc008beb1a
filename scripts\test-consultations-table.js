/**
 * Test script to check the consultations table
 */
const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');

// Read config file
const configPath = path.join(__dirname, '../config/config.json');
const configFile = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const config = configFile.development;

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: 'postgres',
    logging: (sql) => console.log('SQL Query:', sql)
  }
);

async function testConsultationsTable() {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');

    // Check consultations table
    console.log('\n🔍 Testing table: consultations');
    const tableExists = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'consultations'
      );`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tableExists[0].exists) {
      console.log('✅ Table \'consultations\' exists.');

      // Get table columns
      const columns = await sequelize.query(
        `SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'consultations'
        ORDER BY ordinal_position;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      console.log('✅ Table structure:');
      console.table(columns);

      // Get sample data
      const sampleData = await sequelize.query(
        `SELECT * FROM consultations LIMIT 1;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (sampleData.length > 0) {
        console.log('✅ Sample data:');
        console.log(sampleData[0]);
      } else {
        console.log('⚠️ No data found in the table.');
      }
    } else {
      console.log('❌ Table \'consultations\' does not exist.');
    }

  } catch (error) {
    console.error('❌ Error testing database tables:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the tests
testConsultationsTable();
