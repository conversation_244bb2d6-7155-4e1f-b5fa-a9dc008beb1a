import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Card,
  CardContent,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import axios from 'axios';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const MarchandVentes = () => {
  const [ventes, setVentes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [periode, setPeriode] = useState('mois');
  const [statistiques, setStatistiques] = useState({
    totalVentes: 0,
    ventesParPeriode: [],
    ventesParProduit: [],
    moyenneVentes: 0
  });

  useEffect(() => {
    fetchVentes();
    fetchStatistiques();
  }, [periode]);

  const fetchVentes = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/marchand/ventes');
      setVentes(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des ventes');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistiques = async () => {
    try {
      const response = await axios.get(`/api/marchand/ventes/statistiques?periode=${periode}`);
      setStatistiques(response.data);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handlePeriodeChange = (event) => {
    setPeriode(event.target.value);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Analyse des Ventes
      </Typography>

      <Grid container spacing={3}>
        {/* Cartes de statistiques */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total des Ventes
              </Typography>
              <Typography variant="h4">
                {statistiques.totalVentes.toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'DZD'
                })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Moyenne des Ventes
              </Typography>
              <Typography variant="h4">
                {statistiques.moyenneVentes.toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'DZD'
                })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Nombre de Ventes
              </Typography>
              <Typography variant="h4">
                {ventes.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Graphiques */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">Évolution des Ventes</Typography>
              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel>Période</InputLabel>
                <Select
                  value={periode}
                  label="Période"
                  onChange={handlePeriodeChange}
                >
                  <MenuItem value="semaine">Par semaine</MenuItem>
                  <MenuItem value="mois">Par mois</MenuItem>
                  <MenuItem value="annee">Par année</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={statistiques.ventesParPeriode}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="periode" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="montant" fill="#8884d8" name="Montant des ventes" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Répartition des Ventes par Produit
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statistiques.ventesParProduit}
                  dataKey="montant"
                  nameKey="nom"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label
                >
                  {statistiques.ventesParProduit.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Tableau des ventes */}
        <Grid item xs={12}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Référence</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Produits</TableCell>
                  <TableCell align="right">Montant</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {ventes
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((vente) => (
                    <TableRow key={vente.id}>
                      <TableCell>
                        {new Date(vente.date_vente).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell>{vente.reference}</TableCell>
                      <TableCell>{vente.client_nom}</TableCell>
                      <TableCell>
                        {vente.produits.map(p => p.nom).join(', ')}
                      </TableCell>
                      <TableCell align="right">
                        {vente.montant_total.toLocaleString('fr-FR', {
                          style: 'currency',
                          currency: 'DZD'
                        })}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={ventes.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="Lignes par page"
            />
          </TableContainer>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MarchandVentes;
