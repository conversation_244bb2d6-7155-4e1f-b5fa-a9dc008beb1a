/**
 * Service API pour les fonctionnalités vétérinaire
 */

import axiosInstance from '../utils/axiosConfig';

const veterinaireAPI = {
  // === GESTION DES PRESCRIPTIONS ===
  
  // Récupérer toutes les prescriptions du vétérinaire
  getPrescriptions: () => {
    return axiosInstance.get('/api/veterinaire/prescriptions');
  },

  // Créer une nouvelle prescription
  createPrescription: (prescriptionData) => {
    return axiosInstance.post('/api/veterinaire/prescriptions', prescriptionData);
  },

  // Mettre à jour une prescription
  updatePrescription: (prescriptionId, prescriptionData) => {
    return axiosInstance.put(`/api/veterinaire/prescriptions/${prescriptionId}`, prescriptionData);
  },

  // Supprimer une prescription
  deletePrescription: (prescriptionId) => {
    return axiosInstance.delete(`/api/veterinaire/prescriptions/${prescriptionId}`);
  },

  // Imprimer une prescription
  printPrescription: (prescriptionId) => {
    return axiosInstance.get(`/api/veterinaire/prescriptions/${prescriptionId}/print`, {
      responseType: 'blob'
    });
  },

  // Récupérer les statistiques des prescriptions
  getPrescriptionsStats: () => {
    return axiosInstance.get('/api/veterinaire/prescriptions/stats');
  },

  // === GESTION DES CONSULTATIONS ===
  
  // Récupérer toutes les consultations
  getConsultations: () => {
    return axiosInstance.get('/api/veterinaire/consultations');
  },

  // Créer une nouvelle consultation
  createConsultation: (consultationData) => {
    return axiosInstance.post('/api/veterinaire/consultations', consultationData);
  },

  // Mettre à jour une consultation
  updateConsultation: (consultationId, consultationData) => {
    return axiosInstance.put(`/api/veterinaire/consultations/${consultationId}`, consultationData);
  },

  // Supprimer une consultation
  deleteConsultation: (consultationId) => {
    return axiosInstance.delete(`/api/veterinaire/consultations/${consultationId}`);
  },

  // Récupérer les statistiques des consultations
  getConsultationsStats: () => {
    return axiosInstance.get('/api/veterinaire/consultations/stats');
  },

  // === GESTION DES RENDEZ-VOUS ===
  
  // Récupérer tous les rendez-vous
  getAppointments: () => {
    return axiosInstance.get('/api/veterinaire/appointments');
  },

  // Créer un nouveau rendez-vous
  createAppointment: (appointmentData) => {
    return axiosInstance.post('/api/veterinaire/appointments', appointmentData);
  },

  // Mettre à jour un rendez-vous
  updateAppointment: (appointmentId, appointmentData) => {
    return axiosInstance.put(`/api/veterinaire/appointments/${appointmentId}`, appointmentData);
  },

  // Supprimer un rendez-vous
  deleteAppointment: (appointmentId) => {
    return axiosInstance.delete(`/api/veterinaire/appointments/${appointmentId}`);
  },

  // Confirmer un rendez-vous
  confirmAppointment: (appointmentId) => {
    return axiosInstance.put(`/api/veterinaire/appointments/${appointmentId}/confirm`);
  },

  // Annuler un rendez-vous
  cancelAppointment: (appointmentId, reason) => {
    return axiosInstance.put(`/api/veterinaire/appointments/${appointmentId}/cancel`, { reason });
  },

  // === GESTION DES PATIENTS ===
  
  // Récupérer tous les patients
  getPatients: () => {
    return axiosInstance.get('/api/veterinaire/patients');
  },

  // Récupérer un patient spécifique
  getPatient: (patientId) => {
    return axiosInstance.get(`/api/veterinaire/patients/${patientId}`);
  },

  // Créer un nouveau patient
  createPatient: (patientData) => {
    return axiosInstance.post('/api/veterinaire/patients', patientData);
  },

  // Mettre à jour un patient
  updatePatient: (patientId, patientData) => {
    return axiosInstance.put(`/api/veterinaire/patients/${patientId}`, patientData);
  },

  // Récupérer l'historique médical d'un patient
  getPatientHistory: (patientId) => {
    return axiosInstance.get(`/api/veterinaire/patients/${patientId}/history`);
  },

  // === MÉDICAMENTS ===
  
  // Récupérer la liste des médicaments
  getMedicaments: () => {
    return axiosInstance.get('/api/veterinaire/medicaments');
  },

  // Rechercher des médicaments
  searchMedicaments: (query) => {
    return axiosInstance.get(`/api/veterinaire/medicaments/search?q=${query}`);
  },

  // === DIAGNOSTICS ===
  
  // Récupérer les diagnostics fréquents
  getFrequentDiagnostics: () => {
    return axiosInstance.get('/api/veterinaire/diagnostics/frequent');
  },

  // Rechercher des diagnostics
  searchDiagnostics: (query) => {
    return axiosInstance.get(`/api/veterinaire/diagnostics/search?q=${query}`);
  },

  // === RAPPORTS ET STATISTIQUES ===
  
  // Récupérer les données du dashboard
  getDashboardData: () => {
    return axiosInstance.get('/api/veterinaire/dashboard');
  },

  // Générer un rapport d'activité
  generateActivityReport: (reportConfig) => {
    return axiosInstance.post('/api/veterinaire/reports/activity', reportConfig);
  },

  // Générer un rapport de prescriptions
  generatePrescriptionsReport: (reportConfig) => {
    return axiosInstance.post('/api/veterinaire/reports/prescriptions', reportConfig);
  },

  // Télécharger un rapport
  downloadReport: (reportId) => {
    return axiosInstance.get(`/api/veterinaire/reports/${reportId}/download`, {
      responseType: 'blob'
    });
  },

  // === NOTIFICATIONS ===
  
  // Récupérer les notifications
  getNotifications: () => {
    return axiosInstance.get('/api/veterinaire/notifications');
  },

  // Marquer une notification comme lue
  markNotificationAsRead: (notificationId) => {
    return axiosInstance.put(`/api/veterinaire/notifications/${notificationId}/read`);
  },

  // === PLANNING ===
  
  // Récupérer le planning du vétérinaire
  getSchedule: (startDate, endDate) => {
    return axiosInstance.get(`/api/veterinaire/schedule?start=${startDate}&end=${endDate}`);
  },

  // Mettre à jour les disponibilités
  updateAvailability: (availabilityData) => {
    return axiosInstance.put('/api/veterinaire/availability', availabilityData);
  },

  // === URGENCES ===
  
  // Récupérer les urgences actives
  getEmergencies: () => {
    return axiosInstance.get('/api/veterinaire/emergencies');
  },

  // Traiter une urgence
  handleEmergency: (emergencyId, actionData) => {
    return axiosInstance.post(`/api/veterinaire/emergencies/${emergencyId}/handle`, actionData);
  },

  // === ÉLEVEURS ===
  
  // Récupérer la liste des éleveurs clients
  getEleveurs: () => {
    return axiosInstance.get('/api/veterinaire/eleveurs');
  },

  // Récupérer les détails d'un éleveur
  getEleveur: (eleveurId) => {
    return axiosInstance.get(`/api/veterinaire/eleveurs/${eleveurId}`);
  },

  // === PROTOCOLES DE SOINS ===
  
  // Récupérer les protocoles de soins
  getProtocols: () => {
    return axiosInstance.get('/api/veterinaire/protocols');
  },

  // Créer un nouveau protocole
  createProtocol: (protocolData) => {
    return axiosInstance.post('/api/veterinaire/protocols', protocolData);
  },

  // Appliquer un protocole à un patient
  applyProtocol: (patientId, protocolId) => {
    return axiosInstance.post(`/api/veterinaire/patients/${patientId}/protocols/${protocolId}`);
  },

  // === VACCINATIONS ===
  
  // Récupérer le calendrier vaccinal
  getVaccinationSchedule: () => {
    return axiosInstance.get('/api/veterinaire/vaccinations/schedule');
  },

  // Enregistrer une vaccination
  recordVaccination: (vaccinationData) => {
    return axiosInstance.post('/api/veterinaire/vaccinations', vaccinationData);
  },

  // Récupérer les rappels de vaccination
  getVaccinationReminders: () => {
    return axiosInstance.get('/api/veterinaire/vaccinations/reminders');
  },

  // === ANALYSES ET EXAMENS ===
  
  // Demander une analyse
  requestAnalysis: (analysisData) => {
    return axiosInstance.post('/api/veterinaire/analyses', analysisData);
  },

  // Récupérer les résultats d'analyses
  getAnalysisResults: (patientId) => {
    return axiosInstance.get(`/api/veterinaire/analyses/results?patient_id=${patientId}`);
  },

  // === FACTURATION ===
  
  // Créer une facture
  createInvoice: (invoiceData) => {
    return axiosInstance.post('/api/veterinaire/invoices', invoiceData);
  },

  // Récupérer les factures
  getInvoices: () => {
    return axiosInstance.get('/api/veterinaire/invoices');
  },

  // Marquer une facture comme payée
  markInvoiceAsPaid: (invoiceId) => {
    return axiosInstance.put(`/api/veterinaire/invoices/${invoiceId}/paid`);
  },

  // === PARAMÈTRES ===
  
  // Récupérer les paramètres du vétérinaire
  getSettings: () => {
    return axiosInstance.get('/api/veterinaire/settings');
  },

  // Mettre à jour les paramètres
  updateSettings: (settings) => {
    return axiosInstance.put('/api/veterinaire/settings', settings);
  }
};

export { veterinaireAPI };
export default veterinaireAPI;
