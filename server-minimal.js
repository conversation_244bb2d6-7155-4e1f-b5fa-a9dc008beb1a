require('dotenv').config();
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3003;

// Configuration CORS spécifique
const corsOptions = {
  origin: [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:3000',  // React dev server alternatif
    'http://127.0.0.1:5173',  // Alternative localhost
    'http://127.0.0.1:3000',   // Alternative localhost
    'http://*************:3003' // Adresse IP de l'application mobile
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token'],
  optionsSuccessStatus: 200 // Pour supporter les anciens navigateurs
};

// Middleware de base
app.use(cors(corsOptions));
app.use(express.json());

// Route de base
app.get('/', (req, res) => {
  res.json({
    message: 'Bienvenue sur l\'API Poultray DZ',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Routes API de base
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API Poultray DZ fonctionnelle',
    version: '1.0.0',
    routes: [
      'GET /',
      'GET /api/test',
      'GET /api/translations',
      'POST /api/auth/login'
    ]
  });
});

// Route de traductions (publique)
app.get('/api/translations', (req, res) => {
  const { lang = 'fr' } = req.query;

  const translations = {
    fr: {
      general: {
        'app.title': 'Poultray DZ',
        'welcome': 'Bienvenue'
      },
      navigation: {
        'nav.dashboard': 'Tableau de bord',
        'nav.volailles': 'Volailles',
        'nav.ventes': 'Ventes'
      }
    },
    en: {
      general: {
        'app.title': 'Poultray DZ',
        'welcome': 'Welcome'
      },
      navigation: {
        'nav.dashboard': 'Dashboard',
        'nav.volailles': 'Poultry',
        'nav.ventes': 'Sales'
      }
    },
    ar: {
      general: {
        'app.title': 'دواجن الجزائر',
        'welcome': 'مرحبا'
      },
      navigation: {
        'nav.dashboard': 'لوحة التحكم',
        'nav.volailles': 'الدواجن',
        'nav.ventes': 'المبيعات'
      }
    }
  };

  res.json({
    language: lang,
    translations: translations[lang] || translations.fr
  });
});

// Route d'authentification (protégée)
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // Simulation d'authentification
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      message: 'Connexion réussie',
      token: 'fake-jwt-token-for-testing',
      user: {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        nom: 'Admin',
        prenom: 'Poultray'
      }
    });
  } else {
    res.status(401).json({
      message: 'Identifiants invalides'
    });
  }
});

// Route admin stats (protégée)
app.get('/api/admin/stats', (req, res) => {
  // Simulation de vérification du token
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'Token manquant' });
  }

  res.json({
    totalEleveurs: 2,
    totalVolailles: 150,
    totalVentes: 25,
    totalAdmins: 1,
    totalVeterinaires: 0,
    totalMarchands: 0,
    totalUsers: 3,
    chiffreAffaires: 125000,
    users: [
      { role: 'admin', count: '1' },
      { role: 'eleveur', count: '2' }
    ],
    volailles: {
      total: 150,
      disponibles: 120,
      vendues: 30
    },
    ventes: {
      total: 25,
      montantTotal: 125000,
      moyenneParVente: 5000
    },
    ventesParMois: [
      { mois: 'Jan', ventes: 45 },
      { mois: 'Fév', ventes: 52 },
      { mois: 'Mar', ventes: 38 },
      { mois: 'Avr', ventes: 65 },
      { mois: 'Mai', ventes: 58 }
    ]
  });
});

// Route admin users (protégée)
app.get('/api/admin/users', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'Token manquant' });
  }

  const { page = 1, limit = 10, role } = req.query;

  let users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      first_name: 'Admin',
      last_name: 'Poultray',
      phone: '+213 123 456 789',
      address: 'Alger, Algérie',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      username: 'eleveur1',
      email: '<EMAIL>',
      role: 'eleveur',
      first_name: 'Jean',
      last_name: 'Dupont',
      phone: '+213 987 654 321',
      address: 'Oran, Algérie',
      created_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 3,
      username: 'eleveur2',
      email: '<EMAIL>',
      role: 'eleveur',
      first_name: 'Marie',
      last_name: 'Martin',
      phone: '+213 555 123 456',
      address: 'Constantine, Algérie',
      created_at: '2024-02-01T00:00:00Z'
    }
  ];

  // Filtrer par rôle si spécifié
  if (role) {
    users = users.filter(user => user.role === role);
  }

  const total = users.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedUsers = users.slice(startIndex, endIndex);

  res.json({
    users: paginatedUsers,
    total: total,
    page: parseInt(page),
    limit: parseInt(limit),
    totalPages: Math.ceil(total / limit)
  });
});

// Route pour les volailles
app.get('/api/volailles', (req, res) => {
  res.json([
    {
      id: 1,
      espece: 'Poulet',
      race: 'Broiler',
      age: 45,
      poids: 2.5,
      quantite: 100,
      prix_unitaire: 500,
      eleveur_nom: 'Jean',
      eleveur_prenom: 'Dupont',
      eleveur_id: 2,
      description: 'Poulets de qualité élevés en plein air'
    },
    {
      id: 2,
      espece: 'Dinde',
      race: 'Bronze',
      age: 60,
      poids: 8.0,
      quantite: 25,
      prix_unitaire: 1200,
      eleveur_nom: 'Marie',
      eleveur_prenom: 'Martin',
      eleveur_id: 3,
      description: 'Dindes fermières de qualité supérieure'
    }
  ]);
});

// Route pour les éleveurs
app.get('/api/eleveurs', (req, res) => {
  res.json([
    {
      id: 2,
      nom: 'Dupont',
      prenom: 'Jean',
      email: '<EMAIL>',
      telephone: '+213 987 654 321',
      adresse: 'Oran, Algérie'
    },
    {
      id: 3,
      nom: 'Martin',
      prenom: 'Marie',
      email: '<EMAIL>',
      telephone: '+213 555 123 456',
      adresse: 'Constantine, Algérie'
    }
  ]);
});

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  console.error('Erreur:', err);
  res.status(500).json({ message: 'Erreur serveur interne' });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route non trouvée' });
});

// Démarrer le serveur
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Serveur Poultray DZ démarré sur le port ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 API: http://localhost:${PORT}/api/test`);
  console.log(`🔧 CORS configuré pour: http://localhost:5173`);
  console.log(`🚀 Serveur prêt à recevoir des requêtes...`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
  process.exit(1);
});
