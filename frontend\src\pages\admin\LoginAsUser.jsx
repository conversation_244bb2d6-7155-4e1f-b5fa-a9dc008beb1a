import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Autocomplete,
  Avatar,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip
} from '@mui/material';
import { Login as LoginIcon, AdminPanelSettings as AdminPanelSettingsIcon, Person as PersonIcon } from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig'; // Assurez-vous que le chemin est correct
import { useAuth } from '../../contexts/AuthContext'; // Contexte d'authentification

const LoginAsUser = () => {
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [loggingIn, setLoggingIn] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user: adminUser, loginAs, logoutAs } = useAuth(); // Fonctions du contexte d'auth

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoadingUsers(true);
        // Endpoint pour récupérer tous les utilisateurs (sauf l'admin actuel)
        const response = await axiosInstance.get('/api/users?exclude_self=true'); 
        setUsers(response.data || []);
        setError('');
      } catch (err) {
        setError(err.response?.data?.message || "Erreur lors de la récupération des utilisateurs.");
        console.error("Erreur fetchUsers:", err);
      } finally {
        setLoadingUsers(false);
      }
    };
    if (adminUser?.role === 'Administrateur') { // S'assurer que seul l'admin peut voir ça
        fetchUsers();
    }
  }, [adminUser]);

  const handleUserSelect = (event, newValue) => {
    setSelectedUser(newValue);
  };

  const handleLoginAs = async () => {
    if (!selectedUser) {
      setError("Veuillez sélectionner un utilisateur.");
      return;
    }
    setLoggingIn(true);
    setError('');
    setSuccess('');
    try {
      // L'appel API backend doit générer un token temporaire pour l'utilisateur cible
      // et potentiellement stocker l'état de "login as" côté serveur.
      const response = await axiosInstance.post(`/admin/login-as-user/${selectedUser._id}`);
      const { token, user: targetUser, originalAdminToken, impersonationExpiresAt } = response.data;
      // Utiliser la fonction du contexte d'authentification pour gérer le changement d'utilisateur
      loginAs(targetUser, token, originalAdminToken, impersonationExpiresAt);

      setSuccess(`Vous êtes maintenant connecté en tant que ${targetUser.nom} ${targetUser.prenom}.`);
      // Redirection ou mise à jour de l'UI gérée par le contexte AuthProvider
    } catch (err) {
      setError(err.response?.data?.message || "Erreur lors de la tentative de connexion en tant qu'utilisateur.");
      console.error("Erreur handleLoginAs:", err);
    } finally {
      setLoggingIn(false);
    }
  };
  
  // Si l'admin est déjà en mode "login as"
  if (adminUser && adminUser.isImpersonating) {
    return (
      <Paper elevation={3} sx={{ p: 4, m: 2, textAlign: 'center' }}>
        <AdminPanelSettingsIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          Mode "Connexion en tant que" Actif
        </Typography>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Vous naviguez actuellement en tant que <Chip label={`${adminUser.nom} ${adminUser.prenom} (${adminUser.role})`} color="secondary" />.
        </Typography>
        <Typography variant="caption" display="block" sx={{ mb: 3 }}>
          La session expirera automatiquement ou vous pouvez revenir à votre compte administrateur.
        </Typography>
        <Button 
          variant="contained" 
          color="error"
          onClick={() => logoutAs()} // Fonction pour arrêter l'impersonnalisation
          startIcon={<PersonIcon />}
        >
          Revenir à mon compte Administrateur
        </Button>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4, m: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <AdminPanelSettingsIcon sx={{ mr: 1 }} /> Se Connecter en tant qu'Utilisateur
      </Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      <Typography variant="body2" sx={{mb:2}} color="text.secondary">
        Cette fonctionnalité permet à un administrateur de se connecter temporairement au compte d'un autre utilisateur pour des raisons de support ou de vérification. Toutes les actions effectuées seront journalisées.
      </Typography>

      <Autocomplete
        options={users}
        getOptionLabel={(option) => `${option.nom} ${option.prenom} (${option.email}) - ${option.role}`}
        value={selectedUser}
        onChange={handleUserSelect}
        loading={loadingUsers}
        isOptionEqualToValue={(option, value) => option._id === value._id}
        renderOption={(props, option) => (
          <ListItem {...props} key={option._id}>
            <ListItemAvatar>
              <Avatar src={option.photoDeProfil || undefined} >{option.nom?.[0]}{option.prenom?.[0]}</Avatar>
            </ListItemAvatar>
            <ListItemText 
                primary={`${option.nom} ${option.prenom}`} 
                secondary={`${option.email} - ${option.role}`}
            />
          </ListItem>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Sélectionner un utilisateur"
            variant="outlined"
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
        sx={{ mb: 3 }}
      />

      <Button
        variant="contained"
        color="primary"
        startIcon={<LoginIcon />}
        onClick={handleLoginAs}
        disabled={!selectedUser || loggingIn || loadingUsers}
        fullWidth
      >
        {loggingIn ? <CircularProgress size={24} color="inherit" /> : `Se connecter en tant que ${selectedUser ? selectedUser.nom : '...'}`}
      </Button>
    </Paper>
  );
};

export default LoginAsUser;