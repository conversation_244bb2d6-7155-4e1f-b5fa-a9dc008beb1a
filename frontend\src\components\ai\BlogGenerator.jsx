import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Chip,
} from '@mui/material';
import {
  Article as ArticleIcon,
  Save as SaveIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const BlogGenerator = () => {
  const [formData, setFormData] = useState({
    title: '',
    topic: '',
    keywords: '',
    language: 'fr',
    maxLength: 500,
  });

  const [generatedContent, setGeneratedContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axiosInstance.post('/ai/blog', formData);
      setGeneratedContent(response.data.content);
      setSuccess('Contenu généré avec succès !');
    } catch (err) {
      console.error('Error generating blog content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération du contenu');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setSuccess('Contenu copié dans le presse-papier !');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleReset = () => {
    setFormData({
      title: '',
      topic: '',
      keywords: '',
      language: 'fr',
      maxLength: 500,
    });
    setGeneratedContent('');
    setError('');
    setSuccess('');
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ArticleIcon color="primary" /> Générateur d'Articles de Blog
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Utilisez l'IA pour générer rapidement des articles de blog sur l'élevage de volailles.
        </Typography>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Titre de l'article"
            name="title"
            value={formData.title}
            onChange={handleChange}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="Sujet"
            name="topic"
            value={formData.topic}
            onChange={handleChange}
            margin="normal"
            required
            multiline
            rows={2}
            placeholder="Ex: Les meilleures pratiques pour l'élevage de poulets en Algérie"
          />

          <TextField
            fullWidth
            label="Mots-clés (séparés par des virgules)"
            name="keywords"
            value={formData.keywords}
            onChange={handleChange}
            margin="normal"
            placeholder="Ex: poulets, élevage, alimentation, santé"
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Langue</InputLabel>
              <Select
                name="language"
                value={formData.language}
                onChange={handleChange}
                label="Langue"
              >
                <MenuItem value="fr">Français</MenuItem>
                <MenuItem value="ar">Arabe</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              type="number"
              label="Longueur maximale (mots)"
              name="maxLength"
              value={formData.maxLength}
              onChange={handleChange}
              margin="normal"
              inputProps={{ min: 100, max: 2000 }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <ArticleIcon />}
            >
              {loading ? 'Génération en cours...' : 'Générer l\'article'}
            </Button>

            <Button
              type="button"
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              startIcon={<RefreshIcon />}
            >
              Réinitialiser
            </Button>
          </Box>
        </form>
      </Paper>

      {generatedContent && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Article Généré</Typography>
            <Box>
              <Button
                variant="outlined"
                size="small"
                startIcon={<CopyIcon />}
                onClick={handleCopyToClipboard}
                sx={{ mr: 1 }}
              >
                Copier
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                color="success"
              >
                Enregistrer
              </Button>
            </Box>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mb: 2 }}>
            <Chip label={`Langue: ${formData.language === 'fr' ? 'Français' : 'Arabe'}`} size="small" sx={{ mr: 1 }} />
            <Chip label={`~${formData.maxLength} mots`} size="small" />
          </Box>

          <Box sx={{
            p: 2,
            bgcolor: 'background.default',
            borderRadius: 1,
            whiteSpace: 'pre-wrap',
            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
            fontSize: '0.875rem',
            lineHeight: 1.6,
          }}>
            {generatedContent}
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default BlogGenerator;
