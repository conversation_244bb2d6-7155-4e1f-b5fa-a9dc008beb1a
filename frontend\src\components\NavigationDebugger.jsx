import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, Typography, Paper, Button, Alert, CircularProgress } from '@mui/material';

const NavigationDebugger = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = { timestamp, message, type };
    setLogs(prev => [...prev, logEntry]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    addLog(`🌍 Page chargée: ${location.pathname}`, 'info');
  }, [location.pathname]);

  const testLogin = async () => {
    setIsLoading(true);
    addLog('🔐 Début du test de connexion', 'info');

    try {
      // Simuler les données de connexion
      const testCredentials = {
        email: '<EMAIL>',
        password: 'admin123'
      };

      addLog(`📡 Tentative de connexion avec: ${testCredentials.email}`, 'info');

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCredentials)
      });

      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Connexion réussie pour ${data.user.email}`, 'success');
        addLog(`👤 Rôle utilisateur: ${data.user.role}`, 'info');

        // Stocker les données
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));

        // Déterminer la route de redirection
        const redirectPath = getRedirectPath(data.user.role);
        addLog(`🎯 Redirection prévue vers: ${redirectPath}`, 'info');

        // Attendre un moment pour simuler React state update
        await new Promise(resolve => setTimeout(resolve, 100));

        addLog(`🚀 Navigation vers: ${redirectPath}`, 'info');
        navigate(redirectPath, { replace: true });

      } else {
        const errorData = await response.text();
        addLog(`❌ Erreur de connexion: ${response.status} - ${errorData}`, 'error');
      }
    } catch (error) {
      addLog(`❌ Erreur réseau: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const getRedirectPath = (role) => {
    const rolePathMap = {
      'admin': '/admin/dashboard',
      'eleveur': '/eleveur/dashboard',
      'veterinaire': '/veterinaire/dashboard',
      'marchand': '/marchand/dashboard'
    };
    return rolePathMap[role] || '/dashboard';
  };

  const testNavigation = (path) => {
    addLog(`🧭 Test de navigation vers: ${path}`, 'info');
    navigate(path);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const clearAuth = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    addLog('🗑️ Authentification effacée', 'info');
  };

  const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    addLog('🔍 Vérification du statut d\'authentification:', 'info');
    addLog(`Token présent: ${!!token}`, 'info');

    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        addLog(`Utilisateur: ${user.email} (${user.role})`, 'info');
      } catch (e) {
        addLog('❌ Erreur parsing utilisateur', 'error');
      }
    } else {
      addLog('Aucun utilisateur stocké', 'info');
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          🐛 Navigation Debugger
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          URL actuelle: {location.pathname}
        </Typography>

        <Box sx={{ mb: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            onClick={testLogin}
            disabled={isLoading}
          >
            {isLoading ? <CircularProgress size={20} /> : '🔐 Test Login'}
          </Button>

          <Button variant="outlined" onClick={() => testNavigation('/admin/dashboard')}>
            Admin Dashboard
          </Button>

          <Button variant="outlined" onClick={() => testNavigation('/eleveur/dashboard')}>
            Éleveur Dashboard
          </Button>

          <Button variant="outlined" onClick={checkAuthStatus}>
            🔍 Check Auth
          </Button>

          <Button variant="outlined" onClick={clearAuth}>
            🗑️ Clear Auth
          </Button>

          <Button variant="outlined" onClick={clearLogs}>
            📋 Clear Logs
          </Button>
        </Box>

        <Paper
          variant="outlined"
          sx={{
            p: 2,
            height: 400,
            overflow: 'auto',
            backgroundColor: '#f5f5f5',
            fontFamily: 'monospace'
          }}
        >
          {logs.length === 0 ? (
            <Typography color="text.secondary">
              Aucun log pour le moment. Cliquez sur un bouton pour commencer les tests.
            </Typography>
          ) : (
            logs.map((log, index) => (
              <Box key={index} sx={{ mb: 1 }}>
                <Typography
                  variant="body2"
                  color={log.type === 'error' ? 'error' : log.type === 'success' ? 'success.main' : 'text.primary'}
                >
                  [{log.timestamp}] {log.message}
                </Typography>
              </Box>
            ))
          )}
        </Paper>
      </Paper>
    </Box>
  );
};

export default NavigationDebugger;
