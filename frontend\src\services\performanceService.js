/**
 * Service d'optimisation des performances
 * Surveille et optimise les performances de l'application React
 */

// Types de métriques de performance
export const PERFORMANCE_METRICS = {
  RENDER_TIME: 'render_time',
  BUNDLE_SIZE: 'bundle_size',
  API_RESPONSE_TIME: 'api_response_time',
  MEMORY_USAGE: 'memory_usage',
  FCP: 'first_contentful_paint',
  LCP: 'largest_contentful_paint',
  FID: 'first_input_delay',
  CLS: 'cumulative_layout_shift'
};

// Seuils de performance
export const PERFORMANCE_THRESHOLDS = {
  [PERFORMANCE_METRICS.RENDER_TIME]: 16, // 16ms pour 60fps
  [PERFORMANCE_METRICS.API_RESPONSE_TIME]: 1000, // 1 seconde
  [PERFORMANCE_METRICS.FCP]: 1800, // 1.8 secondes
  [PERFORMANCE_METRICS.LCP]: 2500, // 2.5 secondes
  [PERFORMANCE_METRICS.FID]: 100, // 100ms
  [PERFORMANCE_METRICS.CLS]: 0.1 // 0.1
};

/**
 * Classe pour surveiller les performances
 */
class PerformanceService {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
    this.isMonitoring = false;
    this.renderTimes = [];
    this.apiTimes = new Map();
    this.setupPerformanceObservers();
  }

  /**
   * Démarrer la surveillance des performances
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🚀 Surveillance des performances démarrée');
    
    // Surveiller les métriques Web Vitals
    this.observeWebVitals();
    
    // Surveiller les performances de rendu
    this.observeRenderPerformance();
    
    // Surveiller l'utilisation mémoire
    this.observeMemoryUsage();
  }

  /**
   * Arrêter la surveillance des performances
   */
  stopMonitoring() {
    this.isMonitoring = false;
    
    // Déconnecter tous les observers
    this.observers.forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
    
    this.observers.clear();
    console.log('⏹️ Surveillance des performances arrêtée');
  }

  /**
   * Configurer les observers de performance
   */
  setupPerformanceObservers() {
    // Observer pour les métriques de navigation
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric(PERFORMANCE_METRICS.RENDER_TIME, entry.duration);
          }
        });
        
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', navObserver);
      } catch (error) {
        console.warn('Navigation observer non supporté:', error);
      }
    }
  }

  /**
   * Observer les Web Vitals
   */
  observeWebVitals() {
    // First Contentful Paint
    this.observeMetric('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.recordMetric(PERFORMANCE_METRICS.FCP, fcpEntry.startTime);
      }
    });

    // Largest Contentful Paint
    this.observeMetric('largest-contentful-paint', (entries) => {
      const lcpEntry = entries[entries.length - 1];
      if (lcpEntry) {
        this.recordMetric(PERFORMANCE_METRICS.LCP, lcpEntry.startTime);
      }
    });

    // First Input Delay
    this.observeMetric('first-input', (entries) => {
      const fidEntry = entries[0];
      if (fidEntry) {
        this.recordMetric(PERFORMANCE_METRICS.FID, fidEntry.processingStart - fidEntry.startTime);
      }
    });

    // Cumulative Layout Shift
    this.observeMetric('layout-shift', (entries) => {
      let clsValue = 0;
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      this.recordMetric(PERFORMANCE_METRICS.CLS, clsValue);
    });
  }

  /**
   * Observer une métrique spécifique
   */
  observeMetric(entryType, callback) {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ entryTypes: [entryType] });
      this.observers.set(entryType, observer);
    } catch (error) {
      console.warn(`Observer pour ${entryType} non supporté:`, error);
    }
  }

  /**
   * Observer les performances de rendu React
   */
  observeRenderPerformance() {
    // Utiliser React DevTools Profiler si disponible
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('📊 React DevTools détecté - surveillance des rendus activée');
    }

    // Surveiller les re-rendus fréquents
    this.startRenderTimeTracking();
  }

  /**
   * Démarrer le suivi des temps de rendu
   */
  startRenderTimeTracking() {
    const originalRender = React.Component.prototype.render;
    const self = this;

    React.Component.prototype.render = function() {
      const startTime = performance.now();
      const result = originalRender.call(this);
      const endTime = performance.now();
      
      self.recordRenderTime(this.constructor.name, endTime - startTime);
      return result;
    };
  }

  /**
   * Enregistrer un temps de rendu
   */
  recordRenderTime(componentName, duration) {
    this.renderTimes.push({
      component: componentName,
      duration,
      timestamp: Date.now()
    });

    // Garder seulement les 100 derniers rendus
    if (this.renderTimes.length > 100) {
      this.renderTimes.shift();
    }

    // Alerter si le rendu est lent
    if (duration > PERFORMANCE_THRESHOLDS[PERFORMANCE_METRICS.RENDER_TIME]) {
      console.warn(`⚠️ Rendu lent détecté: ${componentName} (${duration.toFixed(2)}ms)`);
    }
  }

  /**
   * Observer l'utilisation mémoire
   */
  observeMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = performance.memory;
        this.recordMetric(PERFORMANCE_METRICS.MEMORY_USAGE, {
          used: memInfo.usedJSHeapSize,
          total: memInfo.totalJSHeapSize,
          limit: memInfo.jsHeapSizeLimit
        });
      }, 5000); // Toutes les 5 secondes
    }
  }

  /**
   * Enregistrer une métrique
   */
  recordMetric(type, value) {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }

    this.metrics.get(type).push({
      value,
      timestamp: Date.now()
    });

    // Garder seulement les 50 dernières valeurs
    const values = this.metrics.get(type);
    if (values.length > 50) {
      values.shift();
    }

    // Vérifier les seuils
    this.checkThreshold(type, value);
  }

  /**
   * Vérifier si une métrique dépasse le seuil
   */
  checkThreshold(type, value) {
    const threshold = PERFORMANCE_THRESHOLDS[type];
    if (!threshold) return;

    const numericValue = typeof value === 'object' ? value.used : value;
    
    if (numericValue > threshold) {
      console.warn(`⚠️ Seuil de performance dépassé: ${type} (${numericValue} > ${threshold})`);
      
      // Émettre un événement personnalisé
      window.dispatchEvent(new CustomEvent('performance:threshold-exceeded', {
        detail: { type, value: numericValue, threshold }
      }));
    }
  }

  /**
   * Mesurer le temps d'une opération
   */
  measureOperation(name, operation) {
    return new Promise(async (resolve, reject) => {
      const startTime = performance.now();
      
      try {
        const result = await operation();
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.recordMetric(`operation_${name}`, duration);
        console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
        
        resolve(result);
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.error(`❌ ${name} échoué après ${duration.toFixed(2)}ms:`, error);
        reject(error);
      }
    });
  }

  /**
   * Mesurer le temps de réponse d'une API
   */
  measureApiCall(url, method = 'GET') {
    const startTime = performance.now();
    const key = `${method} ${url}`;
    
    return {
      finish: () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.recordMetric(PERFORMANCE_METRICS.API_RESPONSE_TIME, duration);
        
        if (!this.apiTimes.has(key)) {
          this.apiTimes.set(key, []);
        }
        
        this.apiTimes.get(key).push(duration);
        
        // Garder seulement les 20 derniers appels
        const times = this.apiTimes.get(key);
        if (times.length > 20) {
          times.shift();
        }
        
        return duration;
      }
    };
  }

  /**
   * Obtenir les statistiques de performance
   */
  getPerformanceStats() {
    const stats = {};
    
    // Statistiques des métriques
    this.metrics.forEach((values, type) => {
      if (values.length > 0) {
        const numericValues = values.map(v => typeof v.value === 'object' ? v.value.used : v.value);
        stats[type] = {
          current: numericValues[numericValues.length - 1],
          average: numericValues.reduce((a, b) => a + b, 0) / numericValues.length,
          min: Math.min(...numericValues),
          max: Math.max(...numericValues),
          count: values.length
        };
      }
    });

    // Statistiques des rendus
    if (this.renderTimes.length > 0) {
      const durations = this.renderTimes.map(r => r.duration);
      stats.renders = {
        total: this.renderTimes.length,
        averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        slowRenders: this.renderTimes.filter(r => r.duration > PERFORMANCE_THRESHOLDS[PERFORMANCE_METRICS.RENDER_TIME]).length
      };
    }

    // Statistiques des APIs
    const apiStats = {};
    this.apiTimes.forEach((times, endpoint) => {
      apiStats[endpoint] = {
        calls: times.length,
        averageTime: times.reduce((a, b) => a + b, 0) / times.length,
        minTime: Math.min(...times),
        maxTime: Math.max(...times)
      };
    });
    stats.apis = apiStats;

    return stats;
  }

  /**
   * Générer un rapport de performance
   */
  generateReport() {
    const stats = this.getPerformanceStats();
    const report = {
      timestamp: new Date().toISOString(),
      monitoring: this.isMonitoring,
      stats,
      recommendations: this.generateRecommendations(stats)
    };

    console.log('📊 Rapport de performance:', report);
    return report;
  }

  /**
   * Générer des recommandations d'optimisation
   */
  generateRecommendations(stats) {
    const recommendations = [];

    // Vérifier les temps de rendu
    if (stats.renders && stats.renders.averageDuration > PERFORMANCE_THRESHOLDS[PERFORMANCE_METRICS.RENDER_TIME]) {
      recommendations.push({
        type: 'render_optimization',
        priority: 'high',
        message: 'Temps de rendu élevé détecté. Considérez l\'utilisation de React.memo, useMemo, ou useCallback.',
        value: stats.renders.averageDuration
      });
    }

    // Vérifier les temps d'API
    if (stats.apis) {
      Object.entries(stats.apis).forEach(([endpoint, apiStats]) => {
        if (apiStats.averageTime > PERFORMANCE_THRESHOLDS[PERFORMANCE_METRICS.API_RESPONSE_TIME]) {
          recommendations.push({
            type: 'api_optimization',
            priority: 'medium',
            message: `API lente détectée: ${endpoint}. Considérez la mise en cache ou l'optimisation côté serveur.`,
            value: apiStats.averageTime
          });
        }
      });
    }

    // Vérifier l'utilisation mémoire
    if (stats[PERFORMANCE_METRICS.MEMORY_USAGE]) {
      const memoryUsage = stats[PERFORMANCE_METRICS.MEMORY_USAGE].current;
      if (memoryUsage > 50 * 1024 * 1024) { // 50MB
        recommendations.push({
          type: 'memory_optimization',
          priority: 'medium',
          message: 'Utilisation mémoire élevée. Vérifiez les fuites mémoire et optimisez les composants.',
          value: memoryUsage
        });
      }
    }

    return recommendations;
  }
}

// Instance singleton
const performanceService = new PerformanceService();

export default performanceService;
