import { useState, useEffect } from 'react';
import axios from 'axios';

/**
 * Hook personnalisé pour gérer les données de l'éleveur
 * Récupère les informations du profil, les statistiques et les activités
 */
const useEleveurData = () => {
  const [eleveurData, setEleveurData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchEleveurData = async () => {
      try {
        setLoading(true);

        // Récupérer les données du profil éleveur
        const profileResponse = await axios.get('/api/eleveur/profile');

        // Récupérer les statistiques
        const statsResponse = await axios.get('/api/eleveur/stats');

        // Récupérer les activités récentes
        const activitiesResponse = await axios.get('/api/eleveur/activities');

        // Récupérer les alertes
        const alertesResponse = await axios.get('/api/eleveur/alertes');

        // Combiner toutes les données
        setEleveurData({
          profile: profileResponse.data,
          stats: statsResponse.data,
          activities: activitiesResponse.data,
          alertes: alertesResponse.data
        });

        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des données éleveur:', err);
        setError(err);

        // Utiliser des données fictives en cas d'erreur (pour la démonstration)
        setEleveurData({
          profile: {
            id: 'ELV123',
            nom: 'Benali',
            prenom: 'Mohammed',
            telephone: '0550123456',
            email: '<EMAIL>',
            adresse: 'Blida, Algérie',
            dateInscription: '2023-01-15'
          },
          stats: {
            totalVolailles: 5280,
            productionJournaliere: 1850,
            tauxMortalite: 1.2,
            revenuMensuel: 125000,
            tendances: {
              production: [
                { mois: 'Jan', quantite: 1200 },
                { mois: 'Fév', quantite: 1350 },
                { mois: 'Mar', quantite: 1400 },
                { mois: 'Avr', quantite: 1650 },
                { mois: 'Mai', quantite: 1500 },
                { mois: 'Juin', quantite: 1850 }
              ],
              sante: [
                { semaine: 'S1', taux: 0.8 },
                { semaine: 'S2', taux: 1.0 },
                { semaine: 'S3', taux: 1.5 },
                { semaine: 'S4', taux: 1.2 },
                { semaine: 'S5', taux: 0.9 },
                { semaine: 'S6', taux: 1.2 }
              ]
            }
          },
          activities: [
            {
              id: 1,
              type: 'ajout_lot',
              description: 'Nouveau lot de 500 poules pondeuses ajouté',
              date: '2023-06-07T10:30:00',
              details: 'Lot #P789'
            },
            {
              id: 2,
              type: 'vente',
              description: 'Vente de 1200 œufs enregistrée',
              date: '2023-06-07T09:15:00',
              details: '24000 DZD'
            },
            {
              id: 3,
              type: 'vaccination',
              description: 'Vaccination effectuée sur le lot #A123',
              date: '2023-06-06T14:45:00',
              details: 'Vaccin: Newcastle'
            },
            {
              id: 4,
              type: 'livraison',
              description: 'Livraison d\'aliments reçue',
              date: '2023-06-06T11:20:00',
              details: '500 kg'
            },
            {
              id: 5,
              type: 'production',
              description: 'Production journalière enregistrée',
              date: '2023-06-05T18:00:00',
              details: '1850 œufs'
            },
            {
              id: 6,
              type: 'rapport',
              description: 'Rapport vétérinaire ajouté',
              date: '2023-06-05T15:30:00',
              details: 'Lot #B456'
            }
          ],
          alertes: [
            { id: 1, type: 'critical', message: 'Taux de mortalité élevé dans le lot #A123', date: '2023-06-07' },
            { id: 2, type: 'warning', message: 'Vaccination prévue pour le lot #B456 demain', date: '2023-06-07' },
            { id: 3, type: 'info', message: 'Nouvelle mise à jour des prix du marché disponible', date: '2023-06-06' },
            { id: 4, type: 'warning', message: 'Stock d\'aliments bas pour le lot #C789', date: '2023-06-06' }
          ]
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEleveurData();

    // Rafraîchir les données toutes les 5 minutes
    const intervalId = setInterval(fetchEleveurData, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Fonction pour mettre à jour manuellement les données
  const refreshData = async () => {
    setLoading(true);
    try {
      const profileResponse = await axios.get('/api/eleveur/profile');
      const statsResponse = await axios.get('/api/eleveur/stats');
      const activitiesResponse = await axios.get('/api/eleveur/activities');
      const alertesResponse = await axios.get('/api/eleveur/alertes');

      setEleveurData({
        profile: profileResponse.data,
        stats: statsResponse.data,
        activities: activitiesResponse.data,
        alertes: alertesResponse.data
      });

      setError(null);
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des données:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return {
    eleveurData,
    loading,
    error,
    refreshData
  };
};

export default useEleveurData;