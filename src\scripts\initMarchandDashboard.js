/**
 * Script d'initialisation du tableau de bord Marchand
 * Ce script crée les tables nécessaires pour le tableau de bord Marchand
 * et ajoute des données de test pour le développement
 */

require('dotenv').config();
const Marchand = require('../models/marchand');
const User = require('../models/user');
const bcrypt = require('bcryptjs');

// Fonction pour créer un utilisateur marchand de test si nécessaire
async function createTestMarchand() {
  try {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await User.findByEmail('<EMAIL>');

    if (existingUser) {
      console.log('Utilisateur marchand de test existe déjà');
      return existingUser;
    }

    // Créer un nouvel utilisateur marchand
    const userData = {
      username: 'marchand_test',
      email: '<EMAIL>',
      password: 'password123',  // À changer en production
      role: 'marchand',
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON><PERSON>'
    };

    const newUser = await User.register(userData);
    console.log('Utilisateur marchand de test créé avec succès:', newUser.id);
    return newUser;
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur marchand de test:', error);
    throw error;
  }
}

// Fonction pour ajouter des produits de test
async function addTestProducts(marchandId) {
  try {
    // Produits de test
    const testProducts = [
      {
        marchand_id: marchandId,
        name: 'Poulet fermier bio',
        description: 'Poulet élevé en plein air, nourri aux céréales bio',
        category: 'Volaille',
        price: 850.00,
        stock_quantity: 25,
        stock_alert_threshold: 10,
        unit: 'kg'
      },
      {
        marchand_id: marchandId,
        name: 'Œufs frais',
        description: 'Œufs frais de poules élevées en plein air',
        category: 'Œufs',
        price: 15.50,
        stock_quantity: 120,
        stock_alert_threshold: 30,
        unit: 'unité'
      },
      {
        marchand_id: marchandId,
        name: 'Dinde entière',
        description: 'Dinde fermière de qualité supérieure',
        category: 'Volaille',
        price: 1200.00,
        stock_quantity: 8,
        stock_alert_threshold: 5,
        unit: 'kg'
      },
      {
        marchand_id: marchandId,
        name: 'Aliment pour poules pondeuses',
        description: 'Mélange équilibré pour poules pondeuses',
        category: 'Alimentation',
        price: 45.00,
        stock_quantity: 50,
        stock_alert_threshold: 15,
        unit: 'kg'
      },
      {
        marchand_id: marchandId,
        name: 'Poussins d\'un jour',
        description: 'Poussins de race Leghorn, excellentes pondeuses',
        category: 'Animaux vivants',
        price: 25.00,
        stock_quantity: 100,
        stock_alert_threshold: 20,
        unit: 'unité'
      }
    ];

    // Ajouter les produits
    for (const product of testProducts) {
      await Marchand.createProduct(product);
    }

    console.log('Produits de test ajoutés avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'ajout des produits de test:', error);
    throw error;
  }
}

// Fonction pour ajouter des clients de test
async function addTestClients(marchandId) {
  try {
    // Clients de test
    const testClients = [
      {
        marchand_id: marchandId,
        name: 'Ferme Avicole El Baraka',
        email: '<EMAIL>',
        phone: '0555123456',
        address: 'Route de Blida, Alger'
      },
      {
        marchand_id: marchandId,
        name: 'Restaurant Le Coq d\'Or',
        email: '<EMAIL>',
        phone: '0661789012',
        address: 'Centre commercial Bab Ezzouar, Alger'
      },
      {
        marchand_id: marchandId,
        name: 'Supermarché Familia',
        email: '<EMAIL>',
        phone: '0770456789',
        address: 'Boulevard Mohamed V, Oran'
      }
    ];

    // Ajouter les clients
    const clientIds = [];
    for (const client of testClients) {
      const query = {
        text: 'INSERT INTO clients (marchand_id, name, email, phone, address) VALUES ($1, $2, $3, $4, $5) RETURNING id',
        values: [client.marchand_id, client.name, client.email, client.phone, client.address],
      };

      const result = await global.pool.query(query);
      clientIds.push(result.rows[0].id);
    }

    console.log('Clients de test ajoutés avec succès');
    return clientIds;
  } catch (error) {
    console.error('Erreur lors de l\'ajout des clients de test:', error);
    throw error;
  }
}

// Fonction pour ajouter des commandes de test
async function addTestOrders(marchandId, clientIds, productIds) {
  try {
    // Commandes de test
    const testOrders = [
      {
        marchand_id: marchandId,
        client_id: clientIds[0],
        total_amount: 2550.00,
        status: 'delivered',
        payment_status: 'paid',
        payment_method: 'virement',
        shipping_address: 'Route de Blida, Alger',
        notes: 'Livraison effectuée le 15/12/2023',
        items: [
          { product_id: productIds[0], quantity: 3, unit_price: 850.00 }
        ]
      },
      {
        marchand_id: marchandId,
        client_id: clientIds[1],
        total_amount: 1860.00,
        status: 'pending',
        payment_status: 'unpaid',
        payment_method: 'à la livraison',
        shipping_address: 'Centre commercial Bab Ezzouar, Alger',
        notes: 'Livraison prévue le 22/12/2023',
        items: [
          { product_id: productIds[1], quantity: 120, unit_price: 15.50 }
        ]
      },
      {
        marchand_id: marchandId,
        client_id: clientIds[2],
        total_amount: 3600.00,
        status: 'processing',
        payment_status: 'paid',
        payment_method: 'carte bancaire',
        shipping_address: 'Boulevard Mohamed V, Oran',
        notes: 'Commande urgente',
        items: [
          { product_id: productIds[2], quantity: 3, unit_price: 1200.00 }
        ]
      }
    ];

    // Ajouter les commandes
    for (const order of testOrders) {
      const newOrder = await Marchand.createOrder(order);

      // Ajouter les articles de la commande
      for (const item of order.items) {
        await Marchand.addOrderItem({
          order_id: newOrder.id,
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        });
      }
    }

    console.log('Commandes de test ajoutées avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'ajout des commandes de test:', error);
    throw error;
  }
}

// Fonction principale d'initialisation
async function initMarchandDashboard() {
  try {
    console.log('Initialisation du tableau de bord Marchand...');

    // Créer les tables nécessaires
    await Marchand.createTables();
    console.log('Tables créées avec succès');

    // Créer un utilisateur marchand de test
    const marchand = await createTestMarchand();

    // Ajouter des données de test
    await addTestProducts(marchand.id);

    // Récupérer les IDs des produits
    const productsQuery = {
      text: 'SELECT id FROM products WHERE marchand_id = $1',
      values: [marchand.id],
    };
    const productsResult = await global.pool.query(productsQuery);
    const productIds = productsResult.rows.map(row => row.id);

    // Ajouter des clients de test
    const clientIds = await addTestClients(marchand.id);

    // Ajouter des commandes de test
    await addTestOrders(marchand.id, clientIds, productIds);

    console.log('Initialisation du tableau de bord Marchand terminée avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du tableau de bord Marchand:', error);
  }
}

// Exécuter la fonction d'initialisation
initMarchandDashboard();
