const express = require('express');
const { body, param, query } = require('express-validator');
const feedController = require('../controllers/feedController');
const authMiddleware = require('../middleware/auth');
const { checkRoles } = require('../middleware/roleCheck');

const router = express.Router();

/**
 * Feed Management Routes
 * All routes require authentication
 */

// Apply authentication middleware to all routes
router.use(authMiddleware.auth);

// ==================== FEED ITEMS ROUTES ====================

/**
 * @route GET /api/feed/items
 * @desc Get all feed items with filtering
 * @access Private (All authenticated users)
 */
router.get('/items', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page doit être un entier positif'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite doit être entre 1 et 100'),
  query('category').optional().isIn([
    'starter', 'grower', 'finisher', 'layer', 'breeder', 'supplement', 'medication'
  ]).withMessage('Catégorie invalide'),
  query('poultry_type').optional().isIn([
    'poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade', 'caille'
  ]).withMessage('Type de volaille invalide'),
  query('status').optional().isIn(['active', 'inactive']).withMessage('Statut invalide')
], feedController.getFeedItems);

/**
 * @route GET /api/feed/items/:id
 * @desc Get a specific feed item
 * @access Private (All authenticated users)
 */
router.get('/items/:id', [
  param('id').isUUID().withMessage('ID invalide')
], feedController.getFeedItem);

/**
 * @route POST /api/feed/items
 * @desc Create a new feed item
 * @access Private (Admin, Veterinarian)
 */
router.post('/items', [
  checkRoles(['admin', 'veterinaire']),
  body('name').notEmpty().trim().withMessage('Nom requis'),
  body('brand').optional().trim(),
  body('category').isIn([
    'starter', 'grower', 'finisher', 'layer', 'breeder', 'supplement', 'medication'
  ]).withMessage('Catégorie invalide'),
  body('unit_of_measure').isIn(['kg', 'g', 'tonne', 'sac']).withMessage('Unité de mesure invalide'),
  body('poultry_types').isArray().withMessage('Types de volaille requis'),
  body('poultry_types.*').isIn([
    'poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade', 'caille'
  ]).withMessage('Type de volaille invalide'),
  body('min_age_days').optional().isInt({ min: 0 }).withMessage('Âge minimum invalide'),
  body('max_age_days').optional().isInt({ min: 0 }).withMessage('Âge maximum invalide'),
  body('daily_consumption_per_bird').optional().isFloat({ min: 0 }).withMessage('Consommation journalière invalide'),
  body('shelf_life_days').optional().isInt({ min: 1 }).withMessage('Durée de conservation invalide')
], feedController.createFeedItem);

/**
 * @route PUT /api/feed/items/:id
 * @desc Update a feed item
 * @access Private (Admin, Veterinarian)
 */
router.put('/items/:id', [
  checkRoles(['admin', 'veterinaire']),
  param('id').isUUID().withMessage('ID invalide'),
  body('name').optional().notEmpty().trim().withMessage('Nom invalide'),
  body('category').optional().isIn([
    'starter', 'grower', 'finisher', 'layer', 'breeder', 'supplement', 'medication'
  ]).withMessage('Catégorie invalide'),
  body('unit_of_measure').optional().isIn(['kg', 'g', 'tonne', 'sac']).withMessage('Unité de mesure invalide'),
  body('status').optional().isIn(['active', 'inactive']).withMessage('Statut invalide')
], feedController.updateFeedItem);

// ==================== FEED SUPPLIERS ROUTES ====================

/**
 * @route GET /api/feed/suppliers
 * @desc Get all feed suppliers
 * @access Private (All authenticated users)
 */
router.get('/suppliers', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page doit être un entier positif'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite doit être entre 1 et 100'),
  query('status').optional().isIn(['active', 'inactive']).withMessage('Statut invalide')
], feedController.getFeedSuppliers);

/**
 * @route POST /api/feed/suppliers
 * @desc Create a new feed supplier
 * @access Private (Admin, Eleveur)
 */
router.post('/suppliers', [
  checkRoles(['admin', 'eleveur']),
  body('name').notEmpty().trim().withMessage('Nom requis'),
  body('contact_person').optional().trim(),
  body('phone').optional().isMobilePhone('ar-DZ').withMessage('Numéro de téléphone invalide'),
  body('email').optional().isEmail().withMessage('Email invalide'),
  body('address').optional().trim(),
  body('delivery_zones').optional().isArray().withMessage('Zones de livraison doivent être un tableau'),
  body('payment_terms').optional().trim(),
  body('credit_limit').optional().isFloat({ min: 0 }).withMessage('Limite de crédit invalide')
], feedController.createFeedSupplier);

// ==================== FEED STOCK ROUTES ====================

/**
 * @route GET /api/feed/stock/farm/:farm_id
 * @desc Get feed stock for a farm
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/stock/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('status').optional().isIn(['active', 'inactive', 'expired', 'consumed']).withMessage('Statut invalide'),
  query('low_stock').optional().isBoolean().withMessage('Low stock doit être un booléen'),
  query('expiring_soon').optional().isBoolean().withMessage('Expiring soon doit être un booléen'),
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('Jours doit être entre 1 et 365')
], feedController.getFeedStock);

/**
 * @route POST /api/feed/stock
 * @desc Add new feed stock entry
 * @access Private (Eleveur, Admin)
 */
router.post('/stock', [
  checkRoles(['eleveur', 'admin']),
  body('farm_id').isUUID().withMessage('ID de ferme requis'),
  body('feed_item_id').isUUID().withMessage('ID d\'aliment requis'),
  body('supplier_id').optional().isUUID().withMessage('ID de fournisseur invalide'),
  body('batch_number').optional().trim(),
  body('quantity_received').isFloat({ min: 0 }).withMessage('Quantité reçue invalide'),
  body('unit_cost').optional().isFloat({ min: 0 }).withMessage('Coût unitaire invalide'),
  body('purchase_date').optional().isISO8601().withMessage('Date d\'achat invalide'),
  body('expiry_date').optional().isISO8601().withMessage('Date d\'expiration invalide'),
  body('storage_location').optional().trim(),
  body('low_stock_threshold').optional().isFloat({ min: 0 }).withMessage('Seuil de stock bas invalide')
], feedController.addFeedStock);

/**
 * @route PUT /api/feed/stock/:id
 * @desc Update feed stock
 * @access Private (Eleveur, Admin)
 */
router.put('/stock/:id', [
  checkRoles(['eleveur', 'admin']),
  param('id').isUUID().withMessage('ID invalide'),
  body('quantity_current').optional().isFloat({ min: 0 }).withMessage('Quantité actuelle invalide'),
  body('unit_cost').optional().isFloat({ min: 0 }).withMessage('Coût unitaire invalide'),
  body('storage_location').optional().trim(),
  body('low_stock_threshold').optional().isFloat({ min: 0 }).withMessage('Seuil de stock bas invalide'),
  body('status').optional().isIn(['active', 'inactive', 'expired', 'consumed']).withMessage('Statut invalide')
], feedController.updateFeedStock);

// ==================== FEED CONSUMPTION ROUTES ====================

/**
 * @route GET /api/feed/consumption/farm/:farm_id
 * @desc Get feed consumption logs for a farm
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/consumption/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('start_date').optional().isISO8601().withMessage('Date de début invalide'),
  query('end_date').optional().isISO8601().withMessage('Date de fin invalide'),
  query('feed_stock_id').optional().isUUID().withMessage('ID de stock invalide'),
  query('poultry_batch_id').optional().isUUID().withMessage('ID de lot invalide')
], feedController.getFeedConsumption);

/**
 * @route POST /api/feed/consumption
 * @desc Record feed consumption
 * @access Private (Eleveur, Admin)
 */
router.post('/consumption', [
  checkRoles(['eleveur', 'admin']),
  body('farm_id').isUUID().withMessage('ID de ferme requis'),
  body('feed_stock_id').isUUID().withMessage('ID de stock requis'),
  body('poultry_batch_id').optional().isUUID().withMessage('ID de lot invalide'),
  body('quantity_consumed').isFloat({ min: 0 }).withMessage('Quantité consommée invalide'),
  body('consumption_date').optional().isISO8601().withMessage('Date de consommation invalide'),
  body('consumption_time').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Heure invalide'),
  body('feeding_method').optional().isIn(['manual', 'automatic', 'semi_automatic']).withMessage('Méthode d\'alimentation invalide'),
  body('number_of_birds').optional().isInt({ min: 1 }).withMessage('Nombre d\'oiseaux invalide'),
  body('average_bird_weight').optional().isFloat({ min: 0 }).withMessage('Poids moyen invalide'),
  body('weather_conditions').optional().trim(),
  body('temperature').optional().isFloat().withMessage('Température invalide'),
  body('humidity').optional().isFloat({ min: 0, max: 100 }).withMessage('Humidité invalide')
], feedController.recordFeedConsumption);

/**
 * @route GET /api/feed/consumption/stats/farm/:farm_id
 * @desc Get feed consumption statistics
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/consumption/stats/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('start_date').optional().isISO8601().withMessage('Date de début invalide'),
  query('end_date').optional().isISO8601().withMessage('Date de fin invalide')
], feedController.getFeedConsumptionStats);

// ==================== FEED PLANS ROUTES ====================

/**
 * @route GET /api/feed/plans/farm/:farm_id
 * @desc Get feed plans for a farm
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/plans/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']).withMessage('Statut invalide'),
  query('is_template').optional().isBoolean().withMessage('Is template doit être un booléen')
], feedController.getFeedPlans);

/**
 * @route POST /api/feed/plans
 * @desc Create a new feed plan
 * @access Private (Eleveur, Veterinarian, Admin)
 */
router.post('/plans', [
  checkRoles(['eleveur', 'veterinaire', 'admin']),
  body('farm_id').isUUID().withMessage('ID de ferme requis'),
  body('poultry_batch_id').optional().isUUID().withMessage('ID de lot invalide'),
  body('plan_name').notEmpty().trim().withMessage('Nom du plan requis'),
  body('plan_type').isIn(['standard', 'custom', 'template']).withMessage('Type de plan invalide'),
  body('poultry_type').isIn([
    'poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade', 'caille'
  ]).withMessage('Type de volaille invalide'),
  body('start_age_days').isInt({ min: 0 }).withMessage('Âge de début invalide'),
  body('end_age_days').isInt({ min: 0 }).withMessage('Âge de fin invalide'),
  body('target_weight').optional().isFloat({ min: 0 }).withMessage('Poids cible invalide'),
  body('daily_feed_amount').isFloat({ min: 0 }).withMessage('Quantité journalière invalide'),
  body('feeding_frequency').isInt({ min: 1, max: 10 }).withMessage('Fréquence d\'alimentation invalide'),
  body('estimated_fcr').optional().isFloat({ min: 0 }).withMessage('FCR estimé invalide'),
  body('estimated_cost_per_bird').optional().isFloat({ min: 0 }).withMessage('Coût estimé par oiseau invalide')
], feedController.createFeedPlan);

/**
 * @route PUT /api/feed/plans/:id/activate
 * @desc Activate a feed plan
 * @access Private (Eleveur, Veterinarian, Admin)
 */
router.put('/plans/:id/activate', [
  checkRoles(['eleveur', 'veterinaire', 'admin']),
  param('id').isUUID().withMessage('ID invalide')
], feedController.activateFeedPlan);

// ==================== FEED ALERTS ROUTES ====================

/**
 * @route GET /api/feed/alerts/farm/:farm_id
 * @desc Get feed alerts for a farm
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/alerts/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('severity').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Sévérité invalide'),
  query('alert_type').optional().isIn([
    'low_stock', 'expiry_warning', 'high_fcr', 'consumption_anomaly', 'quality_issue'
  ]).withMessage('Type d\'alerte invalide'),
  query('status').optional().isIn(['active', 'acknowledged', 'resolved', 'dismissed']).withMessage('Statut invalide')
], feedController.getFeedAlerts);

/**
 * @route PUT /api/feed/alerts/:id/resolve
 * @desc Resolve a feed alert
 * @access Private (Eleveur, Admin)
 */
router.put('/alerts/:id/resolve', [
  checkRoles(['eleveur', 'admin']),
  param('id').isUUID().withMessage('ID invalide'),
  body('resolution_notes').optional().trim()
], feedController.resolveFeedAlert);

// ==================== ANALYTICS & REPORTS ROUTES ====================

/**
 * @route GET /api/feed/analytics/farm/:farm_id
 * @desc Get feed analytics dashboard data
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/analytics/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('period').optional().isInt({ min: 1, max: 365 }).withMessage('Période doit être entre 1 et 365 jours')
], feedController.getFeedAnalytics);

/**
 * @route GET /api/feed/recommendations/farm/:farm_id
 * @desc Get feed recommendations
 * @access Private (Owner, Admin, Veterinarian)
 */
router.get('/recommendations/farm/:farm_id', [
  param('farm_id').isUUID().withMessage('ID de ferme invalide'),
  query('poultry_type').notEmpty().isIn([
    'poulet_chair', 'pondeuse', 'dinde', 'canard', 'oie', 'pintade', 'caille'
  ]).withMessage('Type de volaille requis'),
  query('age_in_days').isInt({ min: 0 }).withMessage('Âge en jours requis'),
  query('target_weight').optional().isFloat({ min: 0 }).withMessage('Poids cible invalide')
], feedController.getFeedRecommendations);

// ==================== ERROR HANDLING ====================

// Handle validation errors
router.use((error, req, res, next) => {
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Erreur de validation',
      errors: error.errors
    });
  }
  next(error);
});

module.exports = router;
