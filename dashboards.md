# Tableaux de Bord Poultray DZ

## Tableau de Bord Éleveur

### Composants

- Overview (Vue d'ensemble)
- Production Tracking (Suivi de production)
- Alerts Actions (Alertes et actions)
- Recent Activity (Activité récente)

### Fonctionnalités

- Suivi des volailles
- Gestion de la production d'œufs
- Alertes de stock
- Statistiques de production
- Suivi vétérinaire

## Tableau de Bord Vétérinaire

### Composants

- Consultations en cours
- Historique des consultations
- Statistiques de santé
- Gestion des rendez-vous

### Fonctionnalités

- Suivi des patients
- Gestion des consultations
- Prescriptions et traitements
- Rapports de santé

## Tableau de Bord Marchand

### Composants

- Orders Table (Table des commandes)
- Low Stock Alert (Alerte de stock faible)
- Ventes Dashboard (Tableau de bord des ventes)

### Fonctionnalités

- Gestion des commandes
- Suivi des stocks
- Statistiques de vente
- Gestion des produits

## Tableau de Bord Administrateur

### Composants

- Statistiques générales
- Gestion des utilisateurs
- Blog Generator (Générateur de blog)
- Page Content Generator (Générateur de contenu de page)

### Fonctionnalités

- Gestion des rôles
- Statistiques globales
- Génération de contenu IA
- Administration système

## Fonctionnalités Communes

### Interface

- Support multilingue (Français/Arabe)
- Design responsive
- Thème personnalisable

### Sécurité

- Authentification JWT
- Gestion des rôles
- Audit des actions

### Données

- Synchronisation en temps réel
- Sauvegarde automatique
- Export de données

###### ### Voici la structure du menu latéral

### Éléments Communs (Tous les rôles)

### Menu Administrateur

- Tableau de bord ( DashboardIcon )
- Profil ( PersonIcon )
- Page d'accueil ( ArticleIcon )
- Gestion des utilisateurs ( PersonIcon )
  - Tous les utilisateurs
  - Éleveurs
  - Vétérinaires
  - Marchands
- Volailles ( PetsIcon )
- Statistiques ( ChartIcon )
- Blog ( ArticleIcon )
- Intelligence Artificielle ( AIIcon )
  - Générateur de blog
  - Analyse de données
  - Contenu de page
- Traductions ( TranslateIcon )
- Rôles et Plans ( SettingsIcon )
- Paramètres
- Notifications

### Menu Éleveur

- Tableau de bord ( DashboardIcon )
- Profil ( PersonIcon )
- Gestion des volailles ( PetsIcon )
- Production d'œufs ( ChartIcon )
- Alertes stock ( NotificationsIcon )
- Consultations vétérinaires ( MedicalIcon )

### Menu Vétérinaire

- Tableau de bord ( DashboardIcon )
- Profil ( PersonIcon )
- Prescriptions ( MedicalIcon )
- Consultations
- Historique des patients

### Menu Marchand

- Tableau de bord ( DashboardIcon )
- Profil ( PersonIcon )
- Produits ( StoreIcon )
- Commandes
- Ventes
- Statistiques ( ChartIcon )
  Chaque section du menu est stylisée avec :

- Des icônes colorées thématiques
- Des effets de survol et de sélection
- Une mise en page responsive
- Support multilingue (FR/AR)

Routes Administrateur ( adminRoutes.js )

- GET /api/admin/roles : Récupérer tous les rôles.
- POST /api/admin/roles : Créer un nouveau rôle.
- PUT /api/admin/roles/:id : Mettre à jour un rôle.
- DELETE /api/admin/roles/:id : Supprimer un rôle.
- GET /api/admin/users : Récupérer tous les utilisateurs (admin seulement).
- GET /api/admin/users/:id : Récupérer un utilisateur spécifique (admin seulement).
- PUT /api/admin/users/:id : Mettre à jour un utilisateur (admin seulement).
- DELETE /api/admin/users/:id : Supprimer un utilisateur (admin seulement).
- POST /api/admin/users/:id/change-role : Changer le rôle d'un utilisateur.
- POST /api/admin/login-as-user/:userId : Se connecter en tant qu'autre utilisateur.
- POST /api/admin/logout-as-user : Se déconnecter du mode "login-as-user".
- GET /api/admin/settings : Récupérer les paramètres de l'application.
- PUT /api/admin/settings : Mettre à jour les paramètres de l'application.
- GET /api/admin/notifications : Récupérer les notifications administrateur.
- POST /api/admin/notifications : Créer une notification administrateur.
- PUT /api/admin/notifications/:id/read : Marquer une notification comme lue.
- GET /api/admin/activity-logs : Récupérer les logs d'activité.
- GET /api/admin/system-health : Vérifier l'état du système.
- POST /api/admin/maintenance : Activer/Désactiver le mode maintenance.
- GET /api/admin/config/smtp : Récupérer la configuration SMTP.
- POST /api/admin/config/smtp : Mettre à jour la configuration SMTP.
- POST /api/admin/config/smtp/test : Tester la configuration SMTP.
- GET /api/admin/config/api-keys : Récupérer les clés API.
- POST /api/admin/config/api-keys : Mettre à jour les clés API.
- GET /api/admin/security-settings : Récupérer les paramètres de sécurité.
- POST /api/admin/security-settings : Mettre à jour les paramètres de sécurité.
  Routes Statistiques Administrateur ( adminStatisticsRoutes.js )

- GET /api/admin/statistics/users : Statistiques sur les utilisateurs.
- GET /api/admin/statistics/farms : Statistiques sur les fermes.
- GET /api/admin/statistics/marketplace : Statistiques sur la place de marché.
- GET /api/admin/statistics/transactions : Statistiques sur les transactions.
- GET /api/admin/statistics/system : Statistiques sur le système.
  Routes IA ( aiRoutes.js )

- POST /api/ai/generate-blog-post : Générer un article de blog.
- POST /api/ai/analyze-data : Analyser des données.
- POST /api/ai/recommendations : Obtenir des recommandations.
- GET /api/ai/models : Lister les modèles IA disponibles.
- POST /api/ai/chat : Interagir avec un chatbot IA.
  Routes Alertes Stock ( alerteStockRoutes.js )

- GET /api/alertes-stock : Récupérer toutes les alertes de stock.
- POST /api/alertes-stock : Créer une nouvelle alerte de stock.
- GET /api/alertes-stock/:id : Récupérer une alerte de stock par ID.
- PUT /api/alertes-stock/:id : Mettre à jour une alerte de stock.
- DELETE /api/alertes-stock/:id : Supprimer une alerte de stock.
- GET /api/alertes-stock/eleveur/:eleveurId : Récupérer les alertes de stock pour un éleveur.
  Routes Authentification ( authRoutes.js )

- POST /api/auth/register : Inscription d'un nouvel utilisateur.
- POST /api/auth/login : Connexion d'un utilisateur.
- POST /api/auth/logout : Déconnexion d'un utilisateur.
- GET /api/auth/me : Récupérer les informations de l'utilisateur connecté.
- PUT /api/auth/profile : Mettre à jour le profil de l'utilisateur.
- POST /api/auth/forgot-password : Demande de réinitialisation de mot de passe.
- POST /api/auth/reset-password : Réinitialisation du mot de passe.
- POST /api/auth/refresh-token : Rafraîchir le token JWT.
- GET /api/auth/check-email/:email : Vérifier si un email existe.
- POST /api/auth/verify-email : Vérifier l'email de l'utilisateur.
- POST /api/auth/resend-verification-email : Renvoyer l'email de vérification.
  Routes Blog ( blogRoutes.js )

- GET /api/blog/posts : Récupérer tous les articles de blog.
- POST /api/blog/posts : Créer un nouvel article de blog (admin/éditeur).
- GET /api/blog/posts/:slug : Récupérer un article de blog par son slug.
- PUT /api/blog/posts/:id : Mettre à jour un article de blog (admin/éditeur).
- DELETE /api/blog/posts/:id : Supprimer un article de blog (admin/éditeur).
- GET /api/blog/categories : Récupérer toutes les catégories de blog.
- POST /api/blog/categories : Créer une nouvelle catégorie de blog (admin).
- PUT /api/blog/categories/:id : Mettre à jour une catégorie de blog (admin).
- DELETE /api/blog/categories/:id : Supprimer une catégorie de blog (admin).
- GET /api/blog/tags : Récupérer tous les tags.
- POST /api/blog/posts/:id/comments : Ajouter un commentaire à un article.
- GET /api/blog/posts/:id/comments : Récupérer les commentaires d'un article.
  Routes Éleveurs ( eleveurRoutes.js )

- GET /api/eleveurs : Récupérer tous les éleveurs.
- POST /api/eleveurs : Créer un nouvel éleveur.
- GET /api/eleveurs/:id : Récupérer un éleveur par ID.
- PUT /api/eleveurs/:id : Mettre à jour un éleveur.
- DELETE /api/eleveurs/:id : Supprimer un éleveur.
- GET /api/eleveurs/:id/dashboard : Récupérer les données du tableau de bord d'un éleveur.
- GET /api/eleveurs/:id/volailles : Récupérer les volailles d'un éleveur.
- GET /api/eleveurs/:id/rapports : Récupérer les rapports d'un éleveur.
  Routes Marchands ( marchandRoutes.js )

- GET /api/marchands : Récupérer tous les marchands.
- POST /api/marchands : Créer un nouveau marchand.
- GET /api/marchands/:id : Récupérer un marchand par ID.
- PUT /api/marchands/:id : Mettre à jour un marchand.
- DELETE /api/marchands/:id : Supprimer un marchand.
- GET /api/marchands/:id/produits : Récupérer les produits d'un marchand.
- POST /api/marchands/:id/produits : Ajouter un produit pour un marchand.
- GET /api/marchands/:id/commandes : Récupérer les commandes d'un marchand.
- GET /api/marchands/:id/ventes : Récupérer les ventes d'un marchand.
- GET /api/marchands/:id/dashboard : Récupérer les données du tableau de bord d'un marchand.
- POST /api/marchands/:id/recommendations-ia : Obtenir des recommandations IA pour un marchand.
  Routes Place de Marché ( marketplaceRoutes.js )

- GET /api/marketplace/produits : Récupérer tous les produits de la place de marché.
- GET /api/marketplace/produits/:id : Récupérer un produit par ID.
- POST /api/marketplace/commandes : Créer une nouvelle commande.
- GET /api/marketplace/commandes/:id : Récupérer une commande par ID.
- PUT /api/marketplace/commandes/:id/statut : Mettre à jour le statut d'une commande.
- GET /api/marketplace/categories : Récupérer les catégories de produits.
- GET /api/marketplace/eleveurs/:eleveurId/produits : Récupérer les produits d'un éleveur spécifique.
- POST /api/marketplace/produits/:id/avis : Ajouter un avis sur un produit.
- GET /api/marketplace/produits/:id/avis : Récupérer les avis d'un produit.
  Routes Notifications ( notificationRoutes.js )

- GET /api/notifications : Récupérer les notifications de l'utilisateur.
- POST /api/notifications/read : Marquer les notifications comme lues.
- POST /api/notifications/read/:id : Marquer une notification spécifique comme lue.
- DELETE /api/notifications/:id : Supprimer une notification.
- GET /api/notifications/settings : Récupérer les paramètres de notification de l'utilisateur.
- PUT /api/notifications/settings : Mettre à jour les paramètres de notification.
  Routes Poussins ( poussinRoutes.js )

- GET /api/poussins : Récupérer tous les poussins d'un éleveur.
- POST /api/poussins : Créer un nouveau lot de poussins.
- GET /api/poussins/actifs : Récupérer les poussins actifs.
- GET /api/poussins/statistiques : Récupérer les statistiques des poussins.
- GET /api/poussins/:id : Récupérer un poussin spécifique.
- PUT /api/poussins/:id : Mettre à jour un lot de poussins.
- POST /api/poussins/:id/vaccination : Ajouter une vaccination.
- POST /api/poussins/:id/traitement : Ajouter un traitement.
- POST /api/poussins/:id/mortalite : Enregistrer une mortalité.
- POST /api/poussins/:id/transfert : Transférer des poussins.
- DELETE /api/poussins/:id : Supprimer un lot de poussins.
- GET /api/poussins/lot/:lotNumero : Récupérer un lot par son numéro.
  Routes Production d'Oeufs ( productionOeufsRoutes.js )

- GET /api/production-oeufs : Récupérer toutes les productions d'un éleveur.
- POST /api/production-oeufs : Créer une nouvelle production.
- GET /api/production-oeufs/statistiques : Récupérer les statistiques de production.
- GET /api/production-oeufs/aujourd-hui : Récupérer la production du jour.
- GET /api/production-oeufs/:id : Récupérer une production spécifique.
- PUT /api/production-oeufs/:id : Mettre à jour une production.
- POST /api/production-oeufs/:id/vente : Mettre à jour les ventes d'œufs.
- DELETE /api/production-oeufs/:id : Supprimer une production.
- GET /api/production-oeufs/lot/:volailleId : Récupérer les productions pour un lot de volailles.
  Routes Suivi Vétérinaire ( suiviVeterinaireRoutes.js )

- GET /api/suivi-veterinaire : Récupérer tous les suivis d'un éleveur/vétérinaire.
- POST /api/suivi-veterinaire : Créer un nouveau suivi.
- GET /api/suivi-veterinaire/planifies : Récupérer les interventions planifiées.
- GET /api/suivi-veterinaire/urgentes : Récupérer les interventions urgentes.
- GET /api/suivi-veterinaire/statistiques : Récupérer les statistiques de suivi.
- GET /api/suivi-veterinaire/plan-vaccination : Récupérer le plan de vaccination.
- GET /api/suivi-veterinaire/:id : Récupérer un suivi spécifique.
- PUT /api/suivi-veterinaire/:id : Mettre à jour un suivi.
- POST /api/suivi-veterinaire/:id/rapport : Ajouter un rapport à un suivi.
- POST /api/suivi-veterinaire/:id/prescription : Ajouter une prescription à un suivi.
- DELETE /api/suivi-veterinaire/:id : Supprimer un suivi.
- GET /api/suivi-veterinaire/eleveur/:eleveurId : Récupérer les suivis pour un éleveur.
- GET /api/suivi-veterinaire/veterinaire/:veterinaireId : Récupérer les suivis pour un vétérinaire.
  Routes Traductions ( translationRoutes.js )

- GET /api/translations : Récupérer toutes les traductions pour une langue.
- POST /api/translations : Créer ou mettre à jour une traduction (admin).
- GET /api/translations/languages : Récupérer les langues disponibles.
- GET /api/translations/categories : Récupérer les catégories de traduction (admin).
- PUT /api/translations/:id : Mettre à jour une traduction par ID (admin).
- DELETE /api/translations/:id : Supprimer une traduction par ID (admin).
- POST /api/translations/bulk : Importer des traductions en lot (admin).
- GET /api/translations/export : Exporter les traductions (admin).
  Routes Ventes ( venteRoutes.js )

- GET /api/ventes : Récupérer toutes les ventes.
- POST /api/ventes : Créer une nouvelle vente.
- GET /api/ventes/stats : Récupérer les statistiques des ventes.
- GET /api/ventes/:id : Récupérer une vente par ID.
- PUT /api/ventes/:id : Mettre à jour une vente.
- DELETE /api/ventes/:id : Annuler/Supprimer une vente.
  Routes Vétérinaires ( veterinaireRoutes.js ) (semble être une version plus ancienne ou spécifique pour le profil vétérinaire)

- GET /api/veterinaire/dashboard : Données du tableau de bord du vétérinaire.
- GET /api/veterinaire/prescriptions : Récupérer les prescriptions du vétérinaire.
- POST /api/veterinaire/prescriptions : Créer une nouvelle prescription.
- GET /api/veterinaire/consultations : Récupérer les consultations du vétérinaire.
- POST /api/veterinaire/consultations : Créer une nouvelle consultation.
- GET /api/veterinaire/eleveurs : Récupérer les éleveurs suivis par le vétérinaire.
- GET /api/veterinaire/stats : Récupérer les statistiques du vétérinaire.
- GET /api/veterinaire/consultations/historique : Historique des consultations.
- GET /api/veterinaire/consultations/:id : Détails d'une consultation.
- PATCH /api/veterinaire/consultations/:id/status : Mettre à jour le statut d'une consultation.
  Routes Vétérinaires ( veterinaryRoutes.js ) (semble être pour la gestion CRUD des profils vétérinaires)

- GET /api/veterinaires : Lister les vétérinaires.
- POST /api/veterinaires : Créer un nouveau vétérinaire.
- GET /api/veterinaires/:id : Détails d'un vétérinaire.
- PUT /api/veterinaires/:id : Modifier un vétérinaire.
- DELETE /api/veterinaires/:id : Supprimer un vétérinaire.
  Routes Volailles ( volailleRoutes.js )

- POST /api/volailles : Créer une nouvelle volaille.
- GET /api/volailles : Récupérer toutes les volailles.
- GET /api/volailles/:id : Récupérer une volaille par son ID.
- GET /api/volailles/eleveur/:eleveurId : Récupérer les volailles d'un éleveur.
- PUT /api/volailles/:id : Mettre à jour une volaille.
- DELETE /api/volailles/:id : Supprimer une volaille.
