/**
 * Service de synchronisation des données en temps réel
 * Gère la synchronisation entre le frontend et le backend
 */

import { EventEmitter } from 'events';

// Types d'événements de synchronisation
export const SYNC_EVENTS = {
  DATA_UPDATED: 'data_updated',
  DATA_CONFLICT: 'data_conflict',
  SYNC_ERROR: 'sync_error',
  CONNECTION_STATUS: 'connection_status',
  REFRESH_REQUIRED: 'refresh_required'
};

// Types de données synchronisables
export const DATA_TYPES = {
  DASHBOARD: 'dashboard',
  ELEVEURS: 'eleveurs',
  VOLAILLES: 'volailles',
  CONSULTATIONS: 'consultations',
  PRESCRIPTIONS: 'prescriptions',
  COMMANDES: 'commandes',
  STOCK: 'stock',
  PRODUCTION: 'production',
  ALERTES: 'alertes'
};

/**
 * Classe pour gérer la synchronisation des données
 */
class SyncService extends EventEmitter {
  constructor() {
    super();
    this.syncIntervals = new Map();
    this.lastSyncTimes = new Map();
    this.conflictResolutionStrategies = new Map();
    this.isOnline = navigator.onLine;
    this.pendingUpdates = new Map();
    this.setupConnectionMonitoring();
  }

  /**
   * Surveiller l'état de la connexion
   */
  setupConnectionMonitoring() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.emit(SYNC_EVENTS.CONNECTION_STATUS, { online: true });
      this.processPendingUpdates();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.emit(SYNC_EVENTS.CONNECTION_STATUS, { online: false });
    });
  }

  /**
   * Démarrer la synchronisation automatique pour un type de données
   */
  startAutoSync(dataType, fetchFunction, interval = 30000, options = {}) {
    const {
      immediate = true,
      conflictResolution = 'server_wins',
      retryOnError = true,
      maxRetries = 3
    } = options;

    // Arrêter la synchronisation existante si elle existe
    this.stopAutoSync(dataType);

    // Configuration de la stratégie de résolution de conflits
    this.conflictResolutionStrategies.set(dataType, conflictResolution);

    // Synchronisation immédiate si demandée
    if (immediate) {
      this.syncData(dataType, fetchFunction, { retryOnError, maxRetries });
    }

    // Démarrer la synchronisation périodique
    const intervalId = setInterval(() => {
      this.syncData(dataType, fetchFunction, { retryOnError, maxRetries });
    }, interval);

    this.syncIntervals.set(dataType, intervalId);

    console.log(`🔄 Synchronisation automatique démarrée pour ${dataType} (intervalle: ${interval}ms)`);
  }

  /**
   * Arrêter la synchronisation automatique pour un type de données
   */
  stopAutoSync(dataType) {
    const intervalId = this.syncIntervals.get(dataType);
    if (intervalId) {
      clearInterval(intervalId);
      this.syncIntervals.delete(dataType);
      console.log(`⏹️ Synchronisation automatique arrêtée pour ${dataType}`);
    }
  }

  /**
   * Synchroniser les données pour un type spécifique
   */
  async syncData(dataType, fetchFunction, options = {}) {
    const { retryOnError = true, maxRetries = 3 } = options;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        if (!this.isOnline) {
          console.warn(`⚠️ Synchronisation différée pour ${dataType} (hors ligne)`);
          return;
        }

        const startTime = Date.now();
        const newData = await fetchFunction();
        const endTime = Date.now();

        // Enregistrer le temps de synchronisation
        this.lastSyncTimes.set(dataType, new Date());

        // Émettre l'événement de mise à jour
        this.emit(SYNC_EVENTS.DATA_UPDATED, {
          dataType,
          data: newData,
          syncTime: endTime - startTime,
          timestamp: new Date()
        });

        console.log(`✅ Synchronisation réussie pour ${dataType} (${endTime - startTime}ms)`);
        return newData;

      } catch (error) {
        retryCount++;
        console.error(`❌ Erreur de synchronisation pour ${dataType} (tentative ${retryCount}/${maxRetries + 1}):`, error);

        if (retryCount <= maxRetries && retryOnError) {
          // Attendre avant de réessayer (backoff exponentiel)
          const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // Émettre l'événement d'erreur
          this.emit(SYNC_EVENTS.SYNC_ERROR, {
            dataType,
            error,
            retryCount,
            timestamp: new Date()
          });
          throw error;
        }
      }
    }
  }

  /**
   * Forcer la synchronisation immédiate pour un type de données
   */
  async forcSync(dataType, fetchFunction) {
    console.log(`🔄 Synchronisation forcée pour ${dataType}`);
    return this.syncData(dataType, fetchFunction, { retryOnError: false });
  }

  /**
   * Gérer les conflits de données
   */
  handleDataConflict(dataType, localData, serverData) {
    const strategy = this.conflictResolutionStrategies.get(dataType) || 'server_wins';

    switch (strategy) {
      case 'server_wins':
        console.log(`🔄 Résolution de conflit pour ${dataType}: serveur prioritaire`);
        return serverData;

      case 'client_wins':
        console.log(`🔄 Résolution de conflit pour ${dataType}: client prioritaire`);
        return localData;

      case 'merge':
        console.log(`🔄 Résolution de conflit pour ${dataType}: fusion des données`);
        return this.mergeData(localData, serverData);

      case 'manual':
        console.log(`⚠️ Conflit de données pour ${dataType}: résolution manuelle requise`);
        this.emit(SYNC_EVENTS.DATA_CONFLICT, {
          dataType,
          localData,
          serverData,
          timestamp: new Date()
        });
        return null;

      default:
        return serverData;
    }
  }

  /**
   * Fusionner les données locales et serveur
   */
  mergeData(localData, serverData) {
    // Stratégie de fusion simple basée sur les timestamps
    if (Array.isArray(localData) && Array.isArray(serverData)) {
      const merged = [...serverData];
      
      localData.forEach(localItem => {
        const serverItem = serverData.find(item => item.id === localItem.id);
        if (!serverItem) {
          merged.push(localItem);
        } else if (localItem.updated_at > serverItem.updated_at) {
          const index = merged.findIndex(item => item.id === localItem.id);
          merged[index] = localItem;
        }
      });

      return merged;
    }

    // Pour les objets, fusionner les propriétés
    if (typeof localData === 'object' && typeof serverData === 'object') {
      return {
        ...serverData,
        ...localData,
        // Garder les timestamps du serveur s'ils sont plus récents
        updated_at: serverData.updated_at > localData.updated_at ? serverData.updated_at : localData.updated_at
      };
    }

    return serverData;
  }

  /**
   * Ajouter une mise à jour en attente (pour mode hors ligne)
   */
  addPendingUpdate(dataType, updateFunction, data) {
    if (!this.pendingUpdates.has(dataType)) {
      this.pendingUpdates.set(dataType, []);
    }

    this.pendingUpdates.get(dataType).push({
      updateFunction,
      data,
      timestamp: new Date()
    });

    console.log(`📝 Mise à jour en attente ajoutée pour ${dataType}`);
  }

  /**
   * Traiter les mises à jour en attente
   */
  async processPendingUpdates() {
    if (!this.isOnline || this.pendingUpdates.size === 0) {
      return;
    }

    console.log(`🔄 Traitement de ${this.pendingUpdates.size} type(s) de mises à jour en attente`);

    for (const [dataType, updates] of this.pendingUpdates.entries()) {
      try {
        for (const update of updates) {
          await update.updateFunction(update.data);
          console.log(`✅ Mise à jour en attente traitée pour ${dataType}`);
        }
        
        // Nettoyer les mises à jour traitées
        this.pendingUpdates.delete(dataType);
        
        // Émettre un événement pour indiquer qu'un rafraîchissement est nécessaire
        this.emit(SYNC_EVENTS.REFRESH_REQUIRED, { dataType });
        
      } catch (error) {
        console.error(`❌ Erreur lors du traitement des mises à jour en attente pour ${dataType}:`, error);
      }
    }
  }

  /**
   * Obtenir le statut de synchronisation
   */
  getSyncStatus(dataType = null) {
    if (dataType) {
      return {
        isActive: this.syncIntervals.has(dataType),
        lastSync: this.lastSyncTimes.get(dataType),
        hasPendingUpdates: this.pendingUpdates.has(dataType),
        pendingCount: this.pendingUpdates.get(dataType)?.length || 0
      };
    }

    // Statut global
    const activeTypes = Array.from(this.syncIntervals.keys());
    const totalPending = Array.from(this.pendingUpdates.values())
      .reduce((total, updates) => total + updates.length, 0);

    return {
      isOnline: this.isOnline,
      activeTypes,
      totalPending,
      lastSyncTimes: Object.fromEntries(this.lastSyncTimes)
    };
  }

  /**
   * Nettoyer toutes les synchronisations
   */
  cleanup() {
    // Arrêter tous les intervalles
    for (const [dataType] of this.syncIntervals) {
      this.stopAutoSync(dataType);
    }

    // Nettoyer les données
    this.lastSyncTimes.clear();
    this.conflictResolutionStrategies.clear();
    this.pendingUpdates.clear();

    // Supprimer les listeners d'événements
    this.removeAllListeners();

    console.log('🧹 Service de synchronisation nettoyé');
  }
}

// Instance singleton
const syncService = new SyncService();

export default syncService;
