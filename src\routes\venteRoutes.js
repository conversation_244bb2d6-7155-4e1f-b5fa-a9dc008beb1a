const express = require('express');
const router = express.Router();
const {
  getVentesByEleveur,
  createVente,
  updateVente,
  deleteVente,
  getStatistiquesVentes,
  getVenteById
} = require('../controllers/venteController');
const { auth, checkRole } = require('../middleware/auth');

// Middleware d'authentification pour toutes les routes
router.use(auth);

// Routes pour les ventes

// GET /api/ventes/eleveur/:eleveurId - Obtenir toutes les ventes d'un éleveur
router.get('/eleveur/:eleveurId', checkRole(['admin', 'eleveur']), getVentesByEleveur);

// GET /api/ventes/eleveur/:eleveurId/statistiques - Obtenir les statistiques de vente
router.get('/eleveur/:eleveurId/statistiques', checkRole(['admin', 'eleveur']), getStatistiquesVentes);

// GET /api/ventes/:id - Obtenir une vente par ID
router.get('/:id', checkRole(['admin', 'eleveur', 'marchand']), getVenteById);

// POST /api/ventes - Créer une nouvelle vente
router.post('/', checkRole(['admin', 'eleveur']), createVente);

// PUT /api/ventes/:id - Mettre à jour une vente
router.put('/:id', checkRole(['admin', 'eleveur']), updateVente);

// DELETE /api/ventes/:id - Supprimer une vente
router.delete('/:id', checkRole(['admin', 'eleveur']), deleteVente);

module.exports = router;
