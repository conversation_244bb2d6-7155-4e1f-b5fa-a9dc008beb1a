const { DataTypes, Model } = require('sequelize');

// La création des types ENUM doit être gérée par des migrations Sequelize
// et non directement dans le code du modèle de cette manière.
// async function createEnumTypes(sequelizeInstance) { // Passer l'instance sequelize
//   try {
//     await sequelizeInstance.query(`
//       DO $$
//       BEGIN
//         IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_type_elevage') THEN
//           CREATE TYPE enum_volailles_type_elevage AS ENUM ('chair', 'pondeuse', 'reproducteur', 'mixte');
//         END IF;
//         IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_statut') THEN
//           CREATE TYPE enum_volailles_statut AS ENUM ('actif', 'vendu', 'abattu', 'transfere', 'archive');
//         END IF;
//       END
//       $$;
//     `);
//     console.log('Enum types for Volail<PERSON> checked/created.');
//   } catch (error) {
//     console.error('Erreur lors de la création des types ENUM pour Volaille:', error);
//   }
// }

module.exports = (sequelize, DataTypes) => {
  // Appel pour créer/vérifier les ENUMs - Idéalement, cela se fait dans une migration
  // createEnumTypes(sequelize);

  class Volaille extends Model {
    static associate(models) {
      Volaille.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      // Si Volaille peut avoir plusieurs SuiviVeterinaire
      Volaille.hasMany(models.SuiviVeterinaire, {
        foreignKey: 'volaille_id',
        as: 'suivisVeterinaires'
      });
      // Si Volaille peut être dans plusieurs ProductionOeufs (improbable, mais pour l'exemple)
      // Volaille.hasMany(models.ProductionOeufs, { foreignKey: 'volaille_id', as: 'productionsOeufs' });
      // Si Volaille peut être vendue (via le modèle Vente)
      Volaille.hasMany(models.Vente, {
          foreignKey: 'volaille_id',
          as: 'ventes'
      });
    }

    static async findByEleveur(eleveurId) {
      return await this.findAll({
        where: { eleveur_id: eleveurId }
      });
    }

    static async updateStock(id, quantite) {
      return await this.update(
        { quantite },
        { where: { id } }
      );
    }

    static async updateStatus(id, statut) {
      return await this.update(
        { statut },
        { where: { id } }
      );
    }
  }

  Volaille.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'eleveurs', // Nom de la table Eleveurs
        key: 'id'
      },
      comment: 'Référence vers l\'éleveur propriétaire'
    },
    espece: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Espèce de volaille (poule, canard, dinde, etc.)'
    },
    race: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'Race spécifique'
    },
    souche: {
      type: DataTypes.STRING(50),
      comment: 'Souche de la race (ex: ISA Brown, Lohmann Brown)'
    },
    age: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Âge en jours'
    },
    poids: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      comment: 'Poids moyen en kg'
    },
    quantite: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Nombre d\'animaux dans le lot'
    },
    quantite_initiale: {
      type: DataTypes.INTEGER,
      comment: 'Nombre initial d\'animaux (pour calcul mortalité)'
    },
    prix: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      defaultValue: 0,
      comment: 'Prix unitaire en DA'
    },
    lot_numero: {
      type: DataTypes.STRING(50),
      comment: 'Numéro unique du lot'
    },
    type_elevage: {
      type: DataTypes.ENUM('chair', 'pondeuse', 'reproducteur', 'mixte'),
      defaultValue: 'chair',
      comment: 'Type d\'élevage'
    },
    statut: {
      type: DataTypes.ENUM('actif', 'vendu', 'abattu', 'transfere', 'archive'),
      defaultValue: 'actif',
      comment: 'Statut du lot'
    },
    date_acquisition: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: 'Date d\'acquisition du lot'
    },
    date_eclosion: {
      type: DataTypes.DATE,
      comment: 'Date d\'éclosion (si applicable)'
    },
    fournisseur: {
      type: DataTypes.STRING(100),
      comment: 'Fournisseur du lot'
    },
    batiment: {
      type: DataTypes.STRING(50),
      comment: 'Bâtiment d\'élevage'
    },
    zone_elevage: {
      type: DataTypes.STRING(50),
      comment: 'Zone spécifique dans le bâtiment'
    },
    conditions_elevage: {
      type: DataTypes.JSONB,
      defaultValue: {
        temperature_min: null,
        temperature_max: null,
        humidite: null,
        ventilation: null,
        eclairage_heures: null,
        densite_m2: null
      },
      comment: 'Conditions d\'élevage'
    },
    alimentation: {
      type: DataTypes.JSONB,
      defaultValue: {
        type_aliment: null,
        marque: null,
        consommation_quotidienne: null,
        indice_consommation: null,
        cout_aliment_kg: null
      },
      comment: 'Données d\'alimentation'
    },
    sante: {
      type: DataTypes.JSONB,
      defaultValue: {
        derniere_vaccination: null,
        prochaine_vaccination: null,
        traitements_en_cours: [],
        mortalite_semaine: 0,
        mortalite_cumulative: 0,
        taux_mortalite: 0
      },
      comment: 'Données de santé'
    },
    performances: {
      type: DataTypes.JSONB,
      defaultValue: {
        gain_poids_quotidien: null,
        indice_conversion: null,
        taux_ponte: null, // Pour pondeuses
        poids_oeuf_moyen: null, // Pour pondeuses
        efficacite_alimentaire: null
      },
      comment: 'Données de performance'
    },
    reproduction: {
      type: DataTypes.JSONB,
      defaultValue: {
        age_maturite_sexuelle: null,
        taux_fertilite: null,
        taux_eclosion: null,
        nombre_oeufs_couves: null
      },
      comment: 'Données de reproduction (pour reproducteurs)'
    },
    cout_total: {
      type: DataTypes.DECIMAL(12, 2),
      comment: 'Coût total investi (achat + alimentation + soins)'
    },
    valeur_estimee: {
      type: DataTypes.DECIMAL(12, 2),
      comment: 'Valeur estimée actuelle du lot'
    },
    objectifs: {
      type: DataTypes.JSONB,
      defaultValue: {
        poids_objectif: null,
        age_abattage_prevu: null,
        production_oeufs_objectif: null,
        rentabilite_objectif: null
      },
      comment: 'Objectifs de production'
    },
    notes: {
      type: DataTypes.TEXT,
      comment: 'Notes et observations'
    },
    photos: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Photos du lot'
    },
    alerte_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si une alerte est active'
    },
    suivi_quotidien: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Activer le suivi quotidien'
    },
    // date_modification est géré par timestamps: true (updatedAt)
  }, {
    sequelize,
    modelName: 'Volaille',
    tableName: 'volailles',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at', // Sequelize gère updated_at, donc date_modification est redondant
    indexes: [
      { fields: ['eleveur_id'] },
      { fields: ['lot_numero'] },
      { fields: ['type_elevage'] },
      { fields: ['statut'] },
      { fields: ['date_acquisition'] },
      { unique: true, fields: ['eleveur_id', 'lot_numero'] }
    ],
    hooks: {
      beforeUpdate: (volaille, options) => {
        if (volaille.changed('sante') || volaille.changed('quantite_initiale')) {
          const sante = volaille.sante || {};
          const quantiteInitiale = volaille.quantite_initiale;
          const mortaliteCumulative = sante.mortalite_cumulative || 0;

          if (quantiteInitiale && quantiteInitiale > 0) {
            sante.taux_mortalite = (mortaliteCumulative / quantiteInitiale) * 100;
          } else {
            sante.taux_mortalite = 0;
          }
          volaille.sante = JSON.parse(JSON.stringify(sante)); // Forcer la mise à jour de l'objet JSONB
        }

        const santeData = volaille.sante || {};
        const tauxMortalite = santeData.taux_mortalite || 0;
        const mortaliteSemaine = santeData.mortalite_semaine || 0;

        if (tauxMortalite > 5 || mortaliteSemaine > 10) {
          volaille.alerte_active = true;
        } else {
          // Optionnel: désactiver l'alerte si les conditions ne sont plus remplies
          // et qu'aucune autre condition ne maintient l'alerte.
          // volaille.alerte_active = false;
        }
        // La colonne date_modification est gérée par `updatedAt: 'updated_at'` ou `timestamps: true`
      }
    }
  });

  return Volaille;
};
