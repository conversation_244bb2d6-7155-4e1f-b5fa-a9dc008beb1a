// Import des modules nécessaires
import axiosInstance from '../utils/axiosConfig';

// Service de gestion des paiements
class PaymentService {
  // Configuration des types de paiement disponibles
  static PAYMENT_TYPES = {
    BARIDI: 'baridi',
    CCP: 'ccp',
    BANK_TRANSFER: 'bank_transfer'
  };

  // Initialiser un nouveau paiement
  async initializePayment(paymentData) {
    try {
      const response = await axiosInstance.post('/payments/initialize', {
        ...paymentData,
        payment_type: paymentData.paymentType
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du paiement:', error);
      throw this.handlePaymentError(error);
    }
  }

  // Vérifier le statut d'un paiement
  async checkPaymentStatus(paymentId) {
    try {
      const response = await axiosInstance.get(`/payments/${paymentId}/status`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error);
      throw this.handlePaymentError(error);
    }
  }

  // Confirmer un paiement
  async confirmPayment(paymentId, confirmationData) {
    try {
      const response = await axiosInstance.post(`/payments/${paymentId}/confirm`, confirmationData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la confirmation du paiement:', error);
      throw this.handlePaymentError(error);
    }
  }

  // Obtenir l'historique des paiements
  async getPaymentHistory(userId) {
    try {
      const response = await axiosInstance.get(`/payments/history/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      throw this.handlePaymentError(error);
    }
  }

  // Gestion des erreurs de paiement
  handlePaymentError(error) {
    let message = 'Une erreur est survenue lors du traitement du paiement';

    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = 'Données de paiement invalides';
          break;
        case 401:
          message = 'Non autorisé à effectuer cette opération';
          break;
        case 404:
          message = 'Paiement non trouvé';
          break;
        case 409:
          message = 'Conflit lors du traitement du paiement';
          break;
        default:
          message = error.response.data.message || message;
      }
    }

    return new Error(message);
  }
}

export const paymentService = new PaymentService();
