/**
 * Script pour tester la mise à jour des paramètres de sécurité
 * Ce script peut être exécuté en dehors du frontend pour tester directement l'API
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const API_URL = 'http://localhost:3003/api';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin123';

// Fonction principale
async function testSecuritySettingsUpdate() {
  try {
    console.log('Test de mise à jour des paramètres de sécurité...'.yellow);

    // Connexion en tant qu'administrateur
    console.log('Connexion en tant qu\'admin...'.cyan);
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });

    const token = loginResponse.data.token;
    if (!token) {
      throw new Error('Aucun token reçu lors de la connexion');
    }
    console.log('✅ Connexion réussie!'.green);

    // Configuration des en-têtes avec le token
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      }
    };

    // Récupérer les paramètres de sécurité actuels
    console.log('Récupération des paramètres de sécurité actuels...'.cyan);
    const getResponse = await axios.get(`${API_URL}/admin/settings/security`, config);
    const currentSettings = getResponse.data;
    console.log('✅ Paramètres actuels récupérés!'.green);
    console.log('Paramètres actuels:'.yellow);
    console.log(JSON.stringify(currentSettings, null, 2));

    // Préparer les nouveaux paramètres (en changeant certaines valeurs)
    const newSettings = {
      ...currentSettings,
      sessionTimeout: currentSettings.sessionTimeout + 5, // Augmenter de 5 minutes
      apiRateLimitRequests: currentSettings.apiRateLimitRequests + 10 // Augmenter de 10 requêtes
    };

    // Mettre à jour les paramètres
    console.log('Mise à jour des paramètres de sécurité...'.cyan);
    const updateResponse = await axios.post(`${API_URL}/admin/settings/security`, newSettings, config);
    console.log('✅ Paramètres mis à jour avec succès!'.green);

    // Vérifier les nouveaux paramètres
    console.log('Récupération des paramètres mis à jour...'.cyan);
    const checkResponse = await axios.get(`${API_URL}/admin/settings/security`, config);
    const updatedSettings = checkResponse.data;
    console.log('✅ Nouveaux paramètres récupérés!'.green);
    console.log('Paramètres mis à jour:'.yellow);
    console.log(JSON.stringify(updatedSettings, null, 2));

    // Vérifier si les modifications ont été correctement enregistrées
    if (updatedSettings.sessionTimeout === newSettings.sessionTimeout &&
        updatedSettings.apiRateLimitRequests === newSettings.apiRateLimitRequests) {
      console.log('✅ TEST RÉUSSI: Les modifications ont été correctement enregistrées!'.green.bold);
    } else {
      console.log('❌ TEST ÉCHOUÉ: Les modifications n\'ont pas été correctement enregistrées!'.red.bold);
      console.log('Valeurs attendues:'.red);
      console.log(`- sessionTimeout: ${newSettings.sessionTimeout}`);
      console.log(`- apiRateLimitRequests: ${newSettings.apiRateLimitRequests}`);
      console.log('Valeurs reçues:'.red);
      console.log(`- sessionTimeout: ${updatedSettings.sessionTimeout}`);
      console.log(`- apiRateLimitRequests: ${updatedSettings.apiRateLimitRequests}`);
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:'.red.bold);
    if (error.response) {
      console.error(`Status: ${error.response.status}`.red);
      console.error('Réponse du serveur:'.red);
      console.error(JSON.stringify(error.response.data, null, 2).red);
    } else {
      console.error(error.message.red);
    }
  }
}

// Exécuter le test
testSecuritySettingsUpdate();
