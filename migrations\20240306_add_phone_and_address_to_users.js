'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const tableInfo = await queryInterface.describeTable('Users');
      const addColumns = [];

      if (!tableInfo.phone) {
        addColumns.push(
          queryInterface.addColumn('Users', 'phone', {
            type: Sequelize.STRING(50),
            allowNull: true
          })
        );
      }

      if (!tableInfo.address) {
        addColumns.push(
          queryInterface.addColumn('Users', 'address', {
            type: Sequelize.TEXT,
            allowNull: true
          })
        );
      }

      await Promise.all(addColumns);
      console.log('Successfully added phone and address columns to Users table');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      await Promise.all([
        queryInterface.removeColumn('Users', 'phone'),
        queryInterface.removeColumn('Users', 'address')
      ]);
      console.log('Successfully removed phone and address columns from Users table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
