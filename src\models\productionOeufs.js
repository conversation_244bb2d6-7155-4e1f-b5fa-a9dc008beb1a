const { DataTypes, Model, Op } = require('sequelize'); // Ajout de Op

module.exports = (sequelize, DataTypes) => {
  class ProductionOeufs extends Model {
    static associate(models) {
      ProductionOeufs.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      ProductionOeufs.belongsTo(models.Volaille, {
        foreignKey: 'volaille_id',
        as: 'volailleLot', // Alias pour éviter conflit si Volaille a d'autres associations
        required: false
      });
    }

    calculerPerformances() { // Changé en méthode d'instance
      const oeufsCollectes = this.oeufs_collectes || 0;
      const nombrePoules = this.nombre_poules || 1; // Eviter division par zéro
      const chiffreAffaires = this.chiffre_affaires_jour || 0;
      const pertes = (this.oeufs_casses || 0) + (this.oeufs_deformes || 0) + (this.oeufs_sales || 0) + (this.oeufs_petits || 0);

      return {
        taux_ponte: this.taux_ponte,
        taux_qualite: this.taux_qualite,
        oeufs_par_poule: nombrePoules > 0 ? oeufsCollectes / nombrePoules : 0,
        chiffre_affaires: chiffreAffaires,
        rentabilite_jour: nombrePoules > 0 ? chiffreAffaires / nombrePoules : 0,
        taux_perte: oeufsCollectes > 0 ? (pertes / oeufsCollectes) * 100 : 0
      };
    }

    async mettreAJourStock(oeufsVendus) { // Changé en méthode d'instance et async
      this.oeufs_vendus = (this.oeufs_vendus || 0) + oeufsVendus;
      this.stock_restant = Math.max(0, (this.stock_restant || 0) - oeufsVendus);
      return await this.save(); // Ajout de await
    }

    static async findByEleveur(eleveurId, options = {}) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          ...options.where
        },
        order: [['date_production', 'DESC']],
        ...options
      });
    }

    static async findByPeriode(eleveurId, dateDebut, dateFin) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          date_production: {
            [Op.between]: [dateDebut, dateFin] // Utilisation de Op importé
          }
        },
        order: [['date_production', 'ASC']]
      });
    }

    static async getStatistiquesPeriode(eleveurId, dateDebut, dateFin) {
      const productions = await this.findByPeriode(eleveurId, dateDebut, dateFin);

      if (productions.length === 0) {
        return {
          periode: { debut: dateDebut, fin: dateFin },
          nombre_jours: 0,
          total_oeufs: 0,
          total_vendables: 0,
          taux_ponte_moyen: 0,
          taux_qualite_moyen: 0,
          chiffre_affaires_total: 0,
          prix_moyen: 0
        };
      }

      const stats = {
        periode: { debut: dateDebut, fin: dateFin },
        nombre_jours: productions.length,
        total_oeufs: productions.reduce((sum, p) => sum + (p.oeufs_collectes || 0), 0),
        total_vendables: productions.reduce((sum, p) => sum + (p.oeufs_vendables || 0), 0),
        total_vendus: productions.reduce((sum, p) => sum + (p.oeufs_vendus || 0), 0),
        chiffre_affaires_total: productions.reduce((sum, p) => sum + (p.chiffre_affaires_jour || 0), 0),
        moyenne_poules: productions.reduce((sum, p) => sum + (p.nombre_poules || 0), 0) / productions.length
      };

      stats.taux_ponte_moyen = productions.reduce((sum, p) => sum + (p.taux_ponte || 0), 0) / productions.length;
      stats.taux_qualite_moyen = productions.reduce((sum, p) => sum + (p.taux_qualite || 0), 0) / productions.length;
      stats.prix_moyen = stats.total_vendus > 0 ? stats.chiffre_affaires_total / stats.total_vendus : 0;
      stats.oeufs_par_jour = stats.total_oeufs / productions.length;
      stats.oeufs_par_poule_jour = stats.moyenne_poules > 0 ? stats.total_oeufs / (stats.moyenne_poules * productions.length) : 0;

      return stats;
    }

    static async getStatistiquesAnnuelles(eleveurId, annee) {
      const dateDebut = new Date(annee, 0, 1);
      const dateFin = new Date(annee, 11, 31);
      const productions = await this.findByPeriode(eleveurId, dateDebut, dateFin);

      // Grouper par mois
      const parMois = {};
      productions.forEach(p => {
        const mois = new Date(p.date_production).getMonth(); // Assurer que date_production est un objet Date
        if (!parMois[mois]) {
          parMois[mois] = [];
        }
        parMois[mois].push(p);
      });

      const statistiquesMensuelles = {};
      for (let mois = 0; mois < 12; mois++) {
        const productionsMois = parMois[mois] || [];
        statistiquesMensuelles[mois] = {
          nombre_jours: productionsMois.length,
          total_oeufs: productionsMois.reduce((sum, p) => sum + (p.oeufs_collectes || 0), 0),
          total_vendables: productionsMois.reduce((sum, p) => sum + (p.oeufs_vendables || 0), 0),
          chiffre_affaires: productionsMois.reduce((sum, p) => sum + (p.chiffre_affaires_jour || 0), 0),
          taux_ponte_moyen: productionsMois.length > 0 ?
            productionsMois.reduce((sum, p) => sum + (p.taux_ponte || 0), 0) / productionsMois.length : 0
        };
      }

      return {
        annee,
        statistiques_mensuelles: statistiquesMensuelles,
        total_annuel: await this.getStatistiquesPeriode(eleveurId, dateDebut, dateFin)
      };
    }
  }

  ProductionOeufs.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'eleveurs', // Nom de la table Eleveurs
        key: 'id'
      },
      comment: 'Référence vers l\'éleveur propriétaire'
    },
    volaille_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'volailles', // Nom de la table Volailles
        key: 'id'
      },
      comment: 'Référence vers le lot de pondeuses (optionnel)'
    },
    date_production: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date de la production'
    },
    nombre_poules: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      },
      comment: 'Nombre de poules pondeuses actives'
    },
    oeufs_collectes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre total d\'œufs collectés'
    },
    oeufs_vendables: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs de qualité vendable'
    },
    oeufs_casses: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs cassés'
    },
    oeufs_deformes: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs déformés'
    },
    oeufs_sales: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs sales'
    },
    oeufs_petits: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs trop petits'
    },
    taux_ponte: {
      type: DataTypes.VIRTUAL,
      get() {
        const oeufs = this.getDataValue('oeufs_collectes') || 0;
        const poules = this.getDataValue('nombre_poules') || 1;
        if (poules === 0) return 0;
        return Math.round((oeufs / poules) * 100 * 100) / 100;
      },
      comment: 'Taux de ponte en pourcentage (calculé)'
    },
    taux_qualite: {
      type: DataTypes.VIRTUAL,
      get() {
        const vendables = this.getDataValue('oeufs_vendables') || 0;
        const total = this.getDataValue('oeufs_collectes') || 1;
        if (total === 0) return 0;
        return Math.round((vendables / total) * 100 * 100) / 100;
      },
      comment: 'Taux de qualité en pourcentage (calculé)'
    },
    poids_moyen_oeuf: {
      type: DataTypes.DECIMAL(5, 2),
      comment: 'Poids moyen d\'un œuf en grammes'
    },
    calibre_repartition: {
      type: DataTypes.JSONB,
      defaultValue: {
        petit: 0,    // < 53g
        moyen: 0,    // 53-63g
        gros: 0,     // 63-73g
        tres_gros: 0 // > 73g
      },
      comment: 'Répartition par calibre'
    },
    couleur_coquille: {
      type: DataTypes.ENUM('blanc', 'brun', 'mixte'),
      comment: 'Couleur dominante des coquilles'
    },
    qualite_coquille: {
      type: DataTypes.JSONB,
      defaultValue: {
        epaisseur_moyenne: null,
        resistance: null,
        texture: null
      },
      comment: 'Données sur la qualité de la coquille'
    },
    conditions_collecte: {
      type: DataTypes.JSONB,
      defaultValue: {
        temperature_poulailler: null,
        humidite: null,
        heure_collecte: null,
        frequence_collecte: null
      },
      comment: 'Conditions lors de la collecte'
    },
    alimentation_jour: {
      type: DataTypes.JSONB,
      defaultValue: {
        type_aliment: null,
        quantite_kg: null,
        supplements: []
      },
      comment: 'Alimentation du jour'
    },
    prix_vente_unitaire: {
      type: DataTypes.DECIMAL(6, 2),
      comment: 'Prix de vente par œuf en DA'
    },
    oeufs_vendus: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Nombre d\'œufs vendus ce jour'
    },
    chiffre_affaires_jour: {
      type: DataTypes.VIRTUAL,
      get() {
        const vendus = this.getDataValue('oeufs_vendus') || 0;
        const prix = this.getDataValue('prix_vente_unitaire') || 0;
        return vendus * prix;
      },
      comment: 'Chiffre d\'affaires du jour (calculé)'
    },
    stock_restant: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Stock d\'œufs restant en fin de journée'
    },
    pertes_diverses: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Pertes diverses (vol, prédateurs, etc.)'
    },
    observations: {
      type: DataTypes.TEXT,
      comment: 'Observations de l\'éleveur'
    },
    meteo: {
      type: DataTypes.JSONB,
      defaultValue: {
        temperature_min: null,
        temperature_max: null,
        humidite: null,
        vent: null,
        precipitation: null
      },
      comment: 'Conditions météorologiques du jour'
    },
    alerte_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si une alerte est active'
    },
    validee: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si les données ont été validées'
    },
    // date_modification est géré par timestamps: true (updatedAt)
  }, {
    sequelize,
    modelName: 'ProductionOeufs',
    tableName: 'production_oeufs',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['eleveur_id'] },
      { fields: ['date_production'] },
      { fields: ['volaille_id'] },
      { fields: ['validee'] },
      { unique: true, fields: ['eleveur_id', 'date_production', 'volaille_id'] }
    ],
    hooks: {
      beforeSave: (production, options) => {
        const total = production.oeufs_collectes || 0;
        const vendables = production.oeufs_vendables || 0;
        const casses = production.oeufs_casses || 0;
        const deformes = production.oeufs_deformes || 0;
        const sales = production.oeufs_sales || 0;
        const petits = production.oeufs_petits || 0;
        const totalDefauts = casses + deformes + sales + petits;

        if (vendables + totalDefauts > total) {
          production.oeufs_vendables = Math.max(0, total - totalDefauts);
        }

        const nombrePoules = production.nombre_poules || 1;
        const tauxPonte = nombrePoules > 0 ? (total / nombrePoules) * 100 : 0;
        if (tauxPonte < 60 && nombrePoules > 0) { // Condition pour éviter alerte si 0 poules
          production.alerte_active = true;
        } else {
            // Si l'alerte n'est pas déclenchée par autre chose, on la désactive
            // production.alerte_active = false; // Attention, cela peut écraser une alerte active pour une autre raison
        }

        const tauxQualite = total > 0 ? (production.oeufs_vendables / total) * 100 : 0;
        if (tauxQualite < 85 && total > 0) { // Condition pour éviter alerte si 0 oeufs
          production.alerte_active = true;
        } else if (!production.alerte_active) {
            // Si l'alerte n'est pas déclenchée par autre chose, on la désactive
            // production.alerte_active = false;
        }
        // La colonne date_modification est gérée par `updatedAt: 'updated_at'` ou `timestamps: true`
      }
    }
  });

  return ProductionOeufs;
};
