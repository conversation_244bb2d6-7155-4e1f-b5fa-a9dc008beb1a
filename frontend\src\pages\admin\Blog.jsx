import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  CircularProgress,
  Snackbar,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Pagination,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Article as ArticleIcon,
} from '@mui/icons-material';
import blogService from '../../services/blogService';
import { useAuth } from '../../contexts/AuthContext';

const Blog = () => {
  const { user } = useAuth();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [currentPost, setCurrentPost] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    author: '',
    tags: '',
    status: 'draft',
  });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [confirmDelete, setConfirmDelete] = useState(null);

  // Fetch blog posts
  useEffect(() => {
    fetchPosts();
  }, [page]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      setError('');
      const data = await blogService.getAdminPosts(page, 10);

      console.log('API response data:', data); // Debug: Log the response data

      // Vérifier que la réponse contient bien un tableau de posts
      if (data && Array.isArray(data.posts)) {
        // Debug each post's structure
        data.posts.forEach((post, index) => {
          console.log(`Post ${index} structure:`, {
            id: post.id,
            title: post.title,
            author: post.author,
            status: post.status,
            tags: post.tags
          });
        });

        setPosts(data.posts);
        setTotalPages(Math.ceil((data.total || 0) / 10));
      } else {
        console.warn('Réponse API inattendue:', data);
        setPosts([]);
        setTotalPages(1);
        setError('Format de réponse inattendu du serveur');
      }
    } catch (err) {
      console.error('Error fetching blog posts:', err);
      setPosts([]); // Initialiser avec un tableau vide en cas d'erreur
      setTotalPages(1);
      setError(err.message || 'Erreur lors de la récupération des articles de blog');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleOpenDialog = (post = null) => {
    if (post) {
      setCurrentPost(post);
      // Extract author name from author object if it exists
      const authorName = post.author
        ? post.author.username || `${post.author.first_name || ''} ${post.author.last_name || ''}`.trim()
        : user?.username || '';

      // Process tags that might be in different formats
      let tagString = '';
      try {
        if (typeof post.tags === 'string') {
          // If tags are stored as a JSON string, parse them and join with commas
          const parsedTags = JSON.parse(post.tags);
          tagString = Array.isArray(parsedTags) ? parsedTags.join(', ') : '';
        } else if (Array.isArray(post.tags)) {
          // If tags are already an array, join with commas
          tagString = post.tags.join(', ');
        }
      } catch (e) {
        console.warn('Error parsing tags:', e);
        tagString = '';
      }

      setFormData({
        title: post.title,
        content: post.content,
        excerpt: post.excerpt || '',
        author: authorName,
        tags: tagString,
        status: post.status || 'draft',
      });
    } else {
      setCurrentPost(null);
      setFormData({
        title: '',
        content: '',
        excerpt: '',
        author: user?.username || '',
        tags: '',
        status: 'draft',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const postData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      };

      if (currentPost) {
        await blogService.updatePost(currentPost.id, postData);
        setSuccess('Article mis à jour avec succès');
      } else {
        await blogService.createPost(postData);
        setSuccess('Article créé avec succès');
      }

      fetchPosts();
      handleCloseDialog();
    } catch (err) {
      console.error('Error saving blog post:', err);
      setError(err.message || 'Erreur lors de l\'enregistrement de l\'article');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfirm = (postId) => {
    setConfirmDelete(postId);
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      await blogService.deletePost(confirmDelete);
      setSuccess('Article supprimé avec succès');
      fetchPosts();
      setConfirmDelete(null);
    } catch (err) {
      console.error('Error deleting blog post:', err);
      setError(err.message || 'Erreur lors de la suppression de l\'article');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ArticleIcon color="primary" /> Gestion du Blog
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvel Article
          </Button>
        </Box>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        {loading && !posts.length ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Titre</TableCell>
                    <TableCell>Auteur</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Tags</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(posts) && posts.length > 0 ? (
                    posts.map((post) => (
                      <TableRow key={post.id}>
                        <TableCell>{post.title}</TableCell>
                        <TableCell>{post.author ? `${post.author.first_name || ''} ${post.author.last_name || ''}`.trim() || post.author.username : 'N/A'}</TableCell>
                        <TableCell>{new Date(post.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Chip
                            label={post.status === 'published' ? 'Publié' : 'Brouillon'}
                            color={post.status === 'published' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {(() => {
                            try {
                              // Handle tags that might be stored as JSON strings
                              const tagArray = typeof post.tags === 'string'
                                ? JSON.parse(post.tags)
                                : Array.isArray(post.tags) ? post.tags : [];

                              return tagArray.map((tag) => (
                                <Chip key={tag} label={tag} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                              ));
                            } catch (e) {
                              // If there's an error parsing the tags, display nothing
                              return null;
                            }
                          })()}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            color="primary"
                            onClick={() => handleOpenDialog(post)}
                            size="small"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            color="error"
                            onClick={() => handleDeleteConfirm(post.id)}
                            size="small"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            color="info"
                            size="small"
                            onClick={() => window.open(`/blog/${post.id}`, '_blank')}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        {loading ? (
                          <Box display="flex" alignItems="center" justifyContent="center" py={2}>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            Chargement des articles...
                          </Box>
                        ) : (
                          'Aucun article de blog trouvé'
                        )}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {totalPages > 1 && (
              <Box display="flex" justifyContent="center" mt={3}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Paper>

      {/* Dialog for creating/editing blog posts */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentPost ? 'Modifier l\'article' : 'Nouvel article'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="title"
            label="Titre"
            type="text"
            fullWidth
            value={formData.title}
            onChange={handleChange}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="excerpt"
            label="Extrait"
            type="text"
            fullWidth
            value={formData.excerpt}
            onChange={handleChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="content"
            label="Contenu"
            multiline
            rows={10}
            fullWidth
            value={formData.content}
            onChange={handleChange}
            required
            sx={{ mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                margin="dense"
                name="author"
                label="Auteur"
                type="text"
                fullWidth
                value={formData.author}
                onChange={handleChange}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                margin="dense"
                name="status"
                label="Statut"
                select
                fullWidth
                value={formData.status}
                onChange={handleChange}
                sx={{ mb: 2 }}
              >
                <option value="draft">Brouillon</option>
                <option value="published">Publié</option>
              </TextField>
            </Grid>
          </Grid>
          <TextField
            margin="dense"
            name="tags"
            label="Tags (séparés par des virgules)"
            type="text"
            fullWidth
            value={formData.tags}
            onChange={handleChange}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {loading ? <CircularProgress size={24} /> : 'Enregistrer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation dialog for delete */}
      <Dialog open={!!confirmDelete} onClose={() => setConfirmDelete(null)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDelete(null)}>Annuler</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            {loading ? <CircularProgress size={24} /> : 'Supprimer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={!!error || !!success}
        autoHideDuration={6000}
        onClose={() => {
          setError('');
          setSuccess('');
        }}
      >
        <Alert
          onClose={() => {
            setError('');
            setSuccess('');
          }}
          severity={error ? 'error' : 'success'}
          sx={{ width: '100%' }}
        >
          {error || success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Blog;
