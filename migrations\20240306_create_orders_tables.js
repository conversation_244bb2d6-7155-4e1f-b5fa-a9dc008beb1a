'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First create the orders table
    await queryInterface.createTable('orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      client_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      marchand_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      numero_commande: {
        type: Sequelize.STRING(50),
        unique: true,
        allowNull: false
      },
      montant_total: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      frais_livraison: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0
      },
      adresse_livraison: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      statut: {
        type: Sequelize.ENUM('en_attente', 'confirmée', 'en_préparation', 'expédiée', 'livrée', 'annulée'),
        defaultValue: 'en_attente'
      },
      mode_paiement: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      statut_paiement: {
        type: Sequelize.ENUM('en_attente', 'payé', 'échoué', 'remboursé'),
        defaultValue: 'en_attente'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      date_commande: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Then create the order_items table
    await queryInterface.createTable('order_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      order_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'orders',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      produit_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Produits',
          key: 'id'
        }
      },
      quantite: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      prix_unitaire: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      montant_total: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      remise: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('orders', ['client_id']);
    await queryInterface.addIndex('orders', ['marchand_id']);
    await queryInterface.addIndex('orders', ['statut']);
    await queryInterface.addIndex('orders', ['date_commande']);
    await queryInterface.addIndex('order_items', ['order_id']);
    await queryInterface.addIndex('order_items', ['produit_id']);
  },

  async down(queryInterface, Sequelize) {
    // Remove tables in reverse order
    await queryInterface.dropTable('order_items');
    await queryInterface.dropTable('orders');
  }
};
