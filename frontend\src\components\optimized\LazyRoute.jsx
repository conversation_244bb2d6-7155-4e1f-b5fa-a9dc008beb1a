import React, { Suspense, memo } from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  LinearProgress,
  Alert
} from '@mui/material';
import { ErrorBoundary } from 'react-error-boundary';

/**
 * Composant de chargement pour les routes lazy
 */
const RouteLoader = memo(({ 
  message = "Chargement de la page...",
  variant = 'circular' // 'circular' | 'linear' | 'skeleton'
}) => {
  if (variant === 'linear') {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Box display="flex" justifyContent="center" mt={2}>
          <Typography variant="body2" color="text.secondary">
            {message}
          </Typography>
        </Box>
      </Box>
    );
  }

  if (variant === 'skeleton') {
    return (
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              bgcolor: 'grey.300',
              mr: 2,
              animation: 'pulse 1.5s ease-in-out infinite'
            }}
          />
          <Box sx={{ flex: 1 }}>
            <Box
              sx={{
                height: 20,
                bgcolor: 'grey.300',
                borderRadius: 1,
                mb: 1,
                animation: 'pulse 1.5s ease-in-out infinite'
              }}
            />
            <Box
              sx={{
                height: 16,
                bgcolor: 'grey.200',
                borderRadius: 1,
                width: '60%',
                animation: 'pulse 1.5s ease-in-out infinite'
              }}
            />
          </Box>
        </Box>
        
        {[...Array(3)].map((_, index) => (
          <Box
            key={index}
            sx={{
              height: 60,
              bgcolor: 'grey.100',
              borderRadius: 1,
              mb: 2,
              animation: 'pulse 1.5s ease-in-out infinite',
              animationDelay: `${index * 0.2}s`
            }}
          />
        ))}
      </Box>
    );
  }

  // Variant 'circular' (default)
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="200px"
      gap={2}
    >
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary">
        {message}
      </Typography>
    </Box>
  );
});

/**
 * Composant d'erreur pour les routes lazy
 */
const RouteError = memo(({ error, resetErrorBoundary }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Alert 
        severity="error" 
        action={
          <button onClick={resetErrorBoundary}>
            Réessayer
          </button>
        }
      >
        <Typography variant="h6" gutterBottom>
          Erreur de chargement
        </Typography>
        <Typography variant="body2">
          Une erreur s'est produite lors du chargement de cette page.
        </Typography>
        {process.env.NODE_ENV === 'development' && (
          <Typography variant="caption" component="pre" sx={{ mt: 1, fontSize: '0.7rem' }}>
            {error.message}
          </Typography>
        )}
      </Alert>
    </Box>
  );
});

/**
 * HOC pour créer des routes lazy avec gestion d'erreurs
 */
export const createLazyRoute = (
  importFunction,
  loaderOptions = {}
) => {
  const {
    fallback = <RouteLoader />,
    errorFallback = RouteError,
    retryDelay = 1000,
    maxRetries = 3
  } = loaderOptions;

  // Créer le composant lazy avec retry automatique
  const LazyComponent = React.lazy(() => {
    let retryCount = 0;
    
    const loadWithRetry = async () => {
      try {
        return await importFunction();
      } catch (error) {
        if (retryCount < maxRetries) {
          retryCount++;
          console.warn(`Tentative de rechargement ${retryCount}/${maxRetries} pour le composant lazy`);
          
          // Attendre avant de réessayer
          await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount));
          return loadWithRetry();
        }
        throw error;
      }
    };
    
    return loadWithRetry();
  });

  // Composant wrapper avec ErrorBoundary et Suspense
  const LazyRouteWrapper = memo((props) => {
    return (
      <ErrorBoundary
        FallbackComponent={errorFallback}
        onReset={() => window.location.reload()}
      >
        <Suspense fallback={fallback}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  });

  LazyRouteWrapper.displayName = `LazyRoute(${LazyComponent.displayName || 'Component'})`;
  
  return LazyRouteWrapper;
};

/**
 * Composant LazyRoute générique
 */
const LazyRoute = memo(({
  component: Component,
  loading = <RouteLoader />,
  error = RouteError,
  ...props
}) => {
  return (
    <ErrorBoundary
      FallbackComponent={error}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={loading}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
});

/**
 * Hook pour précharger des composants lazy
 */
export const usePreloadRoute = () => {
  const preloadRoute = React.useCallback((importFunction) => {
    // Précharger le composant sans l'afficher
    const componentImport = importFunction();
    
    // Optionnel: logger le préchargement
    if (process.env.NODE_ENV === 'development') {
      componentImport.then(() => {
        console.log('🚀 Composant préchargé avec succès');
      }).catch((error) => {
        console.warn('⚠️ Échec du préchargement:', error);
      });
    }
    
    return componentImport;
  }, []);

  return { preloadRoute };
};

/**
 * Composant pour précharger des routes au survol
 */
export const PreloadOnHover = memo(({ 
  children, 
  importFunction, 
  delay = 100 
}) => {
  const { preloadRoute } = usePreloadRoute();
  const [isPreloaded, setIsPreloaded] = React.useState(false);
  const timeoutRef = React.useRef(null);

  const handleMouseEnter = React.useCallback(() => {
    if (isPreloaded) return;
    
    timeoutRef.current = setTimeout(() => {
      preloadRoute(importFunction);
      setIsPreloaded(true);
    }, delay);
  }, [importFunction, delay, preloadRoute, isPreloaded]);

  const handleMouseLeave = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
});

RouteLoader.displayName = 'RouteLoader';
RouteError.displayName = 'RouteError';
LazyRoute.displayName = 'LazyRoute';
PreloadOnHover.displayName = 'PreloadOnHover';

export default LazyRoute;
