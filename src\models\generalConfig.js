const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class GeneralConfig extends Model {
    // No associations defined for this model
    // static associate(models) {
    //   // Define associations here if needed
    // }
  }

  GeneralConfig.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    siteName: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Poultray DZ',
    },
    siteDescription: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    contactEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    contactPhone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    logo: {
      type: DataTypes.STRING, // URL to the logo image
      allowNull: true,
    },
    favicon: {
      type: DataTypes.STRING, // URL to the favicon
      allowNull: true,
    },
    primaryColor: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#2c5530', // Default green color
    },
    secondaryColor: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#e7eae2', // Light green/gray
    },
    defaultLanguage: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'fr', // French as default
    },
    availableLanguages: {
      type: DataTypes.TEXT, // JSON string of available languages
      allowNull: false,
      defaultValue: JSON.stringify(['fr', 'ar', 'en']),
      get() {
        const rawValue = this.getDataValue('availableLanguages');
        return JSON.parse(rawValue);
      },
      set(value) {
        this.setDataValue('availableLanguages',
          Array.isArray(value) ? JSON.stringify(value) : JSON.stringify(['fr', 'ar', 'en']));
      }
    },
    dateFormat: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'DD/MM/YYYY',
    },
    timeFormat: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'HH:mm',
    },
    timezone: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Africa/Algiers',
    },
    maintenanceMode: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    maintenanceMessage: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    googleAnalyticsId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    facebookPixelId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    maxUploadSize: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 5, // In MB
    },
    allowUserRegistration: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    defaultUserRole: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'user',
    },
    footerText: {
      type: DataTypes.TEXT,
      allowNull: true,
      defaultValue: '© Poultray DZ',
    },
    socialLinks: {
      type: DataTypes.TEXT, // JSON string of social media links
      allowNull: true,
      get() {
        const rawValue = this.getDataValue('socialLinks');
        return rawValue ? JSON.parse(rawValue) : {};
      },
      set(value) {
        this.setDataValue('socialLinks',
          typeof value === 'object' ? JSON.stringify(value) : null);
      }
    },
  }, {
    sequelize,
    modelName: 'GeneralConfig',
    tableName: 'general_config',
    timestamps: true,
  });

  return GeneralConfig;
};
