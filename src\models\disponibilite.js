const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Disponibilite extends Model {
    static associate(models) {
      Disponibilite.belongsTo(models.Veterinaire, {
        foreignKey: 'veterinaire_id',
        as: 'veterinaire'
      });
    }
  }

  Disponibilite.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    veterinaire_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'veterinaires',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    heure_debut: {
      type: DataTypes.TIME,
      allowNull: false
    },
    heure_fin: {
      type: DataTypes.TIME,
      allowNull: false
    },
    statut: {
      type: DataTypes.ENUM('disponible', 'reserve', 'indisponible'),
      defaultValue: 'disponible'
    },
    consultation_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'consultations',
        key: 'id'
      }
    },
    recurrence: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: null,
      validate: {
        isValidRecurrence(value) {
          if (value) {
            const validTypes = ['quotidien', 'hebdomadaire', 'mensuel'];
            if (!validTypes.includes(value.type)) {
              throw new Error('Type de récurrence invalide');
            }
            if (!value.fin || !Date.parse(value.fin)) {
              throw new Error('Date de fin de récurrence invalide');
            }
          }
        }
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Disponibilite',
    tableName: 'disponibilites',
    timestamps: true,
    indexes: [
      {
        fields: ['veterinaire_id', 'date'],
        unique: false
      },
      {
        fields: ['statut'],
        unique: false
      }
    ]
  });

  return Disponibilite;
};