'use strict';

const express = require('express');
const router = express.Router();
const homepageSectionController = require('../controllers/homepageSectionController');
const { auth, checkRole } = require('../middleware/auth');

// Get all sections (public)
router.get('/homepage/sections', homepageSectionController.getAllSections);

// Protected admin routes
router.post('/homepage/sections', auth, checkRole(['admin']), homepageSectionController.createSection);
router.put('/homepage/sections/:id', auth, checkRole(['admin']), homepageSectionController.updateSection);
router.delete('/homepage/sections/:id', auth, checkRole(['admin']), homepageSectionController.deleteSection);
router.put('/homepage/sections/order/update', auth, checkRole(['admin']), homepageSectionController.updateSectionOrders);

module.exports = router;
