const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
const Prescription = sequelize.define('Prescription', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  veterinaire_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  eleveur_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  volaille_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'volailles',
      key: 'id'
    }
  },
  numero_prescription: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  date_prescription: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  diagnostic: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  medicaments: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  posologie: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  duree_traitement: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Durée en jours'
  },
  urgence: {
    type: DataTypes.ENUM('faible', 'normale', 'élevée', 'critique'),
    allowNull: false,
    defaultValue: 'normale'
  },
  statut: {
    type: DataTypes.ENUM('en_attente', 'en_cours', 'terminé', 'annulé'),
    allowNull: false,
    defaultValue: 'en_attente'
  },
  suivi_requis: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  date_suivi: {
    type: DataTypes.DATE,
    allowNull: true
  },
  cout_estime: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  date_fin_traitement: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'prescriptions',
  timestamps: true,
  hooks: {
    beforeCreate: async (prescription) => {
      if (!prescription.numero_prescription) {
        const count = await Prescription.count();
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        prescription.numero_prescription = `PRESC-${year}${month}-${String(count + 1).padStart(4, '0')}`;
      }

      if (prescription.duree_traitement && !prescription.date_fin_traitement) {
        const dateDebut = new Date(prescription.date_prescription);
        const dateFin = new Date(dateDebut);
        dateFin.setDate(dateFin.getDate() + prescription.duree_traitement);
        prescription.date_fin_traitement = dateFin;
      }
    },
    beforeUpdate: async (prescription) => {
      if (prescription.changed('duree_traitement') || prescription.changed('date_prescription')) {
        const dateDebut = new Date(prescription.date_prescription);
        const dateFin = new Date(dateDebut);
        dateFin.setDate(dateFin.getDate() + prescription.duree_traitement);
        prescription.date_fin_traitement = dateFin;
      }
    }
  },
  indexes: [
    {
      fields: ['veterinaire_id']
    },
    {
      fields: ['eleveur_id']
    },
    {
      fields: ['volaille_id']
    },
    {
      fields: ['numero_prescription'],
      unique: true
    },
    {
      fields: ['date_prescription']
    },
    {
      fields: ['statut']
    },
    {
      fields: ['urgence']
    }
  ]
});

// Associations - Temporairement désactivées
// Prescription.associate = (models) => {
//   // Association avec le vétérinaire
//   Prescription.belongsTo(models.User, {
//     foreignKey: 'veterinaire_id',
//     as: 'veterinaire'
//   });
//
//   // Association avec l'éleveur
//   Prescription.belongsTo(models.User, {
//     foreignKey: 'eleveur_id',
//     as: 'eleveur'
//   });
//
//   // Association avec la volaille (optionnelle)
//   Prescription.belongsTo(models.Volaille, {
//     foreignKey: 'volaille_id',
//     as: 'volaille'
//   });
// };

// Méthodes d'instance
Prescription.prototype.calculerCoutTotal = function() {
  if (!this.medicaments || this.medicaments.length === 0) {
    return 0;
  }

  return this.medicaments.reduce((total, medicament) => {
    const prix = parseFloat(medicament.prix_unitaire) || 0;
    const quantite = parseInt(medicament.quantite) || 0;
    return total + (prix * quantite);
  }, 0);
};

Prescription.prototype.estExpire = function() {
  if (!this.date_fin_traitement) {
    return false;
  }
  return new Date() > new Date(this.date_fin_traitement);
};

Prescription.prototype.joursRestants = function() {
  if (!this.date_fin_traitement) {
    return null;
  }

  const maintenant = new Date();
  const dateFin = new Date(this.date_fin_traitement);
  const diffTime = dateFin - maintenant;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

// Méthodes statiques
Prescription.getStatistiques = async function(veterinaire_id = null) {
  const whereClause = veterinaire_id ? { veterinaire_id } : {};

  const [total, enAttente, enCours, terminees, annulees, urgentes, expirees, suiviRequis] = await Promise.all([
    this.count({ where: whereClause }),
    this.count({ where: { ...whereClause, statut: 'en_attente' } }),
    this.count({ where: { ...whereClause, statut: 'en_cours' } }),
    this.count({ where: { ...whereClause, statut: 'terminé' } }),
    this.count({ where: { ...whereClause, statut: 'annulé' } }),
    this.count({ where: { ...whereClause, urgence: ['élevée', 'critique'] } }),
    this.count({
      where: {
        ...whereClause,
        date_fin_traitement: {
          [require('sequelize').Op.lt]: new Date()
        },
        statut: ['en_attente', 'en_cours']
      }
    }),
    this.count({ where: { ...whereClause, suivi_requis: true, statut: 'terminé' } })
  ]);

  return {
    total,
    en_attente: enAttente,
    en_cours: enCours,
    terminees,
    annulees,
    urgentes,
    prescriptions_expirees: expirees,
    prescriptions_suivi_requis: suiviRequis
  };
};

Prescription.getPrescriptionsExpirees = async function(veterinaire_id = null) {
  const whereClause = {
    date_fin_traitement: {
      [require('sequelize').Op.lt]: new Date()
    },
    statut: ['en_attente', 'en_cours']
  };

  if (veterinaire_id) {
    whereClause.veterinaire_id = veterinaire_id;
  }

  return await this.findAll({
    where: whereClause,
    include: [
      { model: require('./user'), as: 'eleveur', attributes: ['nom', 'prenom', 'email'] },
      { model: require('./volaille'), as: 'volaille', attributes: ['race', 'age'] }
    ],
    order: [['date_fin_traitement', 'ASC']]
  });
};

Prescription.getPrescriptionsUrgentes = async function(veterinaire_id = null) {
  const whereClause = {
    urgence: ['élevée', 'critique'],
    statut: ['en_attente', 'en_cours']
  };

  if (veterinaire_id) {
    whereClause.veterinaire_id = veterinaire_id;
  }

  return await this.findAll({
    where: whereClause,
    include: [
      { model: require('./user'), as: 'eleveur', attributes: ['nom', 'prenom', 'email'] },
      { model: require('./volaille'), as: 'volaille', attributes: ['race', 'age'] }
    ],
    order: [
      ['urgence', 'DESC'],
      ['date_prescription', 'ASC']
    ]
  });
};

  return Prescription;
};
