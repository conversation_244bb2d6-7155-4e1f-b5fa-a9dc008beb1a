name: CI/CD Pipeline - Poultray DZ

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18.x'
  FLUTTER_VERSION: '3.16.0'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: poultraydz

jobs:
  # Tests et validation du code
  test:
    name: Tests et Linting
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: ${{ matrix.component }}/package-lock.json
        
    - name: Install dependencies
      working-directory: ${{ matrix.component }}
      run: npm ci
      
    - name: Run linting
      working-directory: ${{ matrix.component }}
      run: npm run lint
      
    - name: Run type checking (Frontend)
      if: matrix.component == 'frontend'
      working-directory: ${{ matrix.component }}
      run: npm run type-check
      
    - name: Run unit tests
      working-directory: ${{ matrix.component }}
      run: npm run test:coverage
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ${{ matrix.component }}/coverage/lcov.info
        flags: ${{ matrix.component }}
        name: ${{ matrix.component }}-coverage

  # Tests d'intégration
  integration-tests:
    name: Tests d'intégration
    runs-on: ubuntu-latest
    needs: test
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: poultraydz_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
        
    - name: Install backend dependencies
      working-directory: backend
      run: npm ci
      
    - name: Run database migrations
      working-directory: backend
      run: npm run migrate:test
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: poultraydz_test
        DB_USER: postgres
        DB_PASSWORD: test_password
        
    - name: Run integration tests
      working-directory: backend
      run: npm run test:integration
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: poultraydz_test
        DB_USER: postgres
        DB_PASSWORD: test_password
        REDIS_HOST: localhost
        REDIS_PORT: 6379

  # Tests E2E
  e2e-tests:
    name: Tests End-to-End
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
        
    - name: Start services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30
        
    - name: Run E2E tests
      working-directory: frontend
      run: npm run test:e2e
      
    - name: Upload E2E artifacts
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: e2e-screenshots
        path: frontend/cypress/screenshots

  # Tests mobile Flutter
  mobile-tests:
    name: Tests Mobile Flutter
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        
    - name: Get dependencies
      working-directory: mobile
      run: flutter pub get
      
    - name: Analyze code
      working-directory: mobile
      run: flutter analyze
      
    - name: Run tests
      working-directory: mobile
      run: flutter test --coverage
      
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: mobile/coverage/lcov.info
        flags: mobile
        name: mobile-coverage

  # Analyse de sécurité
  security-scan:
    name: Analyse de sécurité
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: Run npm audit (Backend)
      working-directory: backend
      run: npm audit --audit-level moderate
      
    - name: Run npm audit (Frontend)
      working-directory: frontend
      run: npm audit --audit-level moderate

  # Build des images Docker
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, integration-tests, security-scan]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component }}
        file: ./${{ matrix.component }}/Dockerfile.production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Build mobile Flutter
  build-mobile:
    name: Build Mobile App
    runs-on: ubuntu-latest
    needs: mobile-tests
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        
    - name: Get dependencies
      working-directory: mobile
      run: flutter pub get
      
    - name: Build APK
      working-directory: mobile
      run: flutter build apk --release
      
    - name: Build App Bundle
      working-directory: mobile
      run: flutter build appbundle --release
      
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-release.apk
        path: mobile/build/app/outputs/flutter-apk/app-release.apk
        
    - name: Upload App Bundle
      uses: actions/upload-artifact@v3
      with:
        name: app-release.aab
        path: mobile/build/app/outputs/bundle/release/app-release.aab

  # Déploiement en staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /opt/poultraydz-staging
          git pull origin develop
          docker-compose -f docker-compose.staging.yml pull
          docker-compose -f docker-compose.staging.yml up -d
          docker system prune -f
          
    - name: Run health checks
      run: |
        sleep 60
        curl -f ${{ secrets.STAGING_URL }}/health || exit 1

  # Déploiement en production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, e2e-tests]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /opt/poultraydz-production
          git pull origin main
          docker-compose -f docker-compose.production.yml pull
          docker-compose -f docker-compose.production.yml up -d
          docker system prune -f
          
    - name: Run health checks
      run: |
        sleep 60
        curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1
        
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        message: |
          🚀 Déploiement en production réussi !
          Version: ${{ github.sha }}
          URL: ${{ secrets.PRODUCTION_URL }}

  # Notification en cas d'échec
  notify-failure:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: [test, integration-tests, e2e-tests, security-scan]
    if: failure()
    
    steps:
    - name: Notify failure
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#alerts'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        message: |
          ❌ Échec du pipeline CI/CD
          Branche: ${{ github.ref }}
          Commit: ${{ github.sha }}
          Auteur: ${{ github.actor }}
