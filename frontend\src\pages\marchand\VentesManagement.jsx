/**
 * Composant de gestion des ventes pour les marchands
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Tabs,
  Tab,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as CartIcon,
  Assessment as AssessmentIcon,
  Receipt as ReceiptIcon,
  Print as PrintIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  Tooltip as RechartsT<PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { marchandAPI } from '../../services/marchandService';

const VentesManagement = () => {
  const { user } = useAuth();
  const [ventes, setVentes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState('1month');
  
  const [stats, setStats] = useState({
    totalVentes: 0,
    chiffreAffaires: 0,
    ventesAujourdhui: 0,
    croissanceMensuelle: 0,
    produitsPlusVendus: [],
    ventesParMois: [],
    repartitionCategories: []
  });

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  useEffect(() => {
    loadVentes();
    loadStats();
  }, [selectedPeriod]);

  const loadVentes = async () => {
    try {
      setLoading(true);
      const response = await marchandAPI.getVentes();
      setVentes(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des ventes');
      console.error('Erreur ventes:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await marchandAPI.getVentesStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats ventes:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getStatutColor = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'payee': return 'success';
      case 'en_attente': return 'warning';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'payee': return 'Payée';
      case 'en_attente': return 'En attente';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  const StatCard = ({ title, value, icon, color = 'primary', subtitle, trend }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ 
              p: 1, 
              borderRadius: 1, 
              bgcolor: `${color}.light`, 
              color: `${color}.contrastText`,
              mr: 2 
            }}>
              {icon}
            </Box>
            <Box>
              <Typography color="textSecondary" gutterBottom>
                {title}
              </Typography>
              <Typography variant="h5">
                {value}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          {trend !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {trend > 0 ? (
                <TrendingUpIcon color="success" />
              ) : trend < 0 ? (
                <TrendingDownIcon color="error" />
              ) : null}
              <Typography 
                variant="body2" 
                color={trend > 0 ? 'success.main' : trend < 0 ? 'error.main' : 'text.secondary'}
                sx={{ ml: 0.5 }}
              >
                {trend > 0 ? '+' : ''}{trend}%
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des ventes...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Ventes
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Période</InputLabel>
              <Select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                size="small"
              >
                <MenuItem value="1week">1 Semaine</MenuItem>
                <MenuItem value="1month">1 Mois</MenuItem>
                <MenuItem value="3months">3 Mois</MenuItem>
                <MenuItem value="6months">6 Mois</MenuItem>
                <MenuItem value="1year">1 Année</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => {/* Exporter rapport */}}
            >
              Exporter
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques principales */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Ventes"
              value={stats.totalVentes}
              icon={<CartIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Chiffre d'Affaires"
              value={`${stats.chiffreAffaires.toLocaleString()} DA`}
              icon={<MoneyIcon />}
              color="success"
              trend={stats.croissanceMensuelle}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Ventes Aujourd'hui"
              value={stats.ventesAujourdhui}
              icon={<AssessmentIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Croissance"
              value={`${stats.croissanceMensuelle > 0 ? '+' : ''}${stats.croissanceMensuelle}%`}
              subtitle="Ce mois"
              icon={stats.croissanceMensuelle > 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
              color={stats.croissanceMensuelle > 0 ? 'success' : 'error'}
            />
          </Grid>
        </Grid>

        {/* Onglets pour différentes vues */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
            <Tab label="Vue d'ensemble" />
            <Tab label="Analyse des ventes" />
            <Tab label="Produits" />
            <Tab label="Historique" />
          </Tabs>
        </Paper>

        {/* Contenu des onglets */}
        {tabValue === 0 && (
          <Grid container spacing={3}>
            {/* Évolution des ventes */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Évolution des Ventes
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={stats.ventesParMois}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="mois" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="ventes" 
                        stroke="#8884d8" 
                        fill="#8884d8" 
                        fillOpacity={0.3}
                        name="Nombre de ventes"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="chiffre_affaires" 
                        stroke="#82ca9d" 
                        fill="#82ca9d" 
                        fillOpacity={0.3}
                        name="Chiffre d'affaires (DA)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Répartition par catégories */}
            <Grid item xs={12} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Ventes par Catégorie
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={stats.repartitionCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {stats.repartitionCategories.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {tabValue === 1 && (
          <Grid container spacing={3}>
            {/* Analyse détaillée des ventes */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Analyse Détaillée des Ventes
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={stats.ventesParMois}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="mois" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <RechartsTooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="ventes" fill="#8884d8" name="Nombre de ventes" />
                      <Line 
                        yAxisId="right" 
                        type="monotone" 
                        dataKey="chiffre_affaires" 
                        stroke="#ff7300" 
                        strokeWidth={3}
                        name="Chiffre d'affaires (DA)"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {tabValue === 2 && (
          <Grid container spacing={3}>
            {/* Produits les plus vendus */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Produits les Plus Vendus
                  </Typography>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Produit</TableCell>
                          <TableCell>Quantité Vendue</TableCell>
                          <TableCell>Chiffre d'Affaires</TableCell>
                          <TableCell>Marge</TableCell>
                          <TableCell>Tendance</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {stats.produitsPlusVendus.map((produit, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {produit.nom}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {produit.categorie}
                              </Typography>
                            </TableCell>
                            <TableCell>{produit.quantite_vendue}</TableCell>
                            <TableCell>{produit.chiffre_affaires} DA</TableCell>
                            <TableCell>{produit.marge}%</TableCell>
                            <TableCell>
                              {produit.tendance > 0 ? (
                                <Chip 
                                  icon={<TrendingUpIcon />} 
                                  label={`+${produit.tendance}%`} 
                                  color="success" 
                                  size="small" 
                                />
                              ) : produit.tendance < 0 ? (
                                <Chip 
                                  icon={<TrendingDownIcon />} 
                                  label={`${produit.tendance}%`} 
                                  color="error" 
                                  size="small" 
                                />
                              ) : (
                                <Chip label="Stable" color="default" size="small" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {tabValue === 3 && (
          <Grid container spacing={3}>
            {/* Historique des ventes */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Historique des Ventes
                  </Typography>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Date</TableCell>
                          <TableCell>N° Vente</TableCell>
                          <TableCell>Client</TableCell>
                          <TableCell>Produits</TableCell>
                          <TableCell>Montant</TableCell>
                          <TableCell>Statut</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {ventes.map((vente) => (
                          <TableRow key={vente.id}>
                            <TableCell>
                              {new Date(vente.date_vente).toLocaleDateString('fr-FR')}
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                #{vente.numero_vente || vente.id}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {vente.client_nom}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {vente.client_telephone}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {vente.produits?.length || 0} article(s)
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {vente.montant_total} DA
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={getStatutLabel(vente.statut)}
                                color={getStatutColor(vente.statut)}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Tooltip title="Voir détails">
                                <IconButton size="small">
                                  <ViewIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Imprimer facture">
                                <IconButton size="small">
                                  <PrintIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Modifier">
                                <IconButton size="small">
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </Container>
    </LocalizationProvider>
  );
};

export default VentesManagement;
