/**
 * Service d'intelligence artificielle pour prédictions avicoles
 */

const tf = require('@tensorflow/tfjs-node');
const { Pool } = require('pg');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');

class AIPredictionService {
  constructor() {
    this.pool = new Pool();
    this.models = new Map();
    this.modelPaths = {
      healthPrediction: path.join(__dirname, '../models/health_prediction_model'),
      productionForecast: path.join(__dirname, '../models/production_forecast_model'),
      mortalityPrediction: path.join(__dirname, '../models/mortality_prediction_model'),
      feedOptimization: path.join(__dirname, '../models/feed_optimization_model')
    };
    this.featureScalers = new Map();
    this.predictionCache = new Map();
  }

  /**
   * Initialiser le service IA
   */
  async initialize() {
    try {
      // Charger les modèles pré-entraînés
      await this.loadModels();
      
      // Charger les scalers de normalisation
      await this.loadFeatureScalers();
      
      // Démarrer l'entraînement périodique
      this.startPeriodicTraining();
      
      logger.info('🤖 Service IA initialisé avec succès');
    } catch (error) {
      logger.error('Erreur lors de l\'initialisation du service IA:', error);
      
      // Créer des modèles par défaut si les modèles pré-entraînés n'existent pas
      await this.createDefaultModels();
    }
  }

  /**
   * Charger les modèles TensorFlow
   */
  async loadModels() {
    for (const [modelName, modelPath] of Object.entries(this.modelPaths)) {
      try {
        const model = await tf.loadLayersModel(`file://${modelPath}/model.json`);
        this.models.set(modelName, model);
        logger.info(`Modèle ${modelName} chargé avec succès`);
      } catch (error) {
        logger.warn(`Impossible de charger le modèle ${modelName}:`, error.message);
      }
    }
  }

  /**
   * Créer des modèles par défaut
   */
  async createDefaultModels() {
    // Modèle de prédiction de santé
    const healthModel = this.createHealthPredictionModel();
    this.models.set('healthPrediction', healthModel);

    // Modèle de prévision de production
    const productionModel = this.createProductionForecastModel();
    this.models.set('productionForecast', productionModel);

    // Modèle de prédiction de mortalité
    const mortalityModel = this.createMortalityPredictionModel();
    this.models.set('mortalityPrediction', mortalityModel);

    // Modèle d'optimisation d'alimentation
    const feedModel = this.createFeedOptimizationModel();
    this.models.set('feedOptimization', feedModel);

    logger.info('Modèles par défaut créés');
  }

  /**
   * Créer le modèle de prédiction de santé
   */
  createHealthPredictionModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [15], // 15 caractéristiques d'entrée
          units: 64,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 32,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 4, // 4 classes: excellent, bon, moyen, mauvais
          activation: 'softmax'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  /**
   * Créer le modèle de prévision de production
   */
  createProductionForecastModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.lstm({
          inputShape: [30, 10], // 30 jours, 10 caractéristiques
          units: 50,
          returnSequences: true
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.lstm({
          units: 50,
          returnSequences: false
        }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({
          units: 25,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 1, // Prédiction de production pour le jour suivant
          activation: 'linear'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * Créer le modèle de prédiction de mortalité
   */
  createMortalityPredictionModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [12], // 12 facteurs de risque
          units: 32,
          activation: 'relu'
        }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 1,
          activation: 'sigmoid' // Probabilité de mortalité
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  /**
   * Créer le modèle d'optimisation d'alimentation
   */
  createFeedOptimizationModel() {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({
          inputShape: [8], // Caractéristiques du troupeau et objectifs
          units: 24,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 16,
          activation: 'relu'
        }),
        tf.layers.dense({
          units: 5, // Recommandations d'alimentation (quantités, types)
          activation: 'linear'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * Prédire l'état de santé d'un troupeau
   */
  async predictHealth(volailleId, features = null) {
    try {
      const cacheKey = `health_${volailleId}`;
      
      // Vérifier le cache
      if (this.predictionCache.has(cacheKey)) {
        const cached = this.predictionCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 3600000) { // 1 heure
          return cached.prediction;
        }
      }

      // Extraire les caractéristiques si non fournies
      if (!features) {
        features = await this.extractHealthFeatures(volailleId);
      }

      const model = this.models.get('healthPrediction');
      if (!model) {
        throw new Error('Modèle de prédiction de santé non disponible');
      }

      // Normaliser les caractéristiques
      const normalizedFeatures = this.normalizeFeatures(features, 'health');
      
      // Faire la prédiction
      const inputTensor = tf.tensor2d([normalizedFeatures]);
      const prediction = model.predict(inputTensor);
      const probabilities = await prediction.data();

      // Nettoyer les tenseurs
      inputTensor.dispose();
      prediction.dispose();

      // Interpréter les résultats
      const healthClasses = ['excellent', 'bon', 'moyen', 'mauvais'];
      const maxIndex = probabilities.indexOf(Math.max(...probabilities));
      
      const result = {
        status: healthClasses[maxIndex],
        confidence: probabilities[maxIndex],
        probabilities: {
          excellent: probabilities[0],
          bon: probabilities[1],
          moyen: probabilities[2],
          mauvais: probabilities[3]
        },
        recommendations: this.generateHealthRecommendations(healthClasses[maxIndex], probabilities),
        riskFactors: this.identifyRiskFactors(features),
        timestamp: new Date().toISOString()
      };

      // Mettre en cache
      this.predictionCache.set(cacheKey, {
        prediction: result,
        timestamp: Date.now()
      });

      // Enregistrer la prédiction
      await this.savePrediction('health', volailleId, result);

      return result;

    } catch (error) {
      logger.error('Erreur lors de la prédiction de santé:', error);
      throw error;
    }
  }

  /**
   * Prédire la production future
   */
  async forecastProduction(volailleId, days = 7) {
    try {
      const cacheKey = `production_${volailleId}_${days}`;
      
      // Vérifier le cache
      if (this.predictionCache.has(cacheKey)) {
        const cached = this.predictionCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 7200000) { // 2 heures
          return cached.prediction;
        }
      }

      // Obtenir les données historiques
      const historicalData = await this.getHistoricalProductionData(volailleId, 30);
      
      if (historicalData.length < 30) {
        throw new Error('Données historiques insuffisantes pour la prédiction');
      }

      const model = this.models.get('productionForecast');
      if (!model) {
        throw new Error('Modèle de prévision de production non disponible');
      }

      // Préparer les données d'entrée
      const inputSequence = this.prepareProductionSequence(historicalData);
      const inputTensor = tf.tensor3d([inputSequence]);

      // Prédictions pour les jours suivants
      const forecasts = [];
      let currentInput = inputTensor;

      for (let day = 1; day <= days; day++) {
        const prediction = model.predict(currentInput);
        const forecastValue = await prediction.data();
        
        forecasts.push({
          day: day,
          date: this.addDays(new Date(), day).toISOString().split('T')[0],
          predictedProduction: Math.max(0, forecastValue[0]),
          confidence: this.calculateConfidence(day, historicalData)
        });

        // Mettre à jour l'entrée pour la prédiction suivante
        // (implémentation simplifiée)
        prediction.dispose();
      }

      currentInput.dispose();

      const result = {
        volailleId: volailleId,
        forecasts: forecasts,
        totalPredicted: forecasts.reduce((sum, f) => sum + f.predictedProduction, 0),
        averageDaily: forecasts.reduce((sum, f) => sum + f.predictedProduction, 0) / days,
        trend: this.analyzeTrend(forecasts),
        factors: await this.getProductionFactors(volailleId),
        timestamp: new Date().toISOString()
      };

      // Mettre en cache
      this.predictionCache.set(cacheKey, {
        prediction: result,
        timestamp: Date.now()
      });

      // Enregistrer la prédiction
      await this.savePrediction('production_forecast', volailleId, result);

      return result;

    } catch (error) {
      logger.error('Erreur lors de la prévision de production:', error);
      throw error;
    }
  }

  /**
   * Prédire le risque de mortalité
   */
  async predictMortalityRisk(volailleId) {
    try {
      const features = await this.extractMortalityFeatures(volailleId);
      const model = this.models.get('mortalityPrediction');
      
      if (!model) {
        throw new Error('Modèle de prédiction de mortalité non disponible');
      }

      const normalizedFeatures = this.normalizeFeatures(features, 'mortality');
      const inputTensor = tf.tensor2d([normalizedFeatures]);
      
      const prediction = model.predict(inputTensor);
      const riskProbability = await prediction.data();

      inputTensor.dispose();
      prediction.dispose();

      const riskLevel = this.categorizeRisk(riskProbability[0]);
      
      const result = {
        riskProbability: riskProbability[0],
        riskLevel: riskLevel,
        riskFactors: this.identifyMortalityRiskFactors(features),
        preventiveMeasures: this.generatePreventiveMeasures(riskLevel, features),
        urgency: riskProbability[0] > 0.7 ? 'high' : riskProbability[0] > 0.4 ? 'medium' : 'low',
        timestamp: new Date().toISOString()
      };

      await this.savePrediction('mortality_risk', volailleId, result);
      return result;

    } catch (error) {
      logger.error('Erreur lors de la prédiction de mortalité:', error);
      throw error;
    }
  }

  /**
   * Optimiser l'alimentation
   */
  async optimizeFeed(volailleId, objectives = {}) {
    try {
      const features = await this.extractFeedOptimizationFeatures(volailleId, objectives);
      const model = this.models.get('feedOptimization');
      
      if (!model) {
        throw new Error('Modèle d\'optimisation d\'alimentation non disponible');
      }

      const normalizedFeatures = this.normalizeFeatures(features, 'feed');
      const inputTensor = tf.tensor2d([normalizedFeatures]);
      
      const prediction = model.predict(inputTensor);
      const recommendations = await prediction.data();

      inputTensor.dispose();
      prediction.dispose();

      const result = {
        feedRecommendations: {
          grains: Math.max(0, recommendations[0]),
          proteins: Math.max(0, recommendations[1]),
          vitamins: Math.max(0, recommendations[2]),
          minerals: Math.max(0, recommendations[3]),
          supplements: Math.max(0, recommendations[4])
        },
        totalDaily: recommendations.reduce((sum, amount) => sum + Math.max(0, amount), 0),
        costEstimate: this.calculateFeedCost(recommendations),
        nutritionalBalance: this.analyzeNutritionalBalance(recommendations),
        expectedResults: this.predictFeedResults(recommendations, features),
        timestamp: new Date().toISOString()
      };

      await this.savePrediction('feed_optimization', volailleId, result);
      return result;

    } catch (error) {
      logger.error('Erreur lors de l\'optimisation d\'alimentation:', error);
      throw error;
    }
  }

  /**
   * Extraire les caractéristiques pour la prédiction de santé
   */
  async extractHealthFeatures(volailleId) {
    const result = await this.pool.query(`
      SELECT 
        v.nombre_actuel,
        v.nombre_total,
        (v.nombre_actuel::float / v.nombre_total) as survival_rate,
        EXTRACT(DAYS FROM NOW() - v.date_creation) as age_days,
        COALESCE(AVG(p.quantite_produite), 0) as avg_production,
        COALESCE(COUNT(c.id), 0) as consultation_count,
        COALESCE(AVG(CASE WHEN c.urgence THEN 1 ELSE 0 END), 0) as urgency_rate,
        -- Facteurs environnementaux (à adapter selon vos données)
        EXTRACT(MONTH FROM NOW()) as current_month,
        EXTRACT(DOW FROM NOW()) as day_of_week,
        -- Métriques de production récentes
        COALESCE(
          (SELECT AVG(quantite_produite) 
           FROM production 
           WHERE volaille_id = v.id 
           AND date_production >= NOW() - INTERVAL '7 days'), 0
        ) as recent_production,
        -- Tendance de production
        COALESCE(
          (SELECT 
            CASE 
              WHEN COUNT(*) >= 2 THEN
                (MAX(quantite_produite) - MIN(quantite_produite)) / COUNT(*)
              ELSE 0 
            END
           FROM production 
           WHERE volaille_id = v.id 
           AND date_production >= NOW() - INTERVAL '14 days'), 0
        ) as production_trend
      FROM volailles v
      LEFT JOIN production p ON v.id = p.volaille_id 
        AND p.date_production >= NOW() - INTERVAL '30 days'
      LEFT JOIN consultations c ON v.id = c.volaille_id 
        AND c.date_consultation >= NOW() - INTERVAL '30 days'
      WHERE v.id = $1
      GROUP BY v.id, v.nombre_actuel, v.nombre_total, v.date_creation
    `, [volailleId]);

    if (result.rows.length === 0) {
      throw new Error('Volaille non trouvée');
    }

    const data = result.rows[0];
    
    return [
      data.nombre_actuel || 0,
      data.nombre_total || 0,
      data.survival_rate || 0,
      data.age_days || 0,
      data.avg_production || 0,
      data.consultation_count || 0,
      data.urgency_rate || 0,
      data.current_month || 0,
      data.day_of_week || 0,
      data.recent_production || 0,
      data.production_trend || 0,
      // Ajouter d'autres caractéristiques selon les besoins
      Math.random() * 0.1, // Facteur météo simulé
      Math.random() * 0.1, // Facteur stress simulé
      Math.random() * 0.1, // Facteur alimentation simulé
      Math.random() * 0.1  // Facteur sanitaire simulé
    ];
  }

  /**
   * Normaliser les caractéristiques
   */
  normalizeFeatures(features, type) {
    // Implémentation simplifiée - en production, utiliser des scalers pré-calculés
    const scaler = this.featureScalers.get(type) || {
      mean: features.reduce((sum, val) => sum + val, 0) / features.length,
      std: 1
    };

    return features.map(feature => (feature - scaler.mean) / scaler.std);
  }

  /**
   * Générer des recommandations de santé
   */
  generateHealthRecommendations(status, probabilities) {
    const recommendations = [];

    if (status === 'mauvais' || probabilities[3] > 0.3) {
      recommendations.push({
        priority: 'urgent',
        action: 'Consultation vétérinaire immédiate',
        description: 'État de santé critique détecté'
      });
    }

    if (status === 'moyen' || probabilities[2] > 0.4) {
      recommendations.push({
        priority: 'high',
        action: 'Surveillance renforcée',
        description: 'Augmenter la fréquence des contrôles'
      });
    }

    if (probabilities[0] < 0.5) { // Pas excellent
      recommendations.push({
        priority: 'medium',
        action: 'Optimiser l\'alimentation',
        description: 'Réviser le programme alimentaire'
      });
    }

    return recommendations;
  }

  /**
   * Identifier les facteurs de risque
   */
  identifyRiskFactors(features) {
    const factors = [];
    
    // Analyser chaque caractéristique
    if (features[2] < 0.9) { // Taux de survie faible
      factors.push({
        factor: 'Taux de survie faible',
        severity: 'high',
        value: features[2]
      });
    }

    if (features[4] < 0.5) { // Production faible
      factors.push({
        factor: 'Production en baisse',
        severity: 'medium',
        value: features[4]
      });
    }

    return factors;
  }

  /**
   * Méthodes utilitaires
   */

  addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  calculateConfidence(day, historicalData) {
    // La confiance diminue avec la distance temporelle
    const baseConfidence = 0.95;
    const decayRate = 0.05;
    return Math.max(0.5, baseConfidence - (day * decayRate));
  }

  analyzeTrend(forecasts) {
    if (forecasts.length < 2) return 'stable';
    
    const firstHalf = forecasts.slice(0, Math.floor(forecasts.length / 2));
    const secondHalf = forecasts.slice(Math.floor(forecasts.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, f) => sum + f.predictedProduction, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, f) => sum + f.predictedProduction, 0) / secondHalf.length;
    
    const change = (secondAvg - firstAvg) / firstAvg;
    
    if (change > 0.05) return 'increasing';
    if (change < -0.05) return 'decreasing';
    return 'stable';
  }

  categorizeRisk(probability) {
    if (probability > 0.7) return 'high';
    if (probability > 0.4) return 'medium';
    return 'low';
  }

  async savePrediction(type, volailleId, prediction) {
    try {
      await this.pool.query(`
        INSERT INTO ai_predictions (type, volaille_id, prediction_data, created_at)
        VALUES ($1, $2, $3, NOW())
      `, [type, volailleId, JSON.stringify(prediction)]);
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde de la prédiction:', error);
    }
  }

  /**
   * Entraîner les modèles avec de nouvelles données
   */
  async trainModels() {
    try {
      logger.info('Début de l\'entraînement des modèles IA...');
      
      // Entraîner chaque modèle avec les nouvelles données
      await this.trainHealthModel();
      await this.trainProductionModel();
      await this.trainMortalityModel();
      await this.trainFeedModel();
      
      // Sauvegarder les modèles mis à jour
      await this.saveModels();
      
      logger.info('Entraînement des modèles terminé');
    } catch (error) {
      logger.error('Erreur lors de l\'entraînement:', error);
    }
  }

  /**
   * Démarrer l'entraînement périodique
   */
  startPeriodicTraining() {
    // Entraîner les modèles chaque semaine
    setInterval(() => {
      this.trainModels();
    }, 7 * 24 * 60 * 60 * 1000); // 7 jours
  }

  // Méthodes d'entraînement spécifiques (à implémenter selon les besoins)
  async trainHealthModel() {
    // Implémenter l'entraînement du modèle de santé
  }

  async trainProductionModel() {
    // Implémenter l'entraînement du modèle de production
  }

  async trainMortalityModel() {
    // Implémenter l'entraînement du modèle de mortalité
  }

  async trainFeedModel() {
    // Implémenter l'entraînement du modèle d'alimentation
  }

  async saveModels() {
    // Sauvegarder les modèles entraînés
    for (const [modelName, model] of this.models.entries()) {
      const modelPath = this.modelPaths[modelName];
      await model.save(`file://${modelPath}`);
    }
  }

  // Méthodes d'extraction de caractéristiques (à implémenter)
  async extractMortalityFeatures(volailleId) {
    // Implémenter l'extraction des caractéristiques de mortalité
    return new Array(12).fill(0).map(() => Math.random());
  }

  async extractFeedOptimizationFeatures(volailleId, objectives) {
    // Implémenter l'extraction des caractéristiques d'alimentation
    return new Array(8).fill(0).map(() => Math.random());
  }

  async getHistoricalProductionData(volailleId, days) {
    // Implémenter la récupération des données historiques
    return [];
  }

  prepareProductionSequence(historicalData) {
    // Implémenter la préparation de la séquence pour LSTM
    return new Array(30).fill(0).map(() => new Array(10).fill(0));
  }

  async getProductionFactors(volailleId) {
    // Implémenter la récupération des facteurs de production
    return {};
  }

  identifyMortalityRiskFactors(features) {
    // Implémenter l'identification des facteurs de risque de mortalité
    return [];
  }

  generatePreventiveMeasures(riskLevel, features) {
    // Implémenter la génération de mesures préventives
    return [];
  }

  calculateFeedCost(recommendations) {
    // Implémenter le calcul du coût de l'alimentation
    return 0;
  }

  analyzeNutritionalBalance(recommendations) {
    // Implémenter l'analyse de l'équilibre nutritionnel
    return {};
  }

  predictFeedResults(recommendations, features) {
    // Implémenter la prédiction des résultats de l'alimentation
    return {};
  }

  async loadFeatureScalers() {
    // Implémenter le chargement des scalers de normalisation
  }
}

module.exports = new AIPredictionService();
