import React, { memo, useMemo } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Chip,
  Grid,
  Skeleton
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadialBarChart,
  RadialBar
} from 'recharts';
import { useOptimizedMemo } from '../../hooks/usePerformance';

/**
 * Couleurs pour les graphiques
 */
const CHART_COLORS = [
  '#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0',
  '#00BCD4', '#8BC34A', '#FFC107', '#E91E63', '#3F51B5'
];

/**
 * Composant de graphique en ligne pour les tendances
 */
const TrendChart = memo(({ 
  data = [], 
  title = "Tendance", 
  xKey = "date", 
  yKey = "value",
  color = CHART_COLORS[0],
  loading = false,
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false
}) => {
  const formattedData = useOptimizedMemo(() => {
    return data.map(item => ({
      ...item,
      [xKey]: new Date(item[xKey]).toLocaleDateString('fr-FR', { 
        month: 'short', 
        day: 'numeric' 
      })
    }));
  }, [data, xKey], 'TrendChart.formattedData');

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Skeleton variant="rectangular" height={height} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <ResponsiveContainer width="100%" height={height}>
          <LineChart data={formattedData}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey={xKey} />
            <YAxis />
            {showTooltip && <Tooltip />}
            {showLegend && <Legend />}
            <Line 
              type="monotone" 
              dataKey={yKey} 
              stroke={color} 
              strokeWidth={2}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
});

/**
 * Graphique en aires pour la production
 */
const ProductionAreaChart = memo(({ 
  data = [], 
  title = "Production", 
  height = 300,
  loading = false 
}) => {
  const formattedData = useOptimizedMemo(() => {
    return data.map(item => ({
      ...item,
      date: new Date(item.date).toLocaleDateString('fr-FR', { 
        month: 'short', 
        day: 'numeric' 
      })
    }));
  }, [data], 'ProductionAreaChart.formattedData');

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Skeleton variant="rectangular" height={height} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <ResponsiveContainer width="100%" height={height}>
          <AreaChart data={formattedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="oeufs" 
              stackId="1"
              stroke={CHART_COLORS[0]} 
              fill={CHART_COLORS[0]}
              fillOpacity={0.6}
            />
            <Area 
              type="monotone" 
              dataKey="viande" 
              stackId="1"
              stroke={CHART_COLORS[1]} 
              fill={CHART_COLORS[1]}
              fillOpacity={0.6}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
});

/**
 * Graphique en barres pour les comparaisons
 */
const ComparisonBarChart = memo(({ 
  data = [], 
  title = "Comparaison", 
  xKey = "name",
  yKey = "value",
  height = 300,
  loading = false,
  color = CHART_COLORS[0]
}) => {
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Skeleton variant="rectangular" height={height} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <ResponsiveContainer width="100%" height={height}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xKey} />
            <YAxis />
            <Tooltip />
            <Bar dataKey={yKey} fill={color} radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
});

/**
 * Graphique en secteurs pour les répartitions
 */
const DistributionPieChart = memo(({ 
  data = [], 
  title = "Répartition", 
  height = 300,
  loading = false,
  showLabels = true,
  showLegend = true
}) => {
  const dataWithColors = useOptimizedMemo(() => {
    return data.map((item, index) => ({
      ...item,
      color: CHART_COLORS[index % CHART_COLORS.length]
    }));
  }, [data], 'DistributionPieChart.dataWithColors');

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (!showLabels || percent < 0.05) return null; // Ne pas afficher les labels < 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Skeleton variant="circular" height={height} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <ResponsiveContainer width="100%" height={height}>
          <PieChart>
            <Pie
              data={dataWithColors}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {dataWithColors.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip />
            {showLegend && <Legend />}
          </PieChart>
        </ResponsiveContainer>
        
        {/* Légende personnalisée */}
        <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
          {dataWithColors.map((item, index) => (
            <Chip
              key={index}
              label={`${item.name}: ${item.value}`}
              size="small"
              sx={{ 
                backgroundColor: item.color,
                color: 'white',
                '& .MuiChip-label': { fontWeight: 'medium' }
              }}
            />
          ))}
        </Box>
      </CardContent>
    </Card>
  );
});

/**
 * Graphique radial pour les KPIs
 */
const KPIRadialChart = memo(({ 
  data = [], 
  title = "Indicateurs", 
  height = 300,
  loading = false 
}) => {
  const formattedData = useOptimizedMemo(() => {
    return data.map((item, index) => ({
      ...item,
      fill: CHART_COLORS[index % CHART_COLORS.length]
    }));
  }, [data], 'KPIRadialChart.formattedData');

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Skeleton variant="rectangular" height={height} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <ResponsiveContainer width="100%" height={height}>
          <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="80%" data={formattedData}>
            <RadialBar dataKey="value" cornerRadius={10} fill="#8884d8" />
            <Tooltip />
          </RadialBarChart>
        </ResponsiveContainer>
        
        <Grid container spacing={1} mt={1}>
          {formattedData.map((item, index) => (
            <Grid item xs={6} key={index}>
              <Box display="flex" alignItems="center">
                <Box
                  width={12}
                  height={12}
                  borderRadius="50%"
                  bgcolor={item.fill}
                  mr={1}
                />
                <Typography variant="body2">
                  {item.name}: {item.value}%
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
});

/**
 * Dashboard de métriques avec filtres
 */
const MetricsDashboard = memo(({ 
  data = [], 
  title = "Tableau de bord", 
  loading = false,
  onPeriodChange,
  period = '7d'
}) => {
  const periods = [
    { value: '7d', label: '7 jours' },
    { value: '30d', label: '30 jours' },
    { value: '90d', label: '3 mois' },
    { value: '1y', label: '1 an' }
  ];

  const metrics = useOptimizedMemo(() => {
    if (!data.length) return [];
    
    return [
      {
        name: 'Production',
        value: data.reduce((sum, item) => sum + (item.production || 0), 0),
        trend: 12.5,
        color: CHART_COLORS[0]
      },
      {
        name: 'Ventes',
        value: data.reduce((sum, item) => sum + (item.ventes || 0), 0),
        trend: -3.2,
        color: CHART_COLORS[1]
      },
      {
        name: 'Mortalité',
        value: data.reduce((sum, item) => sum + (item.mortalite || 0), 0),
        trend: -8.1,
        color: CHART_COLORS[3]
      }
    ];
  }, [data], 'MetricsDashboard.metrics');

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6">{title}</Typography>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Période</InputLabel>
            <Select
              value={period}
              label="Période"
              onChange={(e) => onPeriodChange?.(e.target.value)}
            >
              {periods.map((p) => (
                <MenuItem key={p.value} value={p.value}>
                  {p.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Grid container spacing={2}>
            {[1, 2, 3].map((i) => (
              <Grid item xs={4} key={i}>
                <Skeleton variant="rectangular" height={100} />
              </Grid>
            ))}
          </Grid>
        ) : (
          <Grid container spacing={2}>
            {metrics.map((metric, index) => (
              <Grid item xs={12} sm={4} key={index}>
                <Box
                  p={2}
                  border={1}
                  borderColor="divider"
                  borderRadius={2}
                  textAlign="center"
                >
                  <Typography variant="h4" color={metric.color} gutterBottom>
                    {metric.value.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {metric.name}
                  </Typography>
                  <Chip
                    label={`${metric.trend > 0 ? '+' : ''}${metric.trend}%`}
                    color={metric.trend > 0 ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        )}
      </CardContent>
    </Card>
  );
});

TrendChart.displayName = 'TrendChart';
ProductionAreaChart.displayName = 'ProductionAreaChart';
ComparisonBarChart.displayName = 'ComparisonBarChart';
DistributionPieChart.displayName = 'DistributionPieChart';
KPIRadialChart.displayName = 'KPIRadialChart';
MetricsDashboard.displayName = 'MetricsDashboard';

export {
  TrendChart,
  ProductionAreaChart,
  ComparisonBarChart,
  DistributionPieChart,
  KPIRadialChart,
  MetricsDashboard,
  CHART_COLORS
};
