import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  CircularProgress,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  ContentCopy as CopyIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Translate as TranslateIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const AiBlogGenerator = () => {
  const [formData, setFormData] = useState({
    topic: '',
    keywords: '',
    tone: 'professional',
    length: 'medium',
    language: 'french',
    includeImages: true,
  });
  const [generatedContent, setGeneratedContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [history, setHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axiosInstance.post('/ai/blog', formData);
      setGeneratedContent(response.data.content);
      setSuccess('Contenu généré avec succès!');

      // Add to history
      const historyItem = {
        id: Date.now(),
        topic: formData.topic,
        content: response.data.content,
        timestamp: new Date().toISOString(),
      };
      setHistory([historyItem, ...history.slice(0, 9)]); // Keep only last 10 items
    } catch (err) {
      console.error('Error generating blog content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération du contenu');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setSuccess('Contenu copié dans le presse-papiers!');
  };

  const handleSaveAsDraft = async () => {
    try {
      setLoading(true);
      const blogData = {
        title: formData.topic,
        content: generatedContent,
        excerpt: generatedContent.substring(0, 150) + '...',
        tags: formData.keywords.split(',').map(tag => tag.trim()).filter(tag => tag),
        status: 'draft',
      };

      const response = await axiosInstance.post('/admin/blog', blogData);
      setSuccess('Article enregistré comme brouillon!');
    } catch (err) {
      console.error('Error saving blog post:', err);
      setError(err.response?.data?.message || 'Erreur lors de l\'enregistrement de l\'article');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadFromHistory = (historyItem) => {
    setGeneratedContent(historyItem.content);
    setFormData(prev => ({
      ...prev,
      topic: historyItem.topic,
    }));
    setShowHistory(false);
  };

  const handleTranslate = async (targetLanguage) => {
    if (!generatedContent) return;

    try {
      setLoading(true);
      const response = await axiosInstance.post('/ai/translate', {
        content: generatedContent,
        targetLanguage,
      });
      setGeneratedContent(response.data.translatedContent);
      setSuccess(`Contenu traduit en ${targetLanguage === 'french' ? 'français' : 'arabe'}!`);
    } catch (err) {
      console.error('Error translating content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la traduction du contenu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AIIcon color="primary" /> Générateur de Blog IA
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<HistoryIcon />}
            onClick={() => setShowHistory(!showHistory)}
          >
            Historique
          </Button>
        </Box>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

        <Grid container spacing={3}>
          <Grid item xs={12} md={showHistory ? 4 : 6}>
            <form onSubmit={handleSubmit}>
              <TextField
                label="Sujet de l'article"
                name="topic"
                value={formData.topic}
                onChange={handleChange}
                fullWidth
                required
                margin="normal"
              />
              <TextField
                label="Mots-clés (séparés par des virgules)"
                name="keywords"
                value={formData.keywords}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Ton</InputLabel>
                    <Select
                      name="tone"
                      value={formData.tone}
                      label="Ton"
                      onChange={handleChange}
                    >
                      <MenuItem value="professional">Professionnel</MenuItem>
                      <MenuItem value="casual">Décontracté</MenuItem>
                      <MenuItem value="enthusiastic">Enthousiaste</MenuItem>
                      <MenuItem value="informative">Informatif</MenuItem>
                      <MenuItem value="authoritative">Autoritaire</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Longueur</InputLabel>
                    <Select
                      name="length"
                      value={formData.length}
                      label="Longueur"
                      onChange={handleChange}
                    >
                      <MenuItem value="short">Court (300-500 mots)</MenuItem>
                      <MenuItem value="medium">Moyen (500-800 mots)</MenuItem>
                      <MenuItem value="long">Long (800-1200 mots)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Langue</InputLabel>
                    <Select
                      name="language"
                      value={formData.language}
                      label="Langue"
                      onChange={handleChange}
                    >
                      <MenuItem value="french">Français</MenuItem>
                      <MenuItem value="arabic">Arabe</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center' }}>
                  <FormControl component="fieldset">
                    <label>
                      <input
                        type="checkbox"
                        name="includeImages"
                        checked={formData.includeImages}
                        onChange={handleCheckboxChange}
                      />
                      Inclure des suggestions d'images
                    </label>
                  </FormControl>
                </Grid>
              </Grid>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                sx={{ mt: 3 }}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <AIIcon />}
              >
                {loading ? 'Génération en cours...' : 'Générer le contenu'}
              </Button>
            </form>
          </Grid>

          {showHistory && (
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Historique des générations
              </Typography>
              <Box sx={{ maxHeight: '500px', overflow: 'auto' }}>
                {history.length > 0 ? (
                  history.map((item) => (
                    <Card key={item.id} sx={{ mb: 2, cursor: 'pointer' }} onClick={() => handleLoadFromHistory(item)}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          {item.topic}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(item.timestamp).toLocaleString()}
                        </Typography>
                        <Typography variant="body2" noWrap sx={{ mt: 1 }}>
                          {item.content.substring(0, 100)}...
                        </Typography>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Aucun historique disponible
                  </Typography>
                )}
              </Box>
            </Grid>
          )}

          <Grid item xs={12} md={showHistory ? 4 : 6}>
            <Box sx={{ position: 'relative' }}>
              <Typography variant="h6" gutterBottom>
                Contenu généré
              </Typography>
              {generatedContent && (
                <Box sx={{ position: 'absolute', top: 0, right: 0 }}>
                  <Tooltip title="Copier le contenu">
                    <IconButton onClick={handleCopyToClipboard}>
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Enregistrer comme brouillon">
                    <IconButton onClick={handleSaveAsDraft}>
                      <SaveIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Éditer dans le gestionnaire de blog">
                    <IconButton onClick={() => window.location.href = '/admin/blog'}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
              <TextField
                multiline
                rows={20}
                fullWidth
                variant="outlined"
                value={generatedContent}
                onChange={(e) => setGeneratedContent(e.target.value)}
                placeholder="Le contenu généré apparaîtra ici..."
              />
              {generatedContent && (
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                  <Box>
                    <Chip
                      icon={<TranslateIcon />}
                      label="Traduire en français"
                      onClick={() => handleTranslate('french')}
                      sx={{ mr: 1 }}
                      disabled={formData.language === 'french'}
                    />
                    <Chip
                      icon={<TranslateIcon />}
                      label="Traduire en arabe"
                      onClick={() => handleTranslate('arabic')}
                      disabled={formData.language === 'arabic'}
                    />
                  </Box>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveAsDraft}
                  >
                    Enregistrer comme brouillon
                  </Button>
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AiBlogGenerator;
