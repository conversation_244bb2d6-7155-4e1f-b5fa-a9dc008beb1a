const { Volaille, Consultation, User } = require('../models');
const { Op } = require('sequelize');

// Analyser les tendances de santé des volailles
const analyserTendancesSante = async (req, res) => {
  try {
    const { periode } = req.query; // 'semaine', 'mois', 'annee'
    const dateDebut = new Date();

    switch(periode) {
      case 'semaine':
        dateDebut.setDate(dateDebut.getDate() - 7);
        break;
      case 'mois':
        dateDebut.setMonth(dateDebut.getMonth() - 1);
        break;
      case 'annee':
        dateDebut.setFullYear(dateDebut.getFullYear() - 1);
        break;
      default:
        dateDebut.setMonth(dateDebut.getMonth() - 1); // Par défaut 1 mois
    }

    const consultations = await Consultation.findAll({
      where: {
        date: {
          [Op.gte]: dateDebut
        },
        veterinaire_id: req.user.id
      },
      include: [{
        model: <PERSON><PERSON><PERSON>,
        attributes: ['type', 'race', 'age']
      }]
    });

    // Analyse des symptômes et diagnostics fréquents
    const analyseSymptomes = {};
    const analyseDiagnostics = {};

    consultations.forEach(consultation => {
      // Analyse des symptômes
      const symptomes = consultation.symptomes.split(',').map(s => s.trim());
      symptomes.forEach(symptome => {
        analyseSymptomes[symptome] = (analyseSymptomes[symptome] || 0) + 1;
      });

      // Analyse des diagnostics
      const diagnostic = consultation.diagnostic;
      analyseDiagnostics[diagnostic] = (analyseDiagnostics[diagnostic] || 0) + 1;
    });

    res.json({
      total_consultations: consultations.length,
      symptomes_frequents: Object.entries(analyseSymptomes)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5),
      diagnostics_frequents: Object.entries(analyseDiagnostics)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
    });

  } catch (error) {
    console.error('Erreur lors de l\'analyse des tendances:', error);
    res.status(500).json({ message: 'Erreur lors de l\'analyse' });
  }
};

// Prédire les risques de maladies
const predireRisques = async (req, res) => {
  try {
    const { type_volaille, age, saison } = req.body;

    // Récupérer l'historique des consultations pour ce type de volaille
    const historiqueConsultations = await Consultation.findAll({
      include: [{
        model: Volaille,
        where: {
          type: type_volaille
        }
      }]
    });

    // Analyse des facteurs de risque
    const risques = analyserFacteursRisque(historiqueConsultations, age, saison);

    res.json({
      type_volaille,
      risques
    });

  } catch (error) {
    console.error('Erreur lors de la prédiction des risques:', error);
    res.status(500).json({ message: 'Erreur lors de la prédiction' });
  }
};

// Fonction utilitaire pour analyser les facteurs de risque
const analyserFacteursRisque = (consultations, age, saison) => {
  const risques = [];

  // Analyse basée sur l'âge
  if (age < 4) {
    risques.push({
      facteur: 'Âge',
      niveau: 'Élevé',
      description: 'Volailles jeunes plus susceptibles aux infections'
    });
  }

  // Analyse basée sur la saison
  if (saison === 'hiver') {
    risques.push({
      facteur: 'Saison',
      niveau: 'Modéré',
      description: 'Risque accru de maladies respiratoires en hiver'
    });
  }

  // Analyse des tendances historiques
  const maladiesFrequentes = new Map();
  consultations.forEach(consultation => {
    if (consultation.diagnostic) {
      const count = maladiesFrequentes.get(consultation.diagnostic) || 0;
      maladiesFrequentes.set(consultation.diagnostic, count + 1);
    }
  });

  // Identifier les maladies les plus fréquentes
  const [maladieFrequente] = [...maladiesFrequentes.entries()]
    .sort((a, b) => b[1] - a[1])[0] || [];

  if (maladieFrequente) {
    risques.push({
      facteur: 'Historique',
      niveau: 'Modéré',
      description: `Tendance historique: ${maladieFrequente}`
    });
  }

  return risques;
};

module.exports = {
  analyserTendancesSante,
  predireRisques
};