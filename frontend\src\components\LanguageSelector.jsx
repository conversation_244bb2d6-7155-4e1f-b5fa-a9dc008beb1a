import React from 'react';
import { Box, IconButton, Menu, MenuItem, Typography } from '@mui/material';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageIcon from '@mui/icons-material/Language';

const LanguageSelector = ({ color = 'inherit', variant = 'icon' }) => {
  const { language, changeLanguage } = useLanguage();
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = (lang) => {
    changeLanguage(lang);
    handleClose();
  };

  // Afficher le nom de la langue en fonction du code
  const getLanguageName = (code) => {
    switch (code) {
      case 'fr':
        return 'Français';
      case 'ar':
        return 'العربية';
      default:
        return code;
    }
  };

  return (
    <Box>
      {variant === 'icon' ? (
        <IconButton
          color={color}
          onClick={handleClick}
          aria-controls="language-menu"
          aria-haspopup="true"
        >
          <LanguageIcon />
        </IconButton>
      ) : (
        <Box
          onClick={handleClick}
          sx={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            color: color,
          }}
        >
          <LanguageIcon sx={{ mr: 0.5 }} />
          <Typography variant="body2">{getLanguageName(language)}</Typography>
        </Box>
      )}

      <Menu
        id="language-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <MenuItem
          onClick={() => handleLanguageChange('fr')}
          selected={language === 'fr'}
        >
          Français
        </MenuItem>
        <MenuItem
          onClick={() => handleLanguageChange('ar')}
          selected={language === 'ar'}
        >
          العربية
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default LanguageSelector;
