const { Disponibilite, Veterinaire, Consultation } = require('../models');
const { Op } = require('sequelize');

// Récupérer les disponibilités d'un vétérinaire
const getDisponibilites = async (req, res) => {
  try {
    const { debut, fin } = req.query;
    const veterinaire_id = req.params.id || req.user.id;

    const disponibilites = await Disponibilite.findAll({
      where: {
        veterinaire_id,
        date: {
          [Op.between]: [debut, fin]
        }
      },
      include: [{
        model: Consultation,
        as: 'consultation',
        attributes: ['id', 'eleveur_id', 'motif'],
        required: false
      }],
      order: [['date', 'ASC'], ['heure_debut', 'ASC']]
    });

    res.json(disponibilites);
  } catch (error) {
    console.error('Erreur lors de la récupération des disponibilités:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Ajouter une nouvelle disponibilité
const ajouterDisponibilite = async (req, res) => {
  try {
    const {
      date,
      heure_debut,
      heure_fin,
      recurrence,
      notes
    } = req.body;

    // Vérifier les chevauchements
    const chevauchement = await Disponibilite.findOne({
      where: {
        veterinaire_id: req.user.id,
        date,
        [Op.or]: [
          {
            heure_debut: {
              [Op.between]: [heure_debut, heure_fin]
            }
          },
          {
            heure_fin: {
              [Op.between]: [heure_debut, heure_fin]
            }
          }
        ]
      }
    });

    if (chevauchement) {
      return res.status(400).json({
        message: 'Cette plage horaire chevauche une disponibilité existante'
      });
    }

    // Créer la disponibilité
    const disponibilite = await Disponibilite.create({
      veterinaire_id: req.user.id,
      date,
      heure_debut,
      heure_fin,
      recurrence,
      notes
    });

    // Si récurrence, créer les disponibilités récurrentes
    if (recurrence) {
      await creerDisponibilitesRecurrentes(disponibilite);
    }

    res.status(201).json(disponibilite);
  } catch (error) {
    console.error('Erreur lors de l\'ajout de la disponibilité:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Mettre à jour une disponibilité
const updateDisponibilite = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      date,
      heure_debut,
      heure_fin,
      statut,
      notes
    } = req.body;

    const disponibilite = await Disponibilite.findOne({
      where: {
        id,
        veterinaire_id: req.user.id
      }
    });

    if (!disponibilite) {
      return res.status(404).json({ message: 'Disponibilité non trouvée' });
    }

    // Vérifier si la disponibilité peut être modifiée
    if (disponibilite.statut === 'reserve') {
      return res.status(400).json({
        message: 'Impossible de modifier une disponibilité réservée'
      });
    }

    await disponibilite.update({
      date,
      heure_debut,
      heure_fin,
      statut,
      notes
    });

    res.json(disponibilite);
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la disponibilité:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Supprimer une disponibilité
const supprimerDisponibilite = async (req, res) => {
  try {
    const { id } = req.params;

    const disponibilite = await Disponibilite.findOne({
      where: {
        id,
        veterinaire_id: req.user.id
      }
    });

    if (!disponibilite) {
      return res.status(404).json({ message: 'Disponibilité non trouvée' });
    }

    if (disponibilite.statut === 'reserve') {
      return res.status(400).json({
        message: 'Impossible de supprimer une disponibilité réservée'
      });
    }

    await disponibilite.destroy();

    res.json({ message: 'Disponibilité supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression de la disponibilité:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Fonction utilitaire pour créer des disponibilités récurrentes
const creerDisponibilitesRecurrentes = async (disponibiliteInitiale) => {
  const { recurrence, date, heure_debut, heure_fin } = disponibiliteInitiale;
  const dateDebut = new Date(date);
  const dateFin = new Date(recurrence.fin);
  const disponibilitesACreer = [];

  let dateActuelle = new Date(dateDebut);
  dateActuelle.setDate(dateActuelle.getDate() + 1); // Commencer au jour suivant

  while (dateActuelle <= dateFin) {
    switch (recurrence.type) {
      case 'quotidien':
        disponibilitesACreer.push({
          veterinaire_id: disponibiliteInitiale.veterinaire_id,
          date: dateActuelle.toISOString().split('T')[0],
          heure_debut,
          heure_fin,
          statut: 'disponible'
        });
        dateActuelle.setDate(dateActuelle.getDate() + 1);
        break;

      case 'hebdomadaire':
        if (dateActuelle.getDay() === dateDebut.getDay()) {
          disponibilitesACreer.push({
            veterinaire_id: disponibiliteInitiale.veterinaire_id,
            date: dateActuelle.toISOString().split('T')[0],
            heure_debut,
            heure_fin,
            statut: 'disponible'
          });
        }
        dateActuelle.setDate(dateActuelle.getDate() + 1);
        break;

      case 'mensuel':
        if (dateActuelle.getDate() === dateDebut.getDate()) {
          disponibilitesACreer.push({
            veterinaire_id: disponibiliteInitiale.veterinaire_id,
            date: dateActuelle.toISOString().split('T')[0],
            heure_debut,
            heure_fin,
            statut: 'disponible'
          });
        }
        dateActuelle.setDate(dateActuelle.getDate() + 1);
        break;
    }
  }

  if (disponibilitesACreer.length > 0) {
    await Disponibilite.bulkCreate(disponibilitesACreer);
  }
};

module.exports = {
  getDisponibilites,
  ajouterDisponibilite,
  updateDisponibilite,
  supprimerDisponibilite
};