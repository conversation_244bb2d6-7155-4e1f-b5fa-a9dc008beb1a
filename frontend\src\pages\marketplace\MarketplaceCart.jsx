import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  Grid,
  Divider,
  Card,
  CardContent,
  <PERSON><PERSON>,
  <PERSON>per,
  <PERSON>,
  StepLabel
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import DeleteIcon from '@mui/icons-material/Delete';
import axios from 'axios';

const steps = ['Panier', 'Livraison', 'Paiement', 'Confirmation'];

const MarketplaceCart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [shippingAddress, setShippingAddress] = useState({
    nom: '',
    adresse: '',
    ville: '',
    telephone: '',
    instructions: ''
  });
  const [error, setError] = useState('');

  useEffect(() => {
    fetchCartItems();
  }, []);

  const fetchCartItems = async () => {
    try {
      const response = await axios.get('/api/marketplace/cart');
      setCartItems(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error);
      setError('Impossible de charger le panier');
    }
  };

  const updateQuantity = async (itemId, newQuantity) => {
    if (newQuantity < 1) return;
    try {
      await axios.put(`/api/marketplace/cart/${itemId}`, {
        quantity: newQuantity
      });
      fetchCartItems();
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la quantité:', error);
      setError('Impossible de mettre à jour la quantité');
    }
  };

  const removeItem = async (itemId) => {
    try {
      await axios.delete(`/api/marketplace/cart/${itemId}`);
      fetchCartItems();
    } catch (error) {
      console.error('Erreur lors de la suppression du produit:', error);
      setError('Impossible de supprimer le produit');
    }
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleShippingSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/marketplace/shipping', shippingAddress);
      handleNext();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'adresse:', error);
      setError('Impossible d\'enregistrer l\'adresse de livraison');
    }
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + (item.prix * item.quantity), 0);
  };

  const renderCartContent = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Produit</TableCell>
            <TableCell align="right">Prix unitaire</TableCell>
            <TableCell align="center">Quantité</TableCell>
            <TableCell align="right">Total</TableCell>
            <TableCell align="center">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {cartItems.map((item) => (
            <TableRow key={item.id}>
              <TableCell component="th" scope="row">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <img
                    src={item.image_url}
                    alt={item.nom}
                    style={{ width: 50, height: 50, marginRight: 10 }}
                  />
                  {item.nom}
                </Box>
              </TableCell>
              <TableCell align="right">{item.prix} DA</TableCell>
              <TableCell align="center">
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <IconButton
                    onClick={() => updateQuantity(item.id, item.quantity - 1)}
                    size="small"
                  >
                    <RemoveIcon />
                  </IconButton>
                  <Typography sx={{ mx: 2 }}>{item.quantity}</Typography>
                  <IconButton
                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                    size="small"
                  >
                    <AddIcon />
                  </IconButton>
                </Box>
              </TableCell>
              <TableCell align="right">{item.prix * item.quantity} DA</TableCell>
              <TableCell align="center">
                <IconButton onClick={() => removeItem(item.id)} color="error">
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderShippingForm = () => (
    <form onSubmit={handleShippingSubmit}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            required
            fullWidth
            label="Nom complet"
            value={shippingAddress.nom}
            onChange={(e) => setShippingAddress({ ...shippingAddress, nom: e.target.value })}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            required
            fullWidth
            label="Adresse"
            value={shippingAddress.adresse}
            onChange={(e) => setShippingAddress({ ...shippingAddress, adresse: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            required
            fullWidth
            label="Ville"
            value={shippingAddress.ville}
            onChange={(e) => setShippingAddress({ ...shippingAddress, ville: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            required
            fullWidth
            label="Téléphone"
            value={shippingAddress.telephone}
            onChange={(e) => setShippingAddress({ ...shippingAddress, telephone: e.target.value })}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Instructions de livraison"
            multiline
            rows={4}
            value={shippingAddress.instructions}
            onChange={(e) => setShippingAddress({ ...shippingAddress, instructions: e.target.value })}
          />
        </Grid>
      </Grid>
    </form>
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          {activeStep === 0 && renderCartContent()}
          {activeStep === 1 && renderShippingForm()}
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Résumé de la commande
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Sous-total</Typography>
                <Typography>{calculateTotal()} DA</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Frais de livraison</Typography>
                <Typography>500 DA</Typography>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Total</Typography>
                <Typography variant="h6">{calculateTotal() + 500} DA</Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', pt: 2 }}>
                <Button
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Retour
                </Button>
                <Button
                  variant="contained"
                  onClick={activeStep === steps.length - 1 ? undefined : handleNext}
                  disabled={cartItems.length === 0}
                >
                  {activeStep === steps.length - 1 ? 'Terminer' : 'Suivant'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MarketplaceCart;