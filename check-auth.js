// Script pour vérifier l'authentification
console.log('Token JWT:', localStorage.getItem('token'));
console.log('User Credentials:', localStorage.getItem('user_credentials'));

// Décoder le token JWT (sans vérification de signature)
function decodeJWT(token) {
  if (!token) return null;
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error('Erreur lors du décodage du token:', e);
    return null;
  }
}

// Afficher les informations du token décodé
const token = localStorage.getItem('token');
const decodedToken = decodeJWT(token);
console.log('Token décodé:', decodedToken);

// Vérifier si le token est expiré
if (decodedToken && decodedToken.exp) {
  const expirationDate = new Date(decodedToken.exp * 1000);
  const now = new Date();
  console.log('Date d\'expiration du token:', expirationDate);
  console.log('Token expiré:', expirationDate < now);
}

// Vérifier le rôle de l'utilisateur
if (decodedToken && decodedToken.user && decodedToken.user.role) {
  console.log('Rôle de l\'utilisateur:', decodedToken.user.role);
  console.log('L\'utilisateur est admin:', decodedToken.user.role === 'admin');
}
