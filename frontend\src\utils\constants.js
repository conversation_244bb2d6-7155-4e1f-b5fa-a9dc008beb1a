// Constantes pour les types de volailles
export const TYPES_VOLAILLES = {
  POULET_CHAIR: 'poulet_chair',
  POULE_PONDEUSE: 'poule_pondeuse',
  POULETTE: 'poulette',
  REPRODUCTEUR: 'reproducteur'
};

// Constantes pour les races de volailles
export const RACES_VOLAILLES = {
  ARBOR_ACRES: 'Arbor Acres',
  COBB_500: 'Cobb 500',
  ROSS_308: 'Ross 308',
  HUBBARD: 'Hubbard',
  ISA_BROWN: 'ISA Brown',
  LOHMANN: 'Lohmann',
  NOVOGEN: 'Novogen',
  HY_LINE: 'Hy-Line'
};

// Constantes pour les statuts sanitaires
export const STATUTS_SANITAIRES = {
  SAIN: 'sain',
  MALADE: 'malade',
  EN_TRAITEMENT: 'en_traitement',
  QUARANTAINE: 'quarantaine',
  GUERI: 'gueri'
};

// Constantes pour les types d'alertes
export const TYPES_ALERTES = {
  RUPTURE: 'rupture',
  SEUIL_CRITIQUE: 'seuil_critique',
  SEUIL_ALERTE: 'seuil_alerte',
  PEREMPTION: 'peremption'
};

// Constantes pour les statuts de consultation
export const STATUTS_CONSULTATION = {
  PLANIFIEE: 'planifiee',
  EN_COURS: 'en_cours',
  TERMINEE: 'terminee',
  ANNULEE: 'annulee'
};

// Constantes pour les types de produits
export const TYPES_PRODUITS = {
  ALIMENT: 'aliment',
  VACCIN: 'vaccin',
  MEDICAMENT: 'medicament',
  MATERIEL: 'materiel',
  AUTRE: 'autre'
};

// Constantes pour les unités de mesure
export const UNITES_MESURE = {
  KG: 'kg',
  G: 'g',
  L: 'l',
  ML: 'ml',
  UNITE: 'unite',
  DOSE: 'dose'
};

// Constantes pour les niveaux de priorité
export const NIVEAUX_PRIORITE = {
  BASSE: 'basse',
  MOYENNE: 'moyenne',
  HAUTE: 'haute',
  CRITIQUE: 'critique'
};

// Constantes pour les qualités d'œufs
export const QUALITES_OEUFS = {
  PREMIUM: 'premium',
  STANDARD: 'standard',
  DEFECTUEUX: 'defectueux'
};

// Fonctions utilitaires pour les dates
export const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const formatDateTime = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Fonctions utilitaires pour les calculs
export const calculateAge = (dateNaissance) => {
  const diffTime = Math.abs(new Date() - new Date(dateNaissance));
  return Math.floor(diffTime / (1000 * 60 * 60 * 24));
};

export const calculateMortalityRate = (initial, current) => {
  if (!initial || !current) return 0;
  return ((initial - current) / initial) * 100;
};

export const calculateGrowthRate = (initial, current) => {
  if (!initial || !current) return 0;
  return ((current - initial) / initial) * 100;
};

// Fonctions utilitaires pour les validations
export const isValidQuantity = (quantity) => {
  return typeof quantity === 'number' && quantity >= 0;
};

export const isValidDate = (date) => {
  const d = new Date(date);
  return d instanceof Date && !isNaN(d);
};

export const isValidEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

// Fonctions utilitaires pour les messages d'erreur
export const getErrorMessage = (error) => {
  return error.response?.data?.message || 'Une erreur est survenue';
};

// Fonctions utilitaires pour les notifications
export const getNotificationColor = (type) => {
  switch (type) {
    case 'success': return '#4caf50';
    case 'warning': return '#ff9800';
    case 'error': return '#f44336';
    default: return '#2196f3';
  }
};
