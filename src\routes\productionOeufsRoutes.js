const express = require('express');
const router = express.Router();
const { body, validationResult, param, query } = require('express-validator');
const { auth } = require('../middleware/auth');
const { hasRole } = require('../middleware/roleCheck');
const ProductionOeufs = require('../models/productionOeufs');
const Eleveur = require('../models/eleveur');
const Volaille = require('../models/volaille');

/**
 * Routes pour la gestion de la production d'œufs
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 */

// Middleware de validation pour la création/modification d'une production
const validateProduction = [
  body('date_production')
    .isISO8601()
    .withMessage('La date de production doit être une date valide'),

  body('nombre_poules')
    .isInt({ min: 1 })
    .withMessage('Le nombre de poules doit être un nombre entier positif'),

  body('oeufs_collectes')
    .isInt({ min: 0 })
    .withMessage('Le nombre d\'œufs collectés doit être un nombre entier positif ou nul'),

  body('oeufs_vendables')
    .isInt({ min: 0 })
    .withMessage('Le nombre d\'œufs vendables doit être un nombre entier positif ou nul'),

  body('oeufs_casses')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Le nombre d\'œufs cassés doit être un nombre entier positif ou nul'),

  body('oeufs_deformes')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Le nombre d\'œufs déformés doit être un nombre entier positif ou nul'),

  body('prix_vente_unitaire')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le prix de vente unitaire doit être un nombre positif')
];

// GET /api/production-oeufs - Récupérer toutes les productions d'un éleveur
router.get('/',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    query('date_debut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    query('date_fin')
      .optional()
      .isISO8601()
      .withMessage('La date de fin doit être une date valide'),
    query('volaille_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('L\'ID de volaille doit être un nombre entier positif'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('La page doit être un nombre entier positif'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('La limite doit être entre 1 et 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const { date_debut, date_fin, volaille_id, page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;

      // Construire les conditions de recherche
      const whereConditions = {
        eleveur_id: eleveurId
      };

      if (volaille_id) whereConditions.volaille_id = volaille_id;

      if (date_debut && date_fin) {
        whereConditions.date_production = {
          [require('sequelize').Op.between]: [date_debut, date_fin]
        };
      } else if (date_debut) {
        whereConditions.date_production = {
          [require('sequelize').Op.gte]: date_debut
        };
      } else if (date_fin) {
        whereConditions.date_production = {
          [require('sequelize').Op.lte]: date_fin
        };
      }

      const { count, rows: productions } = await ProductionOeufs.findAndCountAll({
        where: whereConditions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['date_production', 'DESC']],
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          },
          {
            model: Volaille,
            as: 'volaille',
            attributes: ['id', 'lot_numero', 'race', 'quantite'],
            required: false
          }
        ]
      });

      // Calculer les performances pour chaque production
      const productionsAvecPerformances = productions.map(production => ({
        ...production.toJSON(),
        performances: production.calculerPerformances()
      }));

      res.json({
        success: true,
        data: {
          productions: productionsAvecPerformances,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des productions:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des productions'
      });
    }
  }
);

// GET /api/production-oeufs/statistiques - Récupérer les statistiques de production
router.get('/statistiques',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    query('date_debut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être une date valide'),
    query('date_fin')
      .optional()
      .isISO8601()
      .withMessage('La date de fin doit être une date valide'),
    query('type')
      .optional()
      .isIn(['periode', 'annuelle'])
      .withMessage('Le type doit être: periode ou annuelle')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;
      const { date_debut, date_fin, type = 'periode' } = req.query;

      let statistiques;

      if (type === 'annuelle') {
        const annee = req.query.annee || new Date().getFullYear();
        statistiques = await ProductionOeufs.getStatistiquesAnnuelles(eleveurId, annee);
      } else {
        const dateDebutDefault = date_debut || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        const dateFinDefault = date_fin || new Date().toISOString().split('T')[0];

        statistiques = await ProductionOeufs.getStatistiquesPeriode(eleveurId, dateDebutDefault, dateFinDefault);
      }

      res.json({
        success: true,
        data: statistiques
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des statistiques'
      });
    }
  }
);

// GET /api/production-oeufs/aujourd-hui - Récupérer la production du jour
router.get('/aujourd-hui',
  auth,
  hasRole(['eleveur', 'admin']),
  async (req, res) => {
    try {
      const eleveurId = req.user.role === 'admin' ? req.query.eleveur_id || req.user.id : req.user.id;
      const aujourdhui = new Date().toISOString().split('T')[0];

      const production = await ProductionOeufs.findOne({
        where: {
          eleveur_id: eleveurId,
          date_production: aujourdhui
        },
        include: [
          {
            model: Volaille,
            as: 'volaille',
            attributes: ['id', 'lot_numero', 'race', 'quantite'],
            required: false
          }
        ]
      });

      if (production) {
        const productionAvecPerformances = {
          ...production.toJSON(),
          performances: production.calculerPerformances()
        };

        res.json({
          success: true,
          data: productionAvecPerformances
        });
      } else {
        res.json({
          success: true,
          data: null,
          message: 'Aucune production enregistrée pour aujourd\'hui'
        });
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de la production du jour:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération de la production du jour'
      });
    }
  }
);

// GET /api/production-oeufs/:id - Récupérer une production spécifique
router.get('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const production = await ProductionOeufs.findOne({
        where: whereConditions,
        include: [
          {
            model: Eleveur,
            as: 'eleveur',
            attributes: ['id', 'nom', 'prenom', 'email']
          },
          {
            model: Volaille,
            as: 'volaille',
            attributes: ['id', 'lot_numero', 'race', 'quantite'],
            required: false
          }
        ]
      });

      if (!production) {
        return res.status(404).json({
          success: false,
          message: 'Production non trouvée'
        });
      }

      const productionAvecPerformances = {
        ...production.toJSON(),
        performances: production.calculerPerformances()
      };

      res.json({
        success: true,
        data: productionAvecPerformances
      });
    } catch (error) {
      console.error('Erreur lors de la récupération de la production:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération de la production'
      });
    }
  }
);

// POST /api/production-oeufs - Créer une nouvelle production
router.post('/',
  auth,
  hasRole(['eleveur', 'admin']),
  validateProduction,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const eleveurId = req.user.role === 'admin' ? req.body.eleveur_id || req.user.id : req.user.id;

      // Vérifier que l'éleveur existe
      const eleveur = await Eleveur.findByPk(eleveurId);
      if (!eleveur) {
        return res.status(404).json({
          success: false,
          message: 'Éleveur non trouvé'
        });
      }

      // Vérifier qu'il n'y a pas déjà une production pour cette date et ce lot
      const productionExistante = await ProductionOeufs.findOne({
        where: {
          eleveur_id: eleveurId,
          date_production: req.body.date_production,
          volaille_id: req.body.volaille_id || null
        }
      });

      if (productionExistante) {
        return res.status(400).json({
          success: false,
          message: 'Une production existe déjà pour cette date et ce lot'
        });
      }

      // Vérifier que le lot de volailles existe si spécifié
      if (req.body.volaille_id) {
        const volaille = await Volaille.findOne({
          where: {
            id: req.body.volaille_id,
            eleveur_id: eleveurId
          }
        });

        if (!volaille) {
          return res.status(404).json({
            success: false,
            message: 'Lot de volailles non trouvé'
          });
        }
      }

      const productionData = {
        ...req.body,
        eleveur_id: eleveurId
      };

      const production = await ProductionOeufs.create(productionData);

      res.status(201).json({
        success: true,
        message: 'Production créée avec succès',
        data: production
      });
    } catch (error) {
      console.error('Erreur lors de la création de la production:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la création de la production'
      });
    }
  }
);

// PUT /api/production-oeufs/:id - Mettre à jour une production
router.put('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    ...validateProduction
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const production = await ProductionOeufs.findOne({ where: whereConditions });

      if (!production) {
        return res.status(404).json({
          success: false,
          message: 'Production non trouvée'
        });
      }

      // Vérifier l'unicité si la date ou le lot change
      if ((req.body.date_production && req.body.date_production !== production.date_production) ||
          (req.body.volaille_id !== undefined && req.body.volaille_id !== production.volaille_id)) {

        const productionExistante = await ProductionOeufs.findOne({
          where: {
            eleveur_id: production.eleveur_id,
            date_production: req.body.date_production || production.date_production,
            volaille_id: req.body.volaille_id !== undefined ? req.body.volaille_id : production.volaille_id,
            id: { [require('sequelize').Op.ne]: production.id }
          }
        });

        if (productionExistante) {
          return res.status(400).json({
            success: false,
            message: 'Une production existe déjà pour cette date et ce lot'
          });
        }
      }

      await production.update(req.body);

      res.json({
        success: true,
        message: 'Production mise à jour avec succès',
        data: production
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la production:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la mise à jour de la production'
      });
    }
  }
);

// POST /api/production-oeufs/:id/vente - Mettre à jour les ventes d'œufs
router.post('/:id/vente',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif'),
    body('oeufs_vendus')
      .isInt({ min: 1 })
      .withMessage('Le nombre d\'œufs vendus doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const production = await ProductionOeufs.findOne({ where: whereConditions });

      if (!production) {
        return res.status(404).json({
          success: false,
          message: 'Production non trouvée'
        });
      }

      // Vérifier que le stock est suffisant
      const stockDisponible = production.stock_restant;
      if (req.body.oeufs_vendus > stockDisponible) {
        return res.status(400).json({
          success: false,
          message: `Stock insuffisant. Stock disponible: ${stockDisponible} œufs`
        });
      }

      await production.mettreAJourStock(req.body.oeufs_vendus);

      res.json({
        success: true,
        message: 'Vente enregistrée avec succès',
        data: production
      });
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la vente:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de l\'enregistrement de la vente'
      });
    }
  }
);

// DELETE /api/production-oeufs/:id - Supprimer une production
router.delete('/:id',
  auth,
  hasRole(['eleveur', 'admin']),
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('L\'ID doit être un nombre entier positif')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Erreurs de validation',
          errors: errors.array()
        });
      }

      const whereConditions = {
        id: req.params.id
      };

      if (req.user.role !== 'admin') {
        whereConditions.eleveur_id = req.user.id;
      }

      const production = await ProductionOeufs.findOne({ where: whereConditions });

      if (!production) {
        return res.status(404).json({
          success: false,
          message: 'Production non trouvée'
        });
      }

      await production.destroy();

      res.json({
        success: true,
        message: 'Production supprimée avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la suppression de la production:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la suppression de la production'
      });
    }
  }
);

module.exports = router;
