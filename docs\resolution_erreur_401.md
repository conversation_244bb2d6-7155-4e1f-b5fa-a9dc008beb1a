# Résolution des Erreurs 401 dans Poultray DZ

## Problème identifié

L'application rencontre des erreurs 401 (Non autorisé) lors de la récupération des données dans le tableau de bord administrateur. Ces erreurs se produisent généralement lorsque le token JWT a expiré ou n'est pas correctement géré entre le client et le serveur.

```
Erreur lors de la récupération des données:
Object { message: "Request failed with status code 401", name: "AxiosError", code: "ERR_BAD_REQUEST" }
```

## Solution implémentée

Nous avons mis en place une solution complète pour gérer le rafraîchissement automatique des tokens JWT :

### 1. Configuration Axios centralisée

Un nouveau fichier `axiosConfig.js` a été créé dans le dossier `services` pour centraliser la configuration d'Axios avec des intercepteurs qui :

- Ajoutent automatiquement le token JWT à chaque requête
- Détectent les erreurs 401 et tentent de rafraîchir le token
- Mettent à jour le token local lorsque le serveur en fournit un nouveau via l'en-tête `x-auth-token-refreshed`

### 2. Mise à jour du hook useAuthToken

Le hook `useAuthToken` a été simplifié pour utiliser la configuration Axios centralisée, éliminant ainsi la duplication de code et assurant une gestion cohérente des tokens dans toute l'application.

## Comment utiliser cette solution

### Dans les composants React

1. Importez l'instance Axios configurée :

```javascript
import axiosInstance from '../services/axiosConfig';
```

2. Utilisez cette instance pour toutes les requêtes API :

```javascript
// Au lieu de
// axios.get('/api/data')

// Utilisez
axiosInstance.get('/api/data')
```

### Pour le tableau de bord administrateur

Modifiez la fonction `fetchData` dans `AdminDashboard.jsx` pour utiliser l'instance Axios configurée :

```javascript
const fetchData = async () => {
  try {
    // Remplacer axios par axiosInstance
    const response = await axiosInstance.get('/api/admin/dashboard/stats');
    setData(response.data);
  } catch (error) {
    console.error('Erreur lors de la récupération des données:', error);
    setError('Erreur lors de la récupération des données');
  } finally {
    setLoading(false);
  }
};
```

## Fonctionnement technique

1. **Côté serveur** : Le middleware `tokenRefresh.js` détecte les tokens expirés et génère un nouveau token qui est renvoyé dans l'en-tête `x-auth-token-refreshed`.

2. **Côté client** : L'intercepteur de réponse Axios détecte cet en-tête et met à jour le token stocké localement.

3. **En cas d'échec** : Si le rafraîchissement échoue, l'utilisateur est redirigé vers la page de connexion avec un paramètre indiquant que la session a expiré.

## Avantages de cette approche

- **Transparence pour l'utilisateur** : Le rafraîchissement se fait automatiquement sans interruption de l'expérience utilisateur
- **Centralisation** : La logique de gestion des tokens est centralisée et réutilisable
- **Robustesse** : Gestion appropriée des cas d'erreur et redirection en cas d'échec
- **Maintenance simplifiée** : Modification facile de la logique d'authentification à un seul endroit

## Prochaines étapes recommandées

1. Mettre à jour tous les composants qui utilisent directement `axios` pour utiliser `axiosInstance`
2. Ajouter un mécanisme de notification pour informer l'utilisateur lorsque sa session est sur le point d'expirer
3. Implémenter un système de refresh token plus robuste côté serveur pour une sécurité accrue
