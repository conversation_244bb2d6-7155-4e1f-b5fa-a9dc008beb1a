const express = require('express');
const router = express.Router();
const { body, param, query, validationResult } = require('express-validator');
const { auth, checkRole } = require('../middleware/auth');
const validatePagination = require('../middleware/pagination');
const Veterinaire = require('../models/veterinaire');

// Middleware pour vérifier si l'utilisateur est un vétérinaire
const isVeterinaire = async (req, res, next) => {
  try {
    if (req.user.role !== 'veterinaire' && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Accès refusé. Rôle vétérinaire requis.' });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isVeterinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Validation pour la création/modification d'un vétérinaire
const validateVeterinaire = [
  body('numero_ordre')
    .notEmpty()
    .withMessage('Le numéro d\'ordre est requis')
    .isString()
    .withMessage('Le numéro d\'ordre doit être une chaîne de caractères'),
  body('specialites')
    .isArray()
    .withMessage('Les spécialités doivent être un tableau')
    .notEmpty()
    .withMessage('Au moins une spécialité est requise'),
  body('disponibilites')
    .optional()
    .isObject()
    .withMessage('Les disponibilités doivent être un objet'),
  body('telephone')
    .optional()
    .matches(/^[0-9+\-\s()]*$/)
    .withMessage('Format de téléphone invalide'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Format d\'email invalide'),
  body('adresse')
    .optional()
    .isString()
    .withMessage('L\'adresse doit être une chaîne de caractères')
];

/**
 * @swagger
 * /api/veterinaires:
 *   get:
 *     tags: [Veterinaires]
 *     summary: Liste des vétérinaires
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des vétérinaires récupérée
 */
router.get('/', auth, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows: veterinaires } = await Veterinaire.findAndCountAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      veterinaires,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      totalItems: count
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des vétérinaires:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

/**
 * @swagger
 * /api/veterinaires/{id}:
 *   get:
 *     tags: [Veterinaires]
 *     summary: Détails d'un vétérinaire
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Détails du vétérinaire
 */
router.get('/:id', [
  auth,
  param('id').isInt().withMessage('ID invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const veterinaire = await Veterinaire.findByPk(req.params.id);
    if (!veterinaire) {
      return res.status(404).json({ message: 'Vétérinaire non trouvé' });
    }

    res.json(veterinaire);
  } catch (error) {
    console.error('Erreur lors de la récupération du vétérinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

/**
 * @swagger
 * /api/veterinaires:
 *   post:
 *     tags: [Veterinaires]
 *     summary: Créer un nouveau vétérinaire
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Veterinaire'
 *     responses:
 *       201:
 *         description: Vétérinaire créé avec succès
 */
router.post('/', [auth, isVeterinaire, ...validateVeterinaire], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const veterinaire = await Veterinaire.create({
      ...req.body,
      user_id: req.user.id
    });

    res.status(201).json(veterinaire);
  } catch (error) {
    console.error('Erreur lors de la création du vétérinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

/**
 * @swagger
 * /api/veterinaires/{id}:
 *   put:
 *     tags: [Veterinaires]
 *     summary: Modifier un vétérinaire
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Veterinaire'
 *     responses:
 *       200:
 *         description: Vétérinaire modifié avec succès
 */
router.put('/:id', [
  auth,
  isVeterinaire,
  param('id').isInt().withMessage('ID invalide'),
  ...validateVeterinaire
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const veterinaire = await Veterinaire.findByPk(req.params.id);
    if (!veterinaire) {
      return res.status(404).json({ message: 'Vétérinaire non trouvé' });
    }

    if (veterinaire.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Non autorisé à modifier ce profil' });
    }

    await veterinaire.update(req.body);
    res.json(veterinaire);
  } catch (error) {
    console.error('Erreur lors de la modification du vétérinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

/**
 * @swagger
 * /api/veterinaires/{id}:
 *   delete:
 *     tags: [Veterinaires]
 *     summary: Supprimer un vétérinaire
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       204:
 *         description: Vétérinaire supprimé avec succès
 */
router.delete('/:id', [
  auth,
  isVeterinaire,
  param('id').isInt().withMessage('ID invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const veterinaire = await Veterinaire.findByPk(req.params.id);
    if (!veterinaire) {
      return res.status(404).json({ message: 'Vétérinaire non trouvé' });
    }

    if (veterinaire.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Non autorisé à supprimer ce profil' });
    }

    await veterinaire.destroy();
    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression du vétérinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

module.exports = router;
