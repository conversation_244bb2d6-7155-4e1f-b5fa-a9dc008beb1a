/**
 * Service API pour les fonctionnalités météorologiques
 */

import axiosInstance from '../utils/axiosConfig';

const weatherAPI = {
  // === DONNÉES MÉTÉO ACTUELLES ===
  
  // Récupérer les conditions météo actuelles pour une ferme
  getCurrentWeather: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/current?farm_id=${farmId}`);
  },

  // Récupérer les conditions météo par coordonnées
  getCurrentWeatherByCoords: (latitude, longitude) => {
    return axiosInstance.get(`/api/integrations/weather/current?lat=${latitude}&lon=${longitude}`);
  },

  // === PRÉVISIONS MÉTÉO ===
  
  // Récupérer les prévisions météo (5 jours)
  getForecast: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/forecast?farm_id=${farmId}`);
  },

  // Récupérer les prévisions par coordonnées
  getForecastByCoords: (latitude, longitude) => {
    return axiosInstance.get(`/api/integrations/weather/forecast?lat=${latitude}&lon=${longitude}`);
  },

  // Récupérer les prévisions étendues (jusqu'à 14 jours)
  getExtendedForecast: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/forecast/extended?farm_id=${farmId}`);
  },

  // === ALERTES MÉTÉO ===
  
  // Récupérer les alertes météo actives
  getAlerts: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/alerts?farm_id=${farmId}`);
  },

  // Récupérer les alertes par région
  getAlertsByRegion: (region) => {
    return axiosInstance.get(`/api/integrations/weather/alerts/region?region=${region}`);
  },

  // Marquer une alerte comme lue
  markAlertAsRead: (alertId) => {
    return axiosInstance.put(`/api/integrations/weather/alerts/${alertId}/read`);
  },

  // === RECOMMANDATIONS AGRICOLES ===
  
  // Récupérer les recommandations basées sur la météo
  getRecommendations: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/recommendations?farm_id=${farmId}`);
  },

  // Récupérer les recommandations par type d'élevage
  getRecommendationsByType: (farmId, livestockType) => {
    return axiosInstance.get(`/api/integrations/weather/recommendations?farm_id=${farmId}&type=${livestockType}`);
  },

  // === HISTORIQUE MÉTÉO ===
  
  // Récupérer l'historique météo
  getWeatherHistory: (farmId, startDate, endDate) => {
    return axiosInstance.get(`/api/integrations/weather/history?farm_id=${farmId}&start=${startDate}&end=${endDate}`);
  },

  // Récupérer les statistiques météo mensuelles
  getMonthlyStats: (farmId, year, month) => {
    return axiosInstance.get(`/api/integrations/weather/stats/monthly?farm_id=${farmId}&year=${year}&month=${month}`);
  },

  // === IMPACT SUR L'ÉLEVAGE ===
  
  // Analyser l'impact météo sur la production
  getProductionImpact: (farmId, period = '30days') => {
    return axiosInstance.get(`/api/integrations/weather/impact/production?farm_id=${farmId}&period=${period}`);
  },

  // Analyser l'impact météo sur la santé des volailles
  getHealthImpact: (farmId, period = '30days') => {
    return axiosInstance.get(`/api/integrations/weather/impact/health?farm_id=${farmId}&period=${period}`);
  },

  // Récupérer les indices de stress thermique
  getThermalStressIndex: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/stress-index?farm_id=${farmId}`);
  },

  // === CONFIGURATION ===
  
  // Configurer les seuils d'alerte météo
  setAlertThresholds: (farmId, thresholds) => {
    return axiosInstance.put(`/api/integrations/weather/alerts/thresholds?farm_id=${farmId}`, thresholds);
  },

  // Récupérer les seuils d'alerte configurés
  getAlertThresholds: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/alerts/thresholds?farm_id=${farmId}`);
  },

  // === NOTIFICATIONS MÉTÉO ===
  
  // S'abonner aux notifications météo
  subscribeToAlerts: (farmId, alertTypes) => {
    return axiosInstance.post(`/api/integrations/weather/notifications/subscribe`, {
      farm_id: farmId,
      alert_types: alertTypes
    });
  },

  // Se désabonner des notifications
  unsubscribeFromAlerts: (farmId, alertTypes) => {
    return axiosInstance.post(`/api/integrations/weather/notifications/unsubscribe`, {
      farm_id: farmId,
      alert_types: alertTypes
    });
  },

  // === DONNÉES SPÉCIALISÉES ===
  
  // Récupérer les données UV
  getUVIndex: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/uv?farm_id=${farmId}`);
  },

  // Récupérer les données de qualité de l'air
  getAirQuality: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/air-quality?farm_id=${farmId}`);
  },

  // Récupérer les données de précipitations détaillées
  getPrecipitationDetails: (farmId, period = '7days') => {
    return axiosInstance.get(`/api/integrations/weather/precipitation?farm_id=${farmId}&period=${period}`);
  },

  // === INTÉGRATION AVEC L'IA ===
  
  // Obtenir des prédictions météo basées sur l'IA
  getAIPredictions: (farmId, horizon = '7days') => {
    return axiosInstance.get(`/api/integrations/weather/ai/predictions?farm_id=${farmId}&horizon=${horizon}`);
  },

  // Analyser les tendances météo avec l'IA
  getWeatherTrends: (farmId, period = '1year') => {
    return axiosInstance.get(`/api/integrations/weather/ai/trends?farm_id=${farmId}&period=${period}`);
  },

  // === RAPPORTS MÉTÉO ===
  
  // Générer un rapport météo personnalisé
  generateWeatherReport: (farmId, reportConfig) => {
    return axiosInstance.post(`/api/integrations/weather/reports/generate`, {
      farm_id: farmId,
      ...reportConfig
    });
  },

  // Télécharger un rapport météo
  downloadWeatherReport: (reportId) => {
    return axiosInstance.get(`/api/integrations/weather/reports/${reportId}/download`, {
      responseType: 'blob'
    });
  },

  // === COMPARAISONS ET BENCHMARKS ===
  
  // Comparer avec les données historiques
  compareWithHistory: (farmId, currentPeriod, historicalPeriod) => {
    return axiosInstance.get(`/api/integrations/weather/compare/history`, {
      params: {
        farm_id: farmId,
        current_period: currentPeriod,
        historical_period: historicalPeriod
      }
    });
  },

  // Comparer avec d'autres régions
  compareWithRegions: (farmId, regions) => {
    return axiosInstance.get(`/api/integrations/weather/compare/regions`, {
      params: {
        farm_id: farmId,
        regions: regions.join(',')
      }
    });
  },

  // === CACHE ET PERFORMANCE ===
  
  // Forcer la mise à jour du cache météo
  refreshWeatherCache: (farmId) => {
    return axiosInstance.post(`/api/integrations/weather/cache/refresh`, {
      farm_id: farmId
    });
  },

  // Récupérer le statut du cache
  getCacheStatus: (farmId) => {
    return axiosInstance.get(`/api/integrations/weather/cache/status?farm_id=${farmId}`);
  }
};

export { weatherAPI };
export default weatherAPI;
