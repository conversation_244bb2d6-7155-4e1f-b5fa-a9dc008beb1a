import React from 'react';
import { Typography, Box, Divider, List, ListItem, ListItemIcon, ListItemText, Chip } from '@mui/material';
import { AddCircle, LocalShipping, Vaccines, MonetizationOn, Egg, Assignment } from '@mui/icons-material';

const RecentActivity = ({ data }) => {
  // Données fictives pour la démonstration
  const mockData = {
    activities: [
      {
        id: 1,
        type: 'ajout_lot',
        description: 'Nouveau lot de 500 poules pondeuses ajouté',
        date: '2023-06-07T10:30:00',
        details: 'Lot #P789'
      },
      {
        id: 2,
        type: 'vente',
        description: 'Vente de 1200 œufs enregistrée',
        date: '2023-06-07T09:15:00',
        details: '24000 DZD'
      },
      {
        id: 3,
        type: 'vaccination',
        description: 'Vaccination effectuée sur le lot #A123',
        date: '2023-06-06T14:45:00',
        details: 'Vaccin: Newcastle'
      },
      {
        id: 4,
        type: 'livraison',
        description: '<PERSON><PERSON><PERSON> d\'aliments reçue',
        date: '2023-06-06T11:20:00',
        details: '500 kg'
      },
      {
        id: 5,
        type: 'production',
        description: 'Production journalière enregistrée',
        date: '2023-06-05T18:00:00',
        details: '1850 œufs'
      },
      {
        id: 6,
        type: 'rapport',
        description: 'Rapport vétérinaire ajouté',
        date: '2023-06-05T15:30:00',
        details: 'Lot #B456'
      }
    ]
  };

  // Utiliser les données réelles si disponibles, sinon utiliser les données fictives
  const activitiesData = data?.activities || mockData.activities;

  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Aujourd'hui, afficher l'heure
      return `Aujourd'hui à ${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 1) {
      // Hier
      return `Hier à ${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      // Autre jour
      return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', hour: '2-digit', minute: '2-digit' });
    }
  };

  // Fonction pour obtenir l'icône en fonction du type d'activité
  const getActivityIcon = (type) => {
    switch (type) {
      case 'ajout_lot':
        return <AddCircle color="primary" />;
      case 'vente':
        return <MonetizationOn style={{ color: '#10B981' }} />;
      case 'vaccination':
        return <Vaccines color="secondary" />;
      case 'livraison':
        return <LocalShipping color="info" />;
      case 'production':
        return <Egg style={{ color: '#F59E0B' }} />;
      case 'rapport':
        return <Assignment color="default" />;
      default:
        return <Assignment />;
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Activité Récente</Typography>
      <Divider sx={{ mb: 2 }} />

      <List>
        {activitiesData.map((activity) => (
          <ListItem key={activity.id} className="activity-item" alignItems="flex-start" disableGutters>
            <ListItemIcon className="activity-icon">
              {getActivityIcon(activity.type)}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body1" component="span">
                    {activity.description}
                  </Typography>
                  <Chip
                    label={activity.details}
                    size="small"
                    variant="outlined"
                    sx={{ ml: 1 }}
                  />
                </Box>
              }
              secondary={
                <Typography variant="body2" className="activity-time">
                  {formatDate(activity.date)}
                </Typography>
              }
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

export default RecentActivity;