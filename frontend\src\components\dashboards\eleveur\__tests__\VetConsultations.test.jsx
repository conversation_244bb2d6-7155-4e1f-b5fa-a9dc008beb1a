import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { VetConsultations } from '../index';
import testDbService from '../../../../services/testDbService';

// Test data
const testConsultations = [
  {
    veterinaire_id: 1,
    eleveur_id: 1,
    diagnostic: 'TEST Stress thermique',
    notes: 'TEST Consultation de routine',
    created_at: new Date('2024-01-01T10:00:00.000Z'),
    updated_at: new Date('2024-01-01T11:30:00.000Z'),
    volaille_id: 1,
    date: new Date('2024-01-01T10:00:00.000Z'),
    symptomes: 'TEST Baisse d\'appétit',
    traitement: 'TEST Vitamines et électrolytes',
    statut: 'terminé',
    cout: 5000
  },
  {
    veterinaire_id: 2,
    eleveur_id: 1,
    diagnostic: 'TEST Infection respiratoire mineure',
    notes: 'TEST Consultation d\'urgence',
    created_at: new Date('2024-01-03T14:00:00.000Z'),
    updated_at: new Date('2024-01-03T15:30:00.000Z'),
    volaille_id: 2,
    date: new Date('2024-01-03T14:00:00.000Z'),
    symptomes: 'TEST Toux légère',
    traitement: 'TEST Antibiotiques',
    statut: 'en cours',
    cout: 7500
  }
];

describe('VetConsultations Component', () => {
  // Insérer les données de test avant tous les tests
  beforeAll(async () => {
    // Nettoyer les données de test existantes
    await testDbService.cleanTestData();
    
    // Insérer les données de test
    for (const consultation of testConsultations) {
      await testDbService.insertTestConsultations(consultation);
    }
  });
  
  // Nettoyer les données de test après tous les tests
  afterAll(async () => {
    await testDbService.cleanTestData();
  });
  
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };
  
  test('renders loading state initially', () => {
    renderWithI18n(<VetConsultations />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  test('renders consultations list after loading', async () => {
    renderWithI18n(<VetConsultations />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Vérifier que les données de test sont affichées
    expect(screen.getByText('TEST Stress thermique')).toBeInTheDocument();
    expect(screen.getByText('TEST Baisse d\'appétit')).toBeInTheDocument();
    expect(screen.getByText('TEST Infection respiratoire mineure')).toBeInTheDocument();
    expect(screen.getByText('TEST Toux légère')).toBeInTheDocument();
  });
  
  test('opens new consultation dialog', async () => {
    renderWithI18n(<VetConsultations />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue de nouvelle consultation
    fireEvent.click(screen.getByText('vet.newConsultation'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('vet.lot')).toBeInTheDocument();
    expect(screen.getByLabelText('vet.symptoms')).toBeInTheDocument();
  });
  
  test('handles consultation creation successfully', async () => {
    renderWithI18n(<VetConsultations />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue de nouvelle consultation
    fireEvent.click(screen.getByText('vet.newConsultation'));
    
    // Remplir le formulaire de consultation
    fireEvent.change(screen.getByLabelText('vet.lot'), {
      target: { value: 'TEST_LOT003' }
    });
    
    fireEvent.change(screen.getByLabelText('vet.symptoms'), {
      target: { value: 'TEST Perte de poids' }
    });
    
    fireEvent.change(screen.getByLabelText('vet.diagnostic'), {
      target: { value: 'TEST Parasites intestinaux' }
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('common.save'));
    
    // Vérifier que la boîte de dialogue est fermée (la vérification réelle se fait via la base de données)
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });
  
  test('handles follow-up scheduling', async () => {
    renderWithI18n(<VetConsultations />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Cliquer sur le bouton de suivi pour la première consultation
    fireEvent.click(screen.getByText('vet.scheduleFollowUp'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('vet.followUpDate')).toBeInTheDocument();
  });
  
  test('filters consultations by date range', async () => {
    renderWithI18n(<VetConsultations />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Définir le filtre de date
    fireEvent.change(screen.getByLabelText('common.startDate'), {
      target: { value: '2024-01-01' }
    });
    
    fireEvent.change(screen.getByLabelText('common.endDate'), {
      target: { value: '2024-01-02' }
    });
    
    // Seule la première consultation devrait être visible
    expect(screen.getByText('TEST Stress thermique')).toBeInTheDocument();
    expect(screen.queryByText('TEST Infection respiratoire mineure')).not.toBeInTheDocument();
  });
  
  // Note: Le test d'erreur est commenté car il nécessiterait de simuler une erreur de base de données
  // test('handles error state', async () => {
  //   // Simuler une erreur de base de données
  //   jest.spyOn(testDbService, 'getConsultations').mockRejectedValueOnce(new Error('Failed to fetch'));
  //   
  //   renderWithI18n(<VetConsultations />);
  //   
  //   // Attendre que l'erreur soit affichée
  //   await waitFor(() => {
  //     expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  //   
  //   expect(screen.getByText('errors.fetchFailed')).toBeInTheDocument();
  //   
  //   // Restaurer le mock
  //   jest.restoreAllMocks();
  // });
});
