import React, { memo, useMemo, useCallback, useState, useEffect, useRef } from 'react';
import {
  Box,
  List,
  ListItem,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material';
import { useOptimizedMemo, useOptimizedCallback } from '../../hooks/usePerformance';

/**
 * Composant de liste virtualisée pour de grandes quantités de données
 */
const VirtualizedList = memo(({
  items = [],
  itemHeight = 60,
  containerHeight = 400,
  renderItem,
  loading = false,
  error = null,
  emptyMessage = "Aucun élément à afficher",
  overscan = 5, // Nombre d'éléments à rendre en plus pour le scroll fluide
  onItemClick,
  keyExtractor = (item, index) => item.id || index,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState(null);
  const scrollElementRef = useRef(null);

  // Calculer les éléments visibles
  const visibleRange = useOptimizedMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan], 'VirtualizedList.visibleRange');

  // Éléments à rendre
  const visibleItems = useOptimizedMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange.startIndex, visibleRange.endIndex], 'VirtualizedList.visibleItems');

  // Gestionnaire de scroll optimisé
  const handleScroll = useOptimizedCallback((event) => {
    const newScrollTop = event.target.scrollTop;
    setScrollTop(newScrollTop);
  }, [], 'VirtualizedList.handleScroll');

  // Gestionnaire de clic optimisé
  const handleItemClick = useOptimizedCallback((item, index) => {
    if (onItemClick) {
      onItemClick(item, visibleRange.startIndex + index);
    }
  }, [onItemClick, visibleRange.startIndex], 'VirtualizedList.handleItemClick');

  // Effet pour gérer le scroll
  useEffect(() => {
    const element = scrollElementRef.current;
    if (!element) return;

    element.addEventListener('scroll', handleScroll, { passive: true });
    return () => element.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Calculer les dimensions
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height={containerHeight}
      >
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Chargement...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ height: containerHeight }}>
        {error}
      </Alert>
    );
  }

  if (items.length === 0) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height={containerHeight}
        bgcolor="grey.50"
        borderRadius={1}
      >
        <Typography variant="body2" color="text.secondary">
          {emptyMessage}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      ref={scrollElementRef}
      sx={{
        height: containerHeight,
        overflow: 'auto',
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        ...props.sx
      }}
      {...props}
    >
      {/* Container avec la hauteur totale pour maintenir la scrollbar */}
      <Box sx={{ height: totalHeight, position: 'relative' }}>
        {/* Container des éléments visibles */}
        <Box
          sx={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          <List disablePadding>
            {visibleItems.map((item, index) => {
              const actualIndex = visibleRange.startIndex + index;
              const key = keyExtractor(item, actualIndex);
              
              return (
                <VirtualizedListItem
                  key={key}
                  item={item}
                  index={actualIndex}
                  height={itemHeight}
                  renderItem={renderItem}
                  onClick={handleItemClick}
                />
              );
            })}
          </List>
        </Box>
      </Box>
      
      {/* Indicateur de performance en mode développement */}
      {process.env.NODE_ENV === 'development' && (
        <Box
          position="absolute"
          top={8}
          right={8}
          bgcolor="rgba(0,0,0,0.8)"
          color="white"
          px={1}
          py={0.5}
          borderRadius={1}
          fontSize="0.7rem"
          sx={{ opacity: 0.8, pointerEvents: 'none' }}
        >
          Visible: {visibleRange.startIndex}-{visibleRange.endIndex} / {items.length}
        </Box>
      )}
    </Box>
  );
});

/**
 * Composant d'élément de liste optimisé
 */
const VirtualizedListItem = memo(({
  item,
  index,
  height,
  renderItem,
  onClick
}) => {
  const handleClick = useOptimizedCallback(() => {
    if (onClick) {
      onClick(item, index);
    }
  }, [onClick, item, index], 'VirtualizedListItem.handleClick');

  return (
    <ListItem
      sx={{
        height,
        minHeight: height,
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? {
          backgroundColor: 'action.hover'
        } : {}
      }}
      onClick={handleClick}
    >
      {renderItem ? renderItem(item, index) : (
        <Typography variant="body2">
          {typeof item === 'string' ? item : JSON.stringify(item)}
        </Typography>
      )}
    </ListItem>
  );
}, (prevProps, nextProps) => {
  // Comparaison optimisée pour éviter les re-rendus inutiles
  return (
    prevProps.item === nextProps.item &&
    prevProps.index === nextProps.index &&
    prevProps.height === nextProps.height &&
    prevProps.renderItem === nextProps.renderItem &&
    prevProps.onClick === nextProps.onClick
  );
});

VirtualizedList.displayName = 'VirtualizedList';
VirtualizedListItem.displayName = 'VirtualizedListItem';

export default VirtualizedList;
