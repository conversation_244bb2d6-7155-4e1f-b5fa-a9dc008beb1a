const validatePagination = (req, res, next) => {
  try {
    // Extraire et valider les paramètres de pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Valider les valeurs
    if (page < 1) {
      return res.status(400).json({
        status: 'error',
        code: 'INVALID_PAGE',
        message: 'Le numéro de page doit être supérieur à 0'
      });
    }

    if (limit < 1 || limit > 100) {
      return res.status(400).json({
        status: 'error',
        code: 'INVALID_LIMIT',
        message: 'La limite doit être comprise entre 1 et 100'
      });
    }

    // Calculer l'offset
    const offset = (page - 1) * limit;

    // Ajouter les paramètres de pagination à la requête
    req.pagination = {
      page,
      limit,
      offset
    };

    next();
  } catch (error) {
    console.error('Erreur de validation de la pagination:', error);
    res.status(400).json({
      status: 'error',
      code: 'PAGINATION_ERROR',
      message: 'Paramètres de pagination invalides'
    });
  }
};

module.exports = validatePagination;
