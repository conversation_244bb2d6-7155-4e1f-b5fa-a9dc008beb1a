openapi: 3.0.0
info:
  title: Poultry DZ API
  version: 1.0.0
  description: API pour la plateforme de gestion avicole Poultry DZ

servers:
  - url: /api
    description: API Base URL

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        message:
          type: string

    User:
      type: object
      properties:
        id:
          type: integer
        email:
          type: string
        username:
          type: string
        role:
          type: string
          enum: [eleveur, veterinaire, marchand, acheteur, admin]
        first_name:
          type: string
        last_name:
          type: string

    Eleveur:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        nom_exploitation:
          type: string
        adresse:
          type: string
        telephone:
          type: string
        capacite_totale:
          type: integer
        specialites:
          type: array
          items:
            type: string

    Veterinaire:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        numero_ordre:
          type: string
        specialites:
          type: array
          items:
            type: string
        disponibilites:
          type: object

paths:
  /auth/register:
    post:
      tags: [Authentication]
      summary: Inscription d'un nouvel utilisateur
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        201:
          description: Utilisateur créé avec succès

  /auth/login:
    post:
      tags: [Authentication]
      summary: Connexion utilisateur
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        200:
          description: Connexion réussie

  /eleveurs:
    get:
      tags: [Eleveurs]
      summary: Liste des éleveurs
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        200:
          description: Liste des éleveurs récupérée

    post:
      tags: [Eleveurs]
      summary: Créer un nouvel éleveur
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Eleveur'
      responses:
        201:
          description: Éleveur créé avec succès

  /eleveurs/{id}:
    get:
      tags: [Eleveurs]
      summary: Détails d'un éleveur
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Détails de l'éleveur

    put:
      tags: [Eleveurs]
      summary: Modifier un éleveur
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Eleveur'
      responses:
        200:
          description: Éleveur modifié avec succès

    delete:
      tags: [Eleveurs]
      summary: Supprimer un éleveur
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        204:
          description: Éleveur supprimé avec succès

  /veterinaires:
    get:
      tags: [Veterinaires]
      summary: Liste des vétérinaires
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: integer
        - in: query
          name: limit
          schema:
            type: integer
      responses:
        200:
          description: Liste des vétérinaires récupérée

    post:
      tags: [Veterinaires]
      summary: Créer un nouveau vétérinaire
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Veterinaire'
      responses:
        201:
          description: Vétérinaire créé avec succès

  /veterinaires/{id}:
    get:
      tags: [Veterinaires]
      summary: Détails d'un vétérinaire
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Détails du vétérinaire

    put:
      tags: [Veterinaires]
      summary: Modifier un vétérinaire
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Veterinaire'
      responses:
        200:
          description: Vétérinaire modifié avec succès

    delete:
      tags: [Veterinaires]
      summary: Supprimer un vétérinaire
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        204:
          description: Vétérinaire supprimé avec succès

  # Enhanced Dashboard Endpoints

  /veterinaire/dashboard:
    get:
      tags: [Veterinaire Dashboard]
      summary: Get enhanced veterinaire dashboard data
      security:
        - bearerAuth: []
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                      consultationsAVenir:
                        type: array
                      prescriptionsRecentes:
                        type: array
                      alertesSante:
                        type: array
                      graphiques:
                        type: object

  /veterinaire/notifications:
    get:
      tags: [Veterinaire Dashboard]
      summary: Get veterinaire notifications and alerts
      security:
        - bearerAuth: []
      responses:
        200:
          description: Notifications retrieved successfully

  /veterinaire/consultations/quick:
    post:
      tags: [Veterinaire Dashboard]
      summary: Quick action - Schedule new consultation
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                eleveur_id:
                  type: integer
                date_consultation:
                  type: string
                  format: date-time
                motif:
                  type: string
                urgence:
                  type: boolean
      responses:
        201:
          description: Consultation scheduled successfully

  /veterinaire/prescriptions/quick:
    post:
      tags: [Veterinaire Dashboard]
      summary: Quick action - Create new prescription
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                eleveur_id:
                  type: integer
                volaille_id:
                  type: integer
                medicament:
                  type: string
                dosage:
                  type: string
                duree_traitement:
                  type: string
                instructions:
                  type: string
      responses:
        201:
          description: Prescription created successfully

  /marchand/dashboard:
    get:
      tags: [Marchand Dashboard]
      summary: Get enhanced marchand dashboard data
      security:
        - bearerAuth: []
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                      commandesRecentes:
                        type: array
                      produitsPopulaires:
                        type: array
                      alertesStock:
                        type: array
                      graphiques:
                        type: object

  /marchand/dashboard/revenue:
    get:
      tags: [Marchand Dashboard]
      summary: Get marchand revenue by period
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start_date
          required: true
          schema:
            type: string
            format: date
        - in: query
          name: end_date
          required: true
          schema:
            type: string
            format: date
        - in: query
          name: groupBy
          schema:
            type: string
            enum: [day, week, month, year]
      responses:
        200:
          description: Revenue data retrieved successfully

  /marchand/products/quick:
    post:
      tags: [Marchand Dashboard]
      summary: Quick action - Add new product
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                price:
                  type: number
                stock_quantity:
                  type: integer
                category:
                  type: string
                stock_alert_threshold:
                  type: integer
      responses:
        201:
          description: Product added successfully

  /marchand/products/{id}/stock:
    patch:
      tags: [Marchand Dashboard]
      summary: Quick action - Update product stock
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                stock_quantity:
                  type: integer
                operation:
                  type: string
                  enum: [set, add, subtract]
      responses:
        200:
          description: Stock updated successfully

  /marchand/ai/recommendations:
    get:
      tags: [Marchand Dashboard]
      summary: Get AI recommendations for marchand
      security:
        - bearerAuth: []
      responses:
        200:
          description: AI recommendations retrieved successfully

  /eleveurs/{id}/dashboard:
    get:
      tags: [Eleveur Dashboard]
      summary: Get enhanced éleveur dashboard data
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  data:
                    type: object
                    properties:
                      stats:
                        type: object
                      alertes:
                        type: array
                      ventesRecentes:
                        type: array
                      saisiesQuotidiennes:
                        type: array
                      productionOeufs:
                        type: array
                      graphiques:
                        type: object

  /eleveurs/{id}/ouvriers:
    get:
      tags: [Eleveur Dashboard]
      summary: Get farm workers (ouvriers) for an éleveur
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Farm workers retrieved successfully

    post:
      tags: [Eleveur Dashboard]
      summary: Create new farm worker (ouvrier)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                email:
                  type: string
                password:
                  type: string
                first_name:
                  type: string
                last_name:
                  type: string
                fermes:
                  type: array
                  items:
                    type: integer
      responses:
        201:
          description: Farm worker created successfully

  /eleveurs/saisies-quotidiennes:
    post:
      tags: [Eleveur Dashboard]
      summary: Create daily data entry (for farm workers)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                eleveur_id:
                  type: integer
                ferme_id:
                  type: integer
                volaille_id:
                  type: integer
                date_saisie:
                  type: string
                  format: date
                nombre_morts:
                  type: integer
                nombre_malades:
                  type: integer
                temperature_moyenne:
                  type: number
                humidite_moyenne:
                  type: number
                consommation_eau:
                  type: number
                consommation_aliment:
                  type: number
                incidents:
                  type: string
                besoins_materiels:
                  type: string
                observations:
                  type: string
      responses:
        201:
          description: Daily data entry recorded successfully

  /eleveurs/{id}/ventes/quick:
    post:
      tags: [Eleveur Dashboard]
      summary: Quick action - Add new sale
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                volaille_id:
                  type: integer
                quantite:
                  type: integer
                prix_unitaire:
                  type: number
                acheteur:
                  type: string
                date_vente:
                  type: string
                  format: date-time
      responses:
        201:
          description: Sale recorded successfully

  /eleveurs/{id}/activites:
    get:
      tags: [Eleveur Dashboard]
      summary: Get multi-activity view for éleveur
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
        - in: query
          name: type_activite
          schema:
            type: string
            enum: [poussins, dindes, poulets_chair, pondeuses]
      responses:
        200:
          description: Activities data retrieved successfully

