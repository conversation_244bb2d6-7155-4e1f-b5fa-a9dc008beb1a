import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  CircularProgress,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  ContentCopy as CopyIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Translate as TranslateIcon,
  Edit as EditIcon,
  Web as WebIcon,
} from '@mui/icons-material';
import axiosInstance from '../../utils/axiosConfig';

const PageContentGenerator = () => {
  const [formData, setFormData] = useState({
    pageName: '',
    pageType: 'about', // about, contact, services, faq
    sections: '',
    tone: 'professional',
    length: 'medium',
    language: 'french',
    includeImages: true,
  });
  const [generatedContent, setGeneratedContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [history, setHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axiosInstance.post('/ai/page-content', formData);
      setGeneratedContent(response.data.content);
      setSuccess('Contenu généré avec succès!');

      // Add to history
      const historyItem = {
        id: Date.now(),
        pageName: formData.pageName,
        pageType: formData.pageType,
        content: response.data.content,
        timestamp: new Date().toISOString(),
      };
      setHistory([historyItem, ...history.slice(0, 9)]); // Keep only last 10 items
    } catch (err) {
      console.error('Error generating page content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la génération du contenu');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setSuccess('Contenu copié dans le presse-papiers!');
  };

  const handleSaveAsPage = async () => {
    try {
      setLoading(true);
      const pageData = {
        title: formData.pageName,
        content: generatedContent,
        type: formData.pageType,
        language: formData.language,
      };
      await axiosInstance.post('/admin/pages', pageData);
      setSuccess('Page enregistrée avec succès!');
    } catch (err) {
      console.error('Error saving page:', err);
      setError(err.response?.data?.message || 'Erreur lors de l\'enregistrement de la page');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadFromHistory = (item) => {
    setGeneratedContent(item.content);
    setFormData({
      ...formData,
      pageName: item.pageName,
      pageType: item.pageType,
    });
    setShowHistory(false);
  };

  const handleTranslate = async (targetLanguage) => {
    try {
      setLoading(true);
      const response = await axiosInstance.post('/ai/translate', {
        text: generatedContent,
        targetLanguage,
      });
      setGeneratedContent(response.data.translatedText);
      setFormData({
        ...formData,
        language: targetLanguage,
      });
      setSuccess(`Contenu traduit en ${targetLanguage === 'french' ? 'français' : 'arabe'} avec succès!`);
    } catch (err) {
      console.error('Error translating content:', err);
      setError(err.response?.data?.message || 'Erreur lors de la traduction du contenu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <WebIcon sx={{ mr: 1, verticalAlign: 'middle', color: 'primary.main' }} />
        Générateur de Contenu de Page
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Utilisez l'IA pour générer du contenu pour les pages statiques de votre site web.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Paramètres de génération
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Nom de la page"
                    name="pageName"
                    value={formData.pageName}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Type de page</InputLabel>
                    <Select
                      name="pageType"
                      value={formData.pageType}
                      onChange={handleChange}
                      label="Type de page"
                    >
                      <MenuItem value="about">À propos</MenuItem>
                      <MenuItem value="contact">Contact</MenuItem>
                      <MenuItem value="services">Services</MenuItem>
                      <MenuItem value="faq">FAQ</MenuItem>
                      <MenuItem value="terms">Conditions d'utilisation</MenuItem>
                      <MenuItem value="privacy">Politique de confidentialité</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Sections (séparées par des virgules)"
                    name="sections"
                    value={formData.sections}
                    onChange={handleChange}
                    placeholder="Ex: introduction, mission, équipe"
                    helperText="Laissez vide pour une génération automatique des sections"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Ton</InputLabel>
                    <Select
                      name="tone"
                      value={formData.tone}
                      onChange={handleChange}
                      label="Ton"
                    >
                      <MenuItem value="professional">Professionnel</MenuItem>
                      <MenuItem value="friendly">Amical</MenuItem>
                      <MenuItem value="formal">Formel</MenuItem>
                      <MenuItem value="informative">Informatif</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Longueur</InputLabel>
                    <Select
                      name="length"
                      value={formData.length}
                      onChange={handleChange}
                      label="Longueur"
                    >
                      <MenuItem value="short">Courte</MenuItem>
                      <MenuItem value="medium">Moyenne</MenuItem>
                      <MenuItem value="long">Longue</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Langue</InputLabel>
                    <Select
                      name="language"
                      value={formData.language}
                      onChange={handleChange}
                      label="Langue"
                    >
                      <MenuItem value="french">Français</MenuItem>
                      <MenuItem value="arabic">Arabe</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={20} /> : <AIIcon />}
                    >
                      Générer le contenu
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setShowHistory(!showHistory)}
                      startIcon={<HistoryIcon />}
                    >
                      Historique
                    </Button>
                  </Box>
                </Grid>

                {error && (
                  <Grid item xs={12}>
                    <Alert severity="error">{error}</Alert>
                  </Grid>
                )}

                {success && (
                  <Grid item xs={12}>
                    <Alert severity="success">{success}</Alert>
                  </Grid>
                )}
              </Grid>
            </form>

            {showHistory && history.length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Historique de génération
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {history.map((item) => (
                    <Card key={item.id} sx={{ mb: 2, cursor: 'pointer' }} onClick={() => handleLoadFromHistory(item)}>
                      <CardContent>
                        <Typography variant="subtitle1">{item.pageName}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Type: {item.pageType} | {new Date(item.timestamp).toLocaleString()}
                        </Typography>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Contenu généré</Typography>
              {generatedContent && (
                <Box>
                  <Tooltip title="Copier le contenu">
                    <IconButton onClick={handleCopyToClipboard}>
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Enregistrer comme page">
                    <IconButton onClick={handleSaveAsPage} disabled={loading}>
                      <SaveIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Traduire en français">
                    <IconButton
                      onClick={() => handleTranslate('french')}
                      disabled={loading || formData.language === 'french'}
                    >
                      <Chip
                        icon={<TranslateIcon />}
                        label="FR"
                        size="small"
                        color={formData.language === 'french' ? 'primary' : 'default'}
                      />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Traduire en arabe">
                    <IconButton
                      onClick={() => handleTranslate('arabic')}
                      disabled={loading || formData.language === 'arabic'}
                    >
                      <Chip
                        icon={<TranslateIcon />}
                        label="AR"
                        size="small"
                        color={formData.language === 'arabic' ? 'primary' : 'default'}
                      />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </Box>
            <Divider sx={{ mb: 2 }} />

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
                <CircularProgress />
              </Box>
            ) : generatedContent ? (
              <Box
                sx={{
                  p: 2,
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  minHeight: 400,
                  maxHeight: 600,
                  overflow: 'auto',
                  bgcolor: '#f9f9f9',
                  whiteSpace: 'pre-wrap',
                }}
              >
                {generatedContent}
              </Box>
            ) : (
              <Box
                sx={{
                  p: 2,
                  border: '1px dashed #e0e0e0',
                  borderRadius: 1,
                  minHeight: 400,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1" color="text.secondary" align="center">
                  Le contenu généré apparaîtra ici. Configurez les paramètres et cliquez sur "Générer le contenu".
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PageContentGenerator;
