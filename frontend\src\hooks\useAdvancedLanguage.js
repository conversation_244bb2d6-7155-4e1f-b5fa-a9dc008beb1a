import { useLanguage } from '../contexts/LanguageContext';

/**
 * Hook pour formater les dates selon la langue
 */
export const useLocalizedDate = () => {
  const { language } = useLanguage();
  
  const formatDate = (date, options = {}) => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Date(date).toLocaleDateString(locale, options);
  };
  
  const formatDateTime = (date, options = {}) => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Date(date).toLocaleString(locale, options);
  };
  
  const formatTime = (date, options = {}) => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Date(date).toLocaleTimeString(locale, options);
  };
  
  const formatRelativeTime = (date) => {
    const now = new Date();
    const diff = now - new Date(date);
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return language === 'ar' ? 'اليوم' : 'Aujourd\'hui';
    if (days === 1) return language === 'ar' ? 'أمس' : 'Hier';
    if (days < 7) return `${days} ${language === 'ar' ? 'أيام' : 'jours'}`;
    if (days < 30) return `${Math.floor(days / 7)} ${language === 'ar' ? 'أسابيع' : 'semaines'}`;
    if (days < 365) return `${Math.floor(days / 30)} ${language === 'ar' ? 'أشهر' : 'mois'}`;
    return `${Math.floor(days / 365)} ${language === 'ar' ? 'سنوات' : 'années'}`;
  };
  
  return {
    formatDate,
    formatDateTime,
    formatTime,
    formatRelativeTime
  };
};

/**
 * Hook pour formater les nombres selon la langue
 */
export const useLocalizedNumber = () => {
  const { language } = useLanguage();
  
  const formatNumber = (number, options = {}) => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Intl.NumberFormat(locale, options).format(number);
  };
  
  const formatCurrency = (amount, currency = 'DZD') => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount);
  };
  
  const formatPercent = (value) => {
    const locale = language === 'ar' ? 'ar-DZ' : 'fr-FR';
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
  };
  
  return {
    formatNumber,
    formatCurrency,
    formatPercent
  };
};

/**
 * Hook pour la direction du texte et les styles RTL
 */
export const useRTL = () => {
  const { language } = useLanguage();
  const isRTL = language === 'ar';
  
  const getTextAlign = (defaultAlign = 'left') => {
    if (defaultAlign === 'left') return isRTL ? 'right' : 'left';
    if (defaultAlign === 'right') return isRTL ? 'left' : 'right';
    return defaultAlign;
  };
  
  const getMargin = (left = 0, right = 0) => {
    return isRTL ? { marginLeft: right, marginRight: left } : { marginLeft: left, marginRight: right };
  };
  
  const getPadding = (left = 0, right = 0) => {
    return isRTL ? { paddingLeft: right, paddingRight: left } : { paddingLeft: left, paddingRight: right };
  };
  
  const getFlexDirection = (direction = 'row') => {
    if (direction === 'row') return isRTL ? 'row-reverse' : 'row';
    return direction;
  };
  
  return {
    isRTL,
    direction: isRTL ? 'rtl' : 'ltr',
    getTextAlign,
    getMargin,
    getPadding,
    getFlexDirection
  };
};

/**
 * Hook pour les traductions avec interpolation
 */
export const useTranslation = () => {
  const { t: baseT, language } = useLanguage();
  
  const t = (key, params = {}) => {
    let translation = baseT(key);
    
    // Remplacer les paramètres dans la traduction
    Object.keys(params).forEach(param => {
      translation = translation.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
    });
    
    return translation;
  };
  
  const tPlural = (key, count, params = {}) => {
    const pluralKey = count === 1 ? `${key}.singular` : `${key}.plural`;
    return t(pluralKey, { count, ...params });
  };
  
  const tChoice = (key, choice, params = {}) => {
    const choiceKey = `${key}.${choice}`;
    return t(choiceKey, params);
  };
  
  return {
    t,
    tPlural,
    tChoice,
    language
  };
};

/**
 * Hook pour les validations localisées
 */
export const useLocalizedValidation = () => {
  const { t } = useTranslation();
  
  const getValidationMessage = (field, rule, params = {}) => {
    const key = `validation.${rule}`;
    return t(key, { field: t(`field.${field}`), ...params });
  };
  
  const validateRequired = (value, fieldName) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return getValidationMessage(fieldName, 'required');
    }
    return null;
  };
  
  const validateEmail = (email, fieldName = 'email') => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return getValidationMessage(fieldName, 'email');
    }
    return null;
  };
  
  const validateMinLength = (value, minLength, fieldName) => {
    if (value && value.length < minLength) {
      return getValidationMessage(fieldName, 'minLength', { min: minLength });
    }
    return null;
  };
  
  const validateMaxLength = (value, maxLength, fieldName) => {
    if (value && value.length > maxLength) {
      return getValidationMessage(fieldName, 'maxLength', { max: maxLength });
    }
    return null;
  };
  
  return {
    getValidationMessage,
    validateRequired,
    validateEmail,
    validateMinLength,
    validateMaxLength
  };
};

/**
 * Hook pour les messages d'erreur localisés
 */
export const useLocalizedErrors = () => {
  const { t } = useTranslation();
  
  const getErrorMessage = (error) => {
    if (typeof error === 'string') {
      return t(`error.${error}`, error);
    }
    
    if (error?.code) {
      return t(`error.${error.code}`, error.message || 'Une erreur est survenue');
    }
    
    if (error?.message) {
      return error.message;
    }
    
    return t('error.unknown', 'Une erreur inconnue est survenue');
  };
  
  const getFieldError = (fieldErrors, fieldName) => {
    if (!fieldErrors || !fieldErrors[fieldName]) return null;
    
    const error = fieldErrors[fieldName];
    if (Array.isArray(error)) {
      return error.map(getErrorMessage).join(', ');
    }
    
    return getErrorMessage(error);
  };
  
  return {
    getErrorMessage,
    getFieldError
  };
};

export default {
  useLocalizedDate,
  useLocalizedNumber,
  useRTL,
  useTranslation,
  useLocalizedValidation,
  useLocalizedErrors
};
