# Implementation Plan for Poultraydz-Trae Features

This plan outlines the steps to implement the pending features identified in `todo.md` and summarized in `pending_features.md`, prioritizing the "AMÉLIORATION TABLEAU DE BORD ÉLEVEUR". The project repository is located at `/home/<USER>/Poultraydz-Trae_App`.

## Phase 0: Environment Setup & Database Initialization

1.  **Install PostgreSQL:**
    *   Use `sudo apt update && sudo apt install -y postgresql postgresql-contrib`.
2.  **Create Database & User:**
    *   Use `sudo -u postgres psql` to access the PostgreSQL prompt.
    *   Execute SQL commands:
        *   `CREATE DATABASE poultraydz_db;`
        *   `CREATE USER poultraydz_user WITH PASSWORD 'your_secure_password';` (Replace password)
        *   `GRANT ALL PRIVILEGES ON DATABASE poultraydz_db TO poultraydz_user;`
        *   `\q` to exit psql.
3.  **Configure Backend:**
    *   Identify the database configuration file (likely `/home/<USER>/Poultraydz-Trae_App/config/config.json` or similar).
    *   Update the development/production environment settings with the correct database name, username, password, host (`localhost`), and dialect (`postgres`).
4.  **Install Dependencies:**
    *   Backend: `cd /home/<USER>/Poultraydz-Trae_App && npm install`
    *   Frontend: `cd /home/<USER>/Poultraydz-Trae_App/frontend && npm install`
5.  **Apply Database Schema:**
    *   Use `psql -U poultraydz_user -d poultraydz_db -h localhost -f /home/<USER>/Poultraydz-Trae_App/docs/Actual_database_schema.sql`. Enter the password when prompted.
6.  **Verify Database Connection & Models:**
    *   Run any existing backend scripts or tests designed to check database connectivity and model synchronization (e.g., potentially `test-models.js` or similar, or run migrations if applicable - check `package.json` scripts).
    *   This addresses the `🔴 Tests des connexions à la base de données et création des tables` item from `todo.md` Phase 1.

## Phase 1: Backend API Review (Éleveur Focus)

1.  **Review Routes:** Examine the implementation of routes related to Poussin, ProductionOeufs, SuiviVeterinaire, AlerteStock in `/home/<USER>/Poultraydz-Trae_App/src/routes/`.
2.  **Ensure Functionality:** Verify that these routes correctly interact with the corresponding Sequelize models and the database.
3.  **Refine/Implement:** Add any missing logic or endpoints required by the frontend components.

## Phase 2: Frontend Implementation (Éleveur Dashboard)

*Implement the following React components within `/home/<USER>/Poultraydz-Trae_App/frontend/src/components/` or appropriate directory structure:* 

1.  `PondeuseManagement` (Track egg-laying hens)
2.  `ChairManagement` (Track broiler chickens)
3.  `DindeManagement` (Track turkeys)
4.  `DailyCollection` (Input daily egg collection data)
5.  **Integration:** Integrate these new components into the main Éleveur Dashboard view (`/home/<USER>/Poultraydz-Trae_App/frontend/src/pages/dashboards/EleveurDashboard.jsx` or similar).

## Phase 3: Testing (Éleveur Dashboard)

1.  **Component Tests:** Write unit tests for the newly created React components.
2.  **Integration Tests:** Write tests to verify the data flow and interaction between the new components and the backend API within the Éleveur Dashboard.

## Phase 4: Backend Features (General Priority)

1.  **Password Recovery:** Implement the backend logic and API endpoint(s) for password recovery.
2.  **Marketplace API:** Implement the backend routes (`/home/<USER>/Poultraydz-Trae_App/src/routes/marketplaceRoutes.js` likely) for marketplace functionality (listing products, orders etc.).

## Phase 5: Frontend Features (General Priority)

1.  **Vétérinaire Dashboard:** Develop the frontend components and pages for the Vétérinaire Dashboard.
2.  **Mode Sombre:** Implement a dark mode toggle and styling.
3.  **Marketplace UI:** Develop the frontend components for browsing and interacting with the marketplace.

## Phase 6: Alert System

1.  **Backend Logic:** Implement backend services to monitor conditions (stock levels, health metrics) and trigger alerts.
2.  **Frontend Display:** Implement UI components to display alerts to the user.

## Phase 7: Further Testing & Optimization

1.  Conduct comprehensive end-to-end testing.
2.  Optimize frontend and backend performance based on testing.

## Phase 8: Final Integration & Documentation

1.  Ensure all features are integrated smoothly.
2.  Update `README.md`, `How-to-use.md`, and potentially add new documentation.

*This plan will be executed step-by-step. Progress will be tracked by updating the status markers in `/home/<USER>/Poultraydz-Trae_App/todo.md`.*

