/**
 * Tests pour l'authentification
 */

const request = require('supertest');
const { app, testUtils } = require('./setup');

describe('Authentication API', () => {
  describe('POST /api/auth/register', () => {
    it('devrait créer un nouvel utilisateur avec des données valides', async () => {
      const userData = {
        username: 'newtestuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        first_name: 'New',
        last_name: 'User',
        role: 'eleveur'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('créé');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user).not.toHaveProperty('password_hash');
    });

    it('devrait rejeter un utilisateur avec un email invalide', async () => {
      const userData = {
        username: 'testuser2',
        email: 'invalid-email',
        password: 'password123',
        confirmPassword: 'password123',
        first_name: 'Test',
        last_name: 'User',
        role: 'eleveur'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('validation');
      expect(response.body.errors).toHaveLength(1);
      expect(response.body.errors[0].field).toBe('email');
    });

    it('devrait rejeter un utilisateur avec des mots de passe non correspondants', async () => {
      const userData = {
        username: 'testuser3',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'differentpassword',
        first_name: 'Test',
        last_name: 'User',
        role: 'eleveur'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.errors.some(err => err.field === 'confirmPassword')).toBe(true);
    });

    it('devrait rejeter un utilisateur avec un email déjà utilisé', async () => {
      // Créer un premier utilisateur
      await testUtils.createTestUser({
        email: '<EMAIL>',
        username: 'firstuser'
      });

      const userData = {
        username: 'seconduser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        first_name: 'Second',
        last_name: 'User',
        role: 'eleveur'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('existe déjà');
    });
  });

  describe('POST /api/auth/login', () => {
    let testUser;

    beforeEach(async () => {
      // Créer un utilisateur de test
      testUser = await testUtils.createTestUser({
        email: '<EMAIL>',
        username: 'loginuser',
        password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' // password
      });
    });

    it('devrait connecter un utilisateur avec des identifiants valides', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(loginData.email);
      expect(response.body.data.user).not.toHaveProperty('password_hash');
    });

    it('devrait rejeter un utilisateur avec un mot de passe incorrect', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('invalides');
    });

    it('devrait rejeter un utilisateur avec un email inexistant', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('invalides');
    });

    it('devrait rejeter des données de connexion invalides', async () => {
      const loginData = {
        email: 'invalid-email',
        password: ''
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(422);

      expect(response.body.success).toBe(false);
      expect(response.body.errors.length).toBeGreaterThan(0);
    });
  });

  describe('GET /api/auth/profile', () => {
    let testUser;
    let authToken;

    beforeEach(async () => {
      // Créer un utilisateur et obtenir un token
      testUser = await testUtils.createTestUser({
        email: '<EMAIL>',
        username: 'profileuser'
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password'
        });

      authToken = loginResponse.body.data.token;
    });

    it('devrait retourner le profil de l\'utilisateur connecté', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe('<EMAIL>');
      expect(response.body.data).not.toHaveProperty('password_hash');
    });

    it('devrait rejeter une requête sans token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('autorisé');
    });

    it('devrait rejeter une requête avec un token invalide', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('invalide');
    });
  });

  describe('POST /api/auth/logout', () => {
    let testUser;
    let authToken;

    beforeEach(async () => {
      testUser = await testUtils.createTestUser({
        email: '<EMAIL>',
        username: 'logoutuser'
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password'
        });

      authToken = loginResponse.body.data.token;
    });

    it('devrait déconnecter un utilisateur avec succès', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('déconnecté');
    });

    it('devrait rejeter une déconnexion sans token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('devrait limiter les tentatives de connexion excessives', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      // Faire plusieurs tentatives de connexion échouées
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/auth/login')
          .send(loginData);
      }

      // La 6ème tentative devrait être bloquée
      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('tentatives');
    });
  });
});
