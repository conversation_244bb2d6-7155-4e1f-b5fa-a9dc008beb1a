require('dotenv').config();
const db = require('../src/models');

console.log('Testing models initialization...');
console.log('Available models:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));

// Test SmtpConfig model
if (db.SmtpConfig) {
  console.log('✅ SmtpConfig model found');
  console.log('Model details:', db.SmtpConfig);
  console.log('Model methods:', Object.keys(db.SmtpConfig));
} else {
  console.log('❌ SmtpConfig model not found');

  // Check for alternative model names
  const modelNames = Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize');
  const smtpModel = modelNames.find(name => name.toLowerCase().includes('smtp'));
  if (smtpModel) {
    console.log(`Found potential SMTP model: ${smtpModel}`);
    console.log('Model details:', db[smtpModel]);
  }
}

// Test SecuritySettings model
if (db.SecuritySettings) {
  console.log('✅ SecuritySettings model found');
} else {
  console.log('❌ SecuritySettings model not found');

  // Check for alternative model names
  const modelNames = Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize');
  const securityModel = modelNames.find(name => name.toLowerCase().includes('security'));
  if (securityModel) {
    console.log(`Found potential Security model: ${securityModel}`);
  }
}

// Test GeneralConfig model
if (db.GeneralConfig) {
  console.log('✅ GeneralConfig model found');
} else {
  console.log('❌ GeneralConfig model not found');

  // Check for alternative model names
  const modelNames = Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize');
  const generalModel = modelNames.find(name => name.toLowerCase().includes('general'));
  if (generalModel) {
    console.log(`Found potential General model: ${generalModel}`);
  }
}
