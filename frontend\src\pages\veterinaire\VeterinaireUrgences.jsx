import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  Card,
  CardContent,
  IconButton
} from '@mui/material';
import {
  Warning as WarningIcon,
  LocationOn as LocationIcon,
  Message as MessageIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import io from 'socket.io-client';
import axios from 'axios';
import L from 'leaflet';

// Correction des icônes Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

const VeterinaireUrgences = () => {
  const [urgences, setUrgences] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [messages, setMessages] = useState([]);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // Initialisation du socket
    const newSocket = io(process.env.REACT_APP_SOCKET_URL);
    setSocket(newSocket);

    // Chargement initial des données
    fetchUrgences();
    fetchEleveurs();

    // Écoute des messages en temps réel
    newSocket.on('nouveauMessage', (message) => {
      setMessages(prev => [...prev, message]);
    });

    newSocket.on('nouvelleUrgence', (urgence) => {
      setUrgences(prev => [...prev, urgence]);
    });

    return () => newSocket.close();
  }, []);

  const fetchUrgences = async () => {
    try {
      const response = await axios.get('/api/veterinaire/urgences');
      setUrgences(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des urgences:', error);
    }
  };

  const fetchEleveurs = async () => {
    try {
      const response = await axios.get('/api/veterinaire/eleveurs');
      setEleveurs(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des éleveurs:', error);
    }
  };

  const handleUrgenceResponse = async (urgenceId, response) => {
    try {
      await axios.put(`/api/veterinaire/urgences/${urgenceId}`, { response });
      fetchUrgences();
    } catch (error) {
      console.error('Erreur lors de la réponse à l\'urgence:', error);
    }
  };

  const sendMessage = (eleveureId, message) => {
    if (socket) {
      socket.emit('envoyerMessage', {
        eleveurId: eleveureId,
        message: message
      });
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'haute':
        return 'error';
      case 'moyenne':
        return 'warning';
      default:
        return 'info';
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Grid container spacing={3}>
        {/* Liste des urgences */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Urgences en cours
            </Typography>
            <List>
              {urgences.map((urgence) => (
                <ListItem
                  key={urgence.id}
                  sx={{
                    mb: 1,
                    border: 1,
                    borderColor: 'grey.300',
                    borderRadius: 1
                  }}
                >
                  <ListItemIcon>
                    <WarningIcon color={getPriorityColor(urgence.priorite)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={urgence.titre}
                    secondary={
                      <>
                        <Typography variant="body2">{urgence.description}</Typography>
                        <Chip
                          size="small"
                          label={urgence.priorite}
                          color={getPriorityColor(urgence.priorite)}
                          sx={{ mt: 1 }}
                        />
                      </>
                    }
                  />
                  <Box sx={{ ml: 2 }}>
                    <IconButton
                      color="primary"
                      onClick={() => sendMessage(urgence.eleveurId, 'En route')}
                    >
                      <MessageIcon />
                    </IconButton>
                    <IconButton
                      color="secondary"
                      href={`tel:${urgence.eleveur?.telephone}`}
                    >
                      <PhoneIcon />
                    </IconButton>
                  </Box>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Carte des éleveurs */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: '500px' }}>
            <MapContainer
              center={[36.7538, 3.0588]} // Coordonnées d'Alger
              zoom={7}
              style={{ height: '100%', width: '100%' }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              {eleveurs.map((eleveur) => (
                <Marker
                  key={eleveur.id}
                  position={[eleveur.latitude, eleveur.longitude]}
                >
                  <Popup>
                    <Typography variant="subtitle2">{eleveur.nom}</Typography>
                    <Typography variant="body2">{eleveur.adresse}</Typography>
                    <Button
                      size="small"
                      startIcon={<MessageIcon />}
                      onClick={() => sendMessage(eleveur.id, 'Bonjour, puis-je vous aider ?')}
                    >
                      Contacter
                    </Button>
                  </Popup>
                </Marker>
              ))}
            </MapContainer>
          </Paper>
        </Grid>

        {/* Chat en temps réel */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Messages récents
            </Typography>
            <List>
              {messages.map((message, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={message.expediteur}
                    secondary={message.contenu}
                  />
                  <Typography variant="caption" color="textSecondary">
                    {new Date(message.date).toLocaleTimeString()}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VeterinaireUrgences;