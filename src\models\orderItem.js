const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class OrderItem extends Model {
    static associate(models) {
      OrderItem.belongsTo(models.Order, {
        foreignKey: 'order_id',
        as: 'order'
      });
      OrderItem.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
    }

    // Find items by order
    static async findByOrder(orderId) {
      return await this.findAll({
        where: { order_id: orderId },
        include: [{
          model: sequelize.models.Product,
          as: 'product'
        }]
      });
    }

    // Calculate revenue by product
    static async calculateRevenueByProduct(productId, startDate = null, endDate = null) {
      const where = { product_id: productId };
      if (startDate && endDate) {
        where.created_at = {
          [sequelize.Op.between]: [startDate, endDate]
        };
      }

      const result = await this.findAll({
        where,
        attributes: [
          [sequelize.fn('SUM', sequelize.col('quantity')), 'total_quantity'],
          [sequelize.fn('SUM', sequelize.col('total_price')), 'total_revenue']
        ],
        raw: true
      });

      return {
        totalQuantity: parseInt(result[0].total_quantity || 0),
        totalRevenue: parseFloat(result[0].total_revenue || 0)
      };
    }

    // Process return for item
    async processReturn(quantity, reason) {
      const transaction = await sequelize.transaction();
      try {
        // Update return quantity
        await this.update({
          return_quantity: sequelize.literal(`return_quantity + ${quantity}`),
          status: 'returned'
        }, { transaction });

        // Update product stock
        await sequelize.models.Product.increment({
          stock_quantity: quantity
        }, {
          where: { id: this.product_id },
          transaction
        });

        // Create return record
        await sequelize.models.OrderReturn.create({
          order_id: this.order_id,
          product_id: this.product_id,
          quantity,
          reason,
          refund_amount: (this.unit_price * quantity)
        }, { transaction });

        await transaction.commit();
        return true;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }
  }

  OrderItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'orders',
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    return_quantity: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    total_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(50),
      defaultValue: 'pending'
    },
    product_snapshot: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    }
  }, {
    sequelize,
    modelName: 'OrderItem',
    tableName: 'order_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: async (item) => {
        // Calculate total price
        item.total_price = (item.unit_price * item.quantity) - (item.discount_amount || 0);

        // Store product snapshot
        const product = await sequelize.models.Product.findByPk(item.product_id);
        if (product) {
          item.product_snapshot = {
            name: product.name,
            description: product.description,
            category: product.category,
            sku: product.sku
          };
        }
      },
      beforeUpdate: async (item) => {
        if (item.changed('quantity') || item.changed('unit_price') || item.changed('discount_amount')) {
          item.total_price = (item.unit_price * item.quantity) - (item.discount_amount || 0);
        }
      },
      afterCreate: async (item) => {
        // Decrement product stock
        await sequelize.models.Product.decrement({
          stock_quantity: item.quantity
        }, {
          where: { id: item.product_id }
        });
      }
    },
    indexes: [
      { fields: ['order_id'] },
      { fields: ['product_id'] },
      { fields: ['status'] }
    ]
  });

  return OrderItem;
};
