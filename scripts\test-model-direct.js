require('dotenv').config();
const { Sequelize, DataTypes } = require('sequelize');
const colors = require('colors');

// Create Sequelize instance from environment variables
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log,
  }
);

// Define the SmtpConfig model directly
const SmtpConfig = sequelize.define('SmtpConfig', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  host: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  port: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  secure: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  user: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  pass: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  fromName: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  fromEmail: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  replyTo: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  testEmailRecipient: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  isEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  }
}, {
  tableName: 'smtp_configurations',
  timestamps: true,
});

async function testModel() {
  try {
    console.log('Testing direct model access...'.yellow.bold);

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully'.green);

    // Try to find SMTP configuration
    try {
      const smtpConfig = await SmtpConfig.findOne();
      if (smtpConfig) {
        console.log('✅ SMTP configuration found with direct model:'.green);
        console.log(JSON.stringify(smtpConfig, null, 2));
      } else {
        console.log('❌ No SMTP configuration found in database'.red);
      }

      // Test findOne method on model
      console.log('\nTesting model methods:'.yellow);
      console.log('SmtpConfig.findOne exists:'.cyan, typeof SmtpConfig.findOne === 'function');

      // Log all model methods
      console.log('Available methods on model:'.cyan);
      const methods = Object.getOwnPropertyNames(SmtpConfig)
        .filter(prop => typeof SmtpConfig[prop] === 'function');
      console.log(methods);

    } catch (error) {
      console.log('❌ Error accessing SMTP configuration:'.red, error.message);
      console.log('Error stack:'.red, error.stack);
    }

  } catch (error) {
    console.error('❌ Database connection error:'.red, error.message);
  } finally {
    await sequelize.close();
  }
}

testModel();
