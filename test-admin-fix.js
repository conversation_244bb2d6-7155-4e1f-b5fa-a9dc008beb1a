const http = require('http');

async function testAdminEndpoint() {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************.aBqYCJ_bfvKhvtKuNjhKJcvC_Ht_D-t3FZHdmOGh1Yo';

  const makeRequest = (path) => {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3003,
        path: path,
        method: 'GET',
        headers: {
          'x-auth-token': token,
          'Authorization': `Bearer ${token}`
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            resolve({ status: res.statusCode, data: jsonData });
          } catch (err) {
            resolve({ status: res.statusCode, data: data });
          }
        });
      });

      req.on('error', (err) => reject(err));
      req.end();
    });
  };

  try {
    console.log('Testing admin users endpoint...');
    const response = await makeRequest('/api/admin/users?role=eleveur&page=1&limit=10');

    if (response.status === 200) {
      console.log('✅ Success! Response:', JSON.stringify(response.data, null, 2));
    } else {
      console.log('❌ Error:', response.status, response.data);
    }
  } catch (error) {
    console.log('❌ Request Error:', error.message);
  }

  try {
    console.log('\nTesting stats endpoint...');
    const statsResponse = await makeRequest('/api/admin/stats');

    if (statsResponse.status === 200) {
      console.log('✅ Stats Success! Response:', JSON.stringify(statsResponse.data, null, 2));
    } else {
      console.log('❌ Stats Error:', statsResponse.status, statsResponse.data);
    }
  } catch (error) {
    console.log('❌ Stats Request Error:', error.message);
  }
}

testAdminEndpoint();
