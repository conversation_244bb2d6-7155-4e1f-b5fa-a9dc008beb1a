require('dotenv').config({ path: 'K:/Projets_Sites_Web/Poultray-dz-TraeDev/Web_App/.env' });
const { Pool } = require('pg');

// Utiliser des valeurs par défaut si les variables d'environnement ne sont pas définies
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '5432';
const DB_NAME = process.env.DB_NAME || 'poultraydz';

console.log('Connexion à la base de données avec les paramètres suivants:');
console.log(`User: ${DB_USER}`);
console.log(`Host: ${DB_HOST}`);
console.log(`Database: ${DB_NAME}`);
console.log(`Port: ${DB_PORT}`);

// Utiliser une chaîne de connexion au lieu des paramètres individuels
const connectionString = `postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
console.log(`Connection string: ${connectionString}`);

const pool = new Pool({
  connectionString,
});

async function createUsersTable() {
  const query = `
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      username VARCHAR(100) NOT NULL UNIQUE,
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      role VARCHAR(20) NOT NULL DEFAULT 'eleveur',
      first_name VARCHAR(100),
      last_name VARCHAR(100),
      profile_id INTEGER,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_login TIMESTAMP,
      status VARCHAR(20) DEFAULT 'active',
      preferences JSONB DEFAULT '{}'::jsonb
    )`;

  try {
    await pool.query(query);
    console.log('Table users créée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création de la table users:', error);
    return false;
  }
}

async function createEleveursTable() {
  const query = `
    CREATE TABLE IF NOT EXISTS eleveurs (
      id SERIAL PRIMARY KEY,
      nom VARCHAR(100) NOT NULL,
      prenom VARCHAR(100) NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      telephone VARCHAR(20),
      adresse TEXT,
      date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      statut VARCHAR(20) DEFAULT 'actif'
    )`;

  try {
    await pool.query(query);
    console.log('Table eleveurs créée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création de la table eleveurs:', error);
    return false;
  }
}

async function createVolaillesTable() {
  const query = `
    CREATE TABLE IF NOT EXISTS volailles (
      id SERIAL PRIMARY KEY,
      eleveur_id INTEGER REFERENCES eleveurs(id),
      espece VARCHAR(50) NOT NULL,
      race VARCHAR(50),
      age INTEGER,
      poids DECIMAL(5,2),
      quantite INTEGER NOT NULL,
      prix_unitaire DECIMAL(10,2) NOT NULL,
      date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      statut VARCHAR(20) DEFAULT 'disponible',
      description TEXT
    )`;

  try {
    await pool.query(query);
    console.log('Table volailles créée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création de la table volailles:', error);
    return false;
  }
}

async function createNotificationsTable() {
  const query = `
    CREATE TABLE IF NOT EXISTS notifications (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      type VARCHAR(50) NOT NULL,
      data JSONB DEFAULT '{}'::jsonb,
      is_read BOOLEAN DEFAULT false,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`;

  try {
    await pool.query(query);
    console.log('Table notifications créée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création de la table notifications:', error);
    return false;
  }
}

async function initTables() {
  try {
    console.log('Initialisation des tables...');

    // Test de connexion à la base de données
    await pool.query('SELECT NOW()');
    console.log('Connexion à la base de données établie avec succès');

    // Création des tables dans l'ordre correct
    const usersCreated = await createUsersTable();
    const eleveursCreated = await createEleveursTable();
    const volaillesCreated = await createVolaillesTable();
    const notificationsCreated = await createNotificationsTable();

    if (usersCreated && eleveursCreated && volaillesCreated && notificationsCreated) {
      console.log('Toutes les tables ont été créées avec succès');
    } else {
      console.log('Certaines tables n\'ont pas pu être créées');
    }

    // Fermer la connexion à la base de données
    await pool.end();
    console.log('Connexion à la base de données fermée');

    process.exit(0);
  } catch (error) {
    console.error('Erreur lors de l\'initialisation des tables:', error);
    process.exit(1);
  }
}

initTables();
