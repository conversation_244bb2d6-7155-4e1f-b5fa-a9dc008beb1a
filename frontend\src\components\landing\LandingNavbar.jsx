import React, { useState, useEffect } from 'react';
import { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Box, Container, IconButton, Drawer, List, ListItem, ListItemText, useMediaQuery, useScrollTrigger } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Link as RouterLink } from 'react-router-dom';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import logo from '../../assets/images/logo.svg';
import LanguageSelector from '../LanguageSelector';
import { useLanguage } from '../../contexts/LanguageContext';
import { landingTranslations } from '../../translations/landing';

// Navbar qui change d'apparence au défilement
const StyledAppBar = styled(AppBar)(({ theme, trigger }) => ({
  boxShadow: trigger ? theme.shadows[4] : 'none',
  backgroundColor: trigger ? theme.palette.background.paper : 'transparent',
  color: trigger ? theme.palette.text.primary : theme.palette.common.white,
  transition: 'all 0.3s ease',
  position: 'fixed',
  '& .MuiAppBar-root': {
    // Éviter que l'attribut trigger soit transmis au DOM
    trigger: undefined,
  },
}));

const NavButton = styled(Button)(({ theme, trigger }) => ({
  marginLeft: theme.spacing(2),
  color: trigger ? theme.palette.primary.main : theme.palette.common.white,
  fontWeight: 500,
  '&:hover': {
    backgroundColor: trigger ? 'rgba(76, 175, 80, 0.08)' : 'rgba(255, 255, 255, 0.1)',
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  '& img': {
    height: 40,
    marginRight: theme.spacing(1),
  },
}));

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 250,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.common.white,
    padding: theme.spacing(2),
  },
}));

const DrawerHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(2),
}));

const DrawerItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
}));

const ActionButton = styled(Button)(({ theme, trigger }) => ({
  borderRadius: 30,
  padding: theme.spacing(1, 3),
  backgroundColor: trigger ? theme.palette.primary.main : theme.palette.secondary.main,
  color: theme.palette.common.white,
  fontWeight: 'bold',
  boxShadow: '0 4px 10px rgba(0, 0, 0, 0.15)',
  '&:hover': {
    backgroundColor: trigger ? theme.palette.primary.dark : theme.palette.secondary.dark,
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 15px rgba(0, 0, 0, 0.2)',
  },
  transition: 'all 0.3s ease',
}));

function LandingNavbar() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const { language } = useLanguage();
  const translations = landingTranslations[language];

  // Détecte le défilement pour changer l'apparence de la navbar
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 100,
  });

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Fonction pour faire défiler vers une section
  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
      setDrawerOpen(false);
    }
  };

  const navItems = [
    { name: translations.navbar.home, path: 'accueil' },
    { name: translations.navbar.features, path: 'fonctionnalites' },
    { name: translations.navbar.pricing, path: 'tarifs' },
    { name: translations.navbar.faq, path: 'faq' },
    { name: translations.navbar.contact, path: 'contact' },
  ];

  return (
    <>
      <StyledAppBar trigger={trigger ? "true" : "false"}>
        <Container>
          <Toolbar disableGutters>
            <LogoContainer sx={{ flexGrow: 1 }}>
              <img src={logo} alt="Poultray DZ Logo" />
              <Typography
                variant="h6"
                component={RouterLink}
                to="/"
                sx={{
                  textDecoration: 'none',
                  color: trigger ? 'primary.main' : 'white',
                  fontWeight: 'bold',
                }}
              >
                Poultray DZ
              </Typography>
            </LogoContainer>

            {isMobile ? (
              <IconButton
                edge="end"
                color="inherit"
                aria-label="menu"
                onClick={toggleDrawer}
              >
                <MenuIcon />
              </IconButton>
            ) : (
              <>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {navItems.map((item) => (
                    <NavButton
                      key={item.name}
                      onClick={() => scrollToSection(item.path)}
                      trigger={trigger ? "true" : "false"}
                    >
                      {item.name}
                    </NavButton>
                  ))}
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LanguageSelector color={trigger ? 'primary' : 'inherit'} />
                    <ActionButton
                      variant="contained"
                      component={RouterLink}
                      to="/login"
                      trigger={trigger ? "true" : "false"}
                      sx={{ ml: 2, mr: 1, backgroundColor: trigger ? 'secondary.main' : 'primary.main' }}
                    >
                      {translations.navbar.login || 'Connexion'}
                    </ActionButton>
                    <ActionButton
                      variant="contained"
                      component={RouterLink}
                      to="/register"
                      trigger={trigger ? "true" : "false"}
                    >
                      {translations.navbar.createAccount}
                    </ActionButton>
                  </Box>
                </Box>
              </>
            )}
          </Toolbar>
        </Container>
      </StyledAppBar>

      {/* Drawer pour mobile */}
      <StyledDrawer
        anchor="right"
        open={drawerOpen}
        onClose={toggleDrawer}
      >
        <DrawerHeader>
          <Typography variant="h6" fontWeight="bold">
            Menu
          </Typography>
          <IconButton onClick={toggleDrawer} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </DrawerHeader>
        <List>
          {navItems.map((item) => (
            <DrawerItem
              key={item.name}
              onClick={() => scrollToSection(item.path)}
              button
            >
              <ListItemText primary={item.name} />
            </DrawerItem>
          ))}
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <LanguageSelector variant="text" color="white" />
            </Box>
            <Button
              variant="contained"
              fullWidth
              component={RouterLink}
              to="/login"
              sx={{
                backgroundColor: 'white',
                color: 'secondary.main',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                },
                mb: 1
              }}
            >
              {translations.navbar.login || 'Connexion'}
            </Button>
            <Button
              variant="contained"
              fullWidth
              component={RouterLink}
              to="/register"
              sx={{
                backgroundColor: 'white',
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                },
              }}
            >
              {translations.navbar.createAccount}
            </Button>
          </Box>
        </List>
      </StyledDrawer>

      {/* Espace pour compenser la navbar fixe */}
      <Toolbar />
    </>
  );
}

export default LandingNavbar;
