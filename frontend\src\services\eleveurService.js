/**
 * Service API pour les fonctionnalités éleveur
 */

import axiosInstance from '../utils/axiosConfig';

const eleveurAPI = {
  // === GESTION DES VENTES ===
  
  // Récupérer toutes les ventes de l'éleveur
  getVentes: () => {
    return axiosInstance.get('/api/eleveur/ventes');
  },

  // Créer une nouvelle vente
  createVente: (venteData) => {
    return axiosInstance.post('/api/eleveur/ventes', venteData);
  },

  // Mettre à jour une vente
  updateVente: (venteId, venteData) => {
    return axiosInstance.put(`/api/eleveur/ventes/${venteId}`, venteData);
  },

  // Supprimer une vente
  deleteVente: (venteId) => {
    return axiosInstance.delete(`/api/eleveur/ventes/${venteId}`);
  },

  // Récupérer les statistiques des ventes
  getVentesStats: () => {
    return axiosInstance.get('/api/eleveur/ventes/stats');
  },

  // === STATISTIQUES DÉTAILLÉES ===
  
  // Récupérer les statistiques détaillées
  getDetailedStatistics: (period = '6months') => {
    return axiosInstance.get(`/api/eleveur/statistics/detailed?period=${period}`);
  },

  // Récupérer les KPIs
  getKPIs: (period = '6months') => {
    return axiosInstance.get(`/api/eleveur/statistics/kpis?period=${period}`);
  },

  // Récupérer les données de production
  getProductionData: (period = '6months') => {
    return axiosInstance.get(`/api/eleveur/statistics/production?period=${period}`);
  },

  // Récupérer les données de santé
  getHealthData: (period = '6months') => {
    return axiosInstance.get(`/api/eleveur/statistics/health?period=${period}`);
  },

  // Récupérer les données financières
  getFinancialData: (period = '6months') => {
    return axiosInstance.get(`/api/eleveur/statistics/financial?period=${period}`);
  },

  // === GESTION DES FERMES ===
  
  // Récupérer les fermes de l'éleveur
  getFermes: () => {
    return axiosInstance.get('/api/eleveur/fermes');
  },

  // Récupérer une ferme spécifique
  getFerme: (fermeId) => {
    return axiosInstance.get(`/api/eleveur/fermes/${fermeId}`);
  },

  // Mettre à jour les informations d'une ferme
  updateFerme: (fermeId, fermeData) => {
    return axiosInstance.put(`/api/eleveur/fermes/${fermeId}`, fermeData);
  },

  // === GESTION DES VOLAILLES ===
  
  // Récupérer toutes les volailles
  getVolailles: () => {
    return axiosInstance.get('/api/eleveur/volailles');
  },

  // Récupérer les volailles par ferme
  getVolaillesByFerme: (fermeId) => {
    return axiosInstance.get(`/api/eleveur/volailles?ferme_id=${fermeId}`);
  },

  // Créer un nouveau lot de volailles
  createVolaille: (volailleData) => {
    return axiosInstance.post('/api/eleveur/volailles', volailleData);
  },

  // Mettre à jour un lot de volailles
  updateVolaille: (volailleId, volailleData) => {
    return axiosInstance.put(`/api/eleveur/volailles/${volailleId}`, volailleData);
  },

  // Supprimer un lot de volailles
  deleteVolaille: (volailleId) => {
    return axiosInstance.delete(`/api/eleveur/volailles/${volailleId}`);
  },

  // === PRODUCTION ===
  
  // Enregistrer la production quotidienne
  recordProduction: (productionData) => {
    return axiosInstance.post('/api/eleveur/production', productionData);
  },

  // Récupérer l'historique de production
  getProductionHistory: (period = '1month') => {
    return axiosInstance.get(`/api/eleveur/production/history?period=${period}`);
  },

  // === SANTÉ ET VÉTÉRINAIRE ===
  
  // Enregistrer un événement de santé
  recordHealthEvent: (healthData) => {
    return axiosInstance.post('/api/eleveur/health', healthData);
  },

  // Récupérer l'historique de santé
  getHealthHistory: () => {
    return axiosInstance.get('/api/eleveur/health/history');
  },

  // Programmer une consultation vétérinaire
  scheduleConsultation: (consultationData) => {
    return axiosInstance.post('/api/eleveur/consultations', consultationData);
  },

  // === ALIMENTATION ===
  
  // Enregistrer la consommation d'aliments
  recordFeeding: (feedingData) => {
    return axiosInstance.post('/api/eleveur/feeding', feedingData);
  },

  // Récupérer l'historique d'alimentation
  getFeedingHistory: () => {
    return axiosInstance.get('/api/eleveur/feeding/history');
  },

  // === ALERTES ===
  
  // Récupérer les alertes actives
  getAlerts: () => {
    return axiosInstance.get('/api/eleveur/alerts');
  },

  // Marquer une alerte comme lue
  markAlertAsRead: (alertId) => {
    return axiosInstance.put(`/api/eleveur/alerts/${alertId}/read`);
  },

  // === RAPPORTS ===
  
  // Générer un rapport personnalisé
  generateReport: (reportConfig) => {
    return axiosInstance.post('/api/eleveur/reports/generate', reportConfig);
  },

  // Récupérer les rapports disponibles
  getAvailableReports: () => {
    return axiosInstance.get('/api/eleveur/reports');
  },

  // Télécharger un rapport
  downloadReport: (reportId) => {
    return axiosInstance.get(`/api/eleveur/reports/${reportId}/download`, {
      responseType: 'blob'
    });
  },

  // === NOTIFICATIONS ===
  
  // Récupérer les notifications
  getNotifications: () => {
    return axiosInstance.get('/api/eleveur/notifications');
  },

  // Marquer une notification comme lue
  markNotificationAsRead: (notificationId) => {
    return axiosInstance.put(`/api/eleveur/notifications/${notificationId}/read`);
  },

  // === PARAMÈTRES ===
  
  // Récupérer les paramètres de l'éleveur
  getSettings: () => {
    return axiosInstance.get('/api/eleveur/settings');
  },

  // Mettre à jour les paramètres
  updateSettings: (settings) => {
    return axiosInstance.put('/api/eleveur/settings', settings);
  },

  // === DASHBOARD ===
  
  // Récupérer les données du dashboard
  getDashboardData: () => {
    return axiosInstance.get('/api/eleveur/dashboard');
  },

  // Récupérer les métriques en temps réel
  getRealTimeMetrics: () => {
    return axiosInstance.get('/api/eleveur/metrics/realtime');
  }
};

export { eleveurAPI };
export default eleveurAPI;
