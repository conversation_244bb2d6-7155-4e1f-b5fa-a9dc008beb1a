import axios from 'axios';

// Créer une instance axios avec la configuration de base
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api'
    : 'http://localhost:3003/api',
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000 // 10 secondes de timeout
});

// Intercepteur pour ajouter le token à chaque requête
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      // Utiliser l'en-tête Authorization standard avec Bearer
      config.headers['Authorization'] = `Bearer ${token}`;
      // Conserver également x-auth-token pour la compatibilité avec le backend existant
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs d'authentification
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Si l'erreur est 401 (non autorisé) et que nous n'avons pas déjà essayé de rafraîchir
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Essayer de récupérer les informations d'identification stockées
        const userCredentials = JSON.parse(localStorage.getItem('user_credentials'));

        if (userCredentials) {
          // Réauthentifier l'utilisateur
          const response = await axios.post(
            `${originalRequest.baseURL}/auth/login`,
            userCredentials
          );

          const { token } = response.data;

          // Mettre à jour le token dans le stockage local
          localStorage.setItem('token', token);

          // Mettre à jour les en-têtes d'autorisation pour la requête originale
          originalRequest.headers['Authorization'] = `Bearer ${token}`;
          originalRequest.headers['x-auth-token'] = token;

          // Réessayer la requête originale
          return axios(originalRequest);
        }
      } catch (refreshError) {
        console.error('Échec de réauthentification:', refreshError);
      }

      // En cas d'échec du rafraîchissement, déconnecter l'utilisateur
      localStorage.removeItem('token');
      localStorage.removeItem('user_credentials');

      // Rediriger vers la page de connexion
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// Méthodes API simplifiées
const apiService = {
  // Méthodes d'authentification
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    me: () => api.get('/auth/me'),
    logout: () => {
      localStorage.removeItem('token');
      localStorage.removeItem('user_credentials');
      return Promise.resolve();
    }
  },

  // Méthodes pour l'admin
  admin: {
    getStats: () => api.get('/admin/stats'),
    getUsers: () => api.get('/admin/users'),
    updateUser: (userId, userData) => api.put(`/admin/users/${userId}`, userData),
    deleteUser: (userId) => api.delete(`/admin/users/${userId}`),
    getRoles: () => api.get('/admin/roles'),
    createRole: (roleData) => api.post('/admin/roles', roleData),
    updateRole: (roleId, roleData) => api.put(`/admin/roles/${roleId}`, roleData),
    deleteRole: (roleId) => api.delete(`/admin/roles/${roleId}`),
    getPlans: () => api.get('/admin/plans'),
    createPlan: (planData) => api.post('/admin/plans', planData),
    updatePlan: (planId, planData) => api.put(`/admin/plans/${planId}`, planData),
    deletePlan: (planId) => api.delete(`/admin/plans/${planId}`)
  }
};

export default apiService;
export { api }; // Exporter également l'instance axios pour une utilisation directe si nécessaire
