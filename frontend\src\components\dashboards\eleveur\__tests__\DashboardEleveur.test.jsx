import React from 'react';
import { render, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { DashboardEleveur } from '../index';

// Mock des composants enfants
jest.mock('../PoussinManagement', () => () => <div data-testid="poussin-management">PoussinManagement</div>);
jest.mock('../EggProductionManagement', () => () => <div data-testid="egg-production">EggProductionManagement</div>);
jest.mock('../VetConsultations', () => () => <div data-testid="vet-consultations">VetConsultations</div>);
jest.mock('../AlerteStock', () => () => <div data-testid="alerte-stock">AlerteStock</div>);
jest.mock('../DailyCollection', () => () => <div data-testid="daily-collection">DailyCollection</div>);
jest.mock('../EggSalesManagement', () => () => <div data-testid="egg-sales">EggSalesManagement</div>);
jest.mock('../ActiveTreatments', () => () => <div data-testid="active-treatments">ActiveTreatments</div>);
jest.mock('../VaccinationSchedule', () => () => <div data-testid="vaccination-schedule">VaccinationSchedule</div>);
jest.mock('../EmergencyVet', () => () => <div data-testid="emergency-vet">EmergencyVet</div>);
jest.mock('../StockDashboard', () => () => <div data-testid="stock-dashboard">StockDashboard</div>);
jest.mock('../AlertHistory', () => () => <div data-testid="alert-history">AlertHistory</div>);
jest.mock('../AlertSettings', () => () => <div data-testid="alert-settings">AlertSettings</div>);

describe('DashboardEleveur Component', () => {
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };

  test.skip('renders dashboard title', () => {
    renderWithI18n(<DashboardEleveur />);
    expect(screen.getByText('dashboard.eleveur.title')).toBeInTheDocument();
  });

  test.skip('renders all section titles', () => {
    renderWithI18n(<DashboardEleveur />);
    expect(screen.getByText('dashboard.sections.poussins')).toBeInTheDocument();
    expect(screen.getByText('dashboard.sections.eggs')).toBeInTheDocument();
    expect(screen.getByText('dashboard.sections.veterinary')).toBeInTheDocument();
    expect(screen.getByText('dashboard.sections.alerts')).toBeInTheDocument();
  });

  test.skip('renders all child components', () => {
    renderWithI18n(<DashboardEleveur />);
    // Gestion des Poussins
    expect(screen.getByTestId('poussin-management')).toBeInTheDocument();
    // Production d'Œufs
    expect(screen.getByTestId('daily-collection')).toBeInTheDocument();
    expect(screen.getByTestId('egg-production')).toBeInTheDocument();
    expect(screen.getByTestId('egg-sales')).toBeInTheDocument();
    // Suivi Vétérinaire
    expect(screen.getByTestId('vet-consultations')).toBeInTheDocument();
    expect(screen.getByTestId('active-treatments')).toBeInTheDocument();
    expect(screen.getByTestId('vaccination-schedule')).toBeInTheDocument();
    expect(screen.getByTestId('emergency-vet')).toBeInTheDocument();
    // Alertes et Stocks
    expect(screen.getByTestId('alerte-stock')).toBeInTheDocument();
    expect(screen.getByTestId('stock-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('alert-history')).toBeInTheDocument();
    expect(screen.getByTestId('alert-settings')).toBeInTheDocument();
  });

  test.skip('renders components in correct grid layout', () => {
    renderWithI18n(<DashboardEleveur />);
    // Vérification de la structure du grid
    const gridContainers = document.querySelectorAll('.MuiGrid-container');
    expect(gridContainers).toHaveLength(3); // Eggs, Veterinary, and Alerts sections
    // Vérification des Paper containers
    const paperContainers = document.querySelectorAll('.MuiPaper-root');
    expect(paperContainers).toHaveLength(4); // One for each main section
  });
});
