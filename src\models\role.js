const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Role extends Model {
  static associate(models) {
    // Définir l'association avec le modèle User
    Role.hasMany(models.User, {
      foreignKey: 'role_id',
      as: 'users'
    });
  }
}

Role.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT
  },
  permissions: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  sequelize,
  modelName: 'Role',
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Méthodes statiques
Role.findByName = async function(name) {
  return await this.findOne({ where: { name } });
};

Role.updatePermissions = async function(id, permissions) {
  return await this.update(
    { permissions },
    { where: { id } }
  );
};

Role.toggleActive = async function(id) {
  const role = await this.findByPk(id);
  if (role) {
    return await role.update({ is_active: !role.is_active });
  }
  return null;
};

  return Role;
};
