import { useState, useEffect, useCallback, useRef } from 'react';
import performanceService, { PERFORMANCE_METRICS } from '../services/performanceService';

/**
 * Hook pour surveiller les performances d'un composant
 */
export const usePerformance = (componentName, options = {}) => {
  const {
    trackRenders = true,
    trackMounts = true,
    trackUpdates = true,
    logSlowRenders = true,
    slowRenderThreshold = 16
  } = options;

  const [renderCount, setRenderCount] = useState(0);
  const [averageRenderTime, setAverageRenderTime] = useState(0);
  const [lastRenderTime, setLastRenderTime] = useState(0);
  const [mountTime, setMountTime] = useState(null);

  const renderTimes = useRef([]);
  const mountStartTime = useRef(null);
  const renderStartTime = useRef(null);

  // Démarrer le chrono de rendu
  const startRenderTimer = useCallback(() => {
    if (trackRenders) {
      renderStartTime.current = performance.now();
    }
  }, [trackRenders]);

  // Arrêter le chrono de rendu
  const endRenderTimer = useCallback(() => {
    if (trackRenders && renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      
      renderTimes.current.push(renderTime);
      setLastRenderTime(renderTime);
      setRenderCount(prev => prev + 1);
      
      // Calculer la moyenne
      const average = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length;
      setAverageRenderTime(average);
      
      // Garder seulement les 50 derniers rendus
      if (renderTimes.current.length > 50) {
        renderTimes.current.shift();
      }
      
      // Logger les rendus lents
      if (logSlowRenders && renderTime > slowRenderThreshold) {
        console.warn(`🐌 Rendu lent: ${componentName} (${renderTime.toFixed(2)}ms)`);
      }
      
      renderStartTime.current = null;
    }
  }, [trackRenders, componentName, logSlowRenders, slowRenderThreshold]);

  // Mesurer le temps de montage
  useEffect(() => {
    if (trackMounts) {
      mountStartTime.current = performance.now();
      
      return () => {
        if (mountStartTime.current) {
          const totalMountTime = performance.now() - mountStartTime.current;
          setMountTime(totalMountTime);
          console.log(`⏱️ Temps de montage ${componentName}: ${totalMountTime.toFixed(2)}ms`);
        }
      };
    }
  }, [trackMounts, componentName]);

  // Démarrer le timer à chaque rendu
  useEffect(() => {
    startRenderTimer();
    return endRenderTimer;
  });

  return {
    renderCount,
    averageRenderTime,
    lastRenderTime,
    mountTime,
    startRenderTimer,
    endRenderTimer
  };
};

/**
 * Hook pour mesurer les performances d'opérations asynchrones
 */
export const useAsyncPerformance = () => {
  const [operations, setOperations] = useState(new Map());

  const measureAsync = useCallback(async (name, operation) => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setOperations(prev => {
        const newMap = new Map(prev);
        if (!newMap.has(name)) {
          newMap.set(name, []);
        }
        newMap.get(name).push({
          duration,
          timestamp: Date.now(),
          success: true
        });
        return newMap;
      });
      
      console.log(`✅ ${name}: ${duration.toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setOperations(prev => {
        const newMap = new Map(prev);
        if (!newMap.has(name)) {
          newMap.set(name, []);
        }
        newMap.get(name).push({
          duration,
          timestamp: Date.now(),
          success: false,
          error: error.message
        });
        return newMap;
      });
      
      console.error(`❌ ${name} échoué après ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, []);

  const getOperationStats = useCallback((name) => {
    const ops = operations.get(name) || [];
    if (ops.length === 0) return null;

    const durations = ops.map(op => op.duration);
    const successCount = ops.filter(op => op.success).length;
    
    return {
      count: ops.length,
      successRate: (successCount / ops.length) * 100,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      lastOperation: ops[ops.length - 1]
    };
  }, [operations]);

  return {
    measureAsync,
    operations,
    getOperationStats
  };
};

/**
 * Hook pour surveiller les performances globales de l'application
 */
export const useGlobalPerformance = (options = {}) => {
  const {
    autoStart = true,
    reportInterval = 30000 // 30 secondes
  } = options;

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [stats, setStats] = useState(null);
  const [recommendations, setRecommendations] = useState([]);

  // Démarrer/arrêter la surveillance
  const startMonitoring = useCallback(() => {
    performanceService.startMonitoring();
    setIsMonitoring(true);
  }, []);

  const stopMonitoring = useCallback(() => {
    performanceService.stopMonitoring();
    setIsMonitoring(false);
  }, []);

  // Générer un rapport
  const generateReport = useCallback(() => {
    const report = performanceService.generateReport();
    setStats(report.stats);
    setRecommendations(report.recommendations);
    return report;
  }, []);

  // Démarrer automatiquement si demandé
  useEffect(() => {
    if (autoStart) {
      startMonitoring();
    }

    return () => {
      if (isMonitoring) {
        stopMonitoring();
      }
    };
  }, [autoStart, startMonitoring, stopMonitoring, isMonitoring]);

  // Générer des rapports périodiques
  useEffect(() => {
    if (!isMonitoring || !reportInterval) return;

    const interval = setInterval(() => {
      generateReport();
    }, reportInterval);

    return () => clearInterval(interval);
  }, [isMonitoring, reportInterval, generateReport]);

  return {
    isMonitoring,
    stats,
    recommendations,
    startMonitoring,
    stopMonitoring,
    generateReport
  };
};

/**
 * Hook pour optimiser les re-rendus avec des dépendances
 */
export const useOptimizedCallback = (callback, deps, debugName) => {
  const memoizedCallback = useCallback(callback, deps);
  
  // En mode développement, logger les changements de dépendances
  const prevDeps = useRef(deps);
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && debugName) {
      const changedDeps = deps.filter((dep, index) => dep !== prevDeps.current[index]);
      if (changedDeps.length > 0) {
        console.log(`🔄 ${debugName}: dépendances changées`, {
          previous: prevDeps.current,
          current: deps,
          changed: changedDeps
        });
      }
      prevDeps.current = deps;
    }
  }, deps);

  return memoizedCallback;
};

/**
 * Hook pour optimiser les valeurs calculées
 */
export const useOptimizedMemo = (factory, deps, debugName) => {
  const memoizedValue = useMemo(factory, deps);
  
  // En mode développement, logger les recalculs
  const calculationCount = useRef(0);
  const prevDeps = useRef(deps);
  
  useEffect(() => {
    calculationCount.current++;
    
    if (process.env.NODE_ENV === 'development' && debugName) {
      const changedDeps = deps.filter((dep, index) => dep !== prevDeps.current[index]);
      console.log(`🧮 ${debugName}: recalculé (${calculationCount.current} fois)`, {
        previous: prevDeps.current,
        current: deps,
        changed: changedDeps
      });
      prevDeps.current = deps;
    }
  }, deps);

  return memoizedValue;
};

/**
 * Hook pour détecter les fuites mémoire
 */
export const useMemoryLeak = (componentName) => {
  const [memoryUsage, setMemoryUsage] = useState(null);
  const intervalRef = useRef(null);

  useEffect(() => {
    if ('memory' in performance) {
      intervalRef.current = setInterval(() => {
        const memInfo = performance.memory;
        const usage = {
          used: memInfo.usedJSHeapSize,
          total: memInfo.totalJSHeapSize,
          limit: memInfo.jsHeapSizeLimit,
          timestamp: Date.now()
        };
        
        setMemoryUsage(usage);
        
        // Alerter si l'utilisation mémoire est élevée
        const usagePercent = (usage.used / usage.limit) * 100;
        if (usagePercent > 80) {
          console.warn(`🚨 Utilisation mémoire élevée dans ${componentName}: ${usagePercent.toFixed(1)}%`);
        }
      }, 5000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [componentName]);

  return memoryUsage;
};

export default usePerformance;
