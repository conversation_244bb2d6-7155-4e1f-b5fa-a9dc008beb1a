const models = require('../models');

/**
 * Contrôleur pour la gestion des rôles
 */
const roleController = {
  /**
   * Récupère tous les rôles
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  getAllRoles: async (req, res) => {
    try {
      const roles = await models.Role.findAll();
      res.json(roles);
    } catch (error) {
      console.error('Erreur lors de la récupération des rôles:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des rôles',
        error: error.message
      });
    }
  },

  /**
   * Récupère un rôle par son ID
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  getRoleById: async (req, res) => {
    try {
      const role = await models.Role.findByPk(req.params.id);
      if (!role) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }
      res.json(role);
    } catch (error) {
      console.error('Erreur lors de la récupération du rôle:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération du rôle',
        error: error.message
      });
    }
  },

  /**
   * Crée un nouveau rôle
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  createRole: async (req, res) => {
    try {
      const { name, description, permissions, isActive } = req.body;

      // Validation des données
      if (!name) {
        return res.status(400).json({ message: 'Le nom du rôle est requis' });
      }

      const roleData = {
        name,
        description,
        permissions,
        is_active: isActive
      };

      const newRole = await models.Role.create(roleData);
      res.status(201).json(newRole);
    } catch (error) {
      console.error('Erreur lors de la création du rôle:', error);
      res.status(500).json({
        message: 'Erreur lors de la création du rôle',
        error: error.message
      });
    }
  },

  /**
   * Met à jour un rôle existant
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  updateRole: async (req, res) => {
    try {
      const { name, description, permissions, isActive } = req.body;

      // Validation des données
      if (!name) {
        return res.status(400).json({ message: 'Le nom du rôle est requis' });
      }

      const roleData = {
        name,
        description,
        permissions,
        is_active: isActive
      };

      const updatedRole = await models.Role.update(roleData, {
        where: { id: req.params.id },
        returning: true
      });

      if (!updatedRole[0]) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      res.json(updatedRole[1][0]);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du rôle:', error);
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du rôle',
        error: error.message
      });
    }
  },

  /**
   * Supprime un rôle
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  deleteRole: async (req, res) => {
    try {
      const deletedRole = await models.Role.destroy({
        where: { id: req.params.id }
      });
      if (!deletedRole) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      res.json({ message: 'Rôle supprimé avec succès' });
    } catch (error) {
      console.error('Erreur lors de la suppression du rôle:', error);
      res.status(500).json({
        message: 'Erreur lors de la suppression du rôle',
        error: error.message
      });
    }
  }
};

module.exports = roleController;
