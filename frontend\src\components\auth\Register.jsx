import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Container,
  Alert,
  Grid
} from '@mui/material';
import { PersonAdd } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import ValidatedForm from '../forms/ValidatedForm';
import ValidatedTextField from '../forms/ValidatedTextField';
import ValidatedSelect from '../forms/ValidatedSelect';
import { ValidationSchemas } from '../../utils/validation';

const Register = () => {
  const { register, login, error } = useAuth();
  const navigate = useNavigate();
  const [successMessage, setSuccessMessage] = React.useState('');

  // Options pour le sélecteur de rôle
  const roleOptions = [
    { value: 'eleveur', label: 'Éleveur' },
    { value: 'veterinaire', label: 'Vétérinaire' },
    { value: 'marchand', label: 'Marchand' }
  ];

  const getRedirectPath = (role) => {
    const rolePathMap = {
      'admin': '/admin/dashboard',
      'eleveur': '/eleveur/dashboard',
      'veterinaire': '/veterinaire/dashboard',
      'marchand': '/marchand/dashboard'
    };
    return rolePathMap[role] || '/dashboard';
  };

  const handleSubmit = async (formValues, { setErrors }) => {
    try {
      console.log('📝 Début de l\'inscription avec:', formValues.email);

      // Supprimer confirmPassword avant d'envoyer à l'API
      const { confirmPassword, ...registerData } = formValues;

      await register(registerData);

      setSuccessMessage('🎉 Inscription réussie! Connexion automatique en cours...');

      // Connexion automatique après inscription réussie
      setTimeout(async () => {
        try {
          const user = await login(formValues.email, formValues.password);

          // Gérer les rôles string (legacy) et object (nouveau format API)
          const userRole = typeof user.role === 'object' && user.role !== null ? user.role.name : user.role;

          console.log('✅ Connexion automatique réussie, redirection vers:', userRole);

          const targetPath = getRedirectPath(userRole);
          navigate(targetPath, { replace: true });
        } catch (loginErr) {
          console.error('❌ Erreur lors de la connexion automatique:', loginErr);
          setSuccessMessage('Inscription réussie! Veuillez vous connecter manuellement.');
          setTimeout(() => navigate('/login'), 2000);
        }
      }, 1500);

    } catch (err) {
      console.error('❌ Erreur lors de l\'inscription:', err);
      setErrors({
        submit: err.response?.data?.message || err.message || 'Erreur lors de l\'inscription. Veuillez réessayer.'
      });
    }
  };

  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography component="h1" variant="h5" sx={{ mb: 3 }}>
            📝 Créer un compte
          </Typography>

          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {error}
            </Alert>
          )}

          {successMessage && (
            <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
              {successMessage}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  id="first_name"
                  label="Prénom"
                  name="first_name"
                  autoComplete="given-name"
                  value={formData.first_name}
                  onChange={handleChange}
                  error={!!formError}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  id="last_name"
                  label="Nom"
                  name="last_name"
                  autoComplete="family-name"
                  value={formData.last_name}
                  onChange={handleChange}
                  error={!!formError}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  id="username"
                  label="Nom d'utilisateur"
                  name="username"
                  autoComplete="username"
                  value={formData.username}
                  onChange={handleChange}
                  error={!!formError}
                />
              </Grid>
              <Grid item xs={12}>
                <ValidatedTextField
                  name="email"
                  label="Adresse email"
                  type="email"
                  autoComplete="email"
                  required
                  placeholder="<EMAIL>"
                  tooltip="Entrez une adresse email valide"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="password"
                  label="Mot de passe"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="new-password"
                  value={formData.password}
                  onChange={handleChange}
                  error={!!formError}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={toggleShowPassword}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="confirmPassword"
                  label="Confirmer le mot de passe"
                  type={showPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  error={!!formError}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="role-label">Rôle</InputLabel>
                  <Select
                    labelId="role-label"
                    id="role"
                    name="role"
                    value={formData.role}
                    label="Rôle"
                    onChange={handleChange}
                  >
                    <MenuItem value="eleveur">Éleveur</MenuItem>
                    <MenuItem value="veterinaire">Vétérinaire</MenuItem>
                    <MenuItem value="marchand">Marchand</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              sx={{ mt: 3, mb: 2 }}
              disabled={isSubmitting}
              startIcon={<PersonAdd />}
            >
              {isSubmitting ? 'Inscription en cours...' : 'S\'inscrire'}
            </Button>

            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Typography variant="body2">
                Vous avez déjà un compte ?{' '}
                <Link to="/login" style={{ color: 'inherit' }}>
                  Connectez-vous
                </Link>
              </Typography>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default Register;
