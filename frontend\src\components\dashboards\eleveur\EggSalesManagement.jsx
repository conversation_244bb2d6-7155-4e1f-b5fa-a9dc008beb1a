import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  IconButton
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useTranslation } from 'react-i18next';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useEggProduction } from '../../../hooks/useEggProduction';
import { QUALITES_OEUFS } from '../../../utils/constants';

const EggSalesManagement = () => {
  const { t } = useTranslation();
  const {
    productions,
    loading,
    error,
    calculateDailyAverage,
    calculateWeeklyTrend,
    getProductionQuality
  } = useEggProduction();

  const [salesData, setSalesData] = useState({
    date: new Date(),
    quantite: '',
    prix_unitaire: '',
    client: '',
    type_qualite: 'premium',
    notes: ''
  });

  const [sales, setSales] = useState([]);
  const [editingId, setEditingId] = useState(null);
  const [success, setSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setSalesData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (date) => {
    setSalesData(prev => ({
      ...prev,
      date
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setSubmitting(true);

    try {
      const newSale = {
        ...salesData,
        quantite: parseInt(salesData.quantite),
        prix_unitaire: parseFloat(salesData.prix_unitaire),
        montant_total: parseInt(salesData.quantite) * parseFloat(salesData.prix_unitaire)
      };

      if (editingId) {
        // Update existing sale
        const updatedSales = sales.map(sale =>
          sale.id === editingId ? { ...newSale, id: editingId } : sale
        );
        setSales(updatedSales);
        setEditingId(null);
      } else {
        // Add new sale
        setSales([...sales, { ...newSale, id: Date.now() }]);
      }

      setSalesData({
        date: new Date(),
        quantite: '',
        prix_unitaire: '',
        client: '',
        type_qualite: 'premium',
        notes: ''
      });
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement de la vente:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (sale) => {
    setEditingId(sale.id);
    setSalesData({
      date: new Date(sale.date),
      quantite: sale.quantite.toString(),
      prix_unitaire: sale.prix_unitaire.toString(),
      client: sale.client,
      type_qualite: sale.type_qualite,
      notes: sale.notes
    });
  };

  const handleDelete = (id) => {
    setSales(sales.filter(sale => sale.id !== id));
  };

  const getChartData = () => {
    return sales.map(sale => ({
      date: new Date(sale.date).toLocaleDateString(),
      montant: sale.montant_total,
      quantite: sale.quantite
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {t('Gestion des Ventes d\'Œufs')}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {t('Vente enregistrée avec succès')}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label={t('Date de vente')}
                    value={salesData.date}
                    onChange={handleDateChange}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                    maxDate={new Date()}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label={t('Client')}
                    name="client"
                    value={salesData.client}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label={t('Quantité')}
                    name="quantite"
                    type="number"
                    value={salesData.quantite}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label={t('Prix unitaire')}
                    name="prix_unitaire"
                    type="number"
                    value={salesData.prix_unitaire}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    select
                    fullWidth
                    label={t('Qualité')}
                    name="type_qualite"
                    value={salesData.type_qualite}
                    onChange={handleChange}
                    required
                  >
                    {Object.entries(QUALITES_OEUFS).map(([key, value]) => (
                      <MenuItem key={key} value={key}>
                        {t(value)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={t('Notes')}
                    name="notes"
                    multiline
                    rows={2}
                    value={salesData.notes}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={submitting}
                    fullWidth
                  >
                    {submitting ? (
                      <CircularProgress size={24} />
                    ) : editingId ? (
                      t('Modifier la vente')
                    ) : (
                      t('Enregistrer la vente')
                    )}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('Historique des Ventes')}
            </Typography>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('Date')}</TableCell>
                    <TableCell>{t('Client')}</TableCell>
                    <TableCell align="right">{t('Quantité')}</TableCell>
                    <TableCell align="right">{t('Prix unitaire')}</TableCell>
                    <TableCell align="right">{t('Montant total')}</TableCell>
                    <TableCell align="center">{t('Actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sales.map((sale) => (
                    <TableRow key={sale.id}>
                      <TableCell>
                        {new Date(sale.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{sale.client}</TableCell>
                      <TableCell align="right">{sale.quantite}</TableCell>
                      <TableCell align="right">
                        {sale.prix_unitaire.toFixed(2)} DA
                      </TableCell>
                      <TableCell align="right">
                        {sale.montant_total.toFixed(2)} DA
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(sale)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(sale.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('Analyse des Ventes')}
            </Typography>

            <Box height={300}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={getChartData()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="montant"
                    name={t('Montant (DA)')}
                    stroke="#8884d8"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="quantite"
                    name={t('Quantité')}
                    stroke="#82ca9d"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default EggSalesManagement;
