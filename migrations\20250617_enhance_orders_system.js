const { DataTypes } = require('sequelize');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add tracking fields to orders table
    await queryInterface.addColumn('orders', 'tracking_number', {
      type: DataTypes.STRING(50)
    });

    await queryInterface.addColumn('orders', 'shipping_method', {
      type: DataTypes.STRING(50)
    });

    await queryInterface.addColumn('orders', 'shipping_cost', {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    });

    await queryInterface.addColumn('orders', 'tax_amount', {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    });

    await queryInterface.addColumn('orders', 'estimated_delivery_date', {
      type: DataTypes.DATE
    });

    await queryInterface.addColumn('orders', 'actual_delivery_date', {
      type: DataTypes.DATE
    });

    // Add more detailed status tracking
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'processing';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'confirmed';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'shipped';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'out_for_delivery';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'delivered';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'cancelled';
      ALTER TYPE "enum_orders_status" ADD VALUE IF NOT EXISTS 'refunded';
    `);

    // Add payment tracking enum
    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_orders_payment_status" AS ENUM (
        'pending',
        'authorized',
        'paid',
        'failed',
        'refunded',
        'partially_refunded'
      );
    `);

    // Add status history to track order progress
    await queryInterface.createTable('order_status_history', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      order_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'orders',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      status: {
        type: DataTypes.STRING(50),
        allowNull: false
      },
      notes: {
        type: DataTypes.TEXT
      },
      created_by: {
        type: DataTypes.INTEGER,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add order returns tracking
    await queryInterface.createTable('order_returns', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      order_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'orders',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      return_reason: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      status: {
        type: DataTypes.STRING(50),
        defaultValue: 'pending'
      },
      refund_amount: {
        type: DataTypes.DECIMAL(10, 2)
      },
      notes: {
        type: DataTypes.TEXT
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add order item status tracking
    await queryInterface.addColumn('order_items', 'status', {
      type: DataTypes.STRING(50),
      defaultValue: 'pending'
    });

    await queryInterface.addColumn('order_items', 'return_quantity', {
      type: DataTypes.INTEGER,
      defaultValue: 0
    });

    // Add indexes for better performance
    await queryInterface.addIndex('orders', ['status']);
    await queryInterface.addIndex('orders', ['payment_status']);
    await queryInterface.addIndex('orders', ['created_at']);
    await queryInterface.addIndex('order_items', ['status']);
    await queryInterface.addIndex('order_status_history', ['order_id']);
    await queryInterface.addIndex('order_status_history', ['created_at']);
    await queryInterface.addIndex('order_returns', ['order_id']);
    await queryInterface.addIndex('order_returns', ['status']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('orders', 'tracking_number');
    await queryInterface.removeColumn('orders', 'shipping_method');
    await queryInterface.removeColumn('orders', 'shipping_cost');
    await queryInterface.removeColumn('orders', 'tax_amount');
    await queryInterface.removeColumn('orders', 'estimated_delivery_date');
    await queryInterface.removeColumn('orders', 'actual_delivery_date');
    await queryInterface.removeColumn('order_items', 'status');
    await queryInterface.removeColumn('order_items', 'return_quantity');
    await queryInterface.dropTable('order_status_history');
    await queryInterface.dropTable('order_returns');
    await queryInterface.removeIndex('orders', ['status']);
    await queryInterface.removeIndex('orders', ['payment_status']);
    await queryInterface.removeIndex('orders', ['created_at']);
    await queryInterface.removeIndex('order_items', ['status']);
    await queryInterface.removeIndex('order_status_history', ['order_id']);
    await queryInterface.removeIndex('order_status_history', ['created_at']);
    await queryInterface.removeIndex('order_returns', ['order_id']);
    await queryInterface.removeIndex('order_returns', ['status']);
  }
};
