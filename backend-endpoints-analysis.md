# Analyse des Endpoints Manquants - Dashboard Admin

## Endpoints identifiés avec erreurs 404

### 🔴 **PRIORITÉ HAUTE - Endpoints essentiels pour le dashboard admin**

#### 1. `/admin/data` (GET)
- **URL complète**: `http://localhost:3003/api/admin/data?type=sales&timeRange=month`
- **Méthode**: GET
- **Paramètres query**:
  - `type`: string (sales, users, revenue, etc.)
  - `timeRange`: string (day, week, month, year)
- **Réponse attendue**: 
  ```json
  {
    "data": [
      {"date": "2024-01-01", "value": 1500},
      {"date": "2024-01-02", "value": 1800}
    ],
    "total": 45000,
    "growth": 12.5
  }
  ```
- **Usage**: Graphiques et statistiques du dashboard

#### 2. `/admin/roles` (GET, POST, PUT, DELETE)
- **URL complète**: `http://localhost:3003/api/admin/roles`
- **Méthodes**: GET (liste), POST (créer), PUT (modifier), DELETE (supprimer)
- **Réponse GET**:
  ```json
  {
    "roles": [
      {"id": 1, "name": "admin", "permissions": ["all"]},
      {"id": 2, "name": "eleveur", "permissions": ["create_posts", "manage_livestock"]},
      {"id": 3, "name": "veterinaire", "permissions": ["view_health", "create_reports"]}
    ]
  }
  ```
- **Usage**: Gestion des rôles utilisateurs

#### 3. `/admin/subscription-plans` (GET, POST, PUT, DELETE)
- **URL complète**: `http://localhost:3003/api/admin/subscription-plans`
- **Méthodes**: GET (liste), POST (créer), PUT (modifier), DELETE (supprimer)
- **Réponse GET**:
  ```json
  {
    "plans": [
      {"id": 1, "name": "Basic", "price": 2000, "features": ["basic_listing"]},
      {"id": 2, "name": "Premium", "price": 5000, "features": ["premium_listing", "analytics"]}
    ]
  }
  ```
- **Usage**: Gestion des plans d'abonnement

#### 4. `/admin/notifications` (GET, POST, PUT, DELETE)
- **URL complète**: `http://localhost:3003/api/admin/notifications?page=1&limit=10`
- **Méthodes**: GET (liste), POST (créer), PUT (marquer lu), DELETE (supprimer)
- **Paramètres query**:
  - `page`: number (pagination)
  - `limit`: number (nombre par page)
- **Réponse GET**:
  ```json
  {
    "notifications": [
      {"id": 1, "title": "Nouveau utilisateur", "message": "Un nouvel éleveur s'est inscrit", "read": false, "createdAt": "2024-12-19T10:00:00Z"}
    ],
    "total": 25,
    "page": 1,
    "totalPages": 3
  }
  ```
- **Usage**: Système de notifications admin

### 🟠 **PRIORITÉ MOYENNE - Endpoints pour fonctionnalités spécifiques**

#### 5. `/blog/admin/posts` (GET, POST, PUT, DELETE)
- **URL complète**: `http://localhost:3003/api/blog/admin/posts?page=1&limit=10`
- **Méthodes**: GET (liste), POST (créer), PUT (modifier), DELETE (supprimer)
- **Paramètres query**:
  - `page`: number
  - `limit`: number
  - `status`: string (draft, published, archived)
- **Réponse GET**:
  ```json
  {
    "posts": [
      {"id": 1, "title": "Guide élevage", "content": "...", "status": "published", "author": "admin", "createdAt": "2024-12-19T10:00:00Z"}
    ],
    "total": 15,
    "page": 1,
    "totalPages": 2
  }
  ```
- **Usage**: Gestion des articles de blog

### ✅ **ENDPOINTS DÉJÀ FONCTIONNELS**

#### 1. `/admin/stats` (GET) ✅
- **Status**: Fonctionnel
- **Réponse**: Statistiques générales du dashboard

#### 2. `/admin/users` (GET) ✅
- **Status**: Fonctionnel avec paramètres
- **Paramètres**: `role`, `page`, `limit`

#### 3. `/auth/login` (POST) ✅
- **Status**: Fonctionnel
- **Usage**: Authentification utilisateur

#### 4. `/auth/user` (GET) ✅
- **Status**: Fonctionnel
- **Usage**: Récupération profil utilisateur

## Priorisation des développements

### Phase 1 (Immédiate) - Dashboard fonctionnel
1. `/admin/data` - Graphiques et métriques
2. `/admin/notifications` - Notifications système

### Phase 2 (Court terme) - Gestion utilisateurs
1. `/admin/roles` - Gestion des rôles
2. `/admin/subscription-plans` - Plans d'abonnement

### Phase 3 (Moyen terme) - Contenu
1. `/blog/admin/posts` - Gestion blog

## Technologies recommandées

### Base de données
- **PostgreSQL** (recommandé pour la robustesse et les relations complexes)
- Alternative: **MySQL** (plus simple à configurer)

### Framework Backend
- **Node.js + Express** (cohérence avec l'existant)
- **Prisma** ou **Sequelize** pour l'ORM
- **JWT** pour l'authentification (déjà en place)

### Structure recommandée
```
backend/
├── src/
│   ├── controllers/
│   │   ├── adminController.js
│   │   ├── authController.js
│   │   └── blogController.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Role.js
│   │   └── Notification.js
│   ├── routes/
│   │   ├── admin.js
│   │   ├── auth.js
│   │   └── blog.js
│   ├── middleware/
│   │   ├── auth.js
│   │   └── validation.js
│   ├── database/
│   │   ├── connection.js
│   │   └── migrations/
│   └── utils/
├── package.json
└── .env
```
