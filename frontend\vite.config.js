import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 5174,
    host: 'localhost',
    strictPort: true,
    hmr: {
      port: 5174
    },
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Content-Length, X-Requested-With'
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3003',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
  },
  optimizeDeps: {
    include: [
      'firebase/app',
      'firebase/auth',
      'react',
      'react-dom',
      'react/jsx-runtime',
      '@mui/material',
      '@mui/icons-material',
      '@mui/system',
      '@emotion/react',
      '@emotion/styled',
      'react-router-dom',
      'axios',
      'date-fns',
      'date-fns/locale'
    ],
    exclude: ['firebase'],
    esbuildOptions: {
      target: 'es2020'
    }
  },
  build: {
    target: 'es2020',
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Séparer les vendors par taille et fréquence d'utilisation
          if (id.includes('node_modules')) {
            // Chunks pour les grandes librairies
            if (id.includes('@mui/material') || id.includes('@mui/icons-material')) {
              return 'mui';
            }
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react';
            }
            if (id.includes('firebase')) {
              return 'firebase';
            }
            if (id.includes('axios') || id.includes('react-router')) {
              return 'routing-http';
            }
            if (id.includes('date-fns') || id.includes('lodash')) {
              return 'utils';
            }
            // Autres dépendances dans un chunk vendor général
            return 'vendor';
          }

          // Séparer les pages par route pour le lazy loading
          if (id.includes('/pages/')) {
            if (id.includes('/admin/')) return 'admin-pages';
            if (id.includes('/eleveur/')) return 'eleveur-pages';
            if (id.includes('/veterinaire/')) return 'veterinaire-pages';
            if (id.includes('/marchand/')) return 'marchand-pages';
            return 'common-pages';
          }

          // Séparer les composants par fonctionnalité
          if (id.includes('/components/')) {
            if (id.includes('/dashboards/')) return 'dashboard-components';
            if (id.includes('/forms/')) return 'form-components';
            if (id.includes('/charts/')) return 'chart-components';
            return 'common-components';
          }
        },
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `js/[name]-[hash].js`;
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  esbuild: {
    target: 'es2020',
    loader: 'jsx',
    include: /src\/.*\.[jt]sx?$/,
    exclude: []
  },
  define: {
    global: 'globalThis',
  }
})
