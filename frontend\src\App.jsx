import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import EleveursList from './pages/EleveursList';
import VolaillesList from './pages/VolaillesList';
import Dashboard from './pages/Dashboard';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import DashboardLayout from './layouts/DashboardLayout';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import HomePageManager from './components/HomePageManager';
import ErrorBoundary from './components/ErrorBoundary';
import Notifications from './pages/admin/Notifications';
import SessionExpiredDialog from './components/auth/SessionExpiredDialog';
import GlobalErrorHandler from './components/errors/GlobalErrorHandler';

// Pages publiques
import LandingPage from './components/landing/LandingPage';

// Pages privées
import Profile from './pages/admin/Profile';

// Pages dashboards
import AdminDashboard from './pages/dashboards/AdminDashboard';
import EleveurDashboard from './pages/dashboards/EleveurDashboard';
import VeterinaireDashboard from './pages/dashboards/VeterinaireDashboard';
import MarchandDashboard from './pages/dashboards/MarchandDashboard';

// Pages admin
import UsersManagement from './pages/admin/UsersManagement';
import Blog from './pages/admin/Blog';
import AiBlogGenerator from './pages/admin/AiBlogGenerator';
import AiDataAnalysis from './pages/admin/AiDataAnalysis';
import PageContentGenerator from './pages/admin/PageContentGenerator';
import TranslationsManager from './pages/admin/TranslationsManager';
import RolesPlans from './pages/admin/RolesPlans';
import NavigationDebugger from './components/NavigationDebugger';

// Lazy load admin components
const ApiConfig = lazy(() => import('./pages/admin/ApiConfig'));
const SmtpConfig = lazy(() => import('./pages/admin/SmtpConfigPage'));
const GeneralSettings = lazy(() => import('./pages/admin/GeneralSettings'));
const SecuritySettings = lazy(() => import('./pages/admin/SecuritySettingsPage'));
const LoginAsUser = lazy(() => import('./pages/admin/LoginAsUser'));

const theme = createTheme({
  palette: {
    primary: {
      main: '#2E7D32', // Vert forêt
    },
    secondary: {
      main: '#FFA000', // Orange doré
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <LanguageProvider>
            <GlobalErrorHandler>
              <Router
                future={{
                  v7_startTransition: true,
                  v7_relativeSplatPath: true
                }}
              >
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Navbar><Home /></Navbar>} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Debug route for testing navigation */}
              <Route path="/debug-navigation" element={<NavigationDebugger />} />

              {/* Legacy routes - will be removed later */}
              <Route path="/eleveurs" element={<Navbar><EleveursList /></Navbar>} />
              <Route path="/volailles" element={<Navbar><VolaillesList /></Navbar>} />
              <Route path="/dashboard" element={<Navbar><Dashboard /></Navbar>} />

              {/* Role-specific dashboard routes */}
              <Route path="/admin/*" element={<DashboardLayout requiredRole="admin" />}>
                <Route index element={<Navigate to="dashboard" />} />
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="profile" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Profile />
                  </Suspense>
                } />
                <Route path="roles-plans" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <RolesPlans />
                  </Suspense>
                } />
                <Route path="homepage" element={<HomePageManager />} />
                <Route path="users" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <UsersManagement />
                  </Suspense>
                } />
                <Route path="users/:role" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <UsersManagement />
                  </Suspense>
                } />
                <Route path="volailles" element={<VolaillesList />} />
                <Route path="blog" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Blog />
                  </Suspense>
                } />
                <Route path="notifications" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Notifications />
                  </Suspense>
                } />
                <Route path="translations" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <TranslationsManager />
                  </Suspense>
                } />
                <Route path="roles-plans" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <RolesPlans />
                  </Suspense>
                } />
                <Route path="ai/blog-generator" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <AiBlogGenerator />
                  </Suspense>
                } />
                <Route path="ai/data-analysis" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <AiDataAnalysis />
                  </Suspense>
                } />
                <Route path="ai/page-content" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <PageContentGenerator />
                  </Suspense>
                } />
                <Route path="ai/api-config" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <ApiConfig />
                  </Suspense>
                } />
                <Route path="settings/smtp" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <SmtpConfig />
                  </Suspense>
                } />
                <Route path="settings/general" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <GeneralSettings />
                  </Suspense>
                } />
                <Route path="settings/security" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <SecuritySettings />
                  </Suspense>
                } />
                <Route path="users/login-as" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <LoginAsUser />
                  </Suspense>
                } />
                <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
              </Route>

              <Route path="/eleveur/*" element={<DashboardLayout requiredRole="eleveur" />}>
                <Route path="dashboard" element={<EleveurDashboard />} />
                <Route path="profile" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Profile />
                  </Suspense>
                } />
                <Route path="volailles" element={<VolaillesList />} />
                <Route path="*" element={<Navigate to="/eleveur/dashboard" replace />} />
              </Route>

              <Route path="/veterinaire/*" element={<DashboardLayout requiredRole="veterinaire" />}>
                <Route path="dashboard" element={<VeterinaireDashboard />} />
                <Route path="profile" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Profile />
                  </Suspense>
                } />
                <Route path="*" element={<Navigate to="/veterinaire/dashboard" replace />} />
              </Route>

              <Route path="/marchand/*" element={<DashboardLayout requiredRole="marchand" />}>
                <Route path="dashboard" element={<MarchandDashboard />} />
                <Route path="profile" element={
                  <Suspense fallback={<div>Chargement...</div>}>
                    <Profile />
                  </Suspense>
                } />
                <Route path="*" element={<Navigate to="/marchand/dashboard" replace />} />
              </Route>

              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>

            {/* Composant global pour gérer les sessions expirées */}
            <SessionExpiredDialog />
          </Router>
          </LanguageProvider>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
