const models = require('../models');
const { Op } = require('sequelize');

// R<PERSON><PERSON>érer le profil du vétérinaire
const getProfile = async (req, res) => {
  try {
    const veterinaire = await models.Veterinaire.findOne({
      where: { user_id: req.user.id },
      include: [{
        model: models.User,
        as: 'user',
        attributes: ['nom', 'prenom', 'email']
      }]
    });

    if (!veterinaire) {
      return res.status(404).json({ message: 'Profil vétérinaire non trouvé' });
    }

    res.json(veterinaire);
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Mettre à jour le profil du vétérinaire
const updateProfile = async (req, res) => {
  try {
    const {
      specialites,
      disponibilites,
      telephone,
      email_professionnel,
      adresse_cabinet,
      description,
      tarifs
    } = req.body;

    const veterinaire = await models.Veterinaire.findOne({
      where: { user_id: req.user.id }
    });

    if (!veterinaire) {
      return res.status(404).json({ message: 'Profil vétérinaire non trouvé' });
    }

    await veterinaire.update({
      specialites: specialites || veterinaire.specialites,
      disponibilites: disponibilites || veterinaire.disponibilites,
      telephone: telephone || veterinaire.telephone,
      email_professionnel: email_professionnel || veterinaire.email_professionnel,
      adresse_cabinet: adresse_cabinet || veterinaire.adresse_cabinet,
      description: description || veterinaire.description,
      tarifs: tarifs || veterinaire.tarifs
    });

    res.json({ message: 'Profil mis à jour avec succès', veterinaire });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Mettre à jour les disponibilités
const updateDisponibilites = async (req, res) => {
  try {
    const { disponibilites } = req.body;

    const veterinaire = await models.Veterinaire.findOne({
      where: { user_id: req.user.id }
    });

    if (!veterinaire) {
      return res.status(404).json({ message: 'Profil vétérinaire non trouvé' });
    }

    await veterinaire.update({ disponibilites });

    res.json({ message: 'Disponibilités mises à jour avec succès', disponibilites });
  } catch (error) {
    console.error('Erreur lors de la mise à jour des disponibilités:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Récupérer l'historique des consultations d'un vétérinaire
const getConsultationsHistorique = async (req, res) => {
  try {
    const veterinaire_id = req.user.id; // ID du vétérinaire connecté

    const consultations = await models.Consultation.findAll({
      where: {
        veterinaire_id,
      },
      include: [
        {
          model: models.User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'prenom', 'email']
        },
        {
          model: models.Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race']
        }
      ],
      order: [['date', 'DESC']]
    });

    // Formater les données pour le frontend
    const formattedConsultations = consultations.map(consultation => ({
      id: consultation.id,
      date: consultation.date,
      eleveur: {
        id: consultation.eleveur.id,
        nom: `${consultation.eleveur.prenom} ${consultation.eleveur.nom}`,
        email: consultation.eleveur.email
      },
      typeVolaille: `${consultation.volaille.type} - ${consultation.volaille.race}`,
      symptomes: consultation.symptomes,
      diagnostic: consultation.diagnostic,
      traitement: consultation.traitement,
      statut: consultation.statut,
      notes: consultation.notes,
      createdAt: consultation.createdAt,
      updatedAt: consultation.updatedAt
    }));

    res.json(formattedConsultations);

  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique des consultations:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération de l\'historique des consultations'
    });
  }
};

// Récupérer les détails d'une consultation spécifique
const getConsultationDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const veterinaire_id = req.user.id;

    const consultation = await models.Consultation.findOne({
      where: {
        id,
        veterinaire_id // Vérifier que la consultation appartient bien au vétérinaire
      },
      include: [
        {
          model: models.User,
          as: 'eleveur',
          attributes: ['id', 'nom', 'prenom', 'email', 'telephone']
        },
        {
          model: models.Volaille,
          as: 'volaille',
          attributes: ['id', 'type', 'race', 'age', 'poids']
        }
      ]
    });

    if (!consultation) {
      return res.status(404).json({
        error: 'Consultation non trouvée'
      });
    }

    // Formater la réponse
    const consultationDetails = {
      id: consultation.id,
      date: consultation.date,
      eleveur: {
        id: consultation.eleveur.id,
        nom: `${consultation.eleveur.prenom} ${consultation.eleveur.nom}`,
        email: consultation.eleveur.email,
        telephone: consultation.eleveur.telephone
      },
      volaille: {
        id: consultation.volaille.id,
        type: consultation.volaille.type,
        race: consultation.volaille.race,
        age: consultation.volaille.age,
        poids: consultation.volaille.poids
      },
      symptomes: consultation.symptomes,
      diagnostic: consultation.diagnostic,
      traitement: consultation.traitement,
      statut: consultation.statut,
      notes: consultation.notes,
      createdAt: consultation.createdAt,
      updatedAt: consultation.updatedAt
    };

    res.json(consultationDetails);

  } catch (error) {
    console.error('Erreur lors de la récupération des détails de la consultation:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des détails de la consultation'
    });
  }
};

// Mettre à jour le statut d'une consultation
const updateConsultationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { statut } = req.body;
    const veterinaire_id = req.user.id;

    // Vérifier que le statut est valide
    const statutsValides = ['en_attente', 'en_cours', 'terminée', 'annulée'];
    if (!statutsValides.includes(statut)) {
      return res.status(400).json({
        error: 'Statut invalide'
      });
    }

    const consultation = await models.Consultation.findOne({
      where: {
        id,
        veterinaire_id
      }
    });

    if (!consultation) {
      return res.status(404).json({
        error: 'Consultation non trouvée'
      });
    }

    // Mettre à jour le statut
    await consultation.update({ statut });

    res.json({
      message: 'Statut de la consultation mis à jour avec succès',
      consultation: {
        id: consultation.id,
        statut: consultation.statut
      }
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut de la consultation:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour du statut de la consultation'
    });
  }
};

module.exports = {
  getProfile,
  updateProfile,
  updateDisponibilites,
  getConsultationsHistorique,
  getConsultationDetails,
  updateConsultationStatus
};
