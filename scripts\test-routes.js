#!/usr/bin/env node

/**
 * Script de test pour toutes les routes de l'API Poultray DZ
 * Teste les nouvelles routes créées et vérifie leur fonctionnement
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3004';
const API_URL = `${BASE_URL}/api`;

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testRoute(method, endpoint, data = null, headers = {}, expectStatus = 200) {
  try {
    const config = {
      method,
      url: `${API_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    
    if (response.status === expectStatus) {
      log(`✅ ${method.toUpperCase()} ${endpoint} - Status: ${response.status}`, 'green');
      return { success: true, data: response.data };
    } else {
      log(`⚠️  ${method.toUpperCase()} ${endpoint} - Expected: ${expectStatus}, Got: ${response.status}`, 'yellow');
      return { success: false, status: response.status };
    }
  } catch (error) {
    if (error.response && error.response.status === expectStatus) {
      log(`✅ ${method.toUpperCase()} ${endpoint} - Status: ${error.response.status} (Expected)`, 'green');
      return { success: true, status: error.response.status };
    } else {
      log(`❌ ${method.toUpperCase()} ${endpoint} - Error: ${error.message}`, 'red');
      if (error.response) {
        log(`   Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`, 'red');
      }
      return { success: false, error: error.message };
    }
  }
}

async function testAllRoutes() {
  log('🚀 Test des routes de l\'API Poultray DZ', 'bold');
  log('=====================================', 'blue');

  let totalTests = 0;
  let passedTests = 0;

  // Test des routes publiques
  log('\n📋 Test des routes publiques:', 'blue');
  
  // Route de base
  const baseTest = await testRoute('get', '/', null, {}, 200);
  totalTests++;
  if (baseTest.success) passedTests++;

  // Routes de traduction
  const translationsTest = await testRoute('get', '/translations?lang=fr');
  totalTests++;
  if (translationsTest.success) passedTests++;

  const languagesTest = await testRoute('get', '/translations/languages');
  totalTests++;
  if (languagesTest.success) passedTests++;

  // Test des routes protégées (doivent retourner 401)
  log('\n🔒 Test des routes protégées (sans token):', 'blue');

  // Routes admin
  const adminStatsTest = await testRoute('get', '/admin/stats', null, {}, 401);
  totalTests++;
  if (adminStatsTest.success) passedTests++;

  // Routes vétérinaires
  const vetDashboardTest = await testRoute('get', '/veterinaire/dashboard', null, {}, 401);
  totalTests++;
  if (vetDashboardTest.success) passedTests++;

  const vetPrescriptionsTest = await testRoute('get', '/veterinaire/prescriptions', null, {}, 401);
  totalTests++;
  if (vetPrescriptionsTest.success) passedTests++;

  // Routes éleveurs
  const eleveurDashboardTest = await testRoute('get', '/eleveur/dashboard', null, {}, 401);
  totalTests++;
  if (eleveurDashboardTest.success) passedTests++;

  const eleveurVolaillesTest = await testRoute('get', '/eleveur/volailles', null, {}, 401);
  totalTests++;
  if (eleveurVolaillesTest.success) passedTests++;

  const eleveurVentesTest = await testRoute('get', '/eleveur/ventes', null, {}, 401);
  totalTests++;
  if (eleveurVentesTest.success) passedTests++;

  const eleveurStatsTest = await testRoute('get', '/eleveur/stats', null, {}, 401);
  totalTests++;
  if (eleveurStatsTest.success) passedTests++;

  // Routes de ventes
  const ventesTest = await testRoute('get', '/ventes', null, {}, 401);
  totalTests++;
  if (ventesTest.success) passedTests++;

  const ventesStatsTest = await testRoute('get', '/ventes/stats', null, {}, 401);
  totalTests++;
  if (ventesStatsTest.success) passedTests++;

  // Routes d'authentification
  log('\n🔑 Test des routes d\'authentification:', 'blue');

  const authUserTest = await testRoute('get', '/auth/user', null, {}, 401);
  totalTests++;
  if (authUserTest.success) passedTests++;

  // Test des routes CRUD existantes
  log('\n📊 Test des routes CRUD existantes:', 'blue');

  const eleveursTest = await testRoute('get', '/eleveurs', null, {}, 401);
  totalTests++;
  if (eleveursTest.success) passedTests++;

  const volaillesTest = await testRoute('get', '/volailles', null, {}, 401);
  totalTests++;
  if (volaillesTest.success) passedTests++;

  // Résumé des tests
  log('\n📈 Résumé des tests:', 'bold');
  log('==================', 'blue');
  log(`Total des tests: ${totalTests}`, 'blue');
  log(`Tests réussis: ${passedTests}`, 'green');
  log(`Tests échoués: ${totalTests - passedTests}`, passedTests === totalTests ? 'green' : 'red');
  log(`Taux de réussite: ${Math.round((passedTests / totalTests) * 100)}%`, passedTests === totalTests ? 'green' : 'yellow');

  if (passedTests === totalTests) {
    log('\n🎉 Tous les tests sont passés avec succès !', 'green');
    log('✅ L\'API est entièrement fonctionnelle', 'green');
  } else {
    log('\n⚠️  Certains tests ont échoué', 'yellow');
    log('🔧 Vérifiez les logs ci-dessus pour plus de détails', 'yellow');
  }

  return { totalTests, passedTests };
}

// Test de connexion au serveur
async function testServerConnection() {
  log('🔌 Test de connexion au serveur...', 'blue');
  try {
    const response = await axios.get(BASE_URL, { timeout: 5000 });
    log('✅ Serveur accessible', 'green');
    return true;
  } catch (error) {
    log('❌ Impossible de se connecter au serveur', 'red');
    log(`   Assurez-vous que le serveur fonctionne sur ${BASE_URL}`, 'yellow');
    return false;
  }
}

// Fonction principale
async function main() {
  log('🧪 Script de test des routes API Poultray DZ', 'bold');
  log('===========================================', 'blue');

  // Test de connexion
  const serverConnected = await testServerConnection();
  if (!serverConnected) {
    process.exit(1);
  }

  // Test des routes
  const results = await testAllRoutes();

  // Code de sortie
  process.exit(results.passedTests === results.totalTests ? 0 : 1);
}

// Exécuter le script
if (require.main === module) {
  main().catch(error => {
    log(`❌ Erreur fatale: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { testAllRoutes, testRoute };
