{"name": "poultry-dz-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:update": "jest --updateSnapshot"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mui/lab": "^5.0.0-alpha.170", "@mui/x-data-grid": "^6.20.4", "@mui/x-date-pickers": "^8.5.1", "@mui/x-date-pickers-pro": "^8.5.1", "axios": "^1.4.0", "date-fns": "^2.30.0", "firebase": "^11.9.0", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "recharts": "^2.7.3"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/runtime": "^7.27.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.5.1", "babel-jest": "^29.7.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "i18next": "^25.2.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-serializer-html": "^7.1.0", "jest-transform-css": "^6.0.1", "jest-transform-stub": "^2.0.0", "pg": "^8.16.0", "react-i18next": "^15.5.2", "rimraf": "^5.0.1", "vite": "^6.3.5"}}