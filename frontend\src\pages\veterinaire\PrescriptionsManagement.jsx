/**
 * Composant de gestion des prescriptions pour les vétérinaires
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Autocomplete,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Send as SendIcon,
  MedicalServices as MedicalIcon,
  LocalPharmacy as PharmacyIcon,
  Assignment as PrescriptionIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { veterinaireAPI } from '../../services/veterinaireService';

const PrescriptionsManagement = () => {
  const { user } = useAuth();
  const [prescriptions, setPrescriptions] = useState([]);
  const [patients, setPatients] = useState([]);
  const [medicaments, setMedicaments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPrescription, setSelectedPrescription] = useState(null);

  const [formData, setFormData] = useState({
    patient_id: '',
    diagnostic: '',
    date_prescription: new Date(),
    date_fin_traitement: null,
    notes: '',
    medicaments: []
  });

  const [currentMedicament, setCurrentMedicament] = useState({
    medicament_id: '',
    nom_medicament: '',
    dosage: '',
    frequence: '',
    duree: '',
    instructions: ''
  });

  const [stats, setStats] = useState({
    totalPrescriptions: 0,
    prescriptionsActives: 0,
    prescriptionsTerminees: 0,
    prescriptionsUrgentes: 0
  });

  useEffect(() => {
    loadPrescriptions();
    loadPatients();
    loadMedicaments();
    loadStats();
  }, []);

  const loadPrescriptions = async () => {
    try {
      setLoading(true);
      const response = await veterinaireAPI.getPrescriptions();
      setPrescriptions(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des prescriptions');
      console.error('Erreur prescriptions:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    try {
      const response = await veterinaireAPI.getPatients();
      setPatients(response.data || []);
    } catch (err) {
      console.error('Erreur patients:', err);
    }
  };

  const loadMedicaments = async () => {
    try {
      const response = await veterinaireAPI.getMedicaments();
      setMedicaments(response.data || []);
    } catch (err) {
      console.error('Erreur médicaments:', err);
    }
  };

  const loadStats = async () => {
    try {
      const response = await veterinaireAPI.getPrescriptionsStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats prescriptions:', err);
    }
  };

  const handleOpenDialog = (prescription = null) => {
    if (prescription) {
      setSelectedPrescription(prescription);
      setFormData({
        patient_id: prescription.patient_id || '',
        diagnostic: prescription.diagnostic || '',
        date_prescription: new Date(prescription.date_prescription) || new Date(),
        date_fin_traitement: prescription.date_fin_traitement ? new Date(prescription.date_fin_traitement) : null,
        notes: prescription.notes || '',
        medicaments: prescription.medicaments || []
      });
    } else {
      setSelectedPrescription(null);
      setFormData({
        patient_id: '',
        diagnostic: '',
        date_prescription: new Date(),
        date_fin_traitement: null,
        notes: '',
        medicaments: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedPrescription(null);
    setCurrentMedicament({
      medicament_id: '',
      nom_medicament: '',
      dosage: '',
      frequence: '',
      duree: '',
      instructions: ''
    });
  };

  const handleAddMedicament = () => {
    if (currentMedicament.nom_medicament && currentMedicament.dosage) {
      setFormData({
        ...formData,
        medicaments: [...formData.medicaments, { ...currentMedicament, id: Date.now() }]
      });
      setCurrentMedicament({
        medicament_id: '',
        nom_medicament: '',
        dosage: '',
        frequence: '',
        duree: '',
        instructions: ''
      });
    }
  };

  const handleRemoveMedicament = (medicamentId) => {
    setFormData({
      ...formData,
      medicaments: formData.medicaments.filter(med => med.id !== medicamentId)
    });
  };

  const handleSubmit = async () => {
    try {
      const prescriptionData = {
        ...formData,
        veterinaire_id: user.profile_id
      };

      if (selectedPrescription) {
        await veterinaireAPI.updatePrescription(selectedPrescription.id, prescriptionData);
      } else {
        await veterinaireAPI.createPrescription(prescriptionData);
      }

      handleCloseDialog();
      loadPrescriptions();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la prescription');
      console.error('Erreur sauvegarde prescription:', err);
    }
  };

  const handlePrintPrescription = async (prescriptionId) => {
    try {
      const response = await veterinaireAPI.printPrescription(prescriptionId);
      // Ouvrir le PDF dans un nouvel onglet
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (err) {
      setError('Erreur lors de l\'impression de la prescription');
      console.error('Erreur impression:', err);
    }
  };

  const getStatutColor = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'active': return 'success';
      case 'terminee': return 'default';
      case 'suspendue': return 'warning';
      case 'urgente': return 'error';
      default: return 'default';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut?.toLowerCase()) {
      case 'active': return 'Active';
      case 'terminee': return 'Terminée';
      case 'suspendue': return 'Suspendue';
      case 'urgente': return 'Urgente';
      default: return statut;
    }
  };

  const StatCard = ({ title, value, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des prescriptions...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Prescriptions
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Prescription
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Prescriptions"
              value={stats.totalPrescriptions}
              icon={<PrescriptionIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Actives"
              value={stats.prescriptionsActives}
              icon={<CheckIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Terminées"
              value={stats.prescriptionsTerminees}
              icon={<ScheduleIcon />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Urgentes"
              value={stats.prescriptionsUrgentes}
              icon={<WarningIcon />}
              color="error"
            />
          </Grid>
        </Grid>

        {/* Table des prescriptions */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Liste des Prescriptions
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Patient</TableCell>
                    <TableCell>Diagnostic</TableCell>
                    <TableCell>Médicaments</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {prescriptions.map((prescription) => (
                    <TableRow key={prescription.id}>
                      <TableCell>
                        {new Date(prescription.date_prescription).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {prescription.patient_nom}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {prescription.eleveur_nom}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{prescription.diagnostic}</TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {prescription.medicaments?.length || 0} médicament(s)
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatutLabel(prescription.statut)}
                          color={getStatutColor(prescription.statut)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handlePrintPrescription(prescription.id)}
                          title="Imprimer"
                        >
                          <PrintIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(prescription)}
                          title="Modifier"
                        >
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Dialog pour créer/modifier une prescription */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>
            {selectedPrescription ? 'Modifier la Prescription' : 'Nouvelle Prescription'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={patients}
                  getOptionLabel={(option) => `${option.nom} - ${option.eleveur_nom}`}
                  value={patients.find(p => p.id === formData.patient_id) || null}
                  onChange={(event, newValue) => {
                    setFormData({ ...formData, patient_id: newValue?.id || '' });
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Patient" fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Date de Prescription"
                  value={formData.date_prescription}
                  onChange={(date) => setFormData({ ...formData, date_prescription: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Diagnostic"
                  multiline
                  rows={3}
                  value={formData.diagnostic}
                  onChange={(e) => setFormData({ ...formData, diagnostic: e.target.value })}
                />
              </Grid>

              {/* Section médicaments */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Médicaments Prescrits
                </Typography>
                <Divider sx={{ mb: 2 }} />

                {/* Ajouter un médicament */}
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={12} md={3}>
                    <Autocomplete
                      options={medicaments}
                      getOptionLabel={(option) => option.nom}
                      value={medicaments.find(m => m.id === currentMedicament.medicament_id) || null}
                      onChange={(event, newValue) => {
                        setCurrentMedicament({
                          ...currentMedicament,
                          medicament_id: newValue?.id || '',
                          nom_medicament: newValue?.nom || ''
                        });
                      }}
                      renderInput={(params) => (
                        <TextField {...params} label="Médicament" size="small" />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      size="small"
                      fullWidth
                      label="Dosage"
                      value={currentMedicament.dosage}
                      onChange={(e) => setCurrentMedicament({ ...currentMedicament, dosage: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      size="small"
                      fullWidth
                      label="Fréquence"
                      value={currentMedicament.frequence}
                      onChange={(e) => setCurrentMedicament({ ...currentMedicament, frequence: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      size="small"
                      fullWidth
                      label="Durée"
                      value={currentMedicament.duree}
                      onChange={(e) => setCurrentMedicament({ ...currentMedicament, duree: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={handleAddMedicament}
                      startIcon={<AddIcon />}
                    >
                      Ajouter
                    </Button>
                  </Grid>
                </Grid>

                {/* Liste des médicaments ajoutés */}
                <List>
                  {formData.medicaments.map((med, index) => (
                    <ListItem key={med.id || index} divider>
                      <ListItemText
                        primary={med.nom_medicament}
                        secondary={`${med.dosage} - ${med.frequence} - ${med.duree}`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleRemoveMedicament(med.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes et Instructions"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedPrescription ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default PrescriptionsManagement;
