const { User, Transaction, Volaille } = require('../models');
const { Op, fn, col, literal } = require('sequelize');
const sequelize = require('../config/database');

// Fonction utilitaire pour obtenir les mois précédents
const getLastMonths = (count) => {
  const months = [];
  const today = new Date();

  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
    months.push(date);
  }

  return months;
};

// Formater le nom du mois en français
const formatMonth = (date) => {
  return date.toLocaleString('fr-FR', { month: 'long', year: 'numeric' });
};

const getStatistics = async (req, res) => {
  try {
    // Récupérer le nombre d'utilisateurs par rôle
    const userCounts = await User.findAll({
      attributes: [
        'role',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['role']
    });

    const usersCount = {
      total: 0,
      byRole: {
        eleveur: 0,
        veterinaire: 0,
        marchand: 0
      }
    };

    userCounts.forEach(count => {
      const roleCount = parseInt(count.dataValues.count);
      usersCount.total += roleCount;
      usersCount.byRole[count.dataValues.role] = roleCount;
    });

    // Récupérer les transactions des 6 derniers mois
    const lastMonths = getLastMonths(6);
    const transactionStats = await Promise.all(
      lastMonths.map(async (month) => {
        const nextMonth = new Date(month.getFullYear(), month.getMonth() + 1, 1);

        const transactions = await Transaction.findAll({
          where: {
            createdAt: {
              [Op.gte]: month,
              [Op.lt]: nextMonth
            }
          },
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
            [sequelize.fn('SUM', sequelize.col('amount')), 'total']
          ]
        });

        return {
          month: formatMonth(month),
          count: parseInt(transactions[0].dataValues.count) || 0,
          amount: parseFloat(transactions[0].dataValues.total) || 0
        };
      })
    );

    // Récupérer les statistiques des volailles
    const volailleStats = await Volaille.findAll({
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['type']
    });

    const volailleData = {
      total: 0,
      byType: volailleStats.map(stat => ({
        type: stat.dataValues.type,
        count: parseInt(stat.dataValues.count)
      }))
    };

    volailleData.total = volailleData.byType.reduce((sum, item) => sum + item.count, 0);

    // Envoyer les statistiques
    res.json({
      usersCount,
      transactions: {
        total: transactionStats.reduce((sum, month) => sum + month.amount, 0),
        monthly: transactionStats
      },
      volailles: volailleData
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques'
    });
  }
};

// Statistiques détaillées des utilisateurs
const getUserStatistics = async (req, res) => {
  try {
    // Utilisateurs par région
    const usersByRegion = await User.findAll({
      attributes: [
        'region',
        [fn('COUNT', col('id')), 'count']
      ],
      group: ['region'],
      order: [[fn('COUNT', col('id')), 'DESC']]
    });

    // Nouveaux utilisateurs par mois
    const lastMonths = getLastMonths(12);
    const newUsersByMonth = await Promise.all(
      lastMonths.map(async (month) => {
        const nextMonth = new Date(month.getFullYear(), month.getMonth() + 1, 1);
        const count = await User.count({
          where: {
            createdAt: {
              [Op.gte]: month,
              [Op.lt]: nextMonth
            }
          }
        });

        return {
          month: formatMonth(month),
          count
        };
      })
    );

    // Taux d'activité des utilisateurs
    const userActivity = await User.findAll({
      attributes: [
        'role',
        [fn('COUNT', literal('CASE WHEN "lastLoginAt" >= NOW() - INTERVAL \'30 days\' THEN 1 END')), 'active'],
        [fn('COUNT', col('id')), 'total']
      ],
      group: ['role']
    });

    res.json({
      parRegion: usersByRegion.map(region => ({
        region: region.dataValues.region,
        count: parseInt(region.dataValues.count)
      })),
      nouveauxParMois: newUsersByMonth,
      tauxActivite: userActivity.map(stat => ({
        role: stat.dataValues.role,
        actifs: parseInt(stat.dataValues.active),
        total: parseInt(stat.dataValues.total),
        pourcentage: Math.round((parseInt(stat.dataValues.active) / parseInt(stat.dataValues.total)) * 100)
      }))
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques utilisateurs:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques utilisateurs'
    });
  }
};

// Statistiques détaillées des transactions
const getTransactionStatistics = async (req, res) => {
  try {
    // Volume des transactions par type
    const transactionsByType = await Transaction.findAll({
      attributes: [
        'type',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('amount')), 'total']
      ],
      group: ['type']
    });

    // Moyenne des transactions par période
    const lastMonths = getLastMonths(12);
    const avgTransactionsByPeriod = await Promise.all(
      lastMonths.map(async (month) => {
        const nextMonth = new Date(month.getFullYear(), month.getMonth() + 1, 1);
        const stats = await Transaction.findAll({
          where: {
            createdAt: {
              [Op.gte]: month,
              [Op.lt]: nextMonth
            }
          },
          attributes: [
            [fn('AVG', col('amount')), 'average'],
            [fn('COUNT', col('id')), 'count']
          ]
        });

        return {
          period: formatMonth(month),
          average: parseFloat(stats[0].dataValues.average) || 0,
          count: parseInt(stats[0].dataValues.count) || 0
        };
      })
    );

    // Taux de réussite des transactions
    const transactionSuccess = await Transaction.findAll({
      attributes: [
        [fn('COUNT', col('id')), 'total'],
        [fn('COUNT', literal('CASE WHEN status = \'success\' THEN 1 END')), 'success']
      ]
    });

    res.json({
      parType: transactionsByType.map(type => ({
        type: type.dataValues.type,
        nombre: parseInt(type.dataValues.count),
        montant: parseFloat(type.dataValues.total)
      })),
      moyenneParPeriode: avgTransactionsByPeriod,
      tauxReussite: {
        total: parseInt(transactionSuccess[0].dataValues.total),
        reussies: parseInt(transactionSuccess[0].dataValues.success),
        pourcentage: Math.round(
          (parseInt(transactionSuccess[0].dataValues.success) /
            parseInt(transactionSuccess[0].dataValues.total)) *
            100
        )
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques des transactions:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques des transactions'
    });
  }
};

module.exports = {
  getStatistics,
  getUserStatistics,
  getTransactionStatistics
};
