openapi: 3.0.0
info:
  title: Poultry DZ - API Marketplace
  description: |
    Documentation de l'API Marketplace de Poultry DZ.
    Cette API permet la gestion des annonces et des favoris dans la marketplace.
  version: 1.0.0

servers:
  - url: http://localhost:3000/api
    description: Serveur de développement

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Annonce:
      type: object
      properties:
        id:
          type: integer
          format: int64
        titre:
          type: string
          example: "Lot de poulets de chair"
        description:
          type: string
          example: "Poulets de chair de 45 jours, race Cobb500"
        espece:
          type: string
          enum: [poulet, dinde, caille, autre]
        race:
          type: string
          example: "Cobb500"
        quantite:
          type: integer
          minimum: 1
          example: 100
        prix_unitaire:
          type: number
          format: float
          example: 450.00
        images:
          type: array
          items:
            type: string
            format: uri
        status:
          type: string
          enum: [active, vendu, expire]
        vendeur_id:
          type: integer
          format: int64
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Error:
      type: object
      properties:
        error:
          type: string

paths:
  /marketplace/annonces:
    get:
      summary: Liste des annonces
      description: Récupère la liste des annonces avec pagination et filtres
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: Numéro de page
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Nombre d'annonces par page
        - in: query
          name: espece
          schema:
            type: string
          description: Filtre par espèce
        - in: query
          name: prix_min
          schema:
            type: number
          description: Prix minimum
        - in: query
          name: prix_max
          schema:
            type: number
          description: Prix maximum
      responses:
        '200':
          description: Liste des annonces
          content:
            application/json:
              schema:
                type: object
                properties:
                  annonces:
                    type: array
                    items:
                      $ref: '#/components/schemas/Annonce'
                  total:
                    type: integer
                  pages:
                    type: integer
                  currentPage:
                    type: integer

    post:
      summary: Créer une annonce
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - titre
                - description
                - espece
                - quantite
                - prix_unitaire
              properties:
                titre:
                  type: string
                description:
                  type: string
                espece:
                  type: string
                race:
                  type: string
                quantite:
                  type: integer
                prix_unitaire:
                  type: number
                images:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Annonce créée
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Annonce'
        '400':
          description: Données invalides
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /marketplace/annonces/{id}:
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
        description: ID de l'annonce

    get:
      summary: Détails d'une annonce
      responses:
        '200':
          description: Détails de l'annonce
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Annonce'
        '404':
          description: Annonce non trouvée

    put:
      summary: Modifier une annonce
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Annonce'
      responses:
        '200':
          description: Annonce modifiée
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Annonce'
        '403':
          description: Non autorisé
        '404':
          description: Annonce non trouvée

    delete:
      summary: Supprimer une annonce
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Annonce supprimée
        '403':
          description: Non autorisé
        '404':
          description: Annonce non trouvée

  /marketplace/annonces/{id}/favoris:
    post:
      summary: Ajouter/Retirer des favoris
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID de l'annonce
      responses:
        '200':
          description: Statut des favoris mis à jour
        '404':
          description: Annonce non trouvée

  /marketplace/favoris:
    get:
      summary: Liste des favoris
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Liste des annonces favorites
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Annonce'

  /marketplace/favoris/{id}:
    delete:
      summary: Retirer des favoris
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID de l'annonce
      responses:
        '200':
          description: Annonce retirée des favoris
