const express = require('express');
const router = express.Router();
const {
  getPrescriptionsByVeterinaire,
  createPrescription,
  updatePrescription,
  deletePrescription,
  getStatistiquesPrescriptions,
  getPrescriptionById,
  getPrescriptionsByEleveur,
  terminerPrescription
} = require('../controllers/prescriptionController');
const { auth, checkRole } = require('../middleware/auth');

// Middleware d'authentification pour toutes les routes
router.use(auth);

// Routes pour les prescriptions

// GET /api/prescriptions/veterinaire/:veterinaireId - Obtenir toutes les prescriptions d'un vétérinaire
router.get('/veterinaire/:veterinaireId', checkRole(['admin', 'veterinaire']), getPrescriptionsByVeterinaire);

// GET /api/prescriptions/veterinaire/:veterinaireId/statistiques - Obtenir les statistiques de prescription
router.get('/veterinaire/:veterinaireId/statistiques', checkRole(['admin', 'veterinaire']), getStatistiquesPrescriptions);

// GET /api/prescriptions/eleveur/:eleveurId - Obtenir toutes les prescriptions d'un éleveur
router.get('/eleveur/:eleveurId', checkRole(['admin', 'eleveur', 'veterinaire']), getPrescriptionsByEleveur);

// GET /api/prescriptions/:id - Obtenir une prescription par ID
router.get('/:id', checkRole(['admin', 'veterinaire', 'eleveur']), getPrescriptionById);

// POST /api/prescriptions - Créer une nouvelle prescription
router.post('/', checkRole(['admin', 'veterinaire']), createPrescription);

// PUT /api/prescriptions/:id - Mettre à jour une prescription
router.put('/:id', checkRole(['admin', 'veterinaire']), updatePrescription);

// PUT /api/prescriptions/:id/terminer - Marquer une prescription comme terminée
router.put('/:id/terminer', checkRole(['admin', 'veterinaire']), terminerPrescription);

// DELETE /api/prescriptions/:id - Supprimer une prescription
router.delete('/:id', checkRole(['admin', 'veterinaire']), deletePrescription);

module.exports = router;