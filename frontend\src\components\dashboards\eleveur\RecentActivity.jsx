import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  Button,
  useTheme
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  ShoppingCart,
  Egg,
  Pets,
  LocalShipping,
  MedicalServices,
  Inventory,
  MoreHoriz
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import axios from 'axios';
import { useAuthToken } from '../../../hooks/useAuthToken';

/**
 * Composant Activité Récente pour le dashboard éleveur
 * Affiche l'historique des dernières actions, transactions et mises à jour
 */
const RecentActivity = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { token, isAuthenticated } = useAuthToken();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activities, setActivities] = useState([]);
  const [displayCount, setDisplayCount] = useState(5);

  // Charger les activités récentes
  useEffect(() => {
    const fetchActivities = async () => {
      if (!isAuthenticated) {
        setError(new Error('Non authentifié'));
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const response = await axios.get('/api/eleveur/activities', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        setActivities(response.data);
        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération des activités:', err);
        setError(err);
        
        // Données de démonstration en cas d'erreur
        setActivities([
          {
            id: 1,
            type: 'add',
            category: 'lot',
            description: 'Ajout d\'un nouveau lot de poussins',
            details: 'Lot LOT005 - 200 poussins',
            date: '2024-06-05T09:30:00.000Z',
            user: 'Ahmed'
          },
          {
            id: 2,
            type: 'edit',
            category: 'production',
            description: 'Mise à jour de la production d\'œufs',
            details: 'Production du 04/06: 920 œufs',
            date: '2024-06-05T08:15:00.000Z',
            user: 'Ahmed'
          },
          {
            id: 3,
            type: 'add',
            category: 'vente',
            description: 'Nouvelle vente enregistrée',
            details: 'Vente de 500 œufs - 5000 DA',
            date: '2024-06-04T16:45:00.000Z',
            user: 'Sarah'
          },
          {
            id: 4,
            type: 'add',
            category: 'veterinaire',
            description: 'Nouvelle consultation vétérinaire',
            details: 'Dr. Karim - Vaccination lot LOT003',
            date: '2024-06-04T11:20:00.000Z',
            user: 'Ahmed'
          },
          {
            id: 5,
            type: 'edit',
            category: 'stock',
            description: 'Mise à jour du stock d\'aliments',
            details: 'Ajout de 500kg d\'aliment pour pondeuses',
            date: '2024-06-03T14:10:00.000Z',
            user: 'Ahmed'
          },
          {
            id: 6,
            type: 'delete',
            category: 'lot',
            description: 'Suppression d\'un lot',
            details: 'Lot LOT002 - Fin de cycle',
            date: '2024-06-03T10:05:00.000Z',
            user: 'Sarah'
          },
          {
            id: 7,
            type: 'add',
            category: 'achat',
            description: 'Nouvel achat enregistré',
            details: 'Achat de médicaments - 3000 DA',
            date: '2024-06-02T15:30:00.000Z',
            user: 'Ahmed'
          },
          {
            id: 8,
            type: 'edit',
            category: 'production',
            description: 'Mise à jour de la production d\'œufs',
            details: 'Production du 02/06: 880 œufs',
            date: '2024-06-02T08:20:00.000Z',
            user: 'Ahmed'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [isAuthenticated, token]);

  // Obtenir l'icône en fonction de la catégorie
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'lot':
        return <Pets />;
      case 'production':
        return <Egg />;
      case 'vente':
        return <ShoppingCart />;
      case 'achat':
        return <LocalShipping />;
      case 'veterinaire':
        return <MedicalServices />;
      case 'stock':
        return <Inventory />;
      default:
        return <MoreHoriz />;
    }
  };

  // Obtenir la couleur en fonction du type d'action
  const getActionColor = (type) => {
    switch (type) {
      case 'add':
        return theme.palette.success.main;
      case 'edit':
        return theme.palette.info.main;
      case 'delete':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  // Obtenir l'icône en fonction du type d'action
  const getActionIcon = (type) => {
    switch (type) {
      case 'add':
        return <Add />;
      case 'edit':
        return <Edit />;
      case 'delete':
        return <Delete />;
      default:
        return <MoreHoriz />;
    }
  };

  // Formater la date relative
  const formatRelativeDate = (dateString) => {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true, locale: fr });
  };

  // Afficher plus d'activités
  const handleShowMore = () => {
    setDisplayCount(prevCount => prevCount + 5);
  };

  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Limiter le nombre d'activités affichées
  const displayedActivities = activities.slice(0, displayCount);
  const hasMoreActivities = displayCount < activities.length;

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        {t('activity.recentActivity')}
      </Typography>
      <Divider sx={{ mb: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {t('errors.fetchFailed')}: {error.message}
        </Alert>
      )}

      {activities.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body1">
            {t('activity.noActivities')}
          </Typography>
        </Box>
      ) : (
        <List>
          {displayedActivities.map((activity, index) => (
            <React.Fragment key={activity.id}>
              <ListItem alignItems="flex-start">
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: getActionColor(activity.type) }}>
                    {getActionIcon(activity.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      <Typography variant="subtitle1" component="span" sx={{ mr: 1 }}>
                        {activity.description}
                      </Typography>
                      <Chip
                        icon={getCategoryIcon(activity.category)}
                        label={t(`activity.categories.${activity.category}`)}
                        size="small"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    </Box>
                  }
                  secondary={
                    <React.Fragment>
                      <Typography
                        component="span"
                        variant="body2"
                        color="text.primary"
                      >
                        {activity.details}
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {t('activity.by')} {activity.user}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatRelativeDate(activity.date)}
                        </Typography>
                      </Box>
                    </React.Fragment>
                  }
                />
              </ListItem>
              {index < displayedActivities.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>
      )}

      {hasMoreActivities && (
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Button 
            variant="outlined" 
            color="primary" 
            onClick={handleShowMore}
          >
            {t('activity.showMore')}
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default RecentActivity;

