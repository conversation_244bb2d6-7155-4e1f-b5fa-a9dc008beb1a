import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import homepageSectionService from '../../services/homepageSectionService';

const HomePageManager = () => {
  const [sections, setSections] = useState([]);
  const [editingSection, setEditingSection] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Charger les sections existantes
    loadSections();
  }, []);

  const loadSections = async () => {
    setLoading(true);
    try {
      const data = await homepageSectionService.getSections();
      setSections(data);
    } catch (error) {
      console.error('Error loading sections:', error);
      showSnackbar('Erreur lors du chargement des sections', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for a smooth UI experience
    const updatedSections = items.map((item, index) => ({
      ...item,
      order: index,
    }));
    setSections(updatedSections);

    try {
      // Persist the order changes to the backend
      await homepageSectionService.updateSectionOrders(updatedSections);
    } catch (error) {
      console.error('Error updating section orders:', error);
      showSnackbar('Erreur lors de la mise à jour de l\'ordre des sections', 'error');
    }

    setSections(updatedSections);
  };

  const handleAddSection = () => {
    setEditingSection({
      id: Date.now(),
      title: '',
      content: '',
      order: sections.length,
    });
    setOpenDialog(true);
  };

  const handleEditSection = (section) => {
    setEditingSection(section);
    setOpenDialog(true);
  };

  const handleDeleteSection = async (sectionId) => {
    try {
      setLoading(true);
      await homepageSectionService.deleteSection(sectionId);
      setSections(sections.filter((section) => section.id !== sectionId));
      showSnackbar('Section supprimée avec succès');
    } catch (error) {
      showSnackbar('Erreur lors de la suppression de la section', 'error');
    }
  };

  const handleSaveSection = async () => {
    try {
      setLoading(true);
      const isNewSection = !sections.find((s) => s.id === editingSection.id);

      let savedSection;
      if (isNewSection) {
        savedSection = await homepageSectionService.createSection(editingSection);
      } else {
        savedSection = await homepageSectionService.updateSection(editingSection.id, editingSection);
      }

      // Update local state
      if (isNewSection) {
        setSections([...sections, savedSection]);
      } else {
        setSections(sections.map((s) => s.id === savedSection.id ? savedSection : s));
      }

      setOpenDialog(false);
      showSnackbar('Section sauvegardée avec succès');
    } catch (error) {
      showSnackbar('Erreur lors de la sauvegarde de la section', 'error');
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h5" component="h2">
            Gestionnaire de Page d'Accueil
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddSection}
            disabled={loading}
          >
            Ajouter une section
          </Button>
        </Box>

        {loading && (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        )}

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="sections">
            {(provided) => (
              <List {...provided.droppableProps} ref={provided.innerRef}>
                {sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id.toString()}
                    index={index}
                  >
                    {(provided) => (
                      <ListItem
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        sx={{
                          border: '1px solid #e0e0e0',
                          borderRadius: 1,
                          mb: 1,
                          bgcolor: 'background.paper',
                        }}
                      >
                        <DragIndicatorIcon sx={{ mr: 2, color: 'text.secondary' }} />
                        <ListItemText
                          primary={section.title}
                          secondary={section.content}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="edit"
                            onClick={() => handleEditSection(section)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => handleDeleteSection(section.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </List>
            )}
          </Droppable>
        </DragDropContext>
      </Paper>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingSection?.id ? 'Modifier la section' : 'Ajouter une section'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Titre"
            fullWidth
            value={editingSection?.title || ''}
            onChange={(e) =>
              setEditingSection({ ...editingSection, title: e.target.value })
            }
          />
          <TextField
            margin="dense"
            label="Contenu"
            fullWidth
            multiline
            rows={4}
            value={editingSection?.content || ''}
            onChange={(e) =>
              setEditingSection({ ...editingSection, content: e.target.value })
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
          <Button onClick={handleSaveSection} variant="contained" color="primary">
            Sauvegarder
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default HomePageManager;
