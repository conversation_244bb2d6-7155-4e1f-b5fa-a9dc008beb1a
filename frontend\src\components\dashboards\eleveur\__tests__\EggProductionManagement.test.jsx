import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../translations/i18n';
import { EggProductionManagement } from '../index';
import testDbService from '../../../../services/testDbService';

// Test data
const testProductions = [
  {
    eleveur_id: 1,
    volaille_id: 1,
    date_production: new Date('2024-01-01'),
    nombre_poules: 500,
    oeufs_collectes: 1000,
    oeufs_vendables: 950,
    oeufs_casses: 20,
    oeufs_deformes: 15,
    oeufs_sales: 10,
    oeufs_petits: 5,
    poids_moyen_oeuf: 58.5,
    notes: 'TEST Production normale'
  },
  {
    eleveur_id: 1,
    volaille_id: 1,
    date_production: new Date('2024-01-02'),
    nombre_poules: 500,
    oeufs_collectes: 950,
    oeufs_vendables: 900,
    oeufs_casses: 25,
    oeufs_deformes: 10,
    oeufs_sales: 10,
    oeufs_petits: 5,
    poids_moyen_oeuf: 57.8,
    notes: 'TEST Légère baisse'
  }
];

describe('EggProductionManagement Component', () => {
  // Insérer les données de test avant tous les tests
  beforeAll(async () => {
    // Nettoyer les données de test existantes
    await testDbService.cleanTestData();
    
    // Insérer les données de test
    for (const production of testProductions) {
      await testDbService.insertTestProductionOeufs(production);
    }
  });
  
  // Nettoyer les données de test après tous les tests
  afterAll(async () => {
    await testDbService.cleanTestData();
  });
  
  const renderWithI18n = (component) => {
    return render(
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    );
  };
  
  test('renders loading state initially', () => {
    renderWithI18n(<EggProductionManagement />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  test('renders production table after loading', async () => {
    renderWithI18n(<EggProductionManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Vérifier que les données de test sont affichées
    expect(screen.getByText('1000')).toBeInTheDocument();
    expect(screen.getByText('950')).toBeInTheDocument();
    expect(screen.getByText('TEST Production normale')).toBeInTheDocument();
    expect(screen.getByText('TEST Légère baisse')).toBeInTheDocument();
  });
  
  test('opens add production dialog', async () => {
    renderWithI18n(<EggProductionManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue d'ajout
    fireEvent.click(screen.getByText('egg.addProduction'));
    
    // Vérifier que la boîte de dialogue est affichée
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByLabelText('egg.totalQuantity')).toBeInTheDocument();
  });
  
  test('validates production form data', async () => {
    renderWithI18n(<EggProductionManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue d'ajout
    fireEvent.click(screen.getByText('egg.addProduction'));
    
    // Essayer de soumettre avec des données invalides
    fireEvent.change(screen.getByLabelText('egg.qualityA'), {
      target: { value: '200' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.qualityB'), {
      target: { value: '100' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.qualityC'), {
      target: { value: '50' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.totalQuantity'), {
      target: { value: '400' }
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('common.save'));
    
    // Vérifier que le message d'erreur est affiché
    expect(screen.getByText('egg.errors.totalMismatch')).toBeInTheDocument();
  });
  
  test('handles production creation successfully', async () => {
    renderWithI18n(<EggProductionManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Ouvrir la boîte de dialogue d'ajout
    fireEvent.click(screen.getByText('egg.addProduction'));
    
    // Remplir le formulaire avec des données valides
    fireEvent.change(screen.getByLabelText('egg.totalQuantity'), {
      target: { value: '1000' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.qualityA'), {
      target: { value: '700' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.qualityB'), {
      target: { value: '200' }
    });
    
    fireEvent.change(screen.getByLabelText('egg.qualityC'), {
      target: { value: '100' }
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('common.save'));
    
    // Vérifier que la boîte de dialogue est fermée (la vérification réelle se fait via la base de données)
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });
  
  test('displays production statistics', async () => {
    renderWithI18n(<EggProductionManagement />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Vérifier que les statistiques sont affichées
    expect(screen.getByText('egg.stats.totalProduction')).toBeInTheDocument();
    expect(screen.getByText('1950')).toBeInTheDocument(); // Somme de toutes les productions
  });
  
  // Note: Le test d'erreur est commenté car il nécessiterait de simuler une erreur de base de données
  // test('handles error state', async () => {
  //   // Simuler une erreur de base de données
  //   jest.spyOn(testDbService, 'getProductionOeufs').mockRejectedValueOnce(new Error('Failed to fetch'));
  //   
  //   renderWithI18n(<EggProductionManagement />);
  //   
  //   // Attendre que l'erreur soit affichée
  //   await waitFor(() => {
  //     expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  //   }, { timeout: 5000 });
  //   
  //   expect(screen.getByText('errors.fetchFailed')).toBeInTheDocument();
  //   
  //   // Restaurer le mock
  //   jest.restoreAllMocks();
  // });
});
