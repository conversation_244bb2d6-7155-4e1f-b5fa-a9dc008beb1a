import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Container,
  Alert,
  Grid
} from '@mui/material';
import { Login as LoginIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import ValidatedForm from '../forms/ValidatedForm';
import ValidatedTextField from '../forms/ValidatedTextField';
import { ValidationSchemas } from '../../utils/validation';

const Login = () => {
  const { login, error, clearSessionExpired } = useAuth();
  const navigate = useNavigate();

  // Nettoyer l'état de session expirée au chargement
  React.useEffect(() => {
    if (clearSessionExpired) {
      clearSessionExpired();
    }
  }, [clearSessionExpired]);

  const getRedirectPath = (role) => {
    const rolePathMap = {
      'admin': '/admin/dashboard',
      'eleveur': '/eleveur/dashboard',
      'veterinaire': '/veterinaire/dashboard',
      'marchand': '/marchand/dashboard'
    };

    return rolePathMap[role] || '/dashboard';
  };

  const handleSubmit = async (formValues, { setErrors }) => {
    try {
      console.log('🔐 Début de la connexion avec:', formValues.email);

      const user = await login(formValues.email, formValues.password);
      console.log('✅ Connexion réussie:', user);

      // Vérifier que les données utilisateur sont complètes
      if (!user || !user.role) {
        console.error('❌ Données utilisateur invalides après connexion:', user);
        setErrors({ submit: 'Erreur: données utilisateur incomplètes' });
        return;
      }

      // Gérer les rôles string (legacy) et object (nouveau format API)
      const userRole = typeof user.role === 'object' && user.role !== null ? user.role.name : user.role;

      console.log('🎯 Redirection vers le dashboard pour le rôle:', userRole);

      // Petit délai pour s'assurer que l'état est mis à jour
      await new Promise(resolve => setTimeout(resolve, 100));

      // Redirection basée sur le rôle utilisateur
      const targetPath = getRedirectPath(userRole);
      console.log('📍 Navigation vers:', targetPath);

      navigate(targetPath, { replace: true });
    } catch (err) {
      console.error('❌ Erreur lors de la connexion:', err);
      console.error('Détails de l\'erreur:', {
        status: err.response?.status,
        statusText: err.response?.statusText,
        data: err.response?.data,
        message: err.message
      });

      setErrors({
        submit: err.response?.data?.message || err.message || 'Erreur de connexion. Veuillez vérifier vos identifiants.'
      });
    }
  };

  return (
    <Container maxWidth="sm">
      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography component="h1" variant="h5" sx={{ mb: 3 }}>
            🔐 Connexion
          </Typography>

          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {error}
            </Alert>
          )}

          <ValidatedForm
            initialValues={{ email: '', password: '' }}
            validationSchema={ValidationSchemas.login}
            onSubmit={handleSubmit}
            submitText="Se connecter"
            submitButtonProps={{
              fullWidth: true,
              startIcon: <LoginIcon />,
              sx: { mt: 3, mb: 2 }
            }}
          >
            <Grid item xs={12}>
              <ValidatedTextField
                name="email"
                label="Adresse email"
                type="email"
                autoComplete="email"
                autoFocus
                required
                placeholder="<EMAIL>"
                tooltip="Entrez l'adresse email associée à votre compte"
              />
            </Grid>

            <Grid item xs={12}>
              <ValidatedTextField
                name="password"
                label="Mot de passe"
                type="password"
                autoComplete="current-password"
                required
                tooltip="Entrez votre mot de passe"
              />
            </Grid>
          </ValidatedForm>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2">
              Vous n'avez pas de compte ?{' '}
              <Link to="/register" style={{ color: 'inherit' }}>
                Inscrivez-vous
              </Link>
            </Typography>

            {/* Debug link - only in development */}
            {import.meta.env.DEV && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                <Link to="/debug-navigation" style={{ color: '#666', fontSize: '0.8rem' }}>
                  🐛 Debug Navigation
                </Link>
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default Login;
