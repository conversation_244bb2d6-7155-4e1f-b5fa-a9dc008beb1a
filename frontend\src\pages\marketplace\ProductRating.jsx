import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Rating,
  TextField,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress
} from '@mui/material';
import { formatDistance } from 'date-fns';
import { fr } from 'date-fns/locale';
import axios from 'axios';

const ProductRating = ({ productId }) => {
  const [ratings, setRatings] = useState([]);
  const [userRating, setUserRating] = useState(0);
  const [comment, setComment] = useState('');
  const [averageRating, setAverageRating] = useState(0);
  const [totalRatings, setTotalRatings] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRating, setSelectedRating] = useState(null);

  useEffect(() => {
    fetchRatings();
  }, [productId]);

  const fetchRatings = async () => {
    try {
      const response = await axios.get(`/api/marketplace/products/${productId}/ratings`);
      setRatings(response.data.ratings);
      setAverageRating(response.data.averageRating);
      setTotalRatings(response.data.totalRatings);
    } catch (error) {
      console.error('Erreur lors du chargement des avis:', error);
      setError('Impossible de charger les avis');
    }
  };

  const handleSubmitRating = async () => {
    if (!userRating) {
      setError('Veuillez attribuer une note');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await axios.post(`/api/marketplace/products/${productId}/ratings`, {
        rating: userRating,
        comment
      });
      
      setSuccess(true);
      setUserRating(0);
      setComment('');
      fetchRatings();
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'avis:', error);
      setError(error.response?.data?.message || 'Impossible d\'envoyer l\'avis');
    } finally {
      setLoading(false);
    }
  };

  const handleReportRating = async (ratingId) => {
    try {
      await axios.post(`/api/marketplace/ratings/${ratingId}/report`);
      setOpenDialog(false);
      setSuccess(true);
    } catch (error) {
      console.error('Erreur lors du signalement:', error);
      setError('Impossible de signaler cet avis');
    }
  };

  const renderRatingStats = () => {
    const ratingCounts = ratings.reduce((acc, rating) => {
      acc[rating.rating] = (acc[rating.rating] || 0) + 1;
      return acc;
    }, {});

    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Avis clients
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="h3" component="span" sx={{ mr: 2 }}>
            {averageRating.toFixed(1)}
          </Typography>
          <Box>
            <Rating value={averageRating} precision={0.5} readOnly />
            <Typography color="text.secondary">
              {totalRatings} avis
            </Typography>
          </Box>
        </Box>
        {[5, 4, 3, 2, 1].map((star) => (
          <Box
            key={star}
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 0.5
            }}
          >
            <Typography sx={{ minWidth: 30 }}>{star}</Typography>
            <Rating value={star} max={1} size="small" readOnly sx={{ mx: 1 }} />
            <Box
              sx={{
                flexGrow: 1,
                bgcolor: 'background.paper',
                height: 10,
                borderRadius: 1,
                mr: 2
              }}
            >
              <Box
                sx={{
                  width: `${((ratingCounts[star] || 0) / totalRatings) * 100}%`,
                  height: '100%',
                  bgcolor: 'primary.main',
                  borderRadius: 1
                }}
              />
            </Box>
            <Typography color="text.secondary">
              {ratingCounts[star] || 0}
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };

  const renderRatingForm = () => (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Donner votre avis
      </Typography>
      <Box sx={{ mb: 2 }}>
        <Typography component="legend">Votre note</Typography>
        <Rating
          value={userRating}
          onChange={(event, newValue) => {
            setUserRating(newValue);
            setError('');
          }}
        />
      </Box>
      <TextField
        fullWidth
        multiline
        rows={4}
        label="Votre commentaire (optionnel)"
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        sx={{ mb: 2 }}
      />
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Votre avis a été publié avec succès
        </Alert>
      )}
      <Button
        variant="contained"
        onClick={handleSubmitRating}
        disabled={loading}
      >
        {loading ? <CircularProgress size={24} /> : 'Publier'}
      </Button>
    </Paper>
  );

  return (
    <Box>
      {renderRatingStats()}
      {renderRatingForm()}

      <List>
        {ratings.map((rating) => (
          <React.Fragment key={rating.id}>
            <ListItem
              alignItems="flex-start"
              secondaryAction={
                <Button
                  size="small"
                  onClick={() => {
                    setSelectedRating(rating);
                    setOpenDialog(true);
                  }}
                >
                  Signaler
                </Button>
              }
            >
              <ListItemAvatar>
                <Avatar src={rating.user.avatar_url}>
                  {rating.user.nom[0]}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography component="span" variant="subtitle1" sx={{ mr: 1 }}>
                      {rating.user.nom}
                    </Typography>
                    <Rating value={rating.rating} size="small" readOnly />
                  </Box>
                }
                secondary={
                  <React.Fragment>
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.primary"
                    >
                      {rating.comment}
                    </Typography>
                    <Typography
                      component="span"
                      variant="caption"
                      sx={{ display: 'block' }}
                    >
                      {formatDistance(new Date(rating.created_at), new Date(), {
                        addSuffix: true,
                        locale: fr
                      })}
                    </Typography>
                  </React.Fragment>
                }
              />
            </ListItem>
            <Divider variant="inset" component="li" />
          </React.Fragment>
        ))}
      </List>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Signaler un avis</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir signaler cet avis ?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            Annuler
          </Button>
          <Button
            onClick={() => handleReportRating(selectedRating?.id)}
            color="error"
          >
            Signaler
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductRating;