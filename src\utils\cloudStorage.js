const { Storage } = require('@google-cloud/storage');
const path = require('path');

// Initialiser le client Storage avec les credentials
const storage = new Storage({
  keyFilename: process.env.GOOGLE_CLOUD_STORAGE_KEY_PATH,
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID
});

const bucketName = process.env.GOOGLE_CLOUD_STORAGE_BUCKET;

/**
 * Upload un fichier vers Google Cloud Storage
 * @param {Object} file - Le fichier à uploader (req.file de multer)
 * @returns {Promise<string>} L'URL publique du fichier uploadé
 */
exports.uploadToCloudStorage = async (file) => {
  try {
    if (!file) {
      throw new Error('Aucun fichier fourni');
    }

    const bucket = storage.bucket(bucketName);
    const fileName = `${Date.now()}-${file.originalname}`;
    const blob = bucket.file(fileName);

    // Créer un stream pour uploader le fichier
    const blobStream = blob.createWriteStream({
      resumable: false,
      metadata: {
        contentType: file.mimetype
      }
    });

    // Gérer les erreurs de stream
    return new Promise((resolve, reject) => {
      blobStream.on('error', (error) => {
        console.error('Erreur lors de l\'upload vers Cloud Storage:', error);
        reject(error);
      });

      blobStream.on('finish', async () => {
        // Rendre le fichier public
        await blob.makePublic();
        const publicUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;
        resolve(publicUrl);
      });

      blobStream.end(file.buffer);
    });
  } catch (error) {
    console.error('Erreur dans uploadToCloudStorage:', error);
    throw error;
  }
};
