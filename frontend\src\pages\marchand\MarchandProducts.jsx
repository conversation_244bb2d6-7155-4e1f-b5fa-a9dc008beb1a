import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { DataGrid, frFR } from '@mui/x-data-grid';
import { useDropzone } from 'react-dropzone';
import Papa from 'papaparse';
import axios from 'axios';

const MarchandProducts = () => {
  const [products, setProducts] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [formData, setFormData] = useState({
    titre: '',
    description: '',
    espece: '',
    race: '',
    quantite: 0,
    prix_unitaire: 0,
    images: [],
    status: 'disponible'
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await axios.get('/api/marchand/products');
      setProducts(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des produits:', error);
    }
  };

  const handleDialogOpen = (product = null) => {
    if (product) {
      setSelectedProduct(product);
      setFormData(product);
    } else {
      setSelectedProduct(null);
      setFormData({
        titre: '',
        description: '',
        espece: '',
        race: '',
        quantite: 0,
        prix_unitaire: 0,
        images: [],
        status: 'disponible'
      });
    }
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setSelectedProduct(null);
  };

  const handleSubmit = async () => {
    try {
      if (selectedProduct) {
        await axios.put(`/api/marchand/products/${selectedProduct.id}`, formData);
      } else {
        await axios.post('/api/marchand/products', formData);
      }
      fetchProducts();
      handleDialogClose();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du produit:', error);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        await axios.delete(`/api/marchand/products/${id}`);
        fetchProducts();
      } catch (error) {
        console.error('Erreur lors de la suppression du produit:', error);
      }
    }
  };

  const onDrop = (acceptedFiles) => {
    // Gérer l'upload des images
    const formData = new FormData();
    acceptedFiles.forEach(file => {
      formData.append('images', file);
    });

    axios.post('/api/marchand/upload-images', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    .then(response => {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...response.data.urls]
      }));
    })
    .catch(error => {
      console.error('Erreur lors de l\'upload des images:', error);
    });
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: 'image/*',
    multiple: true
  });

  const handleImportCSV = (event) => {
    const file = event.target.files[0];
    if (file) {
      Papa.parse(file, {
        complete: async (results) => {
          try {
            await axios.post('/api/marchand/import-products', {
              data: results.data
            });
            fetchProducts();
          } catch (error) {
            console.error('Erreur lors de l\'import CSV:', error);
          }
        },
        header: true
      });
    }
  };

  const handleExportCSV = async () => {
    try {
      const response = await axios.get('/api/marchand/export-products');
      const csv = Papa.unparse(response.data);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = 'produits.csv';
      link.click();
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
    }
  };

  const columns = [
    { field: 'titre', headerName: 'Titre', flex: 1 },
    { field: 'espece', headerName: 'Espèce', width: 130 },
    { field: 'race', headerName: 'Race', width: 130 },
    { field: 'quantite', headerName: 'Quantité', width: 100, type: 'number' },
    { field: 'prix_unitaire', headerName: 'Prix unitaire', width: 130, type: 'number' },
    { field: 'status', headerName: 'Statut', width: 130 },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 130,
      renderCell: (params) => (
        <Box>
          <IconButton
            color="primary"
            onClick={() => handleDialogOpen(params.row)}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            color="error"
            onClick={() => handleDelete(params.row.id)}
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      )
    }
  ];

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Paper sx={{ p: 2 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">Gestion des Produits</Typography>
          <Box>
            <input
              type="file"
              accept=".csv"
              style={{ display: 'none' }}
              id="csv-upload"
              onChange={handleImportCSV}
            />
            <label htmlFor="csv-upload">
              <Button
                component="span"
                startIcon={<UploadIcon />}
                sx={{ mr: 1 }}
              >
                Importer CSV
              </Button>
            </label>
            <Button
              startIcon={<DownloadIcon />}
              onClick={handleExportCSV}
              sx={{ mr: 1 }}
            >
              Exporter CSV
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleDialogOpen()}
            >
              Nouveau Produit
            </Button>
          </Box>
        </Box>

        <DataGrid
          rows={products}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10]}
          checkboxSelection
          disableSelectionOnClick
          autoHeight
          localeText={frFR.components.MuiDataGrid.defaultProps.localeText}
        />
      </Paper>

      <Dialog open={openDialog} onClose={handleDialogClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProduct ? 'Modifier le produit' : 'Nouveau produit'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Titre"
                value={formData.titre}
                onChange={(e) => setFormData({ ...formData, titre: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Espèce</InputLabel>
                <Select
                  value={formData.espece}
                  label="Espèce"
                  onChange={(e) => setFormData({ ...formData, espece: e.target.value })}
                >
                  <MenuItem value="poule">Poule</MenuItem>
                  <MenuItem value="dinde">Dinde</MenuItem>
                  <MenuItem value="canard">Canard</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Race"
                value={formData.race}
                onChange={(e) => setFormData({ ...formData, race: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Quantité"
                value={formData.quantite}
                onChange={(e) => setFormData({ ...formData, quantite: parseInt(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Prix unitaire"
                value={formData.prix_unitaire}
                onChange={(e) => setFormData({ ...formData, prix_unitaire: parseFloat(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Statut</InputLabel>
                <Select
                  value={formData.status}
                  label="Statut"
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value="disponible">Disponible</MenuItem>
                  <MenuItem value="rupture">Rupture de stock</MenuItem>
                  <MenuItem value="reserve">Réservé</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <Box {...getRootProps()} sx={{
                border: '2px dashed grey',
                p: 2,
                textAlign: 'center',
                cursor: 'pointer'
              }}>
                <input {...getInputProps()} />
                <Typography>Glissez des images ici ou cliquez pour sélectionner</Typography>
              </Box>
              {formData.images.length > 0 && (
                <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {formData.images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`Product ${index + 1}`}
                      style={{ width: 100, height: 100, objectFit: 'cover' }}
                    />
                  ))}
                </Box>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedProduct ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MarchandProducts;