const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SubscriptionPlan extends Model {
    // Pas d'associations définies explicitement ici, mais un plan pourrait être lié à des Users ou Orders.
    // static associate(models) {
    //   SubscriptionPlan.hasMany(models.UserSubscription, { foreignKey: 'plan_id', as: 'userSubscriptions' });
    // }

    static async findByName(name) {
      return await this.findOne({ where: { name } });
    }

    static async findActive() {
      return await this.findAll({
        where: { is_active: true },
        order: [['price', 'ASC']]
      });
    }

    static async updateFeatures(id, features) {
      return await this.update(
        { features },
        { where: { id } }
      );
    }

    static async toggleActive(id) {
      const plan = await this.findByPk(id);
      if (plan) {
        return await plan.update({ is_active: !plan.is_active });
      }
      return null;
    }
  }

  SubscriptionPlan.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    duration_days: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    features: {
      type: DataTypes.JSONB,
      defaultValue: []
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    sequelize,
    modelName: 'SubscriptionPlan',
    tableName: 'subscription_plans',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return SubscriptionPlan;
};
