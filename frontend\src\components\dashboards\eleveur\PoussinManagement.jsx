import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

const PoussinManagement = () => {
  const { t } = useTranslation();
  const [lots, setLots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedLot, setSelectedLot] = useState(null);
  const [formData, setFormData] = useState({
    lot_numero: '',
    race: '',
    souche: '',
    quantite_initiale: '',
    date_eclosion: '',
    poids_objectif: ''
  });

  useEffect(() => {
    fetchLots();
  }, []);

  const fetchLots = async () => {
    try {
      const response = await axios.get('/api/poussins');
      setLots(response.data);
      setLoading(false);
    } catch (err) {
      setError(t('errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleOpenDialog = (lot = null) => {
    if (lot) {
      setSelectedLot(lot);
      setFormData({
        lot_numero: lot.lot_numero,
        race: lot.race,
        souche: lot.souche,
        quantite_initiale: lot.quantite_initiale,
        date_eclosion: lot.date_eclosion.split('T')[0],
        poids_objectif: lot.poids_objectif
      });
    } else {
      setSelectedLot(null);
      setFormData({
        lot_numero: '',
        race: '',
        souche: '',
        quantite_initiale: '',
        date_eclosion: '',
        poids_objectif: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedLot(null);
    setFormData({
      lot_numero: '',
      race: '',
      souche: '',
      quantite_initiale: '',
      date_eclosion: '',
      poids_objectif: ''
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (selectedLot) {
        await axios.put(`/api/poussins/${selectedLot.id}`, formData);
      } else {
        await axios.post('/api/poussins', formData);
      }
      fetchLots();
      handleCloseDialog();
    } catch (err) {
      setError(t('errors.saveFailed'));
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('confirmations.deleteLot'))) {
      try {
        await axios.delete(`/api/poussins/${id}`);
        fetchLots();
      } catch (err) {
        setError(t('errors.deleteFailed'));
      }
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          {t('poussin.management')}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {t('poussin.addLot')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('poussin.lotNumber')}</TableCell>
              <TableCell>{t('poussin.race')}</TableCell>
              <TableCell>{t('poussin.souche')}</TableCell>
              <TableCell align="right">{t('poussin.quantity')}</TableCell>
              <TableCell>{t('poussin.eclosionDate')}</TableCell>
              <TableCell align="right">{t('poussin.targetWeight')}</TableCell>
              <TableCell align="center">{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lots.map((lot) => (
              <TableRow key={lot.id}>
                <TableCell>{lot.lot_numero}</TableCell>
                <TableCell>{lot.race}</TableCell>
                <TableCell>{lot.souche}</TableCell>
                <TableCell align="right">{lot.quantite_initiale}</TableCell>
                <TableCell>{new Date(lot.date_eclosion).toLocaleDateString()}</TableCell>
                <TableCell align="right">{lot.poids_objectif}g</TableCell>
                <TableCell align="center">
                  <IconButton onClick={() => handleOpenDialog(lot)} color="primary">
                    <EditIcon />
                  </IconButton>
                  <IconButton onClick={() => handleDelete(lot.id)} color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedLot ? t('poussin.editLot') : t('poussin.addLot')}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="lot_numero"
                label={t('poussin.lotNumber')}
                value={formData.lot_numero}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="race"
                label={t('poussin.race')}
                value={formData.race}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="souche"
                label={t('poussin.souche')}
                value={formData.souche}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="quantite_initiale"
                label={t('poussin.quantity')}
                type="number"
                value={formData.quantite_initiale}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="date_eclosion"
                label={t('poussin.eclosionDate')}
                type="date"
                value={formData.date_eclosion}
                onChange={handleInputChange}
                fullWidth
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="poids_objectif"
                label={t('poussin.targetWeight')}
                type="number"
                value={formData.poids_objectif}
                onChange={handleInputChange}
                fullWidth
                InputProps={{ endAdornment: 'g' }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {selectedLot ? t('common.save') : t('common.add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PoussinManagement;
