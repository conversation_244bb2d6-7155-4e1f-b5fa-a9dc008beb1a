# Guide de Déploiement - Poultray DZ

Ce guide décrit le processus de déploiement de l'application Poultray DZ en production et staging.

## 🏗️ Architecture de Déploiement

### Environnements

- **Staging**: `staging.poultraydz.com` - Tests et validation
- **Production**: `app.poultraydz.com` - Application live

### Services Déployés

- **Frontend React**: Application web responsive
- **Backend Node.js**: API REST et WebSocket
- **PostgreSQL**: Base de données principale
- **Redis**: Cache et sessions
- **Nginx**: Reverse proxy et serveur web
- **Prometheus**: Monitoring et métriques
- **Grafana**: Dashboards de monitoring
- **AlertManager**: Gestion des alertes
- **Elasticsearch/Kibana**: Logs centralisés

## 🚀 Déploiement Automatique

### Prérequis

```bash
# Installer Docker et Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Installer Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Configuration des Variables d'Environnement

Créer les fichiers `.env.staging` et `.env.production` :

```bash
# Base de données
DB_NAME=poultraydz
DB_USER=postgres
DB_PASSWORD=your_secure_password

# Redis
REDIS_PASSWORD=your_redis_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret

# CORS
CORS_ORIGIN=https://app.poultraydz.com

# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_grafana_password

# Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Sauvegarde S3
S3_BACKUP_BUCKET=poultraydz-backups
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key
S3_REGION=eu-west-1
```

### Déploiement en Staging

```bash
# Déployer la dernière version de develop
./scripts/deploy.sh staging

# Déployer une version spécifique
./scripts/deploy.sh staging v1.2.3
```

### Déploiement en Production

```bash
# Déployer la dernière version de main
./scripts/deploy.sh production

# Déployer une version spécifique
./scripts/deploy.sh production v1.2.3
```

## 🔧 Déploiement Manuel

### 1. Préparation

```bash
# Cloner le repository
git clone https://github.com/your-org/poultraydz.git
cd poultraydz

# Checkout de la branche appropriée
git checkout main  # pour production
git checkout develop  # pour staging
```

### 2. Configuration

```bash
# Copier et configurer les variables d'environnement
cp .env.example .env.production
nano .env.production
```

### 3. Déploiement

```bash
# Construire et démarrer les services
docker-compose -f docker-compose.production.yml up -d

# Vérifier le statut
docker-compose -f docker-compose.production.yml ps

# Voir les logs
docker-compose -f docker-compose.production.yml logs -f
```

### 4. Migrations

```bash
# Exécuter les migrations de base de données
docker-compose -f docker-compose.production.yml exec backend npm run migrate
```

## 📊 Monitoring et Observabilité

### Accès aux Dashboards

- **Grafana**: `https://monitoring.poultraydz.com:3001`
- **Prometheus**: `https://monitoring.poultraydz.com:9090`
- **Kibana**: `https://monitoring.poultraydz.com:5601`
- **AlertManager**: `https://monitoring.poultraydz.com:9093`

### Métriques Surveillées

#### Infrastructure
- Utilisation CPU/RAM/Disque
- Latence réseau
- Disponibilité des services

#### Application
- Temps de réponse API
- Taux d'erreur
- Nombre de requêtes/seconde
- Connexions WebSocket actives

#### Base de Données
- Connexions actives
- Taille de la base
- Performance des requêtes
- Réplication et sauvegarde

#### Métier
- Nombre d'utilisateurs actifs
- Nouvelles inscriptions
- Activité de production
- Consultations vétérinaires

### Alertes Configurées

#### Critiques (Notification immédiate)
- Service indisponible
- Erreur 5xx > 5%
- Espace disque < 10%
- Mémoire > 90%

#### Warnings (Notification groupée)
- CPU > 80%
- Temps de réponse > 2s
- Connexions DB élevées

## 💾 Sauvegarde et Restauration

### Sauvegarde Automatique

Les sauvegardes sont automatiques via cron :

```bash
# Vérifier le cron de sauvegarde
docker-compose exec backup crontab -l

# Forcer une sauvegarde manuelle
docker-compose exec backup /app/backup.sh
```

### Restauration

```bash
# Lister les sauvegardes disponibles
ls -la /backups/

# Restaurer une sauvegarde
./scripts/restore.sh poultraydz_backup_20240101_120000.tar.gz
```

## 🔒 Sécurité

### Certificats SSL

Les certificats sont gérés automatiquement via Let's Encrypt :

```bash
# Renouveler manuellement
docker-compose exec traefik traefik --certificatesresolvers.letsencrypt.acme.caserver=https://acme-v02.api.letsencrypt.org/directory
```

### Mise à Jour de Sécurité

```bash
# Mettre à jour les images de base
docker-compose pull
docker-compose up -d

# Audit de sécurité
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image poultraydz/backend
```

## 🚨 Gestion des Incidents

### Procédure d'Urgence

1. **Identifier le problème**
   ```bash
   # Vérifier le statut des services
   docker-compose ps
   
   # Consulter les logs
   docker-compose logs --tail=100 backend
   ```

2. **Rollback rapide**
   ```bash
   # Rollback automatique
   ./scripts/rollback.sh
   
   # Ou rollback manuel vers une version précédente
   git checkout v1.2.2
   ./scripts/deploy.sh production v1.2.2
   ```

3. **Communication**
   - Notifier l'équipe via Slack
   - Mettre à jour la page de statut
   - Informer les utilisateurs si nécessaire

### Contacts d'Urgence

- **DevOps**: +213 XXX XXX XXX
- **Backend**: +213 XXX XXX XXX
- **DBA**: +213 XXX XXX XXX

## 📈 Performance et Optimisation

### Optimisations Appliquées

#### Frontend
- Compression Gzip/Brotli
- Cache des assets statiques
- Lazy loading des composants
- Service Worker pour PWA

#### Backend
- Connection pooling PostgreSQL
- Cache Redis pour les sessions
- Rate limiting par IP
- Compression des réponses

#### Base de Données
- Index optimisés
- Requêtes préparées
- Monitoring des requêtes lentes
- Partitioning des tables volumineuses

### Tests de Performance

```bash
# Test de charge avec Artillery
npm install -g artillery
artillery run tests/load/api-test.yml

# Test de performance frontend
npm run lighthouse
```

## 🔄 CI/CD Pipeline

### GitHub Actions

Le pipeline CI/CD est configuré dans `.github/workflows/ci-cd.yml` :

1. **Tests** (sur chaque PR)
   - Tests unitaires
   - Tests d'intégration
   - Analyse de sécurité
   - Linting

2. **Build** (sur push main/develop)
   - Construction des images Docker
   - Push vers le registry

3. **Deploy** (automatique)
   - Staging : sur push develop
   - Production : sur push main (avec approbation)

### Hooks de Déploiement

```bash
# Pre-deploy hooks
scripts/hooks/pre-deploy.sh

# Post-deploy hooks
scripts/hooks/post-deploy.sh
```

## 📚 Ressources Supplémentaires

- [Documentation API](./docs/api.md)
- [Guide de Développement](./docs/development.md)
- [Architecture Technique](./docs/architecture.md)
- [Troubleshooting](./docs/troubleshooting.md)

## 🆘 Support

Pour toute question ou problème :

- **Documentation**: [docs.poultraydz.com](https://docs.poultraydz.com)
- **Issues GitHub**: [github.com/your-org/poultraydz/issues](https://github.com/your-org/poultraydz/issues)
- **Email Support**: <EMAIL>
- **Slack**: #poultraydz-support
