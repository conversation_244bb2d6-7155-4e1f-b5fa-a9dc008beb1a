# Poultry DZ Mobile App (Flutter) - Project Plan

## Vision
Créer une application mobile Flutter moderne, simple et puissante pour la gestion avicole en Algérie, connectée au backend Poultry DZ.

## Objectifs clés
- Interface intuitive, adaptée terrain (mobile-first)
- Accès rapide aux fonctionnalités essentielles (dashboard, élevage, ventes, santé, notifications)
- Multilingue : Français (par défaut), Arabe
- Sécurité et synchronisation temps réel
- Notifications push et alertes santé
- Optimisation pour connexions lentes/intermittentes

## Architecture technique
- **Flutter** (Dart) avec Provider ou Bloc pour la gestion d'état
- **API REST** sécurisée (JWT) vers le backend Node.js/Express
- **Stockage local** (SQLite ou Hive) pour mode offline
- **Firebase** pour notifications push
- **Responsive** pour smartphones et tablettes

## Fonctionnalités principales
### 1. Authentification & Sécurité
- Connexion/inscription (JWT)
- Gestion des rôles (Éleveur, Vétérinaire, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)
- Réinitialisation du mot de passe

### 2. Tableau de bord personnalisé
- Statistiques clés (production, ventes, alertes)
- Graphiques interactifs (Recharts Flutter)
- Accès rapide aux modules principaux

### 3. Gestion de l'élevage
- Suivi des lots, mortalité, alimentation
- Ajout/modification des saisies quotidiennes
- Gestion des ouvriers de ferme

### 4. Services vétérinaires
- Consultation à distance (chat, visio, prise de RDV)
- Historique des prescriptions et alertes santé

### 5. Marketplace & ventes
- Recherche/filtrage de produits
- Ajout rapide de ventes
- Gestion des commandes et paiements

### 6. Notifications & alertes
- Notifications push (Firebase)
- Alertes santé, stock, météo

### 7. Paramètres & profil
- Gestion du profil utilisateur
- Préférences de langue
- Sécurité (2FA, gestion des appareils)

## Parcours utilisateur (UX)
- Onboarding simple et tutoriel interactif
- Navigation par onglets (BottomNavigationBar)
- Actions rapides (FloatingActionButton)
- Feedback utilisateur (snackbars, loaders)

## Design
- Palette de couleurs inspirée du branding Poultry DZ
- Icônes Material Design
- Accessibilité (contrastes, tailles, ARIA)
- Responsive pour tous formats

## Roadmap de développement
1. Initialisation du projet Flutter
2. Authentification et navigation de base
3. Intégration API backend (connexion sécurisée)
4. Implémentation progressive des modules (dashboard, élevage, ventes...)
5. Tests utilisateurs terrain et itérations UX
6. Optimisation offline et notifications push
7. Déploiement sur Play Store et App Store

## Liens backend/API
- Utilisation des mêmes endpoints REST sécurisés que la webapp
- Gestion des tokens JWT et rafraîchissement
- Synchronisation des données (mode offline/online)

## Sécurité & conformité
- Chiffrement des données sensibles
- Respect des normes RGPD/algériennes
- Audit de sécurité avant mise en production








### 📦 **Addendum – Intégration avec l’écosystème Poultray DZ Web**

#### 🔗 Intégration API Web existante

L’application mobile Flutter s’appuiera directement sur les **endpoints REST déjà implémentés dans la version Web** (`v2.0.0`) pour :

* Le tableau de bord éleveur
* Le suivi vétérinaire
* Le module marchand & marketplace

**Objectifs :**

* Mutualiser la logique métier entre le frontend web et mobile
* Réduire les duplications côté mobile
* Faciliter la maintenance à long terme

#### 🧪 Authentification et rôles

* Utilisation de l’authentification JWT du backend Web (login + refresh token)
* Gestion multi-rôles (Éleveur, Vétérinaire, Marchand)
* Redirection automatique post-login vers l’interface mobile adaptée
* Stockage sécurisé des tokens via `flutter_secure_storage`

#### 📬 Notifications & alertes

* Intégration de **Firebase Cloud Messaging** (FCM)
* Souscription à des topics dynamiques (`alertes_<type>_<id>`)
* Affichage natif des alertes critiques (santé, stock, commandes)
* Association backend ↔ mobile via webhook Firebase

#### 📡 Synchronisation & mode offline

* Stockage local (Hive / SQLite) pour :

  * Saisies journalières (`saisies_quotidiennes`)
  * Consultations en attente
  * Données élevage / ventes
* Fallback offline avec file d’attente des actions → synchronisation dès retour en ligne

#### 🧰 Navigation & UI

* Structure mobile inspirée de la version Web (tabbed dashboards + quick actions)
* Navigation fluide entre modules : élevage, vétérinaire, vente, alertes
* Matérial Design unifié (couleurs, contrastes, composants)

---

## 📁 2. `MOBILE_INTEGRATION_GUIDE.md`

À ajouter dans ton repo dans `/docs/` :

````markdown
# 📱 MOBILE_INTEGRATION_GUIDE.md

## Objectif
Ce guide décrit comment l’application mobile Flutter s’intègre avec l’infrastructure existante de Poultray DZ Web v2.0.0, notamment l’API backend, les rôles, la sécurité, et les systèmes d’alertes.

---

## 🔐 Authentification

- Endpoint d’authentification partagé : `/api/auth/login`
- Réponse : JWT + Refresh token
- Stockage des tokens :
  - `auth_token` → stocké via `flutter_secure_storage`
  - `refresh_token` → utilisé via Dio Interceptor
- Rafraîchissement :
```dart
final response = await dio.post('/api/auth/refresh', data: {'refreshToken': token});
````

---

## 🧑‍🌾 Rôles & Dashboards

| Rôle        | Endpoint principal        | Interface mobile              |
| ----------- | ------------------------- | ----------------------------- |
| Éleveur     | `/api/eleveur/dashboard`  | Dashboard Éleveur             |
| Vétérinaire | `/api/veterinaire/stats`  | Suivi consultations & alertes |
| Marchand    | `/api/marchand/dashboard` | Commandes & produits          |

---

## 🔁 Données synchronisées

| Donnée               | Endpoint             | Local Storage    |
| -------------------- | -------------------- | ---------------- |
| Saisies quotidiennes | `/api/saisies`       | Hive             |
| Consultations        | `/api/consultations` | Hive             |
| Volaille             | `/api/volailles`     | Cache temporaire |
| Produits             | `/api/products`      | Mise en cache    |

---

## 📬 Notifications

* Utilisation de Firebase Cloud Messaging (FCM)
* Topics dynamiques : `alertes_sante_<user_id>`, `alertes_stock_<ferme_id>`
* Notification affichée même hors ligne (FCM native)
* Endpoint backend à lier : `/api/notifications/send`

---

## 📴 Mode Offline

* Toutes les écritures sont stockées localement en queue si offline
* Synchronisation automatique au retour de connexion :

  * Tentatives multiples avec délai exponentiel
  * Logs d’échec pour debug

---

## 🌐 Sécurité

* Reprise des politiques de validation du backend Web
* Filtrage de rôle au niveau mobile (et backend en fallback)
* Chiffrement local des données sensibles
* Validation des réponses API et erreurs 401 → redirection login

---

## 🛠 Packages Flutter recommandés

* `dio` → HTTP client + interceptors
* `flutter_secure_storage` → stockage sécurisé des tokens
* `hive` → base de données locale rapide
* `firebase_messaging` → notifications
* `flutter_bloc` ou `provider` → gestion d’état
* `fl_chart` ou `syncfusion_flutter_charts` → visualisation des stats

---

## 📅 Roadmap technique mobile (liée à la version Web 2.0.0)

| Étape                  | Description                                     |
| ---------------------- | ----------------------------------------------- |
| ✅ Backend prêt         | Tous les endpoints nécessaires sont disponibles |
| 🔄 Intégration Flutter | En cours – tests sur `/api/eleveur/dashboard`   |
| 🔜 Notifications       | Liaison Firebase ↔ backend                      |
| 🔜 Offline Sync        | Implémentation Hive + synchroniseur             |
| 📦 Déploiement mobile  | Prévu après les tests terrain (étape 6)         |

-
```
