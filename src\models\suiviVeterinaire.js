const { DataTypes, Model, Op } = require('sequelize');

/**
 * Modèle SuiviVeterinaire pour le suivi médical avancé
 * Partie de l'amélioration du tableau de bord éleveur - Phase 1
 *
 * Fonctionnalités:
 * - Suivi médical détaillé
 * - Gestion des vaccinations
 * - Historique des traitements
 * - Planification des interventions
 */
module.exports = (sequelize, DataTypes) => {
  class SuiviVeterinaire extends Model {
    static associate(models) {
      SuiviVeterinaire.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });
      SuiviVeterinaire.belongsTo(models.User, { // En supposant que vétérinaire est un User
        foreignKey: 'veterinaire_id',
        as: 'veterinaire',
        required: false
      });
      SuiviVeterinaire.belongsTo(models.Volaille, {
        foreignKey: 'volaille_id',
        as: 'volailleLot',
        required: false
      });
      SuiviVeterinaire.belongsTo(models.<PERSON><PERSON><PERSON>, {
        foreignKey: 'poussin_id',
        as: 'poussinLot',
        required: false
      });
    }

    async marquerTermine(diagnostic, traitement, recommandations) { // Changé en méthode d'instance et async
      this.statut = 'termine';
      this.diagnostic = diagnostic;
      this.traitement_prescrit = traitement;
      this.recommandations = recommandations;
      return await this.save(); // Ajout de await
    }

    async ajouterSuiviPost(observations, efficacite) { // Changé en méthode d'instance et async
      const suivi = this.suivi_post_intervention || {};
      const maintenant = new Date();
      const dateIntervention = new Date(this.date_intervention); // Assurer que c'est un objet Date
      const joursDepuis = Math.floor((maintenant - dateIntervention) / (1000 * 60 * 60 * 24));

      if (joursDepuis <= 1) {
        suivi.observations_j1 = observations;
      } else if (joursDepuis <= 3) {
        suivi.observations_j3 = observations;
      } else if (joursDepuis <= 7) {
        suivi.observations_j7 = observations;
      }

      if (efficacite) {
        suivi.efficacite_traitement = efficacite;
      }

      this.suivi_post_intervention = suivi;
      return await this.save(); // Ajout de await
    }

    async planifierRappel(type, dateRappel, motif) { // Changé en méthode d'instance et async
      this.prochaine_intervention = {
        type,
        date_prevue: dateRappel,
        motif,
        rappel_active: true
      };
      return await this.save(); // Ajout de await
    }

    static async findByEleveur(eleveurId, options = {}) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          ...options.where
        },
        order: [['date_intervention', 'DESC']],
        ...options
      });
    }

    static async findByVeterinaire(veterinaireId, options = {}) {
      return this.findAll({
        where: {
          veterinaire_id: veterinaireId,
          ...options.where
        },
        order: [['date_intervention', 'DESC']],
        ...options
      });
    }

    static async findInterventionsPlanifiees(eleveurId) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          statut: 'planifie',
          date_intervention: {
            [Op.gte]: new Date() // Utilisation de Op
          }
        },
        order: [['date_intervention', 'ASC']]
      });
    }

    static async findInterventionsUrgentes(eleveurId) {
      return this.findAll({
        where: {
          eleveur_id: eleveurId,
          urgence: { [Op.in]: ['elevee', 'critique'] }, // Utilisation de Op
          statut: { [Op.in]: ['planifie', 'en_cours'] }  // Utilisation de Op
        },
        order: [['urgence', 'DESC'], ['date_intervention', 'ASC']]
      });
    }

    static async getStatistiquesVeterinaires(eleveurId, annee) {
      const dateDebut = new Date(annee, 0, 1);
      const dateFin = new Date(annee, 11, 31);
      const interventions = await this.findAll({
        where: {
          eleveur_id: eleveurId,
          date_intervention: {
            [Op.between]: [dateDebut, dateFin] // Utilisation de Op
          }
        }
      });

      const stats = {
        total_interventions: interventions.length,
        par_type: {},
        par_mois: {},
        cout_total: 0,
        interventions_urgentes: 0,
        taux_satisfaction_moyen: 0,
        vaccinations_effectuees: 0,
        traitements_curatifs: 0
      };

      // Grouper par type
      interventions.forEach(intervention => {
        const type = intervention.type_intervention;
        stats.par_type[type] = (stats.par_type[type] || 0) + 1;

        // Coût total
        stats.cout_total += (intervention.cout_total || 0); // Utilise le virtual field

        // Interventions urgentes
        if (intervention.urgence === 'elevee' || intervention.urgence === 'critique') {
          stats.interventions_urgentes++;
        }

        // Satisfaction
        if (intervention.satisfaction_eleveur) {
          stats.taux_satisfaction_moyen += intervention.satisfaction_eleveur;
        }

        // Types spécifiques
        if (type === 'vaccination') {
          stats.vaccinations_effectuees++;
        }
        if (type === 'traitement_curatif') {
          stats.traitements_curatifs++;
        }

        // Par mois
        const mois = new Date(intervention.date_intervention).getMonth();
        stats.par_mois[mois] = (stats.par_mois[mois] || 0) + 1;
      });

      // Calculer la moyenne de satisfaction
      const interventionsAvecSatisfaction = interventions.filter(i => i.satisfaction_eleveur);
      if (interventionsAvecSatisfaction.length > 0) {
        stats.taux_satisfaction_moyen = stats.taux_satisfaction_moyen / interventionsAvecSatisfaction.length;
      }

      return stats;
    }

    static async getPlanVaccination(eleveurId) {
      const vaccinations = await this.findAll({
        where: {
          eleveur_id: eleveurId,
          type_intervention: 'vaccination'
        },
        order: [['date_intervention', 'DESC']]
      });

      // Analyser les vaccinations pour suggérer les prochaines
      const planVaccination = {
        vaccinations_effectuees: vaccinations.length,
        derniere_vaccination: vaccinations[0] || null,
        vaccinations_a_venir: [],
        retards_possibles: []
      };

      // Logique pour déterminer les vaccinations à venir
      // (à adapter selon les protocoles vétérinaires locaux)

      return planVaccination;
    }
  }

  SuiviVeterinaire.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    eleveur_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'eleveurs',
        key: 'id'
      },
      comment: 'Référence vers l\'éleveur'
    },
    veterinaire_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users', // Table des utilisateurs
        key: 'id'
      },
      comment: 'Référence vers le vétérinaire (optionnel)'
    },
    volaille_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'volailles',
        key: 'id'
      },
      comment: 'Référence vers le lot de volailles concerné (optionnel)'
    },
    poussin_id: {
      type: DataTypes.INTEGER,
      references: {
        model: 'poussins',
        key: 'id'
      },
      comment: 'Référence vers le lot de poussins concerné (optionnel)'
    },
    type_intervention: {
      type: DataTypes.ENUM(
        'vaccination',
        'traitement_preventif',
        'traitement_curatif',
        'consultation',
        'visite_routine',
        'urgence',
        'autopsie',
        'analyse_laboratoire',
        'desinfection',
        'vermifugation'
      ),
      allowNull: false,
      comment: 'Type d\'intervention vétérinaire'
    },
    date_intervention: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Date et heure de l\'intervention'
    },
    date_planifiee: {
      type: DataTypes.DATE,
      comment: 'Date planifiée pour l\'intervention (si différente)'
    },
    statut: {
      type: DataTypes.ENUM('planifie', 'en_cours', 'termine', 'reporte', 'annule'),
      defaultValue: 'planifie',
      comment: 'Statut de l\'intervention'
    },
    urgence: {
      type: DataTypes.ENUM('faible', 'normale', 'elevee', 'critique'),
      defaultValue: 'normale',
      comment: 'Niveau d\'urgence'
    },
    motif: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Motif de l\'intervention'
    },
    symptomes_observes: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Liste des symptômes observés'
    },
    diagnostic: {
      type: DataTypes.TEXT,
      comment: 'Diagnostic établi par le vétérinaire'
    },
    diagnostic_differentiel: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Diagnostics différentiels possibles'
    },
    traitement_prescrit: {
      type: DataTypes.JSONB,
      defaultValue: {
        medicaments: [],
        posologie: null,
        duree_traitement: null,
        voie_administration: null,
        precautions: []
      },
      comment: 'Détails du traitement prescrit'
    },
    vaccin_administre: {
      type: DataTypes.JSONB,
      defaultValue: {
        nom_vaccin: null,
        fabricant: null,
        numero_lot: null,
        date_expiration: null,
        dose: null,
        voie_administration: null,
        site_injection: null
      },
      comment: 'Détails du vaccin administré'
    },
    animaux_concernes: {
      type: DataTypes.JSONB,
      defaultValue: {
        nombre_total: 0,
        nombre_traites: 0,
        age_moyen: null,
        poids_moyen: null,
        sexe: null
      },
      comment: 'Informations sur les animaux concernés'
    },
    examens_complementaires: {
      type: DataTypes.JSONB,
      defaultValue: {
        analyses_demandees: [],
        resultats: [],
        laboratoire: null,
        date_resultats: null
      },
      comment: 'Examens complémentaires et résultats'
    },
    cout_intervention: {
      type: DataTypes.DECIMAL(10, 2),
      comment: 'Coût de l\'intervention en DA'
    },
    cout_medicaments: {
      type: DataTypes.DECIMAL(10, 2),
      comment: 'Coût des médicaments en DA'
    },
    cout_total: {
      type: DataTypes.VIRTUAL,
      get() {
        const intervention = this.getDataValue('cout_intervention') || 0;
        const medicaments = this.getDataValue('cout_medicaments') || 0;
        return intervention + medicaments;
      },
      comment: 'Coût total (calculé)'
    },
    duree_intervention: {
      type: DataTypes.INTEGER,
      comment: 'Durée de l\'intervention en minutes'
    },
    conditions_intervention: {
      type: DataTypes.JSONB,
      defaultValue: {
        temperature_ambiante: null,
        humidite: null,
        conditions_hygiene: null,
        materiel_utilise: []
      },
      comment: 'Conditions lors de l\'intervention'
    },
    suivi_post_intervention: {
      type: DataTypes.JSONB,
      defaultValue: {
        date_controle_prevu: null,
        observations_j1: null,
        observations_j3: null,
        observations_j7: null,
        efficacite_traitement: null,
        effets_secondaires: []
      },
      comment: 'Suivi post-intervention'
    },
    recommandations: {
      type: DataTypes.TEXT,
      comment: 'Recommandations du vétérinaire'
    },
    mesures_preventives: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Mesures préventives recommandées'
    },
    prochaine_intervention: {
      type: DataTypes.JSONB,
      defaultValue: {
        type: null,
        date_prevue: null,
        motif: null,
        rappel_active: false
      },
      comment: 'Planification de la prochaine intervention'
    },
    photos_avant: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Photos avant intervention'
    },
    photos_apres: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Photos après intervention'
    },
    documents_joints: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Documents joints (ordonnances, analyses, etc.)'
    },
    notes_veterinaire: {
      type: DataTypes.TEXT,
      comment: 'Notes privées du vétérinaire'
    },
    notes_eleveur: {
      type: DataTypes.TEXT,
      comment: 'Notes et observations de l\'éleveur'
    },
    satisfaction_eleveur: {
      type: DataTypes.INTEGER,
      validate: {
        min: 1,
        max: 5
      },
      comment: 'Note de satisfaction de l\'éleveur (1-5)'
    },
    alerte_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Indique si une alerte est active'
    },
    confidentiel: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Intervention confidentielle'
    },
    // date_modification est géré par timestamps: true (updatedAt)
  }, {
    sequelize,
    modelName: 'SuiviVeterinaire',
    tableName: 'suivi_veterinaire',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['eleveur_id'] },
      { fields: ['veterinaire_id'] },
      { fields: ['date_intervention'] },
      { fields: ['type_intervention'] },
      { fields: ['statut'] },
      { fields: ['urgence'] },
      { fields: ['volaille_id'] },
      { fields: ['poussin_id'] }
    ],
    hooks: {
      beforeSave: (suivi, options) => {
        if (suivi.urgence === 'critique' || suivi.urgence === 'elevee') {
          suivi.alerte_active = true;
        } else if (suivi.alerte_active && !(suivi.urgence === 'critique' || suivi.urgence === 'elevee')){
          // Si l'urgence n'est plus critique/elevee, et que l'alerte était active à cause de ça,
          // on pourrait la désactiver, mais attention aux autres conditions d'alerte.
          // Pour l'instant, on ne la désactive pas automatiquement ici pour éviter d'écraser d'autres logiques.
        }

        if (suivi.statut === 'termine' && suivi.type_intervention === 'traitement_curatif') {
          const suiviPost = suivi.suivi_post_intervention || {};
          if (!suiviPost.date_controle_prevu) {
            const dateControle = new Date(suivi.date_intervention);
            dateControle.setDate(new Date(suivi.date_intervention).getDate() + 7);
            suiviPost.date_controle_prevu = dateControle;
            suivi.suivi_post_intervention = JSON.parse(JSON.stringify(suiviPost)); // Assurer la mise à jour de l'objet JSONB
          }
        }
        // La colonne date_modification est gérée par `updatedAt: 'updated_at'` ou `timestamps: true`
      }
    }
  });

  return SuiviVeterinaire;
};
