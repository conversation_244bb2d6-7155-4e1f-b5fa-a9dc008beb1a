import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { VisibilityOutlined as VisibilityIcon } from '@mui/icons-material';
import axios from 'axios';

const VeterinaireHistorique = () => {
  const [consultations, setConsultations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    fetchConsultations();
  }, []);

  const fetchConsultations = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/veterinaire/consultations/historique');
      setConsultations(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement de l\'historique des consultations');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'terminée':
        return 'success';
      case 'en_cours':
        return 'warning';
      case 'annulée':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Historique des Consultations
      </Typography>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Éleveur</TableCell>
              <TableCell>Type de Volaille</TableCell>
              <TableCell>Symptômes</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {consultations
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((consultation) => (
                <TableRow key={consultation.id}>
                  <TableCell>{formatDate(consultation.date)}</TableCell>
                  <TableCell>{consultation.eleveur.nom}</TableCell>
                  <TableCell>{consultation.typeVolaille}</TableCell>
                  <TableCell>{consultation.symptomes}</TableCell>
                  <TableCell>
                    <Chip
                      label={consultation.statut}
                      color={getStatusColor(consultation.statut)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Voir les détails">
                      <IconButton
                        size="small"
                        onClick={() => {
                          // Implémenter la logique pour afficher les détails
                          console.log('Voir détails:', consultation.id);
                        }}
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={consultations.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} sur ${count}`
          }
        />
      </TableContainer>
    </Box>
  );
};

export default VeterinaireHistorique;
