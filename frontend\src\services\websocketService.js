/**
 * Service WebSocket pour les communications temps réel
 */

import { EventEmitter } from 'events';

// Implémentation simple d'un émetteur d'événements pour le navigateur
class BrowserEventEmitter {
  constructor() {
    this.listeners = new Map();
  }

  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(listener);
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(listener => listener(data));
    }
  }

  removeListener(event, listener) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(listener);
    }
  }

  removeAllListeners() {
    this.listeners.clear();
  }
}

// Types d'événements WebSocket
export const WS_EVENTS = {
  // Connexion
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error',
  
  // Données
  DATA_UPDATE: 'data_update',
  NOTIFICATION: 'notification',
  ALERT: 'alert',
  
  // Volailles
  VOLAILLE_CREATED: 'volaille_created',
  VOLAILLE_UPDATED: 'volaille_updated',
  VOLAILLE_DELETED: 'volaille_deleted',
  PRODUCTION_UPDATED: 'production_updated',
  
  // Consultations
  CONSULTATION_SCHEDULED: 'consultation_scheduled',
  CONSULTATION_UPDATED: 'consultation_updated',
  CONSULTATION_CANCELLED: 'consultation_cancelled',
  
  // Commandes
  ORDER_CREATED: 'order_created',
  ORDER_UPDATED: 'order_updated',
  ORDER_STATUS_CHANGED: 'order_status_changed',
  
  // Messages
  MESSAGE_RECEIVED: 'message_received',
  USER_TYPING: 'user_typing',
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline'
};

class WebSocketService extends BrowserEventEmitter {
  constructor() {
    super();
    this.ws = null;
    this.url = null;
    this.token = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.heartbeatInterval = null;
    this.isConnecting = false;
    this.isConnected = false;
    this.messageQueue = [];
    this.subscriptions = new Set();
  }

  /**
   * Connecter au serveur WebSocket
   */
  connect(url, token) {
    if (this.isConnecting || this.isConnected) {
      return Promise.resolve();
    }

    this.url = url;
    this.token = token;
    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        // Construire l'URL avec le token
        const wsUrl = `${url}?token=${encodeURIComponent(token)}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connecté');
          this.isConnecting = false;
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Démarrer le heartbeat
          this.startHeartbeat();
          
          // Envoyer les messages en attente
          this.flushMessageQueue();
          
          // Rétablir les souscriptions
          this.resubscribe();
          
          this.emit(WS_EVENTS.CONNECTED);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Erreur parsing message WebSocket:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket fermé:', event.code, event.reason);
          this.isConnected = false;
          this.isConnecting = false;
          this.stopHeartbeat();
          
          this.emit(WS_EVENTS.DISCONNECTED, { code: event.code, reason: event.reason });
          
          // Tentative de reconnexion automatique
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('Erreur WebSocket:', error);
          this.isConnecting = false;
          this.emit(WS_EVENTS.ERROR, error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Déconnecter du serveur WebSocket
   */
  disconnect() {
    if (this.ws) {
      this.stopHeartbeat();
      this.ws.close(1000, 'Déconnexion volontaire');
      this.ws = null;
    }
    this.isConnected = false;
    this.isConnecting = false;
    this.subscriptions.clear();
    this.messageQueue = [];
  }

  /**
   * Envoyer un message
   */
  send(type, data = {}) {
    const message = {
      type,
      data,
      timestamp: new Date().toISOString(),
      id: this.generateMessageId()
    };

    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Mettre en file d'attente si pas connecté
      this.messageQueue.push(message);
    }
  }

  /**
   * S'abonner à un canal
   */
  subscribe(channel, filters = {}) {
    const subscription = { channel, filters };
    this.subscriptions.add(subscription);
    
    if (this.isConnected) {
      this.send('subscribe', subscription);
    }
  }

  /**
   * Se désabonner d'un canal
   */
  unsubscribe(channel) {
    const subscription = Array.from(this.subscriptions).find(sub => sub.channel === channel);
    if (subscription) {
      this.subscriptions.delete(subscription);
      
      if (this.isConnected) {
        this.send('unsubscribe', { channel });
      }
    }
  }

  /**
   * Gérer les messages reçus
   */
  handleMessage(message) {
    const { type, data } = message;

    switch (type) {
      case 'pong':
        // Réponse au ping
        break;
        
      case 'notification':
        this.emit(WS_EVENTS.NOTIFICATION, data);
        break;
        
      case 'alert':
        this.emit(WS_EVENTS.ALERT, data);
        break;
        
      case 'data_update':
        this.emit(WS_EVENTS.DATA_UPDATE, data);
        break;
        
      case 'volaille_created':
        this.emit(WS_EVENTS.VOLAILLE_CREATED, data);
        break;
        
      case 'volaille_updated':
        this.emit(WS_EVENTS.VOLAILLE_UPDATED, data);
        break;
        
      case 'volaille_deleted':
        this.emit(WS_EVENTS.VOLAILLE_DELETED, data);
        break;
        
      case 'production_updated':
        this.emit(WS_EVENTS.PRODUCTION_UPDATED, data);
        break;
        
      case 'consultation_scheduled':
        this.emit(WS_EVENTS.CONSULTATION_SCHEDULED, data);
        break;
        
      case 'consultation_updated':
        this.emit(WS_EVENTS.CONSULTATION_UPDATED, data);
        break;
        
      case 'order_created':
        this.emit(WS_EVENTS.ORDER_CREATED, data);
        break;
        
      case 'order_updated':
        this.emit(WS_EVENTS.ORDER_UPDATED, data);
        break;
        
      case 'message_received':
        this.emit(WS_EVENTS.MESSAGE_RECEIVED, data);
        break;
        
      case 'user_online':
        this.emit(WS_EVENTS.USER_ONLINE, data);
        break;
        
      case 'user_offline':
        this.emit(WS_EVENTS.USER_OFFLINE, data);
        break;
        
      default:
        console.log('Message WebSocket non géré:', type, data);
    }
  }

  /**
   * Démarrer le heartbeat
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
        this.send('ping');
      }
    }, 30000); // Ping toutes les 30 secondes
  }

  /**
   * Arrêter le heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Programmer une reconnexion
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`);
    this.emit(WS_EVENTS.RECONNECTING, { attempt: this.reconnectAttempts, delay });
    
    setTimeout(() => {
      if (this.url && this.token) {
        this.connect(this.url, this.token);
      }
    }, delay);
  }

  /**
   * Vider la file d'attente des messages
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Rétablir les souscriptions
   */
  resubscribe() {
    this.subscriptions.forEach(subscription => {
      this.send('subscribe', subscription);
    });
  }

  /**
   * Générer un ID de message unique
   */
  generateMessageId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtenir le statut de connexion
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions)
    };
  }
}

// Instance singleton
const websocketService = new WebSocketService();

export default websocketService;
