/* Animations pour la landing page de Poultray DZ */

/* Animation d'entrée pour les éléments */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation de flottement pour les images */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Animation de pulse pour les boutons d'appel à l'action */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }
}

/* Animation de rotation pour les icônes */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation de fondu pour les sections au défilement */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation de glissement depuis la gauche */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation de glissement depuis la droite */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation de glissement depuis le bas */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Classes d'animation réutilisables */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-rotate {
  animation: rotate 10s linear infinite;
}

/* Animations au défilement */
.scroll-anim {
  opacity: 0;
  transition: all 0.8s ease-out;
}

.scroll-anim.visible {
  opacity: 1;
}

.fade-in.visible {
  animation: fadeIn 1s ease forwards;
}

.slide-in-left.visible {
  animation: slideInLeft 0.8s ease forwards;
}

.slide-in-right.visible {
  animation: slideInRight 0.8s ease forwards;
}

.slide-in-up.visible {
  animation: slideInUp 0.8s ease forwards;
}

/* Délais d'animation pour créer un effet en cascade */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* Effets de survol pour les cartes */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

/* Optimisations pour les appareils mobiles */
@media (max-width: 600px) {
  .animate-float {
    animation-duration: 2s;
  }

  .scroll-anim {
    transition: all 0.6s ease-out;
  }
}

/* Animation pour le bouton CTA principal */
.cta-button {
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
}
