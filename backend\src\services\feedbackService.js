/**
 * Service de gestion des feedbacks et enquêtes utilisateur
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
const emailService = require('./emailService');
const notificationService = require('./notificationService');

class FeedbackService {
  constructor() {
    this.pool = new Pool();
  }

  /**
   * Créer une nouvelle enquête
   */
  async createSurvey(surveyData) {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Insérer l'enquête
      const surveyResult = await client.query(`
        INSERT INTO surveys (
          title, description, type, target_audience, 
          start_date, end_date, is_active, created_by,
          trigger_conditions, display_settings
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        surveyData.title,
        surveyData.description,
        surveyData.type,
        JSON.stringify(surveyData.targetAudience),
        surveyData.startDate,
        surveyData.endDate,
        surveyData.isActive,
        surveyData.createdBy,
        JSON.stringify(surveyData.triggerConditions),
        JSON.stringify(surveyData.displaySettings)
      ]);

      const survey = surveyResult.rows[0];

      // Insérer les questions
      for (const question of surveyData.questions) {
        const questionResult = await client.query(`
          INSERT INTO survey_questions (
            survey_id, question_text, question_type, 
            options, is_required, order_index, validation_rules
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING *
        `, [
          survey.id,
          question.text,
          question.type,
          JSON.stringify(question.options),
          question.isRequired,
          question.orderIndex,
          JSON.stringify(question.validationRules)
        ]);

        question.id = questionResult.rows[0].id;
      }

      await client.query('COMMIT');
      
      logger.info(`Enquête créée: ${survey.id}`);
      return { ...survey, questions: surveyData.questions };
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de la création de l\'enquête:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Obtenir les enquêtes actives pour un utilisateur
   */
  async getActiveSurveysForUser(userId, userRole, context = {}) {
    try {
      const result = await this.pool.query(`
        SELECT s.*, 
               CASE WHEN sr.id IS NOT NULL THEN true ELSE false END as completed
        FROM surveys s
        LEFT JOIN survey_responses sr ON s.id = sr.survey_id AND sr.user_id = $1
        WHERE s.is_active = true
          AND s.start_date <= NOW()
          AND (s.end_date IS NULL OR s.end_date >= NOW())
          AND (s.target_audience->>'roles' IS NULL 
               OR s.target_audience->'roles' @> $2::jsonb)
          AND sr.id IS NULL
        ORDER BY s.created_at DESC
      `, [userId, JSON.stringify([userRole])]);

      const surveys = result.rows;
      const eligibleSurveys = [];

      for (const survey of surveys) {
        // Vérifier les conditions de déclenchement
        if (await this.checkTriggerConditions(survey, userId, context)) {
          // Charger les questions
          const questionsResult = await this.pool.query(`
            SELECT * FROM survey_questions 
            WHERE survey_id = $1 
            ORDER BY order_index
          `, [survey.id]);

          survey.questions = questionsResult.rows;
          eligibleSurveys.push(survey);
        }
      }

      return eligibleSurveys;
    } catch (error) {
      logger.error('Erreur lors de la récupération des enquêtes:', error);
      throw error;
    }
  }

  /**
   * Vérifier les conditions de déclenchement d'une enquête
   */
  async checkTriggerConditions(survey, userId, context) {
    if (!survey.trigger_conditions) return true;

    const conditions = survey.trigger_conditions;

    // Vérifier la condition de nombre de connexions
    if (conditions.minLoginCount) {
      const loginResult = await this.pool.query(`
        SELECT COUNT(*) as login_count 
        FROM user_sessions 
        WHERE user_id = $1
      `, [userId]);

      if (parseInt(loginResult.rows[0].login_count) < conditions.minLoginCount) {
        return false;
      }
    }

    // Vérifier la condition de temps depuis l'inscription
    if (conditions.minDaysSinceRegistration) {
      const userResult = await this.pool.query(`
        SELECT created_at 
        FROM users 
        WHERE id = $1
      `, [userId]);

      const daysSinceRegistration = Math.floor(
        (Date.now() - new Date(userResult.rows[0].created_at)) / (1000 * 60 * 60 * 24)
      );

      if (daysSinceRegistration < conditions.minDaysSinceRegistration) {
        return false;
      }
    }

    // Vérifier la condition de page/action spécifique
    if (conditions.specificPage && context.currentPage !== conditions.specificPage) {
      return false;
    }

    // Vérifier la condition d'activité récente
    if (conditions.recentActivity) {
      const activityResult = await this.pool.query(`
        SELECT COUNT(*) as activity_count
        FROM user_activities 
        WHERE user_id = $1 
          AND created_at >= NOW() - INTERVAL '7 days'
      `, [userId]);

      if (parseInt(activityResult.rows[0].activity_count) < conditions.recentActivity.minActions) {
        return false;
      }
    }

    return true;
  }

  /**
   * Soumettre une réponse à une enquête
   */
  async submitSurveyResponse(userId, surveyId, responses) {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Vérifier que l'utilisateur n'a pas déjà répondu
      const existingResponse = await client.query(`
        SELECT id FROM survey_responses 
        WHERE user_id = $1 AND survey_id = $2
      `, [userId, surveyId]);

      if (existingResponse.rows.length > 0) {
        throw new Error('Vous avez déjà répondu à cette enquête');
      }

      // Créer la réponse principale
      const responseResult = await client.query(`
        INSERT INTO survey_responses (
          survey_id, user_id, submitted_at, metadata
        ) VALUES ($1, $2, NOW(), $3)
        RETURNING *
      `, [surveyId, userId, JSON.stringify({ userAgent: responses.metadata?.userAgent })]);

      const responseId = responseResult.rows[0].id;

      // Insérer les réponses aux questions
      for (const questionResponse of responses.answers) {
        await client.query(`
          INSERT INTO survey_question_responses (
            response_id, question_id, answer_value, answer_text
          ) VALUES ($1, $2, $3, $4)
        `, [
          responseId,
          questionResponse.questionId,
          JSON.stringify(questionResponse.value),
          questionResponse.text
        ]);
      }

      await client.query('COMMIT');

      // Mettre à jour les statistiques de l'enquête
      await this.updateSurveyStats(surveyId);

      logger.info(`Réponse d'enquête soumise: ${responseId} par utilisateur ${userId}`);
      return responseResult.rows[0];

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de la soumission de la réponse:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Créer un feedback rapide
   */
  async createQuickFeedback(userId, feedbackData) {
    try {
      const result = await this.pool.query(`
        INSERT INTO quick_feedback (
          user_id, type, rating, comment, page_url, 
          feature_name, category, metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
        RETURNING *
      `, [
        userId,
        feedbackData.type,
        feedbackData.rating,
        feedbackData.comment,
        feedbackData.pageUrl,
        feedbackData.featureName,
        feedbackData.category,
        JSON.stringify(feedbackData.metadata)
      ]);

      const feedback = result.rows[0];

      // Notifier l'équipe si c'est un feedback négatif
      if (feedbackData.rating <= 2) {
        await this.notifyTeamOfNegativeFeedback(feedback);
      }

      logger.info(`Feedback rapide créé: ${feedback.id}`);
      return feedback;

    } catch (error) {
      logger.error('Erreur lors de la création du feedback:', error);
      throw error;
    }
  }

  /**
   * Obtenir les statistiques des enquêtes
   */
  async getSurveyAnalytics(surveyId) {
    try {
      // Statistiques générales
      const statsResult = await this.pool.query(`
        SELECT 
          COUNT(DISTINCT sr.user_id) as total_responses,
          AVG(EXTRACT(EPOCH FROM (sr.submitted_at - sr.created_at))) as avg_completion_time,
          COUNT(DISTINCT CASE WHEN sr.submitted_at IS NOT NULL THEN sr.user_id END) as completed_responses,
          COUNT(DISTINCT CASE WHEN sr.submitted_at IS NULL THEN sr.user_id END) as abandoned_responses
        FROM survey_responses sr
        WHERE sr.survey_id = $1
      `, [surveyId]);

      const stats = statsResult.rows[0];

      // Réponses par question
      const questionStatsResult = await this.pool.query(`
        SELECT 
          sq.id,
          sq.question_text,
          sq.question_type,
          COUNT(sqr.id) as response_count,
          CASE 
            WHEN sq.question_type = 'rating' THEN AVG(CAST(sqr.answer_value->>'value' AS NUMERIC))
            ELSE NULL
          END as avg_rating
        FROM survey_questions sq
        LEFT JOIN survey_question_responses sqr ON sq.id = sqr.question_id
        WHERE sq.survey_id = $1
        GROUP BY sq.id, sq.question_text, sq.question_type
        ORDER BY sq.order_index
      `, [surveyId]);

      // Réponses détaillées pour analyse
      const detailedResponsesResult = await this.pool.query(`
        SELECT 
          sqr.question_id,
          sqr.answer_value,
          sqr.answer_text,
          COUNT(*) as count
        FROM survey_question_responses sqr
        JOIN survey_questions sq ON sqr.question_id = sq.id
        WHERE sq.survey_id = $1
        GROUP BY sqr.question_id, sqr.answer_value, sqr.answer_text
        ORDER BY sqr.question_id, count DESC
      `, [surveyId]);

      return {
        overview: stats,
        questionStats: questionStatsResult.rows,
        detailedResponses: detailedResponsesResult.rows
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des analytics:', error);
      throw error;
    }
  }

  /**
   * Obtenir les feedbacks par catégorie
   */
  async getFeedbackByCategory(filters = {}) {
    try {
      let query = `
        SELECT 
          category,
          type,
          AVG(rating) as avg_rating,
          COUNT(*) as count,
          COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_count,
          COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_count
        FROM quick_feedback
        WHERE 1=1
      `;

      const params = [];
      let paramIndex = 1;

      if (filters.startDate) {
        query += ` AND created_at >= $${paramIndex}`;
        params.push(filters.startDate);
        paramIndex++;
      }

      if (filters.endDate) {
        query += ` AND created_at <= $${paramIndex}`;
        params.push(filters.endDate);
        paramIndex++;
      }

      if (filters.category) {
        query += ` AND category = $${paramIndex}`;
        params.push(filters.category);
        paramIndex++;
      }

      query += ` GROUP BY category, type ORDER BY avg_rating ASC`;

      const result = await this.pool.query(query, params);
      return result.rows;

    } catch (error) {
      logger.error('Erreur lors de la récupération des feedbacks:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour les statistiques d'une enquête
   */
  async updateSurveyStats(surveyId) {
    try {
      await this.pool.query(`
        UPDATE surveys 
        SET 
          response_count = (
            SELECT COUNT(*) FROM survey_responses 
            WHERE survey_id = $1 AND submitted_at IS NOT NULL
          ),
          completion_rate = (
            SELECT 
              CASE 
                WHEN COUNT(*) = 0 THEN 0
                ELSE COUNT(CASE WHEN submitted_at IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)
              END
            FROM survey_responses 
            WHERE survey_id = $1
          )
        WHERE id = $1
      `, [surveyId]);

    } catch (error) {
      logger.error('Erreur lors de la mise à jour des stats:', error);
    }
  }

  /**
   * Notifier l'équipe d'un feedback négatif
   */
  async notifyTeamOfNegativeFeedback(feedback) {
    try {
      const message = `
        Feedback négatif reçu:
        - Note: ${feedback.rating}/5
        - Page: ${feedback.page_url}
        - Fonctionnalité: ${feedback.feature_name}
        - Commentaire: ${feedback.comment}
      `;

      await notificationService.sendToTeam('feedback-alerts', {
        title: 'Feedback négatif reçu',
        message: message,
        priority: 'high',
        data: feedback
      });

    } catch (error) {
      logger.error('Erreur lors de la notification:', error);
    }
  }

  /**
   * Exporter les données de feedback
   */
  async exportFeedbackData(format = 'csv', filters = {}) {
    try {
      let query = `
        SELECT 
          qf.*,
          u.email,
          u.role,
          u.created_at as user_created_at
        FROM quick_feedback qf
        JOIN users u ON qf.user_id = u.id
        WHERE 1=1
      `;

      const params = [];
      let paramIndex = 1;

      if (filters.startDate) {
        query += ` AND qf.created_at >= $${paramIndex}`;
        params.push(filters.startDate);
        paramIndex++;
      }

      if (filters.endDate) {
        query += ` AND qf.created_at <= $${paramIndex}`;
        params.push(filters.endDate);
        paramIndex++;
      }

      query += ` ORDER BY qf.created_at DESC`;

      const result = await this.pool.query(query, params);
      
      if (format === 'csv') {
        return this.convertToCSV(result.rows);
      }
      
      return result.rows;

    } catch (error) {
      logger.error('Erreur lors de l\'export:', error);
      throw error;
    }
  }

  /**
   * Convertir les données en CSV
   */
  convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => 
          JSON.stringify(row[header] || '')
        ).join(',')
      )
    ].join('\n');

    return csvContent;
  }
}

module.exports = new FeedbackService();
