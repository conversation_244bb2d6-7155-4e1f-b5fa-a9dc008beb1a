require('dotenv').config();
const axios = require('axios');
const colors = require('colors');

// Base URL for API
const API_URL = 'http://localhost:3003/api';

async function testSecuritySettings() {
  console.log('Testing security settings endpoint...'.cyan);

  try {
    // Login first to get token
    console.log('Logging in as admin...'.yellow);
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful!'.green);

    // Fetch security settings with token
    console.log('Fetching security settings...'.yellow);
    const response = await axios.get(`${API_URL}/admin/settings/security`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (response.status === 200) {
      console.log('✅ Security settings endpoint is working!'.green);
      console.log('Response data:'.cyan);
      console.log(JSON.stringify(response.data, null, 2));
    } else {
      console.log('❌ Unexpected status code:'.red, response.status);
    }
  } catch (error) {
    console.log('❌ Error fetching security settings:'.red);
    console.log('Status:'.yellow, error.response?.status);
    console.log('Data:'.yellow, JSON.stringify(error.response?.data, null, 2));
    console.log('Headers:'.yellow, JSON.stringify(error.response?.headers, null, 2));
  }
}

// Run test
testSecuritySettings();
