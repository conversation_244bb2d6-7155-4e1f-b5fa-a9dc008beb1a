import React from 'react';
import { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, Box, Button, Divider } from '@mui/material';
import InventoryIcon from '@mui/icons-material/Inventory';
import { useTheme } from '@mui/material/styles';

/**
 * Composant pour afficher les alertes de stock faible
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.products - Liste des produits avec stock faible
 */
const LowStockAlert = ({ products = [] }) => {
  const theme = useTheme();

  // Si aucun produit en alerte, afficher un message
  if (products.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          Aucun produit en alerte de stock
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <List sx={{ maxHeight: 300, overflow: 'auto' }}>
        {products.map((product, index) => (
          <React.Fragment key={product.id}>
            <ListItem alignItems="flex-start">
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: theme.palette.warning.light }}>
                  <InventoryIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={product.name}
                secondary={
                  <React.Fragment>
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.primary"
                    >
                      Stock actuel: {product.stock_quantity} {product.unit}
                    </Typography>
                    <Typography variant="body2" color="error">
                      Seuil d'alerte: {product.stock_alert_threshold} {product.unit}
                    </Typography>
                  </React.Fragment>
                }
              />
              <Button
                variant="outlined"
                color="primary"
                size="small"
                sx={{ minWidth: 100 }}
              >
                Réapprovisionner
              </Button>
            </ListItem>
            {index < products.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
      {products.length > 5 && (
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Button variant="text" color="primary">
            Voir tous les produits en alerte ({products.length})
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default LowStockAlert;
