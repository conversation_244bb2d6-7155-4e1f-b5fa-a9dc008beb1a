// Simple test to verify frontend is working
console.log('🧪 Test Frontend Status');

// Test if the frontend is running
fetch('http://localhost:5173/')
  .then(response => {
    console.log('✅ Frontend Status:', response.status);
    if (response.ok) {
      console.log('🎉 Frontend is running successfully');

      // Test the debug navigation page
      return fetch('http://localhost:5173/debug-navigation');
    } else {
      throw new Error(`Frontend not accessible: ${response.status}`);
    }
  })
  .then(response => {
    console.log('✅ Debug Navigation Page Status:', response.status);
    if (response.ok) {
      console.log('🎯 Debug navigation page is accessible');
    }
  })
  .catch(error => {
    console.error('❌ Frontend Test Error:', error.message);
  });

// Test backend API
fetch('http://localhost:3003/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  })
})
.then(response => response.json())
.then(data => {
  if (data.token && data.user) {
    console.log('✅ Backend Login Test: SUCCESS');
    console.log('👤 User Role:', data.user.role);
    console.log('🔑 Token received:', !!data.token);

    // Test navigation logic
    const getRedirectPath = (role) => {
      const rolePathMap = {
        'admin': '/admin/dashboard',
        'eleveur': '/eleveur/dashboard',
        'veterinaire': '/veterinaire/dashboard',
        'marchand': '/marchand/dashboard'
      };
      return rolePathMap[role] || '/dashboard';
    };

    const redirectPath = getRedirectPath(data.user.role);
    console.log('🎯 Expected Navigation Path:', redirectPath);
    console.log('🚀 Navigation test completed successfully');
  } else {
    console.error('❌ Backend Login Test: FAILED');
  }
})
.catch(error => {
  console.error('❌ Backend Test Error:', error.message);
});
