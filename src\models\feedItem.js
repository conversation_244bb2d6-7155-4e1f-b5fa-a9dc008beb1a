const { DataTypes } = require('sequelize');

/**
 * FeedItem model - Represents different types of feed available
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedItem = sequelize.define('FeedItem', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    brand: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        isIn: [['starter', 'grower', 'finisher', 'layer', 'breeder', 'supplement']]
      },
      comment: 'starter, grower, finisher, layer, breeder, supplement'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    unit_of_measure: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'kg',
      validate: {
        isIn: [['kg', 'tonnes', 'sacs', 'grammes']]
      },
      comment: 'kg, tonnes, sacs, grammes'
    },
    nutritional_info: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'protein, energy, fiber, etc.',
      validate: {
        isValidNutritionalInfo(value) {
          if (value && typeof value === 'object') {
            const allowedKeys = ['protein', 'energy', 'fiber', 'fat', 'calcium', 'phosphorus', 'lysine', 'methionine'];
            const keys = Object.keys(value);
            for (let key of keys) {
              if (!allowedKeys.includes(key)) {
                throw new Error(`Invalid nutritional info key: ${key}`);
              }
            }
          }
        }
      }
    },
    recommended_age_min: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 365
      },
      comment: 'minimum age in days'
    },
    recommended_age_max: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 365
      },
      comment: 'maximum age in days'
    },
    poultry_types: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
      comment: 'poulets, dindes, pondeuses, etc.',
      validate: {
        isValidPoultryTypes(value) {
          if (!Array.isArray(value)) {
            throw new Error('Poultry types must be an array');
          }
          const allowedTypes = ['poulets', 'dindes', 'pondeuses', 'canards', 'oies', 'pintades'];
          for (let type of value) {
            if (!allowedTypes.includes(type)) {
              throw new Error(`Invalid poultry type: ${type}`);
            }
          }
        }
      }
    },
    daily_consumption_per_bird: {
      type: DataTypes.DECIMAL(8, 3),
      allowNull: true,
      validate: {
        min: 0,
        max: 1000
      },
      comment: 'average daily consumption per bird in grams'
    },
    storage_instructions: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    shelf_life_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 90,
      validate: {
        min: 1,
        max: 730
      }
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: [['active', 'inactive', 'discontinued']]
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['category', 'status']
      },
      {
        fields: ['poultry_types'],
        using: 'gin'
      }
    ],
    hooks: {
      beforeValidate: (feedItem) => {
        // Ensure poultry_types is an array
        if (typeof feedItem.poultry_types === 'string') {
          feedItem.poultry_types = [feedItem.poultry_types];
        }
        
        // Validate age range
        if (feedItem.recommended_age_min && feedItem.recommended_age_max) {
          if (feedItem.recommended_age_min > feedItem.recommended_age_max) {
            throw new Error('Minimum age cannot be greater than maximum age');
          }
        }
      }
    }
  });

  // Instance methods
  FeedItem.prototype.isValidForPoultryType = function(poultryType) {
    return this.poultry_types.includes(poultryType);
  };

  FeedItem.prototype.isValidForAge = function(ageInDays) {
    if (!this.recommended_age_min && !this.recommended_age_max) {
      return true; // No age restrictions
    }
    
    const minAge = this.recommended_age_min || 0;
    const maxAge = this.recommended_age_max || 365;
    
    return ageInDays >= minAge && ageInDays <= maxAge;
  };

  FeedItem.prototype.getEstimatedDailyCost = function(birdCount, unitCost) {
    if (!this.daily_consumption_per_bird || !unitCost) {
      return 0;
    }
    
    const dailyConsumptionKg = (this.daily_consumption_per_bird * birdCount) / 1000;
    return dailyConsumptionKg * unitCost;
  };

  // Class methods
  FeedItem.findByCategory = function(category, options = {}) {
    return this.findAll({
      where: {
        category,
        status: 'active'
      },
      ...options
    });
  };

  FeedItem.findByPoultryType = function(poultryType, options = {}) {
    return this.findAll({
      where: {
        poultry_types: {
          [sequelize.Sequelize.Op.contains]: [poultryType]
        },
        status: 'active'
      },
      ...options
    });
  };

  FeedItem.findByAgeRange = function(ageInDays, options = {}) {
    return this.findAll({
      where: {
        [sequelize.Sequelize.Op.and]: [
          {
            [sequelize.Sequelize.Op.or]: [
              { recommended_age_min: null },
              { recommended_age_min: { [sequelize.Sequelize.Op.lte]: ageInDays } }
            ]
          },
          {
            [sequelize.Sequelize.Op.or]: [
              { recommended_age_max: null },
              { recommended_age_max: { [sequelize.Sequelize.Op.gte]: ageInDays } }
            ]
          }
        ],
        status: 'active'
      },
      ...options
    });
  };

  // Associations
  FeedItem.associate = function(models) {
    // FeedItem has many FeedStock
    FeedItem.hasMany(models.FeedStock, {
      foreignKey: 'feed_item_id',
      as: 'stock_entries'
    });

    // FeedItem has many FeedComposition (through feed plans)
    FeedItem.hasMany(models.FeedComposition, {
      foreignKey: 'feed_item_id',
      as: 'feed_compositions'
    });
  };

  return FeedItem;
};