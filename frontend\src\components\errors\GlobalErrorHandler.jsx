import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Box } from '@mui/material';
import { Refresh, Close } from '@mui/icons-material';
import useErrorHandler from '../../hooks/useErrorHandler';
import ErrorDisplay, { ConnectionStatus } from './ErrorDisplay';
import { ERROR_TYPES } from '../../services/errorHandler';

/**
 * Composant global pour gérer les erreurs dans l'application
 */
const GlobalErrorHandler = ({ children }) => {
  const {
    errors,
    isOnline,
    clearError,
    clearAllErrors,
    retry,
    hasNetworkErrors,
    hasAuthErrors,
    getErrorsByType
  } = useErrorHandler({
    showNotifications: true,
    autoRetry: false
  });

  const [snackbarOpen, setSnackbarOpen] = React.useState(false);
  const [currentSnackbarError, setCurrentSnackbarError] = React.useState(null);

  // Gérer l'affichage des erreurs en snackbar
  React.useEffect(() => {
    if (errors.length > 0) {
      const latestError = errors[errors.length - 1];
      
      // Afficher en snackbar seulement certains types d'erreurs
      const snackbarTypes = [ERROR_TYPES.NETWORK, ERROR_TYPES.SERVER, ERROR_TYPES.TIMEOUT];
      
      if (snackbarTypes.includes(latestError.type)) {
        setCurrentSnackbarError(latestError);
        setSnackbarOpen(true);
      }
    }
  }, [errors]);

  // Fermer le snackbar
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  // Réessayer une action depuis le snackbar
  const handleRetryFromSnackbar = async () => {
    if (currentSnackbarError && currentSnackbarError.retryable) {
      setSnackbarOpen(false);
      // Ici, on pourrait implémenter une logique de retry plus sophistiquée
      // Pour l'instant, on ferme juste le snackbar
      clearError(currentSnackbarError.timestamp);
    }
  };

  // Obtenir les erreurs critiques (auth, forbidden)
  const criticalErrors = errors.filter(error => 
    error.type === ERROR_TYPES.AUTH || error.type === ERROR_TYPES.FORBIDDEN
  );

  // Obtenir les erreurs non critiques pour l'affichage en liste
  const nonCriticalErrors = errors.filter(error => 
    error.type !== ERROR_TYPES.AUTH && error.type !== ERROR_TYPES.FORBIDDEN
  );

  return (
    <>
      {children}

      {/* Indicateur de connexion */}
      <ConnectionStatus isOnline={isOnline} />

      {/* Erreurs critiques - affichage modal/persistant */}
      {criticalErrors.length > 0 && (
        <Box
          position="fixed"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          zIndex={10001}
          bgcolor="background.paper"
          boxShadow={24}
          p={4}
          borderRadius={2}
          maxWidth={500}
          width="90%"
        >
          <ErrorDisplay
            errors={criticalErrors}
            onClearError={clearError}
            onClearAll={clearAllErrors}
            variant="list"
            showDetails={true}
            allowRetry={false}
            position="relative"
          />
        </Box>
      )}

      {/* Erreurs non critiques - affichage en coin */}
      {nonCriticalErrors.length > 0 && !criticalErrors.length && (
        <ErrorDisplay
          errors={nonCriticalErrors}
          onClearError={clearError}
          onClearAll={clearAllErrors}
          onRetry={retry}
          variant="list"
          maxVisible={3}
          showDetails={false}
          allowRetry={true}
          position="fixed"
        />
      )}

      {/* Snackbar pour les erreurs temporaires */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity="error"
          variant="filled"
          action={
            currentSnackbarError?.retryable && (
              <Button
                color="inherit"
                size="small"
                startIcon={<Refresh />}
                onClick={handleRetryFromSnackbar}
              >
                Réessayer
              </Button>
            )
          }
        >
          <AlertTitle>Erreur de connexion</AlertTitle>
          {currentSnackbarError?.userMessage}
        </Alert>
      </Snackbar>

      {/* Overlay pour les erreurs critiques */}
      {criticalErrors.length > 0 && (
        <Box
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bgcolor="rgba(0, 0, 0, 0.5)"
          zIndex={10000}
        />
      )}
    </>
  );
};

export default GlobalErrorHandler;
