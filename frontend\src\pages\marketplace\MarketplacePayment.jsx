import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  TextField,
  Button,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';
import axios from 'axios';

const steps = ['Sélection du mode', 'Détails du paiement', 'Confirmation'];

const MarketplacePayment = ({ orderId, amount }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('');
  const [cardDetails, setCardDetails] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    name: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [transactionId, setTransactionId] = useState('');

  const handlePaymentMethodChange = (event) => {
    setPaymentMethod(event.target.value);
    setError('');
  };

  const handleCardDetailsChange = (field) => (event) => {
    setCardDetails({
      ...cardDetails,
      [field]: event.target.value
    });
  };

  const validateCardDetails = () => {
    if (paymentMethod === 'cib') {
      if (!cardDetails.cardNumber || cardDetails.cardNumber.length !== 16) {
        setError('Numéro de carte invalide');
        return false;
      }
      if (!cardDetails.expiryDate || !/^\d{2}\/\d{2}$/.test(cardDetails.expiryDate)) {
        setError('Date d\'expiration invalide');
        return false;
      }
      if (!cardDetails.cvv || cardDetails.cvv.length !== 3) {
        setError('CVV invalide');
        return false;
      }
      if (!cardDetails.name) {
        setError('Nom du titulaire requis');
        return false;
      }
    }
    return true;
  };

  const handleNext = () => {
    if (activeStep === 0 && !paymentMethod) {
      setError('Veuillez sélectionner un mode de paiement');
      return;
    }
    if (activeStep === 1 && !validateCardDetails()) {
      return;
    }
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
    setError('');
  };

  const processPayment = async () => {
    setLoading(true);
    setError('');

    try {
      let response;
      if (paymentMethod === 'cib') {
        response = await axios.post('/api/marketplace/payment/cib', {
          orderId,
          amount,
          cardDetails
        });
      } else if (paymentMethod === 'edahabia') {
        response = await axios.post('/api/marketplace/payment/edahabia', {
          orderId,
          amount
        });
      } else {
        response = await axios.post('/api/marketplace/payment/cash', {
          orderId,
          amount
        });
      }

      setTransactionId(response.data.transactionId);
      setSuccess(true);
      setOpenDialog(true);
    } catch (error) {
      console.error('Erreur lors du paiement:', error);
      setError(error.response?.data?.message || 'Une erreur est survenue lors du paiement');
    } finally {
      setLoading(false);
    }
  };

  const renderPaymentMethodSelection = () => (
    <FormControl component="fieldset">
      <FormLabel component="legend">Mode de paiement</FormLabel>
      <RadioGroup value={paymentMethod} onChange={handlePaymentMethodChange}>
        <FormControlLabel
          value="cib"
          control={<Radio />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CreditCardIcon sx={{ mr: 1 }} />
              Carte CIB
            </Box>
          }
        />
        <FormControlLabel
          value="edahabia"
          control={<Radio />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AccountBalanceIcon sx={{ mr: 1 }} />
              EDAHABIA
            </Box>
          }
        />
        <FormControlLabel
          value="cash"
          control={<Radio />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LocalAtmIcon sx={{ mr: 1 }} />
              Paiement à la livraison
            </Box>
          }
        />
      </RadioGroup>
    </FormControl>
  );

  const renderCardDetails = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Numéro de carte"
          value={cardDetails.cardNumber}
          onChange={handleCardDetailsChange('cardNumber')}
          inputProps={{ maxLength: 16 }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Date d'expiration (MM/YY)"
          value={cardDetails.expiryDate}
          onChange={handleCardDetailsChange('expiryDate')}
          inputProps={{ maxLength: 5 }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="CVV"
          type="password"
          value={cardDetails.cvv}
          onChange={handleCardDetailsChange('cvv')}
          inputProps={{ maxLength: 3 }}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Nom du titulaire"
          value={cardDetails.name}
          onChange={handleCardDetailsChange('name')}
        />
      </Grid>
    </Grid>
  );

  const renderConfirmation = () => (
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h6" gutterBottom>
        Récapitulatif du paiement
      </Typography>
      <Typography gutterBottom>
        Montant à payer: {amount} DA
      </Typography>
      <Typography gutterBottom>
        Mode de paiement: {
          paymentMethod === 'cib' ? 'Carte CIB' :
          paymentMethod === 'edahabia' ? 'EDAHABIA' :
          'Paiement à la livraison'
        }
      </Typography>
      {paymentMethod === 'cib' && (
        <Typography gutterBottom>
          Carte se terminant par: {cardDetails.cardNumber.slice(-4)}
        </Typography>
      )}
    </Box>
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ mt: 2, mb: 4 }}>
          {activeStep === 0 && renderPaymentMethodSelection()}
          {activeStep === 1 && renderCardDetails()}
          {activeStep === 2 && renderConfirmation()}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', pt: 2 }}>
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
          >
            Retour
          </Button>
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? processPayment : handleNext}
            disabled={loading}
          >
            {loading ? (
              <CircularProgress size={24} />
            ) : activeStep === steps.length - 1 ? (
              'Confirmer le paiement'
            ) : (
              'Suivant'
            )}
          </Button>
        </Box>
      </Paper>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>
          {success ? 'Paiement réussi' : 'Erreur de paiement'}
        </DialogTitle>
        <DialogContent>
          {success ? (
            <Typography>
              Votre paiement a été traité avec succès.
              Numéro de transaction: {transactionId}
            </Typography>
          ) : (
            <Typography color="error">
              {error}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            Fermer
          </Button>
          {success && (
            <Button
              variant="contained"
              onClick={() => window.location.href = '/orders'}
            >
              Voir mes commandes
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MarketplacePayment;