import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useTranslation } from 'react-i18next';

function PondeuseManagement() {
  const { t } = useTranslation();
  const [pondeuses, setPondeuses] = useState([]);
  const [collectionData, setCollectionData] = useState([]); // [{ date, quantite, qualite }]
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPondeuses = async () => {
      try {
        setLoading(true);
        // const response = await api.get("/api/eleveur/pondeuses");
        // setPondeuses(response.data);
        setPondeuses([
          { id: 1, nom: 'Lot Pondeuses 1', race: '<PERSON><PERSON> Brown', age: 25, production_journaliere: 85 },
          { id: 2, nom: 'Lot Pondeuses 2', race: 'Isa <PERSON>', age: 30, production_journaliere: 90 }
        ]); // Données de test
        
        // Simuler des données de collecte
        setCollectionData([
          { date: new Date(), quantite: 850, qualite: 'A' },
          { date: new Date(Date.now() - 86400000), quantite: 830, qualite: 'A' }
        ]);
        
        setError(null);
      } catch (err) {
        console.error("Erreur chargement pondeuses:", err);
        setError("Impossible de charger les données des pondeuses.");
        setPondeuses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPondeuses();
  }, []);

  const handleCollectionUpdate = async (date, quantite, qualite) => {
    try {
      // Simulated API call to add collection data
      // await api.post('/api/eleveur/pondeuses/collection', { date, quantite, qualite });
      
      // Update local state
      setCollectionData(prev => [
        { date, quantite, qualite },
        ...prev
      ]);
    } catch (err) {
      console.error("Erreur enregistrement collecte:", err);
      setError("Impossible d'enregistrer la collecte.");
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">{error}</Alert>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h5" gutterBottom>
        {t('Gestion des Pondeuses')}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Information sur les lots */}
        {pondeuses.map((pondeuse) => (
          <Grid item xs={12} md={6} key={pondeuse.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{pondeuse.nom}</Typography>
                <Typography color="textSecondary" gutterBottom>
                  {t('Race')}: {pondeuse.race}
                </Typography>
                <Typography>
                  {t('Âge')}: {pondeuse.age} {t('semaines')}
                </Typography>
                <Typography>
                  {t('Production journalière moyenne')}: {pondeuse.production_journaliere}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}

        {/* Historique des collectes */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('Historique des Collectes')}
              </Typography>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('Date')}</TableCell>
                      <TableCell align="right">{t('Quantité')}</TableCell>
                      <TableCell>{t('Qualité')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {collectionData.map((collection, index) => (
                      <TableRow key={index}>
                        <TableCell>{collection.date.toLocaleDateString()}</TableCell>
                        <TableCell align="right">{collection.quantite}</TableCell>
                        <TableCell>{collection.qualite}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default PondeuseManagement;