/**
 * Service de tests A/B pour optimisation UI/UX
 */

import { v4 as uuidv4 } from 'uuid';
import apiService from './apiService';
import storageService from './storageService';

class ABTestingService {
  constructor() {
    this.activeTests = new Map();
    this.userAssignments = new Map();
    this.eventQueue = [];
    this.initialized = false;
  }

  /**
   * Initialiser le service de tests A/B
   */
  async initialize(userId) {
    if (this.initialized) return;

    try {
      // Charger les tests actifs depuis le serveur
      const response = await apiService.get('/api/ab-tests/active');
      const activeTests = response.data;

      // Charger les assignations utilisateur depuis le stockage local
      const storedAssignments = await storageService.getItem('ab_test_assignments') || {};

      for (const test of activeTests) {
        this.activeTests.set(test.id, test);

        // Assigner l'utilisateur à une variante si pas déjà fait
        if (!storedAssignments[test.id]) {
          const variant = this.assignUserToVariant(userId, test);
          storedAssignments[test.id] = variant;
          
          // Enregistrer l'assignation sur le serveur
          await this.recordAssignment(userId, test.id, variant);
        }

        this.userAssignments.set(test.id, storedAssignments[test.id]);
      }

      // Sauvegarder les assignations
      await storageService.setItem('ab_test_assignments', storedAssignments);

      this.initialized = true;
      console.log('🧪 Service de tests A/B initialisé avec', activeTests.length, 'tests actifs');

    } catch (error) {
      console.error('Erreur lors de l\'initialisation des tests A/B:', error);
    }
  }

  /**
   * Assigner un utilisateur à une variante de test
   */
  assignUserToVariant(userId, test) {
    // Utiliser un hash déterministe basé sur l'ID utilisateur et l'ID du test
    const hash = this.hashString(`${userId}_${test.id}`);
    const bucket = hash % 100;

    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += variant.weight;
      if (bucket < cumulativeWeight) {
        return variant.id;
      }
    }

    // Fallback vers la variante de contrôle
    return test.variants.find(v => v.isControl)?.id || test.variants[0].id;
  }

  /**
   * Obtenir la variante assignée pour un test
   */
  getVariant(testId) {
    if (!this.initialized) {
      console.warn('Service de tests A/B non initialisé');
      return null;
    }

    const test = this.activeTests.get(testId);
    if (!test) {
      return null;
    }

    const variantId = this.userAssignments.get(testId);
    return test.variants.find(v => v.id === variantId);
  }

  /**
   * Vérifier si une fonctionnalité est activée pour l'utilisateur
   */
  isFeatureEnabled(testId, featureName) {
    const variant = this.getVariant(testId);
    if (!variant) return false;

    return variant.features?.[featureName] === true;
  }

  /**
   * Obtenir la configuration d'une variante
   */
  getVariantConfig(testId, configKey) {
    const variant = this.getVariant(testId);
    if (!variant) return null;

    return variant.config?.[configKey];
  }

  /**
   * Enregistrer un événement de conversion
   */
  async trackConversion(testId, eventType, eventData = {}) {
    if (!this.initialized) return;

    const variant = this.getVariant(testId);
    if (!variant) return;

    const event = {
      id: uuidv4(),
      testId,
      variantId: variant.id,
      eventType,
      eventData,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      userId: this.getCurrentUserId()
    };

    // Ajouter à la queue pour envoi différé
    this.eventQueue.push(event);

    // Envoyer immédiatement si c'est un événement critique
    if (this.isCriticalEvent(eventType)) {
      await this.flushEvents();
    }

    console.log('📊 Conversion trackée:', eventType, 'pour test', testId, 'variante', variant.id);
  }

  /**
   * Enregistrer une vue de page
   */
  async trackPageView(testId, pageName, additionalData = {}) {
    await this.trackConversion(testId, 'page_view', {
      pageName,
      ...additionalData
    });
  }

  /**
   * Enregistrer un clic sur un élément
   */
  async trackClick(testId, elementId, elementType = 'button') {
    await this.trackConversion(testId, 'click', {
      elementId,
      elementType
    });
  }

  /**
   * Enregistrer une action utilisateur
   */
  async trackUserAction(testId, actionName, actionData = {}) {
    await this.trackConversion(testId, 'user_action', {
      actionName,
      ...actionData
    });
  }

  /**
   * Enregistrer une conversion d'objectif
   */
  async trackGoalConversion(testId, goalName, value = 1) {
    await this.trackConversion(testId, 'goal_conversion', {
      goalName,
      value
    });
  }

  /**
   * Envoyer les événements en attente au serveur
   */
  async flushEvents() {
    if (this.eventQueue.length === 0) return;

    try {
      const events = [...this.eventQueue];
      this.eventQueue = [];

      await apiService.post('/api/ab-tests/events', { events });
      console.log('📤 Événements A/B envoyés:', events.length);

    } catch (error) {
      console.error('Erreur lors de l\'envoi des événements A/B:', error);
      // Remettre les événements dans la queue en cas d'erreur
      this.eventQueue.unshift(...events);
    }
  }

  /**
   * Créer un nouveau test A/B
   */
  async createTest(testConfig) {
    try {
      const response = await apiService.post('/api/ab-tests', testConfig);
      const test = response.data;

      this.activeTests.set(test.id, test);
      console.log('🧪 Nouveau test A/B créé:', test.name);

      return test;

    } catch (error) {
      console.error('Erreur lors de la création du test A/B:', error);
      throw error;
    }
  }

  /**
   * Obtenir les résultats d'un test
   */
  async getTestResults(testId) {
    try {
      const response = await apiService.get(`/api/ab-tests/${testId}/results`);
      return response.data;

    } catch (error) {
      console.error('Erreur lors de la récupération des résultats:', error);
      throw error;
    }
  }

  /**
   * Arrêter un test A/B
   */
  async stopTest(testId, reason = '') {
    try {
      await apiService.post(`/api/ab-tests/${testId}/stop`, { reason });
      
      this.activeTests.delete(testId);
      this.userAssignments.delete(testId);

      // Nettoyer le stockage local
      const storedAssignments = await storageService.getItem('ab_test_assignments') || {};
      delete storedAssignments[testId];
      await storageService.setItem('ab_test_assignments', storedAssignments);

      console.log('🛑 Test A/B arrêté:', testId);

    } catch (error) {
      console.error('Erreur lors de l\'arrêt du test:', error);
      throw error;
    }
  }

  /**
   * Hook React pour utiliser les tests A/B
   */
  useABTest(testId) {
    const variant = this.getVariant(testId);
    
    return {
      variant,
      isFeatureEnabled: (featureName) => this.isFeatureEnabled(testId, featureName),
      getConfig: (configKey) => this.getVariantConfig(testId, configKey),
      trackConversion: (eventType, eventData) => this.trackConversion(testId, eventType, eventData),
      trackClick: (elementId, elementType) => this.trackClick(testId, elementId, elementType),
      trackGoal: (goalName, value) => this.trackGoalConversion(testId, goalName, value)
    };
  }

  /**
   * Composant React pour les tests A/B
   */
  ABTestComponent({ testId, children, fallback = null }) {
    const variant = this.getVariant(testId);
    
    if (!variant) {
      return fallback;
    }

    // Rendre le composant correspondant à la variante
    if (typeof children === 'function') {
      return children(variant);
    }

    return children[variant.id] || fallback;
  }

  /**
   * Méthodes utilitaires privées
   */

  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir en 32-bit integer
    }
    return Math.abs(hash);
  }

  async recordAssignment(userId, testId, variantId) {
    try {
      await apiService.post('/api/ab-tests/assignments', {
        userId,
        testId,
        variantId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'assignation:', error);
    }
  }

  isCriticalEvent(eventType) {
    const criticalEvents = ['goal_conversion', 'purchase', 'signup', 'error'];
    return criticalEvents.includes(eventType);
  }

  getSessionId() {
    let sessionId = sessionStorage.getItem('ab_session_id');
    if (!sessionId) {
      sessionId = uuidv4();
      sessionStorage.setItem('ab_session_id', sessionId);
    }
    return sessionId;
  }

  getCurrentUserId() {
    // À implémenter selon votre système d'authentification
    return localStorage.getItem('user_id') || 'anonymous';
  }

  /**
   * Démarrer l'envoi périodique des événements
   */
  startPeriodicFlush() {
    setInterval(() => {
      this.flushEvents();
    }, 30000); // Envoyer toutes les 30 secondes

    // Envoyer lors de la fermeture de la page
    window.addEventListener('beforeunload', () => {
      this.flushEvents();
    });
  }

  /**
   * Tests prédéfinis pour l'application
   */
  getDefaultTests() {
    return [
      {
        id: 'dashboard_layout_test',
        name: 'Test de layout du dashboard',
        description: 'Comparer différentes dispositions du dashboard',
        variants: [
          {
            id: 'control',
            name: 'Layout actuel',
            isControl: true,
            weight: 50,
            config: {
              layout: 'grid',
              cardsPerRow: 3
            }
          },
          {
            id: 'compact',
            name: 'Layout compact',
            isControl: false,
            weight: 50,
            config: {
              layout: 'list',
              cardsPerRow: 4
            }
          }
        ],
        goals: ['dashboard_engagement', 'time_on_page']
      },
      {
        id: 'onboarding_flow_test',
        name: 'Test du flux d\'onboarding',
        description: 'Optimiser le processus d\'accueil des nouveaux utilisateurs',
        variants: [
          {
            id: 'current',
            name: 'Onboarding actuel',
            isControl: true,
            weight: 50,
            features: {
              skipTutorial: false,
              progressIndicator: true
            }
          },
          {
            id: 'simplified',
            name: 'Onboarding simplifié',
            isControl: false,
            weight: 50,
            features: {
              skipTutorial: true,
              progressIndicator: false
            }
          }
        ],
        goals: ['onboarding_completion', 'first_action']
      }
    ];
  }
}

// Instance singleton
const abTestingService = new ABTestingService();

export default abTestingService;
