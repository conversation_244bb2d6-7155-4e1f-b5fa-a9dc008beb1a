import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Avatar, 
  Chip, 
  IconButton, 
  Button,
  Badge,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Tooltip,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { 
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  Refresh as RefreshIcon,
  WbSunny as SunnyIcon,
  Cloud as CloudIcon,
  Person as PersonIcon,
  ExitToApp as LogoutIcon,
  Dashboard as DashboardIcon,
  Add as AddIcon,
  Sell as SellIcon,
  LocalShipping as ShippingIcon,
  Pets as PetsIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';

/**
 * Composant d'en-tête pour le tableau de bord éleveur
 * Affiche le profil de l'utilisateur, la météo et les actions rapides
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {Object} props.profile - Informations sur le profil de l'éleveur
 * @param {Object} props.weatherData - Données météorologiques actuelles
 * @param {Function} props.onRefresh - Fonction à appeler pour rafraîchir les données
 * @param {boolean} props.refreshing - Indique si les données sont en cours de rafraîchissement
 * @returns {JSX.Element} - Le composant d'en-tête
 */
const DashboardHeader = ({ 
  profile = {}, 
  weatherData = null, 
  onRefresh = () => {}, 
  refreshing = false 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const [profileAnchorEl, setProfileAnchorEl] = useState(null);
  const [actionsAnchorEl, setActionsAnchorEl] = useState(null);
  
  // Données simulées pour le profil
  const defaultProfile = {
    id: 'ELV123',
    firstName: 'Mohammed',
    lastName: 'Benali',
    photoUrl: '',
    role: 'Éleveur',
    location: 'Blida, Algérie',
    since: '2023'
  };
  
  // Données simulées pour la météo
  const defaultWeather = {
    temperature: 24,
    condition: 'sunny', // sunny, cloudy, rainy, stormy, snowy
    location: 'Blida'
  };
  
  // Utiliser les données fournies ou les données par défaut
  const userProfile = Object.keys(profile).length > 0 ? profile : defaultProfile;
  const currentWeather = weatherData || defaultWeather;
  
  // Notifications simulées
  const notifications = [
    { id: 1, type: 'alert', message: 'Stock d\'aliments bas', time: '10 min' },
    { id: 2, type: 'info', message: 'Nouvelle mise à jour des prix', time: '1 heure' },
    { id: 3, type: 'success', message: 'Vente #12345 complétée', time: '3 heures' }
  ];
  
  // Actions rapides
  const quickActions = [
    { id: 1, label: 'Ajouter des volailles', icon: <PetsIcon />, action: () => console.log('Ajouter des volailles') },
    { id: 2, label: 'Enregistrer une vente', icon: <SellIcon />, action: () => console.log('Enregistrer une vente') },
    { id: 3, label: 'Gérer les livraisons', icon: <ShippingIcon />, action: () => console.log('Gérer les livraisons') }
  ];
  
  // Gérer l'ouverture/fermeture des menus
  const handleNotificationsClick = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };
  
  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };
  
  const handleProfileClick = (event) => {
    setProfileAnchorEl(event.currentTarget);
  };
  
  const handleProfileClose = () => {
    setProfileAnchorEl(null);
  };
  
  const handleActionsClick = (event) => {
    setActionsAnchorEl(event.currentTarget);
  };
  
  const handleActionsClose = () => {
    setActionsAnchorEl(null);
  };
  
  // Obtenir l'icône météo
  const getWeatherIcon = () => {
    switch (currentWeather.condition) {
      case 'sunny':
        return <SunnyIcon sx={{ color: '#F59E0B' }} />;
      case 'cloudy':
        return <CloudIcon sx={{ color: '#94A3B8' }} />;
      default:
        return <SunnyIcon sx={{ color: '#F59E0B' }} />;
    }
  };
  
  return (
    <Paper 
      elevation={1} 
      sx={{ 
        p: 2, 
        mb: 3, 
        borderRadius: 2,
        background: 'linear-gradient(to right, #f8fafc, #f1f5f9)'
      }}
    >
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 }
      }}>
        {/* Profil et bienvenue */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar 
            src={userProfile.photoUrl} 
            alt={`${userProfile.firstName} ${userProfile.lastName}`}
            sx={{ 
              width: 56, 
              height: 56, 
              bgcolor: 'primary.main',
              border: '2px solid white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
          >
            {userProfile.firstName?.charAt(0)}{userProfile.lastName?.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
              Bonjour, {userProfile.firstName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {new Date().toLocaleDateString('fr-FR', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}
            </Typography>
          </Box>
        </Box>
        
        {/* Actions et météo */}
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          width: { xs: '100%', sm: 'auto' },
          justifyContent: { xs: 'space-between', sm: 'flex-end' }
        }}>
          {/* Widget météo */}
          {!isMobile && (
            <Chip
              icon={getWeatherIcon()}
              label={`${currentWeather.temperature}°C - ${currentWeather.location}`}
              variant="outlined"
              sx={{ 
                borderRadius: 2,
                bgcolor: 'background.paper',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                '& .MuiChip-label': {
                  fontWeight: 500
                }
              }}
            />
          )}
          
          {/* Actions rapides pour tablette et desktop */}
          {!isMobile && !isTablet && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outlined"
                  size="small"
                  startIcon={action.icon}
                  onClick={action.action}
                  sx={{ 
                    borderRadius: 2,
                    textTransform: 'none',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </Box>
          )}
          
          {/* Menu d'actions pour mobile et tablette */}
          {(isMobile || isTablet) && (
            <>
              <Tooltip title="Actions rapides">
                <IconButton 
                  color="primary"
                  onClick={handleActionsClick}
                  sx={{ bgcolor: 'background.paper', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
                >
                  <AddIcon />
                </IconButton>
              </Tooltip>
              <Menu
                anchorEl={actionsAnchorEl}
                open={Boolean(actionsAnchorEl)}
                onClose={handleActionsClose}
                PaperProps={{
                  elevation: 3,
                  sx: { borderRadius: 2, mt: 1 }
                }}
              >
                {quickActions.map((action) => (
                  <MenuItem key={action.id} onClick={() => { action.action(); handleActionsClose(); }}>
                    <ListItemIcon>{action.icon}</ListItemIcon>
                    <ListItemText>{action.label}</ListItemText>
                  </MenuItem>
                ))}
              </Menu>
            </>
          )}
          
          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton 
              color="primary" 
              onClick={handleNotificationsClick}
              sx={{ bgcolor: 'background.paper', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
            >
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>
          <Menu
            anchorEl={notificationsAnchorEl}
            open={Boolean(notificationsAnchorEl)}
            onClose={handleNotificationsClose}
            PaperProps={{
              elevation: 3,
              sx: { width: 320, maxWidth: '100%', borderRadius: 2, mt: 1 }
            }}
          >
            <Typography variant="subtitle1" sx={{ px: 2, py: 1, fontWeight: 600 }}>
              Notifications
            </Typography>
            <Divider />
            {notifications.map((notification) => (
              <MenuItem key={notification.id} onClick={handleNotificationsClose}>
                <ListItemText 
                  primary={notification.message}
                  secondary={`Il y a ${notification.time}`}
                />
              </MenuItem>
            ))}
            <Divider />
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
              <Button size="small" onClick={handleNotificationsClose}>
                Voir toutes les notifications
              </Button>
            </Box>
          </Menu>
          
          {/* Profil */}
          <Tooltip title="Profil">
            <IconButton 
              onClick={handleProfileClick}
              sx={{ bgcolor: 'background.paper', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
            >
              <PersonIcon />
            </IconButton>
          </Tooltip>
          <Menu
            anchorEl={profileAnchorEl}
            open={Boolean(profileAnchorEl)}
            onClose={handleProfileClose}
            PaperProps={{
              elevation: 3,
              sx: { width: 200, maxWidth: '100%', borderRadius: 2, mt: 1 }
            }}
          >
            <Box sx={{ px: 2, py: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {userProfile.firstName} {userProfile.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {userProfile.role}
              </Typography>
            </Box>
            <Divider />
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon><PersonIcon fontSize="small" /></ListItemIcon>
              <ListItemText>Mon profil</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon><DashboardIcon fontSize="small" /></ListItemIcon>
              <ListItemText>Tableau de bord</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon><SettingsIcon fontSize="small" /></ListItemIcon>
              <ListItemText>Paramètres</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon><LogoutIcon fontSize="small" /></ListItemIcon>
              <ListItemText>Déconnexion</ListItemText>
            </MenuItem>
          </Menu>
          
          {/* Rafraîchir */}
          <Tooltip title="Rafraîchir les données">
            <IconButton 
              onClick={onRefresh} 
              disabled={refreshing}
              sx={{ 
                bgcolor: 'background.paper', 
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                animation: refreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Paper>
  );
};

export default DashboardHeader;
