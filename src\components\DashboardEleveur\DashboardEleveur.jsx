import React, { useState, useEffect } from 'react';
import { Container, Grid, Paper, Typography, Box, useMediaQuery, useTheme } from '@mui/material';
import './DashboardEleveur.css';

// Import des composants
import Overview from './components/Overview/Overview';
import ProductionTracking from './components/ProductionTracking/ProductionTracking';
import AlertsActions from './components/AlertsActions/AlertsActions';
import RecentActivity from './components/RecentActivity/RecentActivity';

// Import des hooks personnalisés
import useEleveurData from './hooks/useEleveurData';
import useProductionData from './hooks/useProductionData';
import useAuthToken from './hooks/useAuthToken';

const DashboardEleveur = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { isAuthenticated, loading: authLoading } = useAuthToken();
  const { eleveurData, loading: eleveurLoading } = useEleveurData();
  const { productionData, loading: productionLoading } = useProductionData();

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Vérifier si toutes les données sont chargées
    if (!authLoading && !eleveurLoading && !productionLoading) {
      setIsLoading(false);
    }
  }, [authLoading, eleveurLoading, productionLoading]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography variant="h5">Chargement du tableau de bord...</Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography variant="h5" color="error">
          Vous devez être connecté pour accéder à cette page.
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" className="dashboard-container">
      <Typography variant="h4" component="h1" className="dashboard-title" gutterBottom>
        Tableau de Bord Éleveur
      </Typography>

      <Grid container spacing={3}>
        {/* Vue d'ensemble */}
        <Grid item xs={12} md={12} lg={12}>
          <Paper elevation={2} className="dashboard-paper">
            <Overview data={eleveurData} />
          </Paper>
        </Grid>

        {/* Suivi de production */}
        <Grid item xs={12} md={8} lg={8}>
          <Paper elevation={2} className="dashboard-paper">
            <ProductionTracking data={productionData} />
          </Paper>
        </Grid>

        {/* Alertes et actions */}
        <Grid item xs={12} md={4} lg={4}>
          <Paper elevation={2} className="dashboard-paper">
            <AlertsActions data={eleveurData} />
          </Paper>
        </Grid>

        {/* Activité récente */}
        <Grid item xs={12}>
          <Paper elevation={2} className="dashboard-paper">
            <RecentActivity data={eleveurData} />
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DashboardEleveur;