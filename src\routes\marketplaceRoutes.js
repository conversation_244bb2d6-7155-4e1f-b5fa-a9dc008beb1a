const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const Product = require('../models/product');
const User = require('../models/user');
const Favori = require('../models/favori');

// @route   GET /api/marketplace/annonces
// @desc    Récupérer toutes les annonces avec pagination et filtres
// @access  Public
router.get('/annonces', async (req, res) => {
  try {
    const { page = 1, limit = 10, espece, prix_min, prix_max, localisation } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (espece) whereClause.espece = espece;
    if (prix_min || prix_max) {
      whereClause.prix_unitaire = {};
      if (prix_min) whereClause.prix_unitaire['$gte'] = prix_min;
      if (prix_max) whereClause.prix_unitaire['$lte'] = prix_max;
    }

    const annonces = await Product.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [{
        model: User,
        as: 'vendeur',
        attributes: ['id', 'username', 'email']
      }],
      order: [['created_at', 'DESC']]
    });

    res.json({
      annonces: annonces.rows,
      total: annonces.count,
      pages: Math.ceil(annonces.count / limit),
      currentPage: page
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// @route   POST /api/marketplace/annonces
// @desc    Créer une nouvelle annonce
// @access  Private
router.post('/annonces', auth, async (req, res) => {
  try {
    const { titre, description, espece, race, quantite, prix_unitaire, images } = req.body;

    const annonce = await Product.create({
      titre,
      description,
      espece,
      race,
      quantite,
      prix_unitaire,
      images,
      vendeur_id: req.user.id,
      status: 'active'
    });

    res.status(201).json(annonce);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// @route   GET /api/marketplace/annonces/:id
// @desc    Récupérer une annonce par son ID
// @access  Public
router.get('/annonces/:id', async (req, res) => {
  try {
    const annonce = await Product.findByPk(req.params.id, {
      include: [{
        model: User,
        as: 'vendeur',
        attributes: ['id', 'username', 'email']
      }]
    });

    if (!annonce) {
      return res.status(404).json({ error: 'Annonce non trouvée' });
    }

    res.json(annonce);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// @route   PUT /api/marketplace/annonces/:id
// @desc    Modifier une annonce
// @access  Private
router.put('/annonces/:id', auth, async (req, res) => {
  try {
    const annonce = await Product.findByPk(req.params.id);
    if (!annonce) {
      return res.status(404).json({ error: 'Annonce non trouvée' });
    }

    if (annonce.vendeur_id !== req.user.id) {
      return res.status(403).json({ error: 'Non autorisé à modifier cette annonce' });
    }

    const { titre, description, espece, race, quantite, prix_unitaire, images, status } = req.body;
    await annonce.update({
      titre,
      description,
      espece,
      race,
      quantite,
      prix_unitaire,
      images,
      status
    });

    res.json(annonce);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// @route   DELETE /api/marketplace/annonces/:id
// @desc    Supprimer une annonce
// @access  Private
router.delete('/annonces/:id', auth, async (req, res) => {
  try {
    const annonce = await Product.findByPk(req.params.id);
    if (!annonce) {
      return res.status(404).json({ error: 'Annonce non trouvée' });
    }

    if (annonce.vendeur_id !== req.user.id) {
      return res.status(403).json({ error: 'Non autorisé à supprimer cette annonce' });
    }

    await annonce.destroy();
    res.json({ message: 'Annonce supprimée avec succès' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// @route   POST /api/marketplace/annonces/:id/favoris
// @desc    Ajouter/Retirer une annonce des favoris
// @access  Private
router.post('/annonces/:id/favoris', auth, async (req, res) => {
  try {
    const annonce = await Product.findByPk(req.params.id);
    if (!annonce) {
      return res.status(404).json({ error: 'Annonce non trouvée' });
    }

    const [favori, created] = await Favori.findOrCreate({
      where: {
        utilisateur_id: req.user.id,
        annonce_id: req.params.id
      }
    });

    if (!created) {
      await favori.destroy();
      return res.json({
        message: 'Annonce retirée des favoris'
      });
    }

    res.json({
      message: 'Annonce ajoutée aux favoris'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// @route   GET /api/marketplace/favoris
// @desc    Récupérer les annonces favorites de l'utilisateur
// @access  Private
router.get('/favoris', auth, async (req, res) => {
  try {
    const favoris = await Favori.findAll({
      where: {
        utilisateur_id: req.user.id
      },
      include: [{
        model: Product,
        as: 'annonce',
        include: [{
          model: User,
          as: 'vendeur',
          attributes: ['id', 'username', 'email']
        }]
      }]
    });

    const annonces = favoris.map(f => f.annonce);
    res.json(annonces);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// @route   DELETE /api/marketplace/favoris/:id
// @desc    Retirer une annonce des favoris
// @access  Private
router.delete('/favoris/:id', auth, async (req, res) => {
  try {
    const deleted = await Favori.destroy({
      where: {
        utilisateur_id: req.user.id,
        annonce_id: req.params.id
      }
    });

    if (!deleted) {
      return res.status(404).json({ error: 'Favori non trouvé' });
    }

    res.json({
      message: 'Annonce retirée des favoris'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
