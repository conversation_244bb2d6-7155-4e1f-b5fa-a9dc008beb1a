/**
 * Test script for security settings service
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const API_URL = 'http://localhost:3003/api';
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Créer une instance axios
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000
});

// Helper pour logger les messages avec couleur
const log = {
  info: (msg) => console.log(colors.cyan(msg)),
  success: (msg) => console.log(colors.green(msg)),
  error: (msg) => console.log(colors.red(msg)),
  warning: (msg) => console.log(colors.yellow(msg)),
  data: (data) => console.log(JSON.stringify(data, null, 2))
};

// Fonction pour se connecter et obtenir un token
async function login() {
  log.info('Connexion en tant qu\'admin...');
  try {
    const response = await api.post('/auth/login', adminCredentials);
    const token = response.data.token;

    // Configurer l'authentification pour les futures requêtes
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    api.defaults.headers.common['x-auth-token'] = token;

    log.success('✅ Connexion réussie!');
    return token;
  } catch (error) {
    log.error('❌ Échec de la connexion!');
    log.error(error.response?.data?.message || error.message);
    process.exit(1);
  }
}

// Fonction pour récupérer les paramètres de sécurité
async function getSecuritySettings() {
  log.info('Récupération des paramètres de sécurité...');
  try {
    const response = await api.get('/admin/settings/security');
    log.success('✅ Paramètres récupérés avec succès!');
    log.info('Paramètres de sécurité:');
    log.data(response.data);
    return response.data;
  } catch (error) {
    log.error('❌ Échec de la récupération des paramètres!');
    log.error(error.response?.data?.message || error.message);
    process.exit(1);
  }
}

// Fonction pour mettre à jour les paramètres de sécurité
async function updateSecuritySettings(settings) {
  log.info('Mise à jour des paramètres de sécurité...');
  try {
    const response = await api.post('/admin/settings/security', settings);
    log.success('✅ Paramètres mis à jour avec succès!');
    return response.data;
  } catch (error) {
    log.error('❌ Échec de la mise à jour des paramètres!');
    log.error(error.response?.data?.message || error.message);
    process.exit(1);
  }
}

// Fonction principale
async function main() {
  log.info('Test du service de paramètres de sécurité...');

  // Login
  await login();

  // Récupérer les paramètres actuels
  const currentSettings = await getSecuritySettings();

  // Créer une copie modifiée des paramètres
  const updatedSettings = {
    ...currentSettings,
    sessionTimeout: 40, // Modifier la valeur de test
    apiRateLimitRequests: 120 // Modifier une autre valeur de test
  };

  // Mettre à jour les paramètres
  log.info('Mise à jour avec de nouvelles valeurs...');
  await updateSecuritySettings(updatedSettings);

  // Vérifier les changements
  log.info('Vérification des changements...');
  const newSettings = await getSecuritySettings();

  // Valider que les changements ont été appliqués
  if (newSettings.sessionTimeout === 40 && newSettings.apiRateLimitRequests === 120) {
    log.success('✅ TEST RÉUSSI: Les changements ont été correctement appliqués!');
  } else {
    log.error('❌ TEST ÉCHOUÉ: Les changements n\'ont pas été correctement appliqués!');
    log.warning('Valeurs attendues:');
    log.warning(`sessionTimeout: 40, reçu: ${newSettings.sessionTimeout}`);
    log.warning(`apiRateLimitRequests: 120, reçu: ${newSettings.apiRateLimitRequests}`);
  }

  // Remettre les valeurs d'origine
  log.info('Restauration des valeurs d\'origine...');
  await updateSecuritySettings(currentSettings);
  log.success('✅ Valeurs d\'origine restaurées!');
}

// Exécuter le test
main().catch(error => {
  log.error('Erreur non gérée:');
  log.error(error.message);
  process.exit(1);
});
