const pool = require('../config/database');
const { uploadToCloudStorage } = require('../utils/cloudStorage');
const { sequelize, Order, OrderItem, Product, User } = require('../models');
const aiRecommendationService = require('../services/aiRecommendationService');
const { QueryTypes } = require('sequelize');

// Récupérer tous les produits d'un marchand
exports.getProduits = async (req, res) => {
  try {
    const { rows } = await pool.query(
      'SELECT * FROM produits WHERE marchand_id = $1 ORDER BY date_creation DESC',
      [req.user.id]
    );
    res.json(rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des produits:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération des produits' });
  }
};

// Créer un nouveau produit
exports.createProduit = async (req, res) => {
  const { nom, description, prix, quantite, categorie } = req.body;
  const image = req.file;

  try {
    let image_url = null;
    if (image) {
      image_url = await uploadToCloudStorage(image);
    }

    const { rows } = await pool.query(
      `INSERT INTO produits
       (nom, description, prix, quantite, categorie, image_url, marchand_id)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING *`,
      [nom, description, prix, quantite, categorie, image_url, req.user.id]
    );

    res.status(201).json(rows[0]);
  } catch (err) {
    console.error('Erreur lors de la création du produit:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la création du produit' });
  }
};

// Mettre à jour un produit
exports.updateProduit = async (req, res) => {
  const { id } = req.params;
  const { nom, description, prix, quantite, categorie } = req.body;
  const image = req.file;

  try {
    // Vérifier que le produit appartient au marchand
    const { rows: [produit] } = await pool.query(
      'SELECT * FROM produits WHERE id = $1 AND marchand_id = $2',
      [id, req.user.id]
    );

    if (!produit) {
      return res.status(404).json({ message: 'Produit non trouvé' });
    }

    let image_url = produit.image_url;
    if (image) {
      image_url = await uploadToCloudStorage(image);
    }

    const { rows: [updatedProduit] } = await pool.query(
      `UPDATE produits
       SET nom = $1, description = $2, prix = $3, quantite = $4,
           categorie = $5, image_url = $6, date_modification = CURRENT_TIMESTAMP
       WHERE id = $7 AND marchand_id = $8
       RETURNING *`,
      [nom, description, prix, quantite, categorie, image_url, id, req.user.id]
    );

    res.json(updatedProduit);
  } catch (err) {
    console.error('Erreur lors de la mise à jour du produit:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du produit' });
  }
};

// Supprimer un produit
exports.deleteProduit = async (req, res) => {
  const { id } = req.params;

  try {
    const { rowCount } = await pool.query(
      'DELETE FROM produits WHERE id = $1 AND marchand_id = $2',
      [id, req.user.id]
    );

    if (rowCount === 0) {
      return res.status(404).json({ message: 'Produit non trouvé' });
    }

    res.json({ message: 'Produit supprimé avec succès' });
  } catch (err) {
    console.error('Erreur lors de la suppression du produit:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la suppression du produit' });
  }
};

// Récupérer les commandes du marchand
exports.getCommandes = async (req, res) => {
  try {
    const { rows: commandes } = await pool.query(
      `SELECT c.*, u.nom as client_nom,
              json_agg(json_build_object(
                'id', p.id,
                'nom', p.nom,
                'quantite', cp.quantite,
                'prix_unitaire', cp.prix_unitaire
              )) as produits
       FROM commandes c
       JOIN users u ON c.client_id = u.id
       JOIN commande_produits cp ON c.id = cp.commande_id
       JOIN produits p ON cp.produit_id = p.id
       WHERE p.marchand_id = $1
       GROUP BY c.id, u.nom
       ORDER BY c.date_commande DESC`,
      [req.user.id]
    );

    res.json(commandes);
  } catch (err) {
    console.error('Erreur lors de la récupération des commandes:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération des commandes' });
  }
};

// Mettre à jour le statut d'une commande
exports.updateCommandeStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  try {
    const { rows: [commande] } = await pool.query(
      `UPDATE commandes c
       SET status = $1,
           date_modification = CURRENT_TIMESTAMP
       FROM produits p
       JOIN commande_produits cp ON p.id = cp.produit_id
       WHERE c.id = $2 AND p.marchand_id = $3
       RETURNING c.*`,
      [status, id, req.user.id]
    );

    if (!commande) {
      return res.status(404).json({ message: 'Commande non trouvée' });
    }

    res.json(commande);
  } catch (err) {
    console.error('Erreur lors de la mise à jour du statut de la commande:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du statut' });
  }
};

// Récupérer les statistiques des ventes
exports.getVentesStatistiques = async (req, res) => {
  const { periode } = req.query;

  try {
    // Calcul du total des ventes
    const { rows: [totalVentes] } = await pool.query(
      `SELECT COALESCE(SUM(montant_total), 0) as total
       FROM ventes v
       JOIN produits p ON v.produit_id = p.id
       WHERE p.marchand_id = $1`,
      [req.user.id]
    );

    // Calcul des ventes par période
    let intervalFormat;
    switch (periode) {
      case 'semaine':
        intervalFormat = 'YYYY-WW';
        break;
      case 'mois':
        intervalFormat = 'YYYY-MM';
        break;
      case 'annee':
        intervalFormat = 'YYYY';
        break;
      default:
        intervalFormat = 'YYYY-MM';
    }

    const { rows: ventesParPeriode } = await pool.query(
      `SELECT TO_CHAR(date_vente, $1) as periode,
              SUM(montant_total) as montant
       FROM ventes v
       JOIN produits p ON v.produit_id = p.id
       WHERE p.marchand_id = $2
       GROUP BY periode
       ORDER BY periode DESC
       LIMIT 12`,
      [intervalFormat, req.user.id]
    );

    // Calcul des ventes par produit
    const { rows: ventesParProduit } = await pool.query(
      `SELECT p.nom,
              SUM(v.montant_total) as montant,
              COUNT(*) as nombre_ventes
       FROM ventes v
       JOIN produits p ON v.produit_id = p.id
       WHERE p.marchand_id = $1
       GROUP BY p.id, p.nom
       ORDER BY montant DESC
       LIMIT 5`,
      [req.user.id]
    );

    // Calcul de la moyenne des ventes
    const { rows: [moyenneVentes] } = await pool.query(
      `SELECT COALESCE(AVG(montant_total), 0) as moyenne
       FROM ventes v
       JOIN produits p ON v.produit_id = p.id
       WHERE p.marchand_id = $1`,
      [req.user.id]
    );

    res.json({
      totalVentes: totalVentes.total,
      ventesParPeriode,
      ventesParProduit,
      moyenneVentes: moyenneVentes.moyenne
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des statistiques:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération des statistiques' });
  }
};

// Récupérer l'historique des ventes
exports.getVentes = async (req, res) => {
  try {
    const { rows: ventes } = await pool.query(
      `SELECT v.*,
              u.nom as client_nom,
              json_agg(json_build_object(
                'id', p.id,
                'nom', p.nom,
                'quantite', v.quantite,
                'prix_unitaire', v.prix_unitaire
              )) as produits
       FROM ventes v
       JOIN users u ON v.client_id = u.id
       JOIN produits p ON v.produit_id = p.id
       WHERE p.marchand_id = $1
       GROUP BY v.id, u.nom
       ORDER BY v.date_vente DESC`,
      [req.user.id]
    );

    res.json(ventes);
  } catch (err) {
    console.error('Erreur lors de la récupération des ventes:', err);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération des ventes' });
  }
};

// Tableau de bord et statistiques avancées
exports.getDashboardStats = async (req, res) => {
  const marchandId = req.user.id;

  try {
    // Obtenir les statistiques de base
    const [stats] = await sequelize.query(`
      WITH stats AS (
        SELECT
          COUNT(DISTINCT p.id) as total_products,
          COUNT(DISTINCT p.id) FILTER (WHERE p.created_at >= NOW() - INTERVAL '30 days') as new_products,
          COUNT(DISTINCT o.id) as total_orders,
          COUNT(DISTINCT o.id) FILTER (WHERE o.created_at >= NOW() - INTERVAL '30 days') as new_orders,
          COALESCE(SUM(o.total_amount), 0) as total_revenue,
          COALESCE(SUM(o.total_amount) FILTER (WHERE o.created_at >= NOW() - INTERVAL '30 days'), 0) as monthly_revenue,
          COUNT(DISTINCT o.id) FILTER (WHERE o.status = 'pending') as pending_orders,
          COUNT(DISTINCT p.id) FILTER (WHERE p.stock_quantity <= p.stock_alert_threshold) as low_stock_products
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON o.id = oi.order_id AND o.marchand_id = $1
        WHERE p.marchand_id = $1
      )
      SELECT *,
        ROUND(
          CASE
            WHEN total_orders > 0 THEN total_revenue / total_orders
            ELSE 0
          END, 2
        ) as average_order_value
      FROM stats
    `, {
      replacements: [marchandId],
      type: QueryTypes.SELECT
    });

    // Obtenir les commandes récentes avec détails
    const recentOrders = await Order.findAll({
      where: {
        marchand_id: marchandId
      },
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Product,
            as: 'product'
          }]
        },
        {
          model: User,
          as: 'client',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 10
    });

    // Obtenir les tendances de revenus
    const revenueTrends = await sequelize.query(`
      SELECT
        DATE_TRUNC('day', o.created_at) as date,
        COUNT(DISTINCT o.id) as order_count,
        COALESCE(SUM(o.total_amount), 0) as daily_revenue,
        COUNT(DISTINCT oi.id) as items_sold
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.marchand_id = $1
        AND o.created_at >= NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', o.created_at)
      ORDER BY date DESC
    `, {
      replacements: [marchandId],
      type: QueryTypes.SELECT
    });

    // Obtenir les meilleurs produits
    const topProducts = await sequelize.query(`
      SELECT
        p.id,
        p.name,
        p.stock_quantity,
        COUNT(DISTINCT o.id) as order_count,
        COALESCE(SUM(oi.quantity), 0) as total_quantity_sold,
        COALESCE(SUM(oi.total_price), 0) as total_revenue
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON o.id = oi.order_id
        AND o.created_at >= NOW() - INTERVAL '30 days'
      WHERE p.marchand_id = $1
      GROUP BY p.id, p.name, p.stock_quantity
      ORDER BY total_quantity_sold DESC
      LIMIT 5
    `, {
      replacements: [marchandId],
      type: QueryTypes.SELECT
    });

    // Obtenir les alertes d'inventaire
    const inventoryAlerts = await Product.findAll({
      where: {
        marchand_id: marchandId,
        stock_quantity: { [sequelize.Op.lte]: sequelize.col('stock_alert_threshold') }
      },
      attributes: [
        'id',
        'name',
        'stock_quantity',
        'stock_alert_threshold',
        [
          sequelize.literal('CASE WHEN stock_quantity = 0 THEN \'out_of_stock\' WHEN stock_quantity <= stock_alert_threshold THEN \'low_stock\' ELSE \'normal\' END'),
          'status'
        ]
      ],
      order: [['stock_quantity', 'ASC']],
      limit: 10
    });

    // Obtenir les recommandations AI
    const recommendations = await aiRecommendationService.getComprehensiveRecommendations(marchandId);

    res.json({
      status: 'success',
      data: {
        stats,
        recentOrders,
        revenueTrends,
        topProducts,
        inventoryAlerts,
        recommendations
      }
    });

  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving dashboard statistics'
    });
  }
};

exports.getDetailedAnalytics = async (req, res) => {
  const marchandId = req.user.id;
  const { startDate, endDate, groupBy = 'day' } = req.query;

  try {
    // Valider les dates
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    let dateGrouping;
    switch (groupBy) {
      case 'week':
        dateGrouping = 'week';
        break;
      case 'month':
        dateGrouping = 'month';
        break;
      case 'year':
        dateGrouping = 'year';
        break;
      default:
        dateGrouping = 'day';
    }

    // Obtenir les analyses de revenus
    const revenueQuery = `
      SELECT
        DATE_TRUNC('${dateGrouping}', o.created_at) as period,
        COUNT(DISTINCT o.id) as order_count,
        COUNT(DISTINCT o.client_id) as unique_customers,
        COALESCE(SUM(o.total_amount), 0) as revenue,
        COALESCE(SUM(oi.quantity), 0) as items_sold,
        COALESCE(AVG(o.total_amount), 0) as average_order_value,
        COALESCE(SUM(o.shipping_cost), 0) as shipping_revenue,
        COALESCE(SUM(o.tax_amount), 0) as tax_collected
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.marchand_id = $1
        AND o.created_at BETWEEN $2 AND $3
        AND o.status != 'cancelled'
      GROUP BY DATE_TRUNC('${dateGrouping}', o.created_at)
      ORDER BY period
    `;

    const revenueData = await sequelize.query(revenueQuery, {
      replacements: [marchandId, start, end],
      type: QueryTypes.SELECT
    });

    // Obtenir la performance des produits
    const productPerformance = await sequelize.query(`
      SELECT
        p.id,
        p.name,
        p.category,
        COUNT(DISTINCT o.id) as order_count,
        COALESCE(SUM(oi.quantity), 0) as quantity_sold,
        COALESCE(SUM(oi.total_price), 0) as revenue,
        COALESCE(AVG(oi.unit_price), 0) as average_price,
        COUNT(DISTINCT o.client_id) as unique_buyers,
        COALESCE(SUM(oi.return_quantity), 0) as returns,
        p.stock_quantity as current_stock
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON o.id = oi.order_id
        AND o.created_at BETWEEN $2 AND $3
      WHERE p.marchand_id = $1
      GROUP BY p.id, p.name, p.category, p.stock_quantity
      ORDER BY revenue DESC
    `, {
      replacements: [marchandId, start, end],
      type: QueryTypes.SELECT
    });

    // Obtenir les insights clients
    const customerInsights = await sequelize.query(`
      WITH customer_stats AS (
        SELECT
          o.client_id,
          u.name as customer_name,
          u.email,
          COUNT(DISTINCT o.id) as order_count,
          COALESCE(SUM(o.total_amount), 0) as total_spent,
          MIN(o.created_at) as first_order,
          MAX(o.created_at) as last_order,
          COUNT(DISTINCT oi.product_id) as unique_products_bought
        FROM orders o
        JOIN users u ON u.id = o.client_id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.marchand_id = $1
          AND o.created_at BETWEEN $2 AND $3
        GROUP BY o.client_id, u.name, u.email
      )
      SELECT
        *,
        total_spent / order_count as average_order_value,
        unique_products_bought / order_count as products_per_order,
        EXTRACT(EPOCH FROM (last_order - first_order)) / 86400 as days_between_orders
      FROM customer_stats
      ORDER BY total_spent DESC
    `, {
      replacements: [marchandId, start, end],
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: {
        period: { start, end, groupBy },
        revenue: revenueData,
        products: productPerformance,
        customers: customerInsights
      }
    });

  } catch (error) {
    console.error('Error getting detailed analytics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving detailed analytics'
    });
  }
};

exports.getAIRecommendations = async (req, res) => {
  const marchandId = req.user.id;

  try {
    const recommendations = await aiRecommendationService.getComprehensiveRecommendations(marchandId);

    res.json({
      status: 'success',
      data: recommendations
    });

  } catch (error) {
    console.error('Error getting AI recommendations:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error generating AI recommendations'
    });
  }
};
