/**
 * Service d'intégration IoT pour dispositifs de ferme
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
const WebSocket = require('ws');
const mqtt = require('mqtt');

class IoTIntegrationService {
  constructor() {
    this.pool = new Pool();
    this.devices = new Map();
    this.mqttClient = null;
    this.wsServer = null;
    this.alertThresholds = new Map();
    this.deviceStatus = new Map();
    
    this.initializeMQTT();
    this.initializeWebSocket();
    this.loadDeviceThresholds();
  }

  /**
   * Initialiser la connexion MQTT
   */
  initializeMQTT() {
    try {
      const mqttUrl = process.env.MQTT_BROKER_URL || 'mqtt://localhost:1883';
      this.mqttClient = mqtt.connect(mqttUrl, {
        clientId: 'poultray_dz_server',
        username: process.env.MQTT_USERNAME,
        password: process.env.MQTT_PASSWORD
      });

      this.mqttClient.on('connect', () => {
        logger.info('Connexion MQTT établie');
        this.subscribeToDeviceTopics();
      });

      this.mqttClient.on('message', (topic, message) => {
        this.handleMQTTMessage(topic, message);
      });

      this.mqttClient.on('error', (error) => {
        logger.error('Erreur MQTT:', error);
      });

    } catch (error) {
      logger.error('Erreur lors de l\'initialisation MQTT:', error);
    }
  }

  /**
   * Initialiser le serveur WebSocket
   */
  initializeWebSocket() {
    try {
      const port = process.env.IOT_WS_PORT || 8080;
      this.wsServer = new WebSocket.Server({ port });

      this.wsServer.on('connection', (ws, req) => {
        logger.info('Nouvelle connexion WebSocket IoT');
        
        ws.on('message', (message) => {
          this.handleWebSocketMessage(ws, message);
        });

        ws.on('close', () => {
          logger.info('Connexion WebSocket IoT fermée');
        });
      });

      logger.info(`Serveur WebSocket IoT démarré sur le port ${port}`);

    } catch (error) {
      logger.error('Erreur lors de l\'initialisation WebSocket:', error);
    }
  }

  /**
   * S'abonner aux topics MQTT des dispositifs
   */
  subscribeToDeviceTopics() {
    const topics = [
      'poultray/+/temperature',
      'poultray/+/humidity',
      'poultray/+/feed_level',
      'poultray/+/water_level',
      'poultray/+/door_status',
      'poultray/+/light_status',
      'poultray/+/ventilation',
      'poultray/+/motion',
      'poultray/+/sound_level',
      'poultray/+/air_quality'
    ];

    topics.forEach(topic => {
      this.mqttClient.subscribe(topic, (err) => {
        if (err) {
          logger.error(`Erreur lors de l'abonnement au topic ${topic}:`, err);
        } else {
          logger.info(`Abonné au topic: ${topic}`);
        }
      });
    });
  }

  /**
   * Traiter les messages MQTT
   */
  async handleMQTTMessage(topic, message) {
    try {
      const topicParts = topic.split('/');
      const deviceId = topicParts[1];
      const sensorType = topicParts[2];
      
      let data;
      try {
        data = JSON.parse(message.toString());
      } catch (e) {
        // Si ce n'est pas du JSON, traiter comme valeur simple
        data = { value: parseFloat(message.toString()) || message.toString() };
      }

      const deviceData = {
        deviceId: deviceId,
        sensorType: sensorType,
        value: data.value,
        unit: data.unit || this.getDefaultUnit(sensorType),
        timestamp: data.timestamp || new Date().toISOString(),
        metadata: data.metadata || {}
      };

      // Sauvegarder les données
      await this.saveDeviceData(deviceData);
      
      // Vérifier les seuils d'alerte
      await this.checkAlertThresholds(deviceData);
      
      // Mettre à jour le statut du dispositif
      this.updateDeviceStatus(deviceId, sensorType, deviceData);
      
      // Diffuser aux clients WebSocket
      this.broadcastToWebSocketClients(deviceData);

    } catch (error) {
      logger.error('Erreur lors du traitement du message MQTT:', error);
    }
  }

  /**
   * Traiter les messages WebSocket
   */
  async handleWebSocketMessage(ws, message) {
    try {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case 'device_data':
          await this.handleDeviceDataFromWebSocket(data);
          break;
        case 'device_command':
          await this.handleDeviceCommand(data);
          break;
        case 'register_device':
          await this.registerDevice(data.device);
          break;
        default:
          logger.warn('Type de message WebSocket inconnu:', data.type);
      }

    } catch (error) {
      logger.error('Erreur lors du traitement du message WebSocket:', error);
    }
  }

  /**
   * Traiter les données de dispositif via WebSocket
   */
  async handleDeviceDataFromWebSocket(data) {
    const deviceData = {
      deviceId: data.deviceId,
      sensorType: data.sensorType,
      value: data.value,
      unit: data.unit || this.getDefaultUnit(data.sensorType),
      timestamp: data.timestamp || new Date().toISOString(),
      metadata: data.metadata || {}
    };

    await this.saveDeviceData(deviceData);
    await this.checkAlertThresholds(deviceData);
    this.updateDeviceStatus(data.deviceId, data.sensorType, deviceData);
  }

  /**
   * Sauvegarder les données de dispositif
   */
  async saveDeviceData(deviceData) {
    try {
      await this.pool.query(`
        INSERT INTO iot_device_logs (
          device_id, sensor_type, value, unit, farm_id, metadata, received_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        deviceData.deviceId,
        deviceData.sensorType,
        deviceData.value,
        deviceData.unit,
        await this.getFarmIdForDevice(deviceData.deviceId),
        JSON.stringify(deviceData.metadata),
        deviceData.timestamp
      ]);

    } catch (error) {
      logger.error('Erreur lors de la sauvegarde des données IoT:', error);
    }
  }

  /**
   * Obtenir l'ID de ferme pour un dispositif
   */
  async getFarmIdForDevice(deviceId) {
    try {
      const result = await this.pool.query(`
        SELECT farm_id FROM iot_devices WHERE device_id = $1
      `, [deviceId]);

      return result.rows.length > 0 ? result.rows[0].farm_id : null;
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'ID de ferme:', error);
      return null;
    }
  }

  /**
   * Vérifier les seuils d'alerte
   */
  async checkAlertThresholds(deviceData) {
    try {
      const thresholds = this.alertThresholds.get(deviceData.sensorType);
      if (!thresholds) return;

      const value = parseFloat(deviceData.value);
      const alerts = [];

      if (thresholds.min !== undefined && value < thresholds.min) {
        alerts.push({
          type: 'threshold_low',
          severity: 'warning',
          message: `${deviceData.sensorType} trop bas: ${value} ${deviceData.unit} (seuil: ${thresholds.min})`
        });
      }

      if (thresholds.max !== undefined && value > thresholds.max) {
        alerts.push({
          type: 'threshold_high',
          severity: 'warning',
          message: `${deviceData.sensorType} trop élevé: ${value} ${deviceData.unit} (seuil: ${thresholds.max})`
        });
      }

      if (thresholds.critical_min !== undefined && value < thresholds.critical_min) {
        alerts.push({
          type: 'threshold_critical_low',
          severity: 'critical',
          message: `${deviceData.sensorType} critique: ${value} ${deviceData.unit} (seuil critique: ${thresholds.critical_min})`
        });
      }

      if (thresholds.critical_max !== undefined && value > thresholds.critical_max) {
        alerts.push({
          type: 'threshold_critical_high',
          severity: 'critical',
          message: `${deviceData.sensorType} critique: ${value} ${deviceData.unit} (seuil critique: ${thresholds.critical_max})`
        });
      }

      // Envoyer les alertes
      for (const alert of alerts) {
        await this.sendAlert(deviceData, alert);
      }

    } catch (error) {
      logger.error('Erreur lors de la vérification des seuils:', error);
    }
  }

  /**
   * Envoyer une alerte
   */
  async sendAlert(deviceData, alert) {
    try {
      const farmId = await this.getFarmIdForDevice(deviceData.deviceId);
      
      await this.pool.query(`
        INSERT INTO iot_alerts (
          device_id, farm_id, sensor_type, alert_type, severity, message, 
          trigger_value, threshold_value, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      `, [
        deviceData.deviceId,
        farmId,
        deviceData.sensorType,
        alert.type,
        alert.severity,
        alert.message,
        deviceData.value,
        this.getThresholdValue(deviceData.sensorType, alert.type)
      ]);

      // Diffuser l'alerte via WebSocket
      this.broadcastAlert(alert, deviceData);

    } catch (error) {
      logger.error('Erreur lors de l\'envoi d\'alerte:', error);
    }
  }

  /**
   * Mettre à jour le statut du dispositif
   */
  updateDeviceStatus(deviceId, sensorType, data) {
    const key = `${deviceId}_${sensorType}`;
    this.deviceStatus.set(key, {
      lastUpdate: new Date(),
      value: data.value,
      unit: data.unit,
      status: 'online'
    });

    // Marquer comme hors ligne si pas de données depuis 5 minutes
    setTimeout(() => {
      const current = this.deviceStatus.get(key);
      if (current && new Date() - current.lastUpdate > 5 * 60 * 1000) {
        current.status = 'offline';
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Diffuser aux clients WebSocket
   */
  broadcastToWebSocketClients(data) {
    if (!this.wsServer) return;

    const message = JSON.stringify({
      type: 'device_data',
      data: data,
      timestamp: new Date().toISOString()
    });

    this.wsServer.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  /**
   * Diffuser une alerte
   */
  broadcastAlert(alert, deviceData) {
    if (!this.wsServer) return;

    const message = JSON.stringify({
      type: 'alert',
      alert: alert,
      device: deviceData,
      timestamp: new Date().toISOString()
    });

    this.wsServer.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  /**
   * Envoyer une commande à un dispositif
   */
  async sendDeviceCommand(deviceId, command, parameters = {}) {
    try {
      const topic = `poultray/${deviceId}/command`;
      const message = JSON.stringify({
        command: command,
        parameters: parameters,
        timestamp: new Date().toISOString()
      });

      if (this.mqttClient && this.mqttClient.connected) {
        this.mqttClient.publish(topic, message);
        
        // Enregistrer la commande
        await this.logDeviceCommand(deviceId, command, parameters);
        
        return { success: true, message: 'Commande envoyée' };
      } else {
        throw new Error('Client MQTT non connecté');
      }

    } catch (error) {
      logger.error('Erreur lors de l\'envoi de commande:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Enregistrer une commande de dispositif
   */
  async logDeviceCommand(deviceId, command, parameters) {
    try {
      await this.pool.query(`
        INSERT INTO iot_device_commands (
          device_id, command, parameters, sent_at
        ) VALUES ($1, $2, $3, NOW())
      `, [deviceId, command, JSON.stringify(parameters)]);

    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement de la commande:', error);
    }
  }

  /**
   * Enregistrer un nouveau dispositif
   */
  async registerDevice(deviceInfo) {
    try {
      await this.pool.query(`
        INSERT INTO iot_devices (
          device_id, device_name, device_type, farm_id, location, 
          configuration, registered_at
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        ON CONFLICT (device_id) DO UPDATE SET
          device_name = EXCLUDED.device_name,
          device_type = EXCLUDED.device_type,
          location = EXCLUDED.location,
          configuration = EXCLUDED.configuration
      `, [
        deviceInfo.deviceId,
        deviceInfo.name,
        deviceInfo.type,
        deviceInfo.farmId,
        deviceInfo.location,
        JSON.stringify(deviceInfo.configuration || {})
      ]);

      logger.info(`Dispositif enregistré: ${deviceInfo.deviceId}`);
      return { success: true };

    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement du dispositif:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Obtenir les données récentes d'un dispositif
   */
  async getDeviceData(deviceId, sensorType = null, hours = 24) {
    try {
      let query = `
        SELECT * FROM iot_device_logs 
        WHERE device_id = $1 
          AND received_at >= NOW() - INTERVAL '${hours} hours'
      `;
      const params = [deviceId];

      if (sensorType) {
        query += ' AND sensor_type = $2';
        params.push(sensorType);
      }

      query += ' ORDER BY received_at DESC';

      const result = await this.pool.query(query, params);
      
      return result.rows.map(row => ({
        ...row,
        metadata: typeof row.metadata === 'string' ? 
          JSON.parse(row.metadata) : row.metadata
      }));

    } catch (error) {
      logger.error('Erreur lors de la récupération des données:', error);
      return [];
    }
  }

  /**
   * Obtenir le statut de tous les dispositifs
   */
  getDevicesStatus() {
    const status = {};
    
    this.deviceStatus.forEach((data, key) => {
      const [deviceId, sensorType] = key.split('_');
      
      if (!status[deviceId]) {
        status[deviceId] = {};
      }
      
      status[deviceId][sensorType] = data;
    });

    return status;
  }

  /**
   * Charger les seuils d'alerte depuis la configuration
   */
  async loadDeviceThresholds() {
    // Seuils par défaut
    this.alertThresholds.set('temperature', {
      min: 15,
      max: 30,
      critical_min: 5,
      critical_max: 40
    });

    this.alertThresholds.set('humidity', {
      min: 40,
      max: 80,
      critical_min: 20,
      critical_max: 95
    });

    this.alertThresholds.set('feed_level', {
      min: 20,
      critical_min: 5
    });

    this.alertThresholds.set('water_level', {
      min: 30,
      critical_min: 10
    });

    this.alertThresholds.set('air_quality', {
      max: 500,
      critical_max: 1000
    });

    // Charger les seuils personnalisés depuis la base
    try {
      const result = await this.pool.query(`
        SELECT sensor_type, thresholds FROM iot_device_thresholds
      `);

      result.rows.forEach(row => {
        const thresholds = typeof row.thresholds === 'string' ? 
          JSON.parse(row.thresholds) : row.thresholds;
        this.alertThresholds.set(row.sensor_type, thresholds);
      });

    } catch (error) {
      logger.error('Erreur lors du chargement des seuils:', error);
    }
  }

  /**
   * Obtenir l'unité par défaut pour un type de capteur
   */
  getDefaultUnit(sensorType) {
    const units = {
      'temperature': '°C',
      'humidity': '%',
      'feed_level': '%',
      'water_level': '%',
      'sound_level': 'dB',
      'air_quality': 'ppm',
      'light_level': 'lux',
      'motion': 'boolean',
      'door_status': 'boolean'
    };

    return units[sensorType] || '';
  }

  /**
   * Obtenir la valeur de seuil pour un type d'alerte
   */
  getThresholdValue(sensorType, alertType) {
    const thresholds = this.alertThresholds.get(sensorType);
    if (!thresholds) return null;

    switch (alertType) {
      case 'threshold_low': return thresholds.min;
      case 'threshold_high': return thresholds.max;
      case 'threshold_critical_low': return thresholds.critical_min;
      case 'threshold_critical_high': return thresholds.critical_max;
      default: return null;
    }
  }

  /**
   * Traiter une commande de dispositif
   */
  async handleDeviceCommand(data) {
    const { deviceId, command, parameters } = data;
    
    switch (command) {
      case 'set_temperature':
        return await this.sendDeviceCommand(deviceId, 'set_temperature', {
          target: parameters.temperature
        });
        
      case 'toggle_light':
        return await this.sendDeviceCommand(deviceId, 'toggle_light', {
          state: parameters.state
        });
        
      case 'open_door':
        return await this.sendDeviceCommand(deviceId, 'open_door');
        
      case 'close_door':
        return await this.sendDeviceCommand(deviceId, 'close_door');
        
      case 'start_ventilation':
        return await this.sendDeviceCommand(deviceId, 'start_ventilation', {
          speed: parameters.speed || 'medium'
        });
        
      case 'stop_ventilation':
        return await this.sendDeviceCommand(deviceId, 'stop_ventilation');
        
      default:
        return { success: false, error: 'Commande inconnue' };
    }
  }

  /**
   * Obtenir les alertes récentes
   */
  async getRecentAlerts(farmId = null, hours = 24) {
    try {
      let query = `
        SELECT * FROM iot_alerts 
        WHERE created_at >= NOW() - INTERVAL '${hours} hours'
      `;
      const params = [];

      if (farmId) {
        query += ' AND farm_id = $1';
        params.push(farmId);
      }

      query += ' ORDER BY created_at DESC';

      const result = await this.pool.query(query, params);
      return result.rows;

    } catch (error) {
      logger.error('Erreur lors de la récupération des alertes:', error);
      return [];
    }
  }
}

module.exports = new IoTIntegrationService();
