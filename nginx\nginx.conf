# Configuration Nginx optimisée pour Poultray DZ
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimisations des événements
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Types MIME
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Format des logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # Format des logs JSON pour monitoring
    log_format json_combined escape=json
        '{'
        '"time_local":"$time_local",'
        '"remote_addr":"$remote_addr",'
        '"remote_user":"$remote_user",'
        '"request":"$request",'
        '"status": "$status",'
        '"body_bytes_sent":"$body_bytes_sent",'
        '"request_time":"$request_time",'
        '"http_referrer":"$http_referer",'
        '"http_user_agent":"$http_user_agent",'
        '"upstream_connect_time":"$upstream_connect_time",'
        '"upstream_header_time":"$upstream_header_time",'
        '"upstream_response_time":"$upstream_response_time"'
        '}';

    access_log /var/log/nginx/access.log json_combined;

    # Optimisations générales
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Tailles des buffers
    client_body_buffer_size 128k;
    client_max_body_size 50m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Timeouts
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;

    # Compression Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;
    gzip_disable "MSIE [1-6]\.";

    # Configuration SSL/TLS
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Headers de sécurité
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Configuration du cache
    map $sent_http_content_type $expires {
        "text/html"                 epoch;
        "text/html; charset=utf-8"  epoch;
        default                     off;
        "text/css"                  max;
        "application/javascript"    max;
        "~image/"                   max;
    }

    # Limitation du taux de requêtes
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

    # Configuration upstream pour le backend
    upstream backend {
        server backend:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Serveur principal - HTTPS
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name app.poultraydz.com;

        # Certificats SSL
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;

        # Configuration des expires
        expires $expires;

        # Racine du site
        root /usr/share/nginx/html;
        index index.html;

        # Configuration pour React Router
        location / {
            try_files $uri $uri/ /index.html;
            
            # Headers pour les PWA
            location ~* \.(?:manifest|appcache|html?|xml|json)$ {
                add_header Cache-Control "max-age=0";
            }

            # Cache pour les assets statiques
            location ~* \.(?:css|js|woff2?|eot|ttf|otf|svg|ico|png|jpg|jpeg|gif|webp)$ {
                add_header Cache-Control "public, max-age=31536000, immutable";
                access_log off;
            }
        }

        # Proxy vers l'API backend
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Headers de sécurité pour l'API
            add_header X-Content-Type-Options nosniff;
            add_header X-Frame-Options DENY;
        }

        # WebSocket pour les notifications temps réel
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Configuration WebSocket
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            proxy_connect_timeout 5s;
        }

        # Endpoint de santé
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Métriques Nginx pour Prometheus
        location /nginx_status {
            stub_status on;
            access_log off;
            allow **********/16;  # Réseau Docker
            deny all;
        }

        # Protection contre les attaques
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Limitation stricte pour les endpoints sensibles
        location ~ ^/api/(auth|admin) {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Gestion des erreurs
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # Redirection HTTP vers HTTPS
    server {
        listen 80;
        listen [::]:80;
        server_name app.poultraydz.com;
        
        # Redirection permanente vers HTTPS
        return 301 https://$server_name$request_uri;
    }

    # Serveur API dédié
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name api.poultraydz.com;

        # Certificats SSL
        ssl_certificate /etc/nginx/ssl/api-fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/api-privkey.pem;

        # Limitation générale
        limit_req zone=general burst=50 nodelay;

        # Proxy vers le backend
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS pour l'API
            add_header Access-Control-Allow-Origin "https://app.poultraydz.com" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            
            # Répondre aux requêtes OPTIONS
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "https://app.poultraydz.com";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type "text/plain charset=UTF-8";
                add_header Content-Length 0;
                return 204;
            }
        }

        # WebSocket
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }
    }

    # Serveur de monitoring (accès restreint)
    server {
        listen 8080;
        server_name monitoring.poultraydz.com;
        
        # Restriction d'accès
        allow **********/16;  # Réseau Docker
        allow 10.0.0.0/8;     # Réseau privé
        deny all;

        # Métriques et statuts
        location /nginx_status {
            stub_status on;
            access_log off;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
