.dashboard-container {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.dashboard-title {
  color: #1F2937;
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #3B82F6;
  padding-bottom: 0.5rem;
}

.dashboard-paper {
  padding: 1.5rem;
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.dashboard-paper:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Styles pour les cartes de statistiques */
.stat-card {
  background-color: #F3F4F6;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.stat-card-title {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 0.5rem;
}

.stat-card-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1F2937;
}

.stat-card-trend {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.trend-up {
  color: #10B981;
}

.trend-down {
  color: #EF4444;
}

/* Styles pour les graphiques */
.chart-container {
  width: 100%;
  height: 300px;
  margin-top: 1rem;
}

/* Styles pour les alertes */
.alert-item {
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.alert-critical {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #EF4444;
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid #F59E0B;
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3B82F6;
}

/* Styles pour l'activité récente */
.activity-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: flex-start;
}

.activity-icon {
  margin-right: 0.75rem;
  color: #3B82F6;
}

.activity-content {
  flex: 1;
}

.activity-time {
  font-size: 0.75rem;
  color: #6B7280;
}

/* Styles responsifs */
@media (max-width: 768px) {
  .dashboard-paper {
    padding: 1rem;
  }

  .chart-container {
    height: 250px;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .chart-container {
    height: 200px;
  }

  .stat-card-value {
    font-size: 1.25rem;
  }
}