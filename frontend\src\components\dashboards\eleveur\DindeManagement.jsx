import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { useTranslation } from 'react-i18next';

function DindeManagement() {
  const { t } = useTranslation();
  const [dindes, setDindes] = useState([]);
  const [cycleData, setCycleData] = useState({}); // { dindeId: { phase: string, duree: number } }
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const phases = [
    'démarrage',
    'croissance',
    'finition'
  ];

  useEffect(() => {
    const fetchDindes = async () => {
      try {
        setLoading(true);
        // const response = await api.get("/api/eleveur/dindes");
        // setDindes(response.data);
        setDindes([
          { id: 1, nom: 'Lot Dindes 1', race: 'BUT Big 6', age: 45, poids_actuel: 4.2, phase_actuelle: 'croissance' },
          { id: 2, nom: 'Lot Dindes 2', race: '<PERSON> Select', age: 30, poids_actuel: 2.8, phase_actuelle: 'démarrage' }
        ]); // Données de test
        setError(null);
      } catch (err) {
        console.error("Erreur chargement dindes:", err);
        setError("Impossible de charger les données des dindes.");
        setDindes([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDindes();
  }, []);

  const handleCycleUpdate = async (dindeId, phase, duree) => {
    try {
      // Simulated API call to update cycle
      // await api.post(`/api/eleveur/dindes/${dindeId}/cycle`, { phase, duree });
      
      // Update local state
      setCycleData(prev => ({
        ...prev,
        [dindeId]: { phase, duree }
      }));
    } catch (err) {
      console.error("Erreur mise à jour cycle:", err);
      setError("Impossible de mettre à jour le cycle.");
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">{error}</Alert>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h5" gutterBottom>
        {t('Gestion des Dindes')}
      </Typography>
      
      <Grid container spacing={3}>
        {dindes.map((dinde) => (
          <Grid item xs={12} md={6} key={dinde.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{dinde.nom}</Typography>
                <Typography color="textSecondary" gutterBottom>
                  {t('Race')}: {dinde.race}
                </Typography>
                <Typography>
                  {t('Âge')}: {dinde.age} {t('jours')}
                </Typography>
                <Typography>
                  {t('Poids actuel')}: {dinde.poids_actuel} kg
                </Typography>
                <Typography>
                  {t('Phase actuelle')}: {dinde.phase_actuelle}
                </Typography>
                
                <Box mt={2}>
                  <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                    <InputLabel>{t('Nouvelle phase')}</InputLabel>
                    <Select
                      value={cycleData[dinde.id]?.phase || ''}
                      label={t('Nouvelle phase')}
                      onChange={(e) => handleCycleUpdate(dinde.id, e.target.value, cycleData[dinde.id]?.duree || 0)}
                    >
                      {phases.map((phase) => (
                        <MenuItem key={phase} value={phase}>
                          {t(phase)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    label={t('Durée (jours)')}
                    type="number"
                    size="small"
                    value={cycleData[dinde.id]?.duree || ''}
                    onChange={(e) => handleCycleUpdate(dinde.id, cycleData[dinde.id]?.phase || phases[0], parseInt(e.target.value))}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

export default DindeManagement;