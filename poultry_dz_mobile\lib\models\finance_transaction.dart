import 'package:json_annotation/json_annotation.dart';

// part 'finance_transaction.g.dart'; // Temporarily commented out

@JsonSerializable()
class FinanceTransaction {
  final int? id;
  final String type; // 'income' or 'expense'
  final String category;
  final double amount;
  final String description;
  @J<PERSON><PERSON><PERSON>(name: 'farm_id')
  final int farmId;
  @Json<PERSON>ey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'transaction_date')
  final DateTime transactionDate;
  @<PERSON>son<PERSON>ey(name: 'payment_method')
  final String? paymentMethod; // cash, bank_transfer, check, card
  final String? reference; // invoice number, receipt number, etc.
  final Map<String, dynamic>? metadata; // additional data like photos, notes
  final String status; // pending, completed, cancelled
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'synced_at')
  final DateTime? syncedAt;
  @<PERSON><PERSON><PERSON>ey(name: 'is_synced')
  final bool isSynced;

  const FinanceTransaction({
    this.id,
    required this.type,
    required this.category,
    required this.amount,
    required this.description,
    required this.farmId,
    required this.userId,
    required this.transactionDate,
    this.paymentMethod,
    this.reference,
    this.metadata,
    this.status = 'completed',
    required this.createdAt,
    required this.updatedAt,
    this.syncedAt,
    this.isSynced = false,
  });

  factory FinanceTransaction.fromJson(Map<String, dynamic> json) =>
      _$FinanceTransactionFromJson(json);
  Map<String, dynamic> toJson() => _$FinanceTransactionToJson(this);

  // Helper getters
  bool get isIncome => type == 'income';
  bool get isExpense => type == 'expense';
  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get needsSync => !isSynced;

  // Create a copy with updated fields
  FinanceTransaction copyWith({
    int? id,
    String? type,
    String? category,
    double? amount,
    String? description,
    int? farmId,
    int? userId,
    DateTime? transactionDate,
    String? paymentMethod,
    String? reference,
    Map<String, dynamic>? metadata,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? syncedAt,
    bool? isSynced,
  }) {
    return FinanceTransaction(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      farmId: farmId ?? this.farmId,
      userId: userId ?? this.userId,
      transactionDate: transactionDate ?? this.transactionDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      reference: reference ?? this.reference,
      metadata: metadata ?? this.metadata,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Mark as synced
  FinanceTransaction markAsSynced() {
    return copyWith(
      isSynced: true,
      syncedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'FinanceTransaction(id: $id, type: $type, category: $category, amount: $amount, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FinanceTransaction &&
        other.id == id &&
        other.type == type &&
        other.category == category &&
        other.amount == amount &&
        other.farmId == farmId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        category.hashCode ^
        amount.hashCode ^
        farmId.hashCode;
  }
}

// Transaction categories enum for type safety
class TransactionCategories {
  // Expense categories
  static const String feed = 'feed';
  static const String medical = 'medical';
  static const String utilities = 'utilities';
  static const String salaries = 'salaries';
  static const String equipment = 'equipment';
  static const String maintenance = 'maintenance';
  static const String transport = 'transport';
  static const String insurance = 'insurance';
  static const String taxes = 'taxes';
  static const String other_expense = 'other_expense';

  // Income categories
  static const String sales = 'sales';
  static const String subsidies = 'subsidies';
  static const String services = 'services';
  static const String other_income = 'other_income';

  static const List<String> expenseCategories = [
    feed,
    medical,
    utilities,
    salaries,
    equipment,
    maintenance,
    transport,
    insurance,
    taxes,
    other_expense,
  ];

  static const List<String> incomeCategories = [
    sales,
    subsidies,
    services,
    other_income,
  ];

  static const List<String> allCategories = [
    ...expenseCategories,
    ...incomeCategories,
  ];

  static bool isExpenseCategory(String category) {
    return expenseCategories.contains(category);
  }

  static bool isIncomeCategory(String category) {
    return incomeCategories.contains(category);
  }
}

// Payment methods enum
class PaymentMethods {
  static const String cash = 'cash';
  static const String bankTransfer = 'bank_transfer';
  static const String check = 'check';
  static const String card = 'card';
  static const String mobile = 'mobile';
  static const String other = 'other';

  static const List<String> all = [
    cash,
    bankTransfer,
    check,
    card,
    mobile,
    other,
  ];
}

// Transaction status enum
class TransactionStatus {
  static const String pending = 'pending';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';

  static const List<String> all = [
    pending,
    completed,
    cancelled,
  ];
}