/**
 * Configuration de la base de données PostgreSQL pour Poultray DZ
 * Ce fichier contient les paramètres de connexion à la base de données
 */

// Configuration de l'environnement
const ENV = import.meta.env.MODE || 'development';

// Configuration des connexions à la base de données selon l'environnement
const DB_CONFIG = {
  development: {
    host: import.meta.env.VITE_DB_HOST || 'localhost',
    port: import.meta.env.VITE_DB_PORT || 5432,
    database: import.meta.env.VITE_DB_NAME || 'poultray_dz',
    user: import.meta.env.VITE_DB_USER || 'postgres',
    password: import.meta.env.VITE_DB_PASSWORD || 'root',
    ssl: false
  },
  test: {
    host: import.meta.env.VITE_DB_HOST || 'localhost',
    port: import.meta.env.VITE_DB_PORT || 5432,
    database: import.meta.env.VITE_DB_NAME || 'poultray_dz_test',
    user: import.meta.env.VITE_DB_USER || 'postgres',
    password: import.meta.env.VITE_DB_PASSWORD || 'postgres',
    ssl: false
  },
  production: {
    host: import.meta.env.VITE_DB_HOST,
    port: import.meta.env.VITE_DB_PORT || 5432,
    database: import.meta.env.VITE_DB_NAME,
    user: import.meta.env.VITE_DB_USER,
    password: import.meta.env.VITE_DB_PASSWORD,
    ssl: true
  }
};

// Exporter la configuration pour l'environnement actuel
export default DB_CONFIG[ENV];

// Exporter les constantes pour les noms de tables
export const TABLES = {
  USERS: 'users',
  ELEVEURS: 'eleveurs',
  VETERINAIRES: 'veterinaires',
  MARCHANDS: 'marchands',
  VOLAILLES: 'volailles',
  VENTES: 'ventes',
  PRESCRIPTIONS: 'prescriptions',
  CONSULTATIONS: 'consultations',
  PRODUITS: 'produits',
  COMMANDES: 'commandes',
  BLOG_POSTS: 'blog_posts',
  TRANSLATIONS: 'translations',
  NOTIFICATIONS: 'notifications',
  SETTINGS: 'settings'
};

// Exporter les constantes pour les relations entre tables
export const RELATIONS = {
  USER_ELEVEUR: 'user_id',
  USER_VETERINAIRE: 'user_id',
  USER_MARCHAND: 'user_id',
  ELEVEUR_VOLAILLE: 'eleveur_id',
  VETERINAIRE_PRESCRIPTION: 'veterinaire_id',
  MARCHAND_PRODUIT: 'marchand_id'
};
