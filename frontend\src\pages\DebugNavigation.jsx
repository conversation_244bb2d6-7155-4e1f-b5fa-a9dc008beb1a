import React from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  Alert,
  Divider
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { Home, BugReport } from '@mui/icons-material';
import ErrorTestPanel from '../components/debug/ErrorTestPanel';
import NetworkStatus from '../components/network/NetworkStatus';
import PerformanceMonitor from '../components/performance/PerformanceMonitor';
import { useAuth } from '../contexts/AuthContext';

/**
 * Page de debug pour tester les fonctionnalités en développement
 */
const DebugNavigation = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  // Rediriger en production
  if (process.env.NODE_ENV === 'production') {
    navigate('/');
    return null;
  }

  const handleNavigateHome = () => {
    if (user) {
      // Rediriger vers le dashboard approprié selon le rôle
      switch (user.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'eleveur':
          navigate('/eleveur/dashboard');
          break;
        case 'veterinaire':
          navigate('/veterinaire/dashboard');
          break;
        case 'marchand':
          navigate('/marchand/dashboard');
          break;
        default:
          navigate('/dashboard');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Typography variant="h4" component="h1">
            🐛 Debug Navigation
          </Typography>

          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Home />}
              onClick={handleNavigateHome}
            >
              Retour à l'accueil
            </Button>

            {user && (
              <Button
                variant="outlined"
                color="secondary"
                onClick={logout}
              >
                Déconnexion
              </Button>
            )}
          </Box>
        </Box>

        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body1">
            Cette page est uniquement disponible en mode développement pour tester les fonctionnalités.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          {/* Informations utilisateur */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                👤 Informations Utilisateur
              </Typography>

              {user ? (
                <Box>
                  <Typography variant="body2">
                    <strong>Email:</strong> {user.email}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Rôle:</strong> {user.role}
                  </Typography>
                  <Typography variant="body2">
                    <strong>ID:</strong> {user.id}
                  </Typography>
                  {user.username && (
                    <Typography variant="body2">
                      <strong>Username:</strong> {user.username}
                    </Typography>
                  )}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Aucun utilisateur connecté
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* État du réseau */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                🌐 État du Réseau
              </Typography>
              <NetworkStatus variant="chip" showLatency={true} />
            </Paper>
          </Grid>

          {/* Navigation rapide */}
          <Grid item xs={12}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                🚀 Navigation Rapide
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => navigate('/login')}
                  >
                    Connexion
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => navigate('/register')}
                  >
                    Inscription
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => navigate('/admin/dashboard')}
                  >
                    Admin Dashboard
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => navigate('/eleveur/dashboard')}
                  >
                    Éleveur Dashboard
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Moniteur de performance */}
          <Grid item xs={12}>
            <PerformanceMonitor variant="dashboard" />
          </Grid>

          {/* Tests d'erreurs */}
          <Grid item xs={12}>
            <ErrorTestPanel />
          </Grid>

          {/* Informations techniques */}
          <Grid item xs={12}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                ⚙️ Informations Techniques
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2">
                    <strong>Mode:</strong> {process.env.NODE_ENV}
                  </Typography>
                  <Typography variant="body2">
                    <strong>User Agent:</strong> {navigator.userAgent}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Langue:</strong> {navigator.language}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="body2">
                    <strong>En ligne:</strong> {navigator.onLine ? 'Oui' : 'Non'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Cookies activés:</strong> {navigator.cookieEnabled ? 'Oui' : 'Non'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Timezone:</strong> {Intl.DateTimeFormat().resolvedOptions().timeZone}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default DebugNavigation;
