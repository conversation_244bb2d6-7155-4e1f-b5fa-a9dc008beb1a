require('dotenv').config();
const { User, Role, sequelize } = require('../models');

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    await sequelize.authenticate();
    console.log('Connexion à la base de données établie avec succès.');
    return true;
  } catch (error) {
    console.error('Impossible de se connecter à la base de données:', error);
    return false;
  }
}

async function createAdminUser() {
  try {
    // Vérifier la connexion à la base de données
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      console.error('Échec de la connexion à la base de données. Arrêt du script.');
      process.exit(1);
    }

    console.log('Début de la création/mise à jour de l\'utilisateur admin...');
    // Vérifier si l'utilisateur admin existe déjà
    const existingUser = await User.findOne({ where: { email: '<EMAIL>' } });

    // Récupérer le rôle admin dans tous les cas
    const adminRole = await Role.findOne({ where: { name: 'admin' } });
    if (!adminRole) {
      console.error('Le rôle admin n\'existe pas. Veuillez d\'abord exécuter initRoles.js');
      process.exit(1);
    }

    if (existingUser) {
      console.log('Utilisateur admin existe déjà. Mise à jour des informations...');
      // Mettre à jour le mot de passe et le rôle
      await existingUser.update({
        password: 'admin123',
        role_id: adminRole.id
      });
      console.log('Informations de l\'administrateur mises à jour avec succès');
      return;
    }

    // Créer un utilisateur administrateur par défaut
    const adminUser = {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role_id: adminRole.id,
      first_name: 'Admin',
      last_name: 'Poultray',
      is_active: true,
      status: 'active'
    };

    console.log('Création de l\'utilisateur administrateur...');
    const user = await User.create(adminUser);
    console.log('Utilisateur administrateur créé avec succès:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });

    process.exit(0);
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur administrateur:', error);
    process.exit(1);
  }
}

createAdminUser();
