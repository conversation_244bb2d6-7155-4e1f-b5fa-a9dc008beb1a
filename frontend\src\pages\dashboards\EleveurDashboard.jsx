import React, { useState, useEffect } from 'react';
import { <PERSON>b<PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ist, TabPanel } from '@mui/lab';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Fade,
  Zoom,
  useTheme,
  useMediaQuery,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Badge,
  Fab,
  Tab,
  Tabs,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Pets as PetsIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  LocalShipping as ShippingIcon,
  People as PeopleIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon,
  CalendarToday as CalendarIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Agriculture as AgricultureIcon,
  Egg as EggIcon,
  Analytics as AnalyticsIcon,
  PersonAdd as PersonAddIcon,
  Dashboard as DashboardIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import { useEleveurData } from '../../hooks/useEleveurData';
import { DashboardSyncWrapper } from '../../components/dashboards/SyncIndicator';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';

// Couleurs pour les graphiques
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];

// Composant pour afficher une carte de statistique
const StatCard = ({ title, value, icon, color, subtitle, trend }) => {
  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h4" component="div" fontWeight="bold" color={color}>
            {typeof value === 'number' && value > 1000 ?
              `${(value / 1000).toFixed(1)}k` :
              value
            }
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="caption" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        {trend && (
          <Box sx={{ textAlign: 'right' }}>
            <Typography
              variant="caption"
              color={trend > 0 ? 'success.main' : 'error.main'}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
              {trend > 0 ? '+' : ''}{trend}%
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

// Composant pour la gestion des ouvriers
const OuvriersManagement = ({ eleveurId, ouvriers, onRefresh }) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      await axios.post(`/api/eleveurs/${eleveurId}/ouvriers`, formData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setFormData({});
      setOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Erreur lors de la création de l\'ouvrier:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <PeopleIcon sx={{ mr: 1 }} />
          Gestion des Ouvriers ({ouvriers.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<PersonAddIcon />}
          onClick={() => setOpen(true)}
          size="small"
        >
          Ajouter
        </Button>
      </Box>

      <Box sx={{ height: '320px', overflow: 'auto' }}>
        <List>
          {ouvriers.map((ouvrier) => (
            <ListItem key={ouvrier.id} divider>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  {ouvrier.first_name?.[0] || ouvrier.username[0]}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={`${ouvrier.first_name || ''} ${ouvrier.last_name || ''} (${ouvrier.username})`}
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Email: {ouvrier.email}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Saisies: {ouvrier.nombre_saisies} | Dernière: {
                        ouvrier.derniere_saisie ?
                        new Date(ouvrier.derniere_saisie).toLocaleDateString() :
                        'Aucune'
                      }
                    </Typography>
                  </Box>
                }
              />
              <Chip
                label={ouvrier.status}
                color={ouvrier.status === 'active' ? 'success' : 'default'}
                size="small"
              />
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Dialog pour ajouter un ouvrier */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Ajouter un Ouvrier</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Nom d'utilisateur"
            value={formData.username || ''}
            onChange={(e) => setFormData({...formData, username: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={formData.email || ''}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Mot de passe"
            type="password"
            value={formData.password || ''}
            onChange={(e) => setFormData({...formData, password: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Prénom"
            value={formData.first_name || ''}
            onChange={(e) => setFormData({...formData, first_name: e.target.value})}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Nom"
            value={formData.last_name || ''}
            onChange={(e) => setFormData({...formData, last_name: e.target.value})}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Annuler</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

// Composant pour les saisies quotidiennes
const SaisiesQuotidiennes = ({ saisies, eleveurId, onRefresh }) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    eleveur_id: eleveurId,
    date_saisie: new Date().toISOString().split('T')[0]
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      await axios.post('/api/eleveurs/saisies-quotidiennes', formData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setFormData({
        eleveur_id: eleveurId,
        date_saisie: new Date().toISOString().split('T')[0]
      });
      setOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Erreur lors de la saisie:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <AssignmentIcon sx={{ mr: 1 }} />
          Saisies Quotidiennes
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpen(true)}
          size="small"
        >
          Nouvelle saisie
        </Button>
      </Box>

      <Box sx={{ height: '320px', overflow: 'auto' }}>
        <List>
          {saisies.map((saisie) => (
            <ListItem key={saisie.id} divider>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <AssignmentIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={`${saisie.ouvrier_nom} - ${saisie.ferme_nom || 'Ferme principale'}`}
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Morts: {saisie.nombre_morts} | Malades: {saisie.nombre_malades}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(saisie.date_saisie).toLocaleDateString()}
                    </Typography>
                    {saisie.incidents && (
                      <Typography variant="caption" color="error.main" display="block">
                        Incident: {saisie.incidents}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Dialog pour nouvelle saisie */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Nouvelle Saisie Quotidienne</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date de saisie"
                type="date"
                value={formData.date_saisie || ''}
                onChange={(e) => setFormData({...formData, date_saisie: e.target.value})}
                margin="normal"
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="ID Volaille"
                type="number"
                value={formData.volaille_id || ''}
                onChange={(e) => setFormData({...formData, volaille_id: parseInt(e.target.value)})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nombre de morts"
                type="number"
                value={formData.nombre_morts || 0}
                onChange={(e) => setFormData({...formData, nombre_morts: parseInt(e.target.value)})}
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nombre de malades"
                type="number"
                value={formData.nombre_malades || 0}
                onChange={(e) => setFormData({...formData, nombre_malades: parseInt(e.target.value)})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Température moyenne (°C)"
                type="number"
                value={formData.temperature_moyenne || ''}
                onChange={(e) => setFormData({...formData, temperature_moyenne: parseFloat(e.target.value)})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Humidité moyenne (%)"
                type="number"
                value={formData.humidite_moyenne || ''}
                onChange={(e) => setFormData({...formData, humidite_moyenne: parseFloat(e.target.value)})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Incidents"
                multiline
                rows={2}
                value={formData.incidents || ''}
                onChange={(e) => setFormData({...formData, incidents: e.target.value})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Observations"
                multiline
                rows={2}
                value={formData.observations || ''}
                onChange={(e) => setFormData({...formData, observations: e.target.value})}
                margin="normal"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Annuler</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Enregistrer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

// Composant pour les activités multi-types
const MultiActivitiesView = ({ eleveurId }) => {
  const [activites, setActivites] = useState([]);
  const [filter, setFilter] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchActivites = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`/api/eleveurs/${eleveurId}/activites`, {
        headers: { Authorization: `Bearer ${token}` },
        params: filter ? { type_activite: filter } : {}
      });
      setActivites(response.data.data.activites);
    } catch (error) {
      console.error('Erreur lors de la récupération des activités:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivites();
  }, [filter, eleveurId]);

  return (
    <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
          <AgricultureIcon sx={{ mr: 1 }} />
          Vue Multi-Activités
        </Typography>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Filtrer</InputLabel>
          <Select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            label="Filtrer"
          >
            <MenuItem value="">Toutes</MenuItem>
            <MenuItem value="poussins">Poussins</MenuItem>
            <MenuItem value="dindes">Dindes</MenuItem>
            <MenuItem value="poulets_chair">Poulets de chair</MenuItem>
            <MenuItem value="pondeuses">Pondeuses</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ height: '320px', overflow: 'auto' }}>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={2}>
            {activites.map((activite, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" component="div" sx={{ display: 'flex', alignItems: 'center' }}>
                      <PetsIcon sx={{ mr: 1, color: COLORS[index % COLORS.length] }} />
                      {activite.type_activite}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Race: {activite.race}
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        Stock: <strong>{activite.stock_total}</strong>
                      </Typography>
                      <Typography variant="body2">
                        Revenus: <strong>{activite.revenus_totaux}€</strong>
                      </Typography>
                      <Typography variant="body2">
                        Lots: <strong>{activite.nombre_lots}</strong>
                      </Typography>
                      {activite.production_oeufs > 0 && (
                        <Typography variant="body2">
                          Œufs: <strong>{activite.production_oeufs}</strong>
                        </Typography>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Paper>
  );
};

const EleveurDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [ouvriers, setOuvriers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Utiliser la synchronisation automatique des données
  const eleveurSyncData = useEleveurData(user?.eleveur_id, {
    enableSync: true,
    syncInterval: 60000 // 1 minute
  });
  const [refreshing, setRefreshing] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const eleveurId = user?.profile_id || 1; // Fallback pour les tests

  // Fonction pour récupérer les données du dashboard
  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      const token = localStorage.getItem('token');

      const [dashboardResponse, ouvriersResponse] = await Promise.all([
        axios.get(`/api/eleveurs/${eleveurId}/dashboard`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`/api/eleveurs/${eleveurId}/ouvriers`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      setDashboardData(dashboardResponse.data.data);
      setOuvriers(ouvriersResponse.data.data.ouvriers);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des données:', err);
      setError('Erreur lors du chargement des données du dashboard');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fonction pour les actions rapides
  const handleQuickSale = async (data) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`/api/eleveurs/${eleveurId}/ventes/quick`, data, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchDashboardData();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de la vente:', error);
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Rafraîchir les données toutes les 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [eleveurId]);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={fetchDashboardData}>
          Réessayer
        </Button>
      </Container>
    );
  }

  const { stats, alertes, ventesRecentes, saisiesQuotidiennes, productionOeufs, graphiques } = dashboardData;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <DashboardSyncWrapper
        syncData={eleveurSyncData}
        title="Dashboard Éleveur"
        showSyncIndicator={true}
      >
        {/* En-tête avec actions rapides */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h6" component="h2" gutterBottom>
            Bienvenue, {user?.first_name || user?.username}
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={alertes.length} color="error">
            <IconButton color="warning">
              <WarningIcon />
            </IconButton>
          </Badge>
          <Button
            variant="outlined"
            startIcon={<MoneyIcon />}
            onClick={() => {/* Ouvrir dialog vente rapide */}}
          >
            Nouvelle vente
          </Button>
          <Button
            variant="contained"
            startIcon={<DashboardIcon />}
            onClick={fetchDashboardData}
            disabled={refreshing}
          >
            {refreshing ? <CircularProgress size={20} /> : 'Actualiser'}
          </Button>
        </Box>
      </Box>

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Total Volailles"
            value={stats.totalVolailles}
            icon={<PetsIcon />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Poussins"
            value={stats.totalPoussins}
            icon={<PetsIcon />}
            color="#2e7d32"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Dindes"
            value={stats.totalDindes}
            icon={<PetsIcon />}
            color="#ed6c02"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Poulets Chair"
            value={stats.totalPouletsChair}
            icon={<PetsIcon />}
            color="#9c27b0"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Pondeuses"
            value={stats.totalPondeuses}
            icon={<EggIcon />}
            color="#d32f2f"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Valeur Cheptel"
            value={`${stats.valeurEstimeeCheptel.toFixed(0)}€`}
            icon={<MoneyIcon />}
            color="#1565c0"
          />
        </Grid>
      </Grid>

      {/* Alertes */}
      {alertes && alertes.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
                Alertes Actives ({alertes.length})
              </Typography>
              <Grid container spacing={2}>
                {alertes.slice(0, 4).map((alerte) => (
                  <Grid item xs={12} sm={6} md={3} key={alerte.id}>
                    <Card
                      sx={{
                        border: `2px solid ${alerte.priorite === 'critique' ? '#f44336' : '#ff9800'}`,
                        bgcolor: alerte.priorite === 'critique' ? '#ffebee' : '#fff3e0'
                      }}
                    >
                      <CardContent>
                        <Typography variant="h6" component="div" noWrap>
                          {alerte.titre}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {alerte.message}
                        </Typography>
                        <Chip
                          label={alerte.priorite.toUpperCase()}
                          color={alerte.priorite === 'critique' ? 'error' : 'warning'}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Onglets pour les différentes vues */}
      <Paper elevation={2} sx={{ mb: 4 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Vue d'ensemble" />
          <Tab label="Gestion Ouvriers" />
          <Tab label="Saisies Quotidiennes" />
          <Tab label="Multi-Activités" />
        </Tabs>
      </Paper>

      {/* Contenu des onglets */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Ventes récentes */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <MoneyIcon sx={{ mr: 1 }} />
                Ventes Récentes
              </Typography>
              <List>
                {ventesRecentes.map((vente) => (
                  <ListItem key={vente.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'success.main' }}>
                        <MoneyIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${vente.quantite} ${vente.espece || 'unités'}`}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Acheteur: {vente.acheteur}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Montant: {vente.total_amount}€
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(vente.created_at).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Production d'œufs */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <EggIcon sx={{ mr: 1 }} />
                Production d'Œufs Récente
              </Typography>
              <List>
                {productionOeufs.map((production) => (
                  <ListItem key={production.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'warning.main' }}>
                        <EggIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${production.quantite_oeufs} œufs`}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Ferme: {production.ferme_nom || 'Principale'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Taux de ponte: {production.taux_ponte}%
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(production.date_production).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Graphiques */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
              <Typography variant="h6" gutterBottom>
                Évolution des Ventes
              </Typography>
              <ResponsiveContainer width="100%" height="85%">
                <AreaChart data={graphiques.evolutionVentes}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="mois"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { month: 'short' })}
                  />
                  <YAxis />
                  <ChartTooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="chiffre_affaires"
                    stroke="#1976d2"
                    fill="#1976d2"
                    fillOpacity={0.3}
                    name="Chiffre d'affaires"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
              <Typography variant="h6" gutterBottom>
                Production d'Œufs (Tendances)
              </Typography>
              <ResponsiveContainer width="100%" height="85%">
                <LineChart data={graphiques.tendancesOeufs}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="jour"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })}
                  />
                  <YAxis />
                  <ChartTooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="production_quotidienne"
                    stroke="#ff9800"
                    name="Production quotidienne"
                  />
                  <Line
                    type="monotone"
                    dataKey="taux_ponte_moyen"
                    stroke="#4caf50"
                    name="Taux de ponte moyen"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <OuvriersManagement
              eleveurId={eleveurId}
              ouvriers={ouvriers}
              onRefresh={fetchDashboardData}
            />
          </Grid>
        </Grid>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <SaisiesQuotidiennes
              saisies={saisiesQuotidiennes}
              eleveurId={eleveurId}
              onRefresh={fetchDashboardData}
            />
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <MultiActivitiesView eleveurId={eleveurId} />
          </Grid>
        </Grid>
      )}

      {/* Bouton de rafraîchissement flottant */}
        <Fab
          color="primary"
          aria-label="refresh"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={fetchDashboardData}
          disabled={refreshing}
        >
          {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
        </Fab>
      </DashboardSyncWrapper>
    </Container>
  );
};



export default EleveurDashboard;
