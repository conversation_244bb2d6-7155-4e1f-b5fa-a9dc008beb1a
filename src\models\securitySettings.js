const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SecuritySettings extends Model {
    // Pas d'associations définies dans le code original pour ce modèle
    // static associate(models) {
    //   // Définir les associations ici si nécessaire
    // }
  }

  SecuritySettings.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    enable2FA: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Enable Two-Factor Authentication for all users or admins.'
    },
    sessionTimeout: {
      type: DataTypes.INTEGER,
      defaultValue: 30, // In minutes
      comment: 'Session timeout duration in minutes.'
    },
    maxLoginAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      comment: 'Maximum number of failed login attempts before lockout.'
    },
    lockoutDuration: {
      type: DataTypes.INTEGER,
      defaultValue: 15, // In minutes
      comment: 'Lockout duration in minutes after too many failed login attempts.'
    },
    passwordComplexityRegex: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Regex for password complexity requirements.'
      // Example: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    },
    passwordHistoryCount: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
      comment: 'Number of previous passwords to remember to prevent reuse.'
    },
    passwordExpiryDays: {
      type: DataTypes.INTEGER,
      defaultValue: 90, // In days
      comment: 'Number of days after which passwords expire.'
    },
    contentSecurityPolicy: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Content Security Policy (CSP) header value.'
    },
    corsAllowedOrigins: {
      type: DataTypes.TEXT, // Stored as a comma-separated string or JSON array string
      allowNull: true,
      comment: 'Allowed origins for CORS.'
    },
    logLevel: {
      type: DataTypes.ENUM('debug', 'info', 'warn', 'error'),
      defaultValue: 'info',
      comment: 'Application logging level.'
    },
    apiRateLimitingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    apiRateLimitRequests: {
      type: DataTypes.INTEGER,
      defaultValue: 100, // requests
    },
    apiRateLimitWindowMs: {
      type: DataTypes.INTEGER,
      defaultValue: 15 * 60 * 1000, // 15 minutes in milliseconds
    }
  }, {
    sequelize,
    modelName: 'SecuritySettings',
    tableName: 'security_settings',
    timestamps: true,
  });

  return SecuritySettings;
};
