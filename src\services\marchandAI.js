const axios = require('axios');
const { Pool } = require('pg');

// Configuration de la connexion à la base de données
const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`;
const pool = new Pool({ connectionString });

// Configuration d'OpenAI
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const openaiClient = axios.create({
  baseURL: 'https://api.openai.com/v1',
  headers: {
    'Authorization': `Bearer ${OPENAI_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

class MarchandAI {
  /**
   * Génère des recommandations de prix basées sur les données historiques et actuelles
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Object>} - Recommandations de prix
   */
  static async getPriceOptimizationRecommendations(marchandId) {
    try {
      // Récupérer les données des produits et des ventes
      const productData = await this.getProductSalesData(marchandId);

      // Préparer le prompt pour OpenAI
      const prompt = {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Vous êtes un expert en analyse de marché et optimisation des prix. Analysez les données de vente et de stock pour suggérer des ajustements de prix optimaux."
          },
          {
            role: "user",
            content: `Voici les données de vente et de stock pour un marchand de produits avicoles. Pour chaque produit, suggérez un prix optimal basé sur la demande, le stock disponible, et les tendances de vente. Fournissez une justification pour chaque recommandation. Données: ${JSON.stringify(productData)}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      };

      // Appeler l'API OpenAI
      const response = await openaiClient.post('/chat/completions', prompt);

      // Traiter et structurer la réponse
      const aiSuggestions = response.data.choices[0].message.content;

      // Dans un environnement de production, on analyserait la réponse pour la structurer
      // Pour cet exemple, on simule une réponse structurée
      const structuredRecommendations = this.parseAIResponse(aiSuggestions, productData);

      return structuredRecommendations;
    } catch (error) {
      console.error('Erreur lors de la génération des recommandations de prix:', error);
      throw error;
    }
  }

  /**
   * Prédit la demande future pour les produits du marchand
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Object>} - Prédictions de demande
   */
  static async getDemandForecast(marchandId) {
    try {
      // Récupérer l'historique des ventes
      const salesHistory = await this.getSalesHistory(marchandId);

      // Préparer le prompt pour OpenAI
      const prompt = {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Vous êtes un expert en prévision de demande et analyse de tendances. Analysez l'historique des ventes pour prédire la demande future."
          },
          {
            role: "user",
            content: `Voici l'historique des ventes pour un marchand de produits avicoles. Prédisez la demande pour les 30 prochains jours pour chaque produit. Identifiez les tendances saisonnières et les facteurs qui pourraient influencer la demande. Données: ${JSON.stringify(salesHistory)}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      };

      // Appeler l'API OpenAI
      const response = await openaiClient.post('/chat/completions', prompt);

      // Traiter la réponse
      const aiPredictions = response.data.choices[0].message.content;

      // Simuler une réponse structurée
      return {
        predictions: this.parseDemandForecast(aiPredictions, salesHistory),
        raw_response: aiPredictions
      };
    } catch (error) {
      console.error('Erreur lors de la prédiction de la demande:', error);
      throw error;
    }
  }

  /**
   * Identifie les produits non rentables ou à faible performance
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Object>} - Analyse de rentabilité
   */
  static async getProductProfitabilityAnalysis(marchandId) {
    try {
      // Récupérer les données de vente et de coûts
      const profitabilityData = await this.getProductProfitabilityData(marchandId);

      // Préparer le prompt pour OpenAI
      const prompt = {
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Vous êtes un expert en analyse financière et rentabilité. Analysez les données de vente et de coûts pour identifier les produits non rentables ou à faible performance."
          },
          {
            role: "user",
            content: `Voici les données de vente et de coûts pour un marchand de produits avicoles. Identifiez les produits non rentables ou à faible performance. Suggérez des actions pour améliorer la rentabilité. Données: ${JSON.stringify(profitabilityData)}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      };

      // Appeler l'API OpenAI
      const response = await openaiClient.post('/chat/completions', prompt);

      // Traiter la réponse
      const aiAnalysis = response.data.choices[0].message.content;

      // Simuler une réponse structurée
      return {
        analysis: this.parseProfitabilityAnalysis(aiAnalysis, profitabilityData),
        raw_response: aiAnalysis
      };
    } catch (error) {
      console.error('Erreur lors de l\'analyse de rentabilité:', error);
      throw error;
    }
  }

  // Méthodes utilitaires pour récupérer les données nécessaires

  /**
   * Récupère les données de vente et de stock pour les produits du marchand
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Array>} - Données des produits et des ventes
   */
  static async getProductSalesData(marchandId) {
    try {
      const query = {
        text: `
          SELECT
            p.id,
            p.name,
            p.price,
            p.stock_quantity,
            p.category,
            COUNT(DISTINCT o.id) as order_count,
            SUM(oi.quantity) as total_sold,
            AVG(oi.unit_price) as average_price
          FROM products p
          LEFT JOIN order_items oi ON p.id = oi.product_id
          LEFT JOIN orders o ON oi.order_id = o.id
          WHERE p.marchand_id = $1
          GROUP BY p.id, p.name, p.price, p.stock_quantity, p.category
        `,
        values: [marchandId]
      };

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération des données de vente:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des ventes pour les produits du marchand
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Array>} - Historique des ventes
   */
  static async getSalesHistory(marchandId) {
    try {
      const query = {
        text: `
          SELECT
            p.id as product_id,
            p.name as product_name,
            DATE_TRUNC('day', o.created_at) as sale_date,
            SUM(oi.quantity) as quantity_sold,
            SUM(oi.total_price) as revenue
          FROM products p
          JOIN order_items oi ON p.id = oi.product_id
          JOIN orders o ON oi.order_id = o.id
          WHERE p.marchand_id = $1
            AND o.status != 'cancelled'
            AND o.created_at >= NOW() - INTERVAL '90 days'
          GROUP BY p.id, p.name, DATE_TRUNC('day', o.created_at)
          ORDER BY p.id, sale_date
        `,
        values: [marchandId]
      };

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des ventes:', error);
      throw error;
    }
  }

  /**
   * Récupère les données de rentabilité pour les produits du marchand
   * @param {number} marchandId - ID du marchand
   * @returns {Promise<Array>} - Données de rentabilité
   */
  static async getProductProfitabilityData(marchandId) {
    // Dans un environnement réel, on aurait une table de coûts
    // Pour cet exemple, on simule des coûts fixes pour chaque produit
    try {
      const query = {
        text: `
          SELECT
            p.id,
            p.name,
            p.price as selling_price,
            p.price * 0.7 as estimated_cost, -- Simulation d'un coût à 70% du prix de vente
            SUM(oi.quantity) as total_sold,
            SUM(oi.total_price) as total_revenue,
            COUNT(DISTINCT o.id) as order_count
          FROM products p
          LEFT JOIN order_items oi ON p.id = oi.product_id
          LEFT JOIN orders o ON oi.order_id = o.id AND o.status != 'cancelled'
          WHERE p.marchand_id = $1
          GROUP BY p.id, p.name, p.price
        `,
        values: [marchandId]
      };

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      console.error('Erreur lors de la récupération des données de rentabilité:', error);
      throw error;
    }
  }

  // Méthodes pour parser les réponses d'OpenAI (simulées pour cet exemple)

  static parseAIResponse(aiResponse, productData) {
    // Dans un environnement réel, on analyserait la réponse texte d'OpenAI
    // Pour cet exemple, on retourne une structure simulée
    return {
      price_recommendations: productData.map(product => ({
        product_id: product.id,
        product_name: product.name,
        current_price: parseFloat(product.price),
        recommended_price: this.calculateRecommendedPrice(product),
        justification: this.generatePriceJustification(product)
      })),
      market_insights: [
        "La demande pour les produits avicoles bio est en hausse de 15% ce trimestre",
        "Les prix des aliments pour volaille ont augmenté de 5%, impactant les marges",
        "La concurrence locale a intensifié ses promotions sur les œufs frais"
      ]
    };
  }

  static parseDemandForecast(aiResponse, salesHistory) {
    // Simulation de prédictions de demande
    const uniqueProducts = [...new Set(salesHistory.map(item => item.product_id))];

    return uniqueProducts.map(productId => {
      const productHistory = salesHistory.filter(item => item.product_id === productId);
      const productName = productHistory[0]?.product_name || `Produit ${productId}`;

      return {
        product_id: productId,
        product_name: productName,
        forecast_30_days: Math.floor(Math.random() * 100) + 20, // Simulation
        trend: this.generateRandomTrend(),
        confidence: (Math.random() * 0.3 + 0.7).toFixed(2), // Entre 0.7 et 1.0
        seasonal_factors: [
          "Augmentation attendue pendant les fêtes",
          "Baisse historique pendant les vacances d'été"
        ]
      };
    });
  }

  static parseProfitabilityAnalysis(aiResponse, profitabilityData) {
    // Simulation d'analyse de rentabilité
    return {
      low_profit_products: profitabilityData
        .filter(p => (p.selling_price - p.estimated_cost) / p.selling_price < 0.2) // Marge < 20%
        .map(product => ({
          product_id: product.id,
          product_name: product.name,
          current_margin: ((product.selling_price - product.estimated_cost) / product.selling_price).toFixed(2),
          recommendation: "Augmenter le prix ou réduire les coûts",
          potential_savings: ((product.estimated_cost * 0.1) * (product.total_sold || 0)).toFixed(2)
        })),
      high_profit_products: profitabilityData
        .filter(p => (p.selling_price - p.estimated_cost) / p.selling_price > 0.4) // Marge > 40%
        .map(product => ({
          product_id: product.id,
          product_name: product.name,
          current_margin: ((product.selling_price - product.estimated_cost) / product.selling_price).toFixed(2),
          recommendation: "Maintenir la stratégie actuelle"
        })),
      overall_recommendation: "Optimisez votre gamme de produits en vous concentrant sur les articles à forte marge et en réévaluant la stratégie pour les produits à faible marge."
    };
  }

  // Méthodes utilitaires pour les simulations

  static calculateRecommendedPrice(product) {
    // Logique simplifiée pour simuler une recommandation de prix
    const currentPrice = parseFloat(product.price);
    const stockLevel = parseInt(product.stock_quantity);
    const totalSold = parseInt(product.total_sold) || 0;

    // Si stock faible et ventes élevées, augmenter le prix
    if (stockLevel < 20 && totalSold > 50) {
      return (currentPrice * 1.1).toFixed(2); // +10%
    }
    // Si stock élevé et peu de ventes, réduire le prix
    else if (stockLevel > 100 && totalSold < 20) {
      return (currentPrice * 0.9).toFixed(2); // -10%
    }
    // Sinon, maintenir le prix avec un léger ajustement
    else {
      const adjustment = (Math.random() * 0.1) - 0.05; // Entre -5% et +5%
      return (currentPrice * (1 + adjustment)).toFixed(2);
    }
  }

  static generatePriceJustification(product) {
    const stockLevel = parseInt(product.stock_quantity);
    const totalSold = parseInt(product.total_sold) || 0;

    if (stockLevel < 20 && totalSold > 50) {
      return "Forte demande et stock limité - une augmentation de prix est recommandée";
    } else if (stockLevel > 100 && totalSold < 20) {
      return "Stock élevé et faible rotation - une réduction de prix pourrait stimuler les ventes";
    } else {
      return "Prix optimal basé sur l'équilibre actuel entre l'offre et la demande";
    }
  }

  static generateRandomTrend() {
    const trends = [
      "en hausse",
      "stable",
      "en légère hausse",
      "en légère baisse",
      "en baisse"
    ];

    return trends[Math.floor(Math.random() * trends.length)];
  }
}

module.exports = MarchandAI;
