const { Model, DataTypes } = require('sequelize'); // Import Model and DataTypes

module.exports = (sequelize, DataTypes) => {
  class Veterinaire extends Model {
    static associate(models) {
      Veterinaire.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });
      // Add other associations here if needed
      // For example, if a Veterinaire can have many Consultations:
      // Veterinaire.hasMany(models.Consultation, { foreignKey: 'veterinaire_id', as: 'consultations' });
    }

    static async findBySpecialite(specialite) {
      return await this.findAll({
        where: {
          specialites: {
            [sequelize.Op.contains]: [specialite] // Use sequelize.Op
          }
        }
      });
    }

    static async findAvailable() {
      return await this.findAll({
        where: {
          statut: 'actif' // Assuming 'statut' is a field in your model
        }
      });
    }
  }

  Veterinaire.init({
    // Define attributes here. Example:
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // Name of the table
        key: 'id'
      }
    },
    specialites: {
      type: DataTypes.ARRAY(DataTypes.STRING)
    },
    disponibilites: { // Make sure this matches your database schema and needs
      type: DataTypes.JSONB // Or DataTypes.TEXT if storing as a string
    },
    telephone: {
      type: DataTypes.STRING
    },
    email_professionnel: {
      type: DataTypes.STRING,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    adresse_cabinet: {
      type: DataTypes.STRING
    },
    description: {
      type: DataTypes.TEXT
    },
    tarifs: { // Consider if this should be JSONB or a separate table
      type: DataTypes.JSONB
    },
    statut: { // Added based on findAvailable method
      type: DataTypes.STRING,
      defaultValue: 'inactif'
    },
    // Add other fields like experience_years, certifications, etc.
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE,
      field: 'created_at'
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE,
      field: 'updated_at'
    }
  }, {
    sequelize,
    modelName: 'Veterinaire',
    tableName: 'veterinaires', // Explicitly define table name
    timestamps: true, // Enables createdAt and updatedAt
    underscored: true, // Uses snake_case for column names and foreign keys
    // Example hooks (if needed, ensure User model is accessible via sequelize.models.User)
    // hooks: {
    //   afterCreate: async (veterinaire, options) => {
    //     try {
    //       const User = sequelize.models.User; // Access User model correctly
    //       if (User) {
    //         const user = await User.findByPk(veterinaire.user_id);
    //         if (user && user.role_id !== VETERINAIRE_ROLE_ID) { // Assuming VETERINAIRE_ROLE_ID is defined
    //           // Potentially update user's role or log an inconsistency
    //           console.warn(`User ${user.id} assigned to veterinaire ${veterinaire.id} might not have veterinaire role.`);
    //         }
    //       } else {
    //         console.error('User model not found for Veterinaire hook.');
    //       }
    //     } catch (error) {
    //       console.error('Error in Veterinaire afterCreate hook:', error);
    //     }
    //   }
    // }
  });

  return Veterinaire;
};
