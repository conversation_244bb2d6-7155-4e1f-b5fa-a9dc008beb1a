import { useState, useEffect } from 'react';
import axiosInstance from '../utils/axiosConfig';
import {
  MonetizationOn,
  Warning,
  Inventory,
  TrendingUp,
  Pets,
  LocalShipping,
  HealthAndSafety,
  Thermostat
} from '@mui/icons-material';

/**
 * Hook personnalisé pour générer des recommandations IA
 * Analyse les données de l'éleveur et du marché pour fournir des suggestions intelligentes
 *
 * @param {Object} eleveurData - Données de l'éleveur (profil, statistiques, etc.)
 * @param {Object} options - Options supplémentaires
 * @param {boolean} options.useMockData - Utiliser des données simulées au lieu de l'API (pour le développement)
 * @param {number} options.refreshInterval - Intervalle de rafraîchissement en millisecondes (par défaut: 12 heures)
 * @returns {Object} - Recommandations, état de chargement et erreurs éventuelles
 */
const useAIRecommendations = (eleveurData = {}, options = {}) => {
  const {
    useMockData = true, // Par défaut, utiliser des données simulées
    refreshInterval = 12 * 60 * 60 * 1000 // 12 heures par défaut
  } = options;

  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fonction pour récupérer ou générer les recommandations
    const fetchRecommendations = async () => {
      try {
        setLoading(true);

        if (useMockData || !eleveurData) {
          // Utiliser des données simulées pour le développement
          setTimeout(() => {
            const mockRecommendations = generateMockRecommendations();
            setRecommendations(mockRecommendations);
            setError(null);
            setLoading(false);
          }, 1000);
        } else {
          // Appel à l'API pour les recommandations réelles
          // Dans une application réelle, vous feriez un appel à une API d'IA
          const response = await axiosInstance.post('/ai/recommendations', {
            eleveurId: eleveurData.profile?.id,
            stats: eleveurData.stats,
            volailles: eleveurData.volailles,
            ventes: eleveurData.ventes,
            alertes: eleveurData.alertes
          });

          setRecommendations(response.data);
          setError(null);
          setLoading(false);
        }
      } catch (err) {
        console.error('Erreur lors de la récupération des recommandations:', err);
        setError('Impossible de charger les recommandations');
        setLoading(false);
      }
    };

    // Appel initial
    fetchRecommendations();

    // Rafraîchir les données à intervalles réguliers
    const intervalId = setInterval(fetchRecommendations, refreshInterval);

    // Nettoyage
    return () => clearInterval(intervalId);
  }, [eleveurData, useMockData, refreshInterval]);

  // Fonction pour générer des recommandations simulées
  const generateMockRecommendations = () => {
    // Liste de recommandations possibles
    const possibleRecommendations = [
      {
        id: 1,
        type: 'market',
        title: 'Opportunité de marché',
        description: 'Les prix des poulets devraient augmenter de 8% la semaine prochaine. Envisagez de retarder vos ventes.',
        importance: 'high',
        icon: MonetizationOn,
        color: '#10B981' // vert
      },
      {
        id: 2,
        type: 'health',
        title: 'Alerte sanitaire',
        description: 'Risque élevé de maladie détecté dans votre région. Vérifiez votre lot #A123.',
        importance: 'high',
        icon: Warning,
        color: '#EF4444' // rouge
      },
      {
        id: 3,
        type: 'stock',
        title: 'Gestion des stocks',
        description: 'Votre stock d\'aliments sera épuisé dans 5 jours. Commandez maintenant pour éviter une rupture.',
        importance: 'medium',
        icon: Inventory,
        color: '#F59E0B' // orange
      },
      {
        id: 4,
        type: 'optimization',
        title: 'Optimisation',
        description: 'Augmentez votre production de 15% en ajustant la température de 2°C dans le bâtiment B.',
        importance: 'medium',
        icon: TrendingUp,
        color: '#3B82F6' // bleu
      },
      {
        id: 5,
        type: 'breeding',
        title: 'Conseil d\'élevage',
        description: 'Vos poulets de chair atteignent leur poids optimal. Planifiez la vente dans les 7 prochains jours.',
        importance: 'medium',
        icon: Pets,
        color: '#8B5CF6' // violet
      },
      {
        id: 6,
        type: 'logistics',
        title: 'Optimisation logistique',
        description: 'Regroupez vos livraisons du 15 et 18 décembre pour économiser 15% sur les frais de transport.',
        importance: 'low',
        icon: LocalShipping,
        color: '#6B7280' // gris
      },
      {
        id: 7,
        type: 'health',
        title: 'Prévention sanitaire',
        description: 'Programmez une vaccination préventive pour votre nouveau lot avant le 20 décembre.',
        importance: 'high',
        icon: HealthAndSafety,
        color: '#EF4444' // rouge
      },
      {
        id: 8,
        type: 'climate',
        title: 'Contrôle climatique',
        description: 'Températures nocturnes en baisse prévues. Augmentez le chauffage de 3°C pendant la nuit.',
        importance: 'medium',
        icon: Thermostat,
        color: '#F59E0B' // orange
      }
    ];

    // Sélectionner aléatoirement 3 à 5 recommandations
    const count = Math.floor(Math.random() * 3) + 3; // 3 à 5
    const shuffled = [...possibleRecommendations].sort(() => 0.5 - Math.random());

    // S'assurer qu'au moins une recommandation importante est incluse
    const highImportance = shuffled.filter(rec => rec.importance === 'high');
    const otherRecommendations = shuffled.filter(rec => rec.importance !== 'high');

    let selected = [];

    if (highImportance.length > 0) {
      // Ajouter au moins une recommandation importante
      selected.push(highImportance[0]);

      // Ajouter d'autres recommandations pour atteindre le compte
      selected = [...selected, ...otherRecommendations.slice(0, count - 1)];
    } else {
      // Si aucune recommandation importante, prendre simplement les premières
      selected = shuffled.slice(0, count);
    }

    return selected;
  };

  // Fonction pour rafraîchir manuellement les recommandations
  const refreshRecommendations = async () => {
    setLoading(true);
    try {
      const mockRecommendations = generateMockRecommendations();
      setRecommendations(mockRecommendations);
      setError(null);
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des recommandations:', err);
      setError('Impossible de rafraîchir les recommandations');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour filtrer les recommandations par type
  const getRecommendationsByType = (type) => {
    return recommendations.filter(rec => rec.type === type);
  };

  // Fonction pour obtenir les recommandations importantes
  const getImportantRecommendations = () => {
    return recommendations.filter(rec => rec.importance === 'high');
  };

  return {
    recommendations,
    loading,
    error,
    refreshRecommendations,
    getRecommendationsByType,
    getImportantRecommendations
  };
};

export default useAIRecommendations;
