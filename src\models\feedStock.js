const { DataTypes } = require('sequelize');

/**
 * FeedStock model - Represents feed inventory at farms
 * @param {import('sequelize').Sequelize} sequelize
 * @param {import('sequelize').DataTypes} DataTypes
 */
module.exports = (sequelize, DataTypes) => {
  const FeedStock = sequelize.define('FeedStock', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    farm_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'fermes',
        key: 'id'
      }
    },
    feed_item_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'feed_items',
        key: 'id'
      }
    },
    supplier_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'feed_suppliers',
        key: 'id'
      }
    },
    batch_number: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    quantity_received: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      validate: {
        min: 0.001
      },
      comment: 'initial quantity received'
    },
    quantity_current: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'current available quantity'
    },
    unit_cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'cost per unit'
    },
    total_cost: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'total cost of the batch'
    },
    purchase_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true,
        isBefore: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // Allow today + 1 day
      }
    },
    expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: true,
        isAfterPurchase(value) {
          if (value && this.purchase_date && new Date(value) <= new Date(this.purchase_date)) {
            throw new Error('Expiry date must be after purchase date');
          }
        }
      }
    },
    storage_location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    minimum_stock_alert: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: true,
      validate: {
        min: 0
      },
      comment: 'minimum quantity before alert'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: [['active', 'depleted', 'expired', 'damaged']]
      },
      comment: 'active, depleted, expired, damaged'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'feed_stock',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['farm_id', 'status']
      },
      {
        fields: ['feed_item_id', 'expiry_date']
      },
      {
        fields: ['quantity_current', 'minimum_stock_alert']
      },
      {
        fields: ['purchase_date']
      }
    ],
    hooks: {
      beforeValidate: (feedStock) => {
        // Auto-calculate total cost if not provided
        if (feedStock.quantity_received && feedStock.unit_cost && !feedStock.total_cost) {
          feedStock.total_cost = feedStock.quantity_received * feedStock.unit_cost;
        }
        
        // Set initial current quantity to received quantity if not set
        if (feedStock.quantity_received && !feedStock.quantity_current) {
          feedStock.quantity_current = feedStock.quantity_received;
        }
        
        // Auto-update status based on quantity
        if (feedStock.quantity_current <= 0 && feedStock.status === 'active') {
          feedStock.status = 'depleted';
        }
        
        // Auto-update status based on expiry
        if (feedStock.expiry_date && new Date(feedStock.expiry_date) < new Date() && feedStock.status === 'active') {
          feedStock.status = 'expired';
        }
      },
      
      afterUpdate: async (feedStock) => {
        // Check for low stock alerts
        if (feedStock.minimum_stock_alert && 
            feedStock.quantity_current <= feedStock.minimum_stock_alert &&
            feedStock.status === 'active') {
          await feedStock.createLowStockAlert();
        }
      }
    }
  });

  // Instance methods
  FeedStock.prototype.consumeFeed = function(quantity, consumptionDate = new Date()) {
    if (quantity <= 0) {
      throw new Error('Consumption quantity must be positive');
    }
    
    if (quantity > this.quantity_current) {
      throw new Error('Cannot consume more than available stock');
    }
    
    this.quantity_current = parseFloat(this.quantity_current) - parseFloat(quantity);
    
    // Update status if depleted
    if (this.quantity_current <= 0) {
      this.status = 'depleted';
    }
    
    return this.save();
  };

  FeedStock.prototype.addStock = function(quantity, unitCost = null) {
    if (quantity <= 0) {
      throw new Error('Added quantity must be positive');
    }
    
    this.quantity_current = parseFloat(this.quantity_current) + parseFloat(quantity);
    this.quantity_received = parseFloat(this.quantity_received) + parseFloat(quantity);
    
    // Update costs if provided
    if (unitCost) {
      const additionalCost = quantity * unitCost;
      this.total_cost = parseFloat(this.total_cost) + additionalCost;
      this.unit_cost = this.total_cost / this.quantity_received;
    }
    
    // Reactivate if was depleted
    if (this.status === 'depleted') {
      this.status = 'active';
    }
    
    return this.save();
  };

  FeedStock.prototype.isExpired = function() {
    if (!this.expiry_date) return false;
    return new Date(this.expiry_date) < new Date();
  };

  FeedStock.prototype.isLowStock = function() {
    if (!this.minimum_stock_alert) return false;
    return this.quantity_current <= this.minimum_stock_alert;
  };

  FeedStock.prototype.getDaysUntilExpiry = function() {
    if (!this.expiry_date) return null;
    const today = new Date();
    const expiry = new Date(this.expiry_date);
    const diffTime = expiry - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  FeedStock.prototype.getConsumptionRate = function(days = 30) {
    // This would typically calculate based on consumption logs
    // For now, return a simple calculation
    const totalConsumed = this.quantity_received - this.quantity_current;
    const daysSincePurchase = Math.max(1, Math.floor((new Date() - new Date(this.purchase_date)) / (1000 * 60 * 60 * 24)));
    return totalConsumed / Math.min(days, daysSincePurchase);
  };

  FeedStock.prototype.getEstimatedDaysRemaining = function() {
    const consumptionRate = this.getConsumptionRate();
    if (consumptionRate <= 0) return null;
    return Math.floor(this.quantity_current / consumptionRate);
  };

  FeedStock.prototype.createLowStockAlert = async function() {
    const { FeedAlert } = sequelize.models;
    
    // Check if alert already exists
    const existingAlert = await FeedAlert.findOne({
      where: {
        farm_id: this.farm_id,
        feed_stock_id: this.id,
        alert_type: 'low_stock',
        is_resolved: false
      }
    });
    
    if (!existingAlert) {
      return FeedAlert.create({
        farm_id: this.farm_id,
        feed_stock_id: this.id,
        alert_type: 'low_stock',
        message: `Stock faible pour ${this.FeedItem?.name || 'aliment'}: ${this.quantity_current} ${this.FeedItem?.unit_of_measure || 'kg'} restant`,
        severity: this.quantity_current <= (this.minimum_stock_alert * 0.5) ? 'high' : 'medium'
      });
    }
  };

  // Class methods
  FeedStock.findByFarm = function(farmId, options = {}) {
    return this.findAll({
      where: {
        farm_id: farmId,
        status: { [sequelize.Sequelize.Op.ne]: 'depleted' }
      },
      include: [
        { model: sequelize.models.FeedItem, as: 'feed_item' },
        { model: sequelize.models.FeedSupplier, as: 'supplier' }
      ],
      order: [['expiry_date', 'ASC'], ['quantity_current', 'ASC']],
      ...options
    });
  };

  FeedStock.findLowStock = function(farmId = null, options = {}) {
    const whereClause = {
      status: 'active',
      [sequelize.Sequelize.Op.and]: [
        sequelize.Sequelize.where(
          sequelize.Sequelize.col('quantity_current'),
          sequelize.Sequelize.Op.lte,
          sequelize.Sequelize.col('minimum_stock_alert')
        )
      ]
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.FeedItem, as: 'feed_item' },
        { model: sequelize.models.FeedSupplier, as: 'supplier' }
      ],
      order: [['quantity_current', 'ASC']],
      ...options
    });
  };

  FeedStock.findExpiringSoon = function(days = 30, farmId = null, options = {}) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    
    const whereClause = {
      status: 'active',
      expiry_date: {
        [sequelize.Sequelize.Op.lte]: futureDate,
        [sequelize.Sequelize.Op.gte]: new Date()
      }
    };
    
    if (farmId) {
      whereClause.farm_id = farmId;
    }
    
    return this.findAll({
      where: whereClause,
      include: [
        { model: sequelize.models.FeedItem, as: 'feed_item' },
        { model: sequelize.models.FeedSupplier, as: 'supplier' }
      ],
      order: [['expiry_date', 'ASC']],
      ...options
    });
  };

  FeedStock.getTotalValueByFarm = function(farmId) {
    return this.findAll({
      where: {
        farm_id: farmId,
        status: { [sequelize.Sequelize.Op.in]: ['active'] }
      },
      attributes: [
        [sequelize.Sequelize.fn('SUM', 
          sequelize.Sequelize.literal('quantity_current * unit_cost')
        ), 'total_value']
      ],
      raw: true
    }).then(result => parseFloat(result[0]?.total_value || 0));
  };

  // Associations
  FeedStock.associate = function(models) {
    // FeedStock belongs to Farm (Eleveur)
    FeedStock.belongsTo(models.Eleveur, {
      foreignKey: 'farm_id',
      as: 'farm'
    });

    // FeedStock belongs to FeedItem
    FeedStock.belongsTo(models.FeedItem, {
      foreignKey: 'feed_item_id',
      as: 'feed_item'
    });

    // FeedStock belongs to FeedSupplier
    FeedStock.belongsTo(models.FeedSupplier, {
      foreignKey: 'supplier_id',
      as: 'supplier'
    });

    // FeedStock has many FeedConsumptionLogs
    FeedStock.hasMany(models.FeedConsumptionLog, {
      foreignKey: 'feed_stock_id',
      as: 'consumption_logs'
    });

    // FeedStock has many FeedAlerts
    FeedStock.hasMany(models.FeedAlert, {
      foreignKey: 'feed_stock_id',
      as: 'alerts'
    });
  };

  return FeedStock;
};