// Test proxy configuration
console.log('🧪 Testing Vite Proxy Configuration');

// Test through proxy (development)
fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  })
})
.then(response => {
  console.log('✅ Proxy Status:', response.status);
  if (response.ok) {
    return response.json();
  }
  throw new Error(`Proxy request failed: ${response.status}`);
})
.then(data => {
  console.log('🎉 Proxy Login Success!');
  console.log('👤 User:', data.user.email);
  console.log('🔑 Role:', data.user.role);
  console.log('📊 Token received:', !!data.token);
})
.catch(error => {
  console.error('❌ Proxy Error:', error.message);
});
