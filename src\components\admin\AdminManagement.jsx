import React, { useState, useEffect } from 'react';
import { Box, Tabs, Tab, Typography, Paper, Button, Dialog, DialogTitle, DialogContent,
  DialogActions, TextField, Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, IconButton, Snackbar, Alert, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import axios from 'axios';

// Composant TabPanel pour afficher le contenu de chaque onglet
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Fonction pour créer les propriétés des onglets
function a11yProps(index) {
  return {
    id: `admin-tab-${index}`,
    'aria-controls': `admin-tabpanel-${index}`,
  };
}

/**
 * Composant AdminManagement pour gérer les utilisateurs, les rôles et les plans
 * Ce composant permet à l'administrateur de :
 * - Voir, ajouter, modifier et supprimer des utilisateurs
 * - Gérer les rôles et les permissions
 * - Configurer les plans d'abonnement
 */
const AdminManagement = () => {
  // État pour gérer l'onglet actif
  const [tabValue, setTabValue] = useState(0);

  // États pour les données
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [plans, setPlans] = useState([]);

  // États pour les dialogues
  const [userDialog, setUserDialog] = useState(false);
  const [roleDialog, setRoleDialog] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(false);

  // États pour l'élément en cours d'édition
  const [currentUser, setCurrentUser] = useState(null);
  const [currentRole, setCurrentRole] = useState(null);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [itemToDelete, setItemToDelete] = useState(null);

  // État pour les notifications
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fonction pour changer d'onglet
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Chargement initial des données
  useEffect(() => {
    fetchData();
  }, []);

  // Fonction pour récupérer toutes les données
  const fetchData = async () => {
    try {
      // Utiliser les en-têtes d'autorisation appropriés
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      // Récupérer les utilisateurs
      const usersResponse = await axios.get('http://localhost:3003/api/admin/users', { headers });
      setUsers(usersResponse.data);

      // Récupérer les rôles
      const rolesResponse = await axios.get('http://localhost:3003/api/admin/roles', { headers });
      setRoles(rolesResponse.data);

      // Récupérer les plans
      const plansResponse = await axios.get('http://localhost:3003/api/admin/plans', { headers });
      setPlans(plansResponse.data);
    } catch (error) {
      console.error('Erreur lors de la récupération des données:', error);
      showNotification('Erreur lors du chargement des données', 'error');
    }
  };

  // Fonction pour afficher une notification
  const showNotification = (message, severity = 'success') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  // Fonction pour fermer la notification
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Fonctions pour la gestion des utilisateurs
  const openUserDialog = (user = null) => {
    setCurrentUser(user || {
      username: '',
      email: '',
      role: '',
      first_name: '',
      last_name: ''
    });
    setUserDialog(true);
  };

  const handleUserChange = (e) => {
    const { name, value } = e.target;
    setCurrentUser({
      ...currentUser,
      [name]: value
    });
  };

  const saveUser = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      if (currentUser.id) {
        // Mise à jour d'un utilisateur existant
        await axios.put(`http://localhost:3003/api/admin/users/${currentUser.id}`, currentUser, { headers });
        showNotification('Utilisateur mis à jour avec succès');
      } else {
        // Création d'un nouvel utilisateur
        await axios.post('http://localhost:3003/api/admin/users', currentUser, { headers });
        showNotification('Utilisateur créé avec succès');
      }

      setUserDialog(false);
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'utilisateur:', error);
      showNotification('Erreur lors de la sauvegarde de l\'utilisateur', 'error');
    }
  };

  const deleteUser = async (userId) => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      await axios.delete(`http://localhost:3003/api/admin/users/${userId}`, { headers });
      showNotification('Utilisateur supprimé avec succès');
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error);
      showNotification('Erreur lors de la suppression de l\'utilisateur', 'error');
    }
  };

  // Fonctions pour la gestion des rôles
  const openRoleDialog = (role = null) => {
    setCurrentRole(role || {
      name: '',
      description: '',
      permissions: []
    });
    setRoleDialog(true);
  };

  const handleRoleChange = (e) => {
    const { name, value } = e.target;
    setCurrentRole({
      ...currentRole,
      [name]: value
    });
  };

  const saveRole = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      if (currentRole.id) {
        // Mise à jour d'un rôle existant
        await axios.put(`http://localhost:3003/api/admin/roles/${currentRole.id}`, currentRole, { headers });
        showNotification('Rôle mis à jour avec succès');
      } else {
        // Création d'un nouveau rôle
        await axios.post('http://localhost:3003/api/admin/roles', currentRole, { headers });
        showNotification('Rôle créé avec succès');
      }

      setRoleDialog(false);
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du rôle:', error);
      showNotification('Erreur lors de la sauvegarde du rôle', 'error');
    }
  };

  const deleteRole = async (roleId) => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      await axios.delete(`http://localhost:3003/api/admin/roles/${roleId}`, { headers });
      showNotification('Rôle supprimé avec succès');
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la suppression du rôle:', error);
      showNotification('Erreur lors de la suppression du rôle', 'error');
    }
  };

  // Fonctions pour la gestion des plans
  const openPlanDialog = (plan = null) => {
    setCurrentPlan(plan || {
      name: '',
      description: '',
      price: 0,
      duration: 30, // Durée en jours
      features: []
    });
    setPlanDialog(true);
  };

  const handlePlanChange = (e) => {
    const { name, value } = e.target;
    setCurrentPlan({
      ...currentPlan,
      [name]: name === 'price' || name === 'duration' ? Number(value) : value
    });
  };

  const savePlan = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      if (currentPlan.id) {
        // Mise à jour d'un plan existant
        await axios.put(`http://localhost:3003/api/admin/plans/${currentPlan.id}`, currentPlan, { headers });
        showNotification('Plan mis à jour avec succès');
      } else {
        // Création d'un nouveau plan
        await axios.post('http://localhost:3003/api/admin/plans', currentPlan, { headers });
        showNotification('Plan créé avec succès');
      }

      setPlanDialog(false);
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du plan:', error);
      showNotification('Erreur lors de la sauvegarde du plan', 'error');
    }
  };

  const deletePlan = async (planId) => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'x-auth-token': token
      };

      await axios.delete(`http://localhost:3003/api/admin/plans/${planId}`, { headers });
      showNotification('Plan supprimé avec succès');
      fetchData(); // Rafraîchir les données
    } catch (error) {
      console.error('Erreur lors de la suppression du plan:', error);
      showNotification('Erreur lors de la suppression du plan', 'error');
    }
  };

  // Fonction pour ouvrir le dialogue de confirmation de suppression
  const openConfirmDialog = (type, id, name) => {
    setItemToDelete({ type, id, name });
    setConfirmDialog(true);
  };

  // Fonction pour confirmer la suppression
  const confirmDelete = () => {
    const { type, id } = itemToDelete;

    if (type === 'user') {
      deleteUser(id);
    } else if (type === 'role') {
      deleteRole(id);
    } else if (type === 'plan') {
      deletePlan(id);
    }

    setConfirmDialog(false);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="admin management tabs">
          <Tab label="Utilisateurs" {...a11yProps(0)} />
          <Tab label="Rôles" {...a11yProps(1)} />
          <Tab label="Plans" {...a11yProps(2)} />
        </Tabs>
      </Box>

      {/* Onglet Gestion des Utilisateurs */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Gestion des Utilisateurs</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => openUserDialog()}
          >
            Ajouter un Utilisateur
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Nom d'utilisateur</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Prénom</TableCell>
                <TableCell>Nom</TableCell>
                <TableCell>Rôle</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.first_name}</TableCell>
                  <TableCell>{user.last_name}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => openUserDialog(user)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => openConfirmDialog('user', user.id, user.username)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Onglet Gestion des Rôles */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Gestion des Rôles</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => openRoleDialog()}
          >
            Ajouter un Rôle
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Nom</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Permissions</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>{role.id}</TableCell>
                  <TableCell>{role.name}</TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>{role.permissions?.join(', ')}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => openRoleDialog(role)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => openConfirmDialog('role', role.id, role.name)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Onglet Gestion des Plans */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Gestion des Plans</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => openPlanDialog()}
          >
            Ajouter un Plan
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Nom</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Prix</TableCell>
                <TableCell>Durée (jours)</TableCell>
                <TableCell>Fonctionnalités</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {plans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell>{plan.id}</TableCell>
                  <TableCell>{plan.name}</TableCell>
                  <TableCell>{plan.description}</TableCell>
                  <TableCell>{plan.price}</TableCell>
                  <TableCell>{plan.duration}</TableCell>
                  <TableCell>{plan.features?.join(', ')}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => openPlanDialog(plan)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => openConfirmDialog('plan', plan.id, plan.name)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Dialogue pour ajouter/modifier un utilisateur */}
      <Dialog open={userDialog} onClose={() => setUserDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{currentUser?.id ? 'Modifier l\'utilisateur' : 'Ajouter un utilisateur'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Nom d'utilisateur"
              name="username"
              value={currentUser?.username || ''}
              onChange={handleUserChange}
              fullWidth
            />
            <TextField
              label="Email"
              name="email"
              type="email"
              value={currentUser?.email || ''}
              onChange={handleUserChange}
              fullWidth
            />
            <TextField
              label="Prénom"
              name="first_name"
              value={currentUser?.first_name || ''}
              onChange={handleUserChange}
              fullWidth
            />
            <TextField
              label="Nom"
              name="last_name"
              value={currentUser?.last_name || ''}
              onChange={handleUserChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>Rôle</InputLabel>
              <Select
                name="role"
                value={currentUser?.role || ''}
                onChange={handleUserChange}
                label="Rôle"
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.name}>{role.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
            {!currentUser?.id && (
              <TextField
                label="Mot de passe"
                name="password"
                type="password"
                value={currentUser?.password || ''}
                onChange={handleUserChange}
                fullWidth
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDialog(false)}>Annuler</Button>
          <Button onClick={saveUser} variant="contained" color="primary">Enregistrer</Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue pour ajouter/modifier un rôle */}
      <Dialog open={roleDialog} onClose={() => setRoleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{currentRole?.id ? 'Modifier le rôle' : 'Ajouter un rôle'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Nom"
              name="name"
              value={currentRole?.name || ''}
              onChange={handleRoleChange}
              fullWidth
            />
            <TextField
              label="Description"
              name="description"
              value={currentRole?.description || ''}
              onChange={handleRoleChange}
              fullWidth
              multiline
              rows={3}
            />
            <TextField
              label="Permissions (séparées par des virgules)"
              name="permissions"
              value={Array.isArray(currentRole?.permissions) ? currentRole?.permissions.join(', ') : ''}
              onChange={(e) => {
                const permissions = e.target.value.split(',').map(p => p.trim()).filter(p => p);
                setCurrentRole({
                  ...currentRole,
                  permissions
                });
              }}
              fullWidth
              multiline
              rows={2}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRoleDialog(false)}>Annuler</Button>
          <Button onClick={saveRole} variant="contained" color="primary">Enregistrer</Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue pour ajouter/modifier un plan */}
      <Dialog open={planDialog} onClose={() => setPlanDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{currentPlan?.id ? 'Modifier le plan' : 'Ajouter un plan'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Nom"
              name="name"
              value={currentPlan?.name || ''}
              onChange={handlePlanChange}
              fullWidth
            />
            <TextField
              label="Description"
              name="description"
              value={currentPlan?.description || ''}
              onChange={handlePlanChange}
              fullWidth
              multiline
              rows={3}
            />
            <TextField
              label="Prix"
              name="price"
              type="number"
              value={currentPlan?.price || 0}
              onChange={handlePlanChange}
              fullWidth
            />
            <TextField
              label="Durée (jours)"
              name="duration"
              type="number"
              value={currentPlan?.duration || 30}
              onChange={handlePlanChange}
              fullWidth
            />
            <TextField
              label="Fonctionnalités (séparées par des virgules)"
              name="features"
              value={Array.isArray(currentPlan?.features) ? currentPlan?.features.join(', ') : ''}
              onChange={(e) => {
                const features = e.target.value.split(',').map(f => f.trim()).filter(f => f);
                setCurrentPlan({
                  ...currentPlan,
                  features
                });
              }}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPlanDialog(false)}>Annuler</Button>
          <Button onClick={savePlan} variant="contained" color="primary">Enregistrer</Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation de suppression */}
      <Dialog open={confirmDialog} onClose={() => setConfirmDialog(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer {itemToDelete?.type === 'user' ? 'l\'utilisateur' :
              itemToDelete?.type === 'role' ? 'le rôle' : 'le plan'} "{itemToDelete?.name}" ?
            Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog(false)}>Annuler</Button>
          <Button onClick={confirmDelete} variant="contained" color="error">Supprimer</Button>
        </DialogActions>
      </Dialog>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdminManagement;
