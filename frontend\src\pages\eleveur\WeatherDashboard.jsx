/**
 * Composant de dashboard météorologique pour les éleveurs
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Alert,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  WbSunny as SunnyIcon,
  Cloud as CloudIcon,
  Grain as RainIcon,
  Air as WindIcon,
  Thermostat as TempIcon,
  Water as HumidityIcon,
  Visibility as VisibilityIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  Agriculture as AgricultureIcon,
  Pets as PetsIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import { weatherAPI } from '../../services/api';
import WeatherCard from '../../components/integrations/WeatherCard';

const WeatherDashboard = () => {
  const { user } = useAuth();
  const [weatherData, setWeatherData] = useState(null);
  const [forecast, setForecast] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    loadWeatherData();
    // Actualiser toutes les 10 minutes
    const interval = setInterval(loadWeatherData, 10 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const loadWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les données météo pour la ferme de l'éleveur
      const farmId = user.profile_id; // Assuming farm is linked to user profile

      const [currentResponse, forecastResponse, alertsResponse, recommendationsResponse] = await Promise.all([
        weatherAPI.getCurrentWeather(farmId),
        weatherAPI.getForecast(farmId),
        weatherAPI.getAlerts(farmId),
        weatherAPI.getRecommendations(farmId)
      ]);

      setWeatherData(currentResponse.data);
      setForecast(forecastResponse.data.forecasts || []);
      setAlerts(alertsResponse.data.alerts || []);
      setRecommendations(recommendationsResponse.data.recommendations || []);
      setLastUpdate(new Date());

    } catch (err) {
      setError('Erreur lors du chargement des données météo');
      console.error('Erreur météo:', err);
    } finally {
      setLoading(false);
    }
  };

  const getWeatherIcon = (condition) => {
    switch (condition?.toLowerCase()) {
      case 'clear':
      case 'sunny':
        return <SunnyIcon sx={{ color: '#FFA726' }} />;
      case 'clouds':
      case 'cloudy':
        return <CloudIcon sx={{ color: '#78909C' }} />;
      case 'rain':
      case 'drizzle':
        return <RainIcon sx={{ color: '#42A5F5' }} />;
      default:
        return <SunnyIcon sx={{ color: '#FFA726' }} />;
    }
  };

  const getAlertSeverity = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'minor': return 'info';
      case 'moderate': return 'warning';
      case 'severe': return 'error';
      case 'extreme': return 'error';
      default: return 'info';
    }
  };

  const getRecommendationIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'feeding': return <AgricultureIcon />;
      case 'health': return <PetsIcon />;
      case 'housing': return <CloudIcon />;
      default: return <TrendingUpIcon />;
    }
  };

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des données météo...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Dashboard Météorologique
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {lastUpdate && (
            <Typography variant="body2" color="text.secondary">
              Dernière mise à jour: {lastUpdate.toLocaleTimeString('fr-FR')}
            </Typography>
          )}
          <Tooltip title="Actualiser">
            <IconButton onClick={loadWeatherData}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Alertes météo importantes */}
      {alerts.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Alert severity="warning" icon={<WarningIcon />}>
              <Typography variant="h6" gutterBottom>
                Alertes Météorologiques Actives
              </Typography>
              {alerts.map((alert, index) => (
                <Box key={index} sx={{ mt: 1 }}>
                  <Chip
                    label={alert.event}
                    color={getAlertSeverity(alert.severity)}
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2" component="span">
                    {alert.description}
                  </Typography>
                </Box>
              ))}
            </Alert>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={3}>
        {/* Conditions actuelles */}
        <Grid item xs={12} md={8}>
          <WeatherCard farmId={user.profile_id} />
        </Grid>

        {/* Recommandations pour l'élevage */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recommandations Élevage
              </Typography>
              <List>
                {recommendations.map((rec, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        {getRecommendationIcon(rec.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={rec.title}
                        secondary={rec.description}
                      />
                      <Chip
                        label={rec.priority}
                        color={rec.priority === 'high' ? 'error' : 'default'}
                        size="small"
                      />
                    </ListItem>
                    {index < recommendations.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
                {recommendations.length === 0 && (
                  <ListItem>
                    <ListItemText
                      primary="Aucune recommandation spéciale"
                      secondary="Les conditions météo sont favorables pour l'élevage"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Graphique des tendances */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tendances Météorologiques (5 jours)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={forecast}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(date) => new Date(date).toLocaleDateString('fr-FR', {
                      weekday: 'short',
                      day: 'numeric'
                    })}
                  />
                  <YAxis yAxisId="temp" orientation="left" />
                  <YAxis yAxisId="humidity" orientation="right" />
                  <RechartsTooltip
                    labelFormatter={(date) => new Date(date).toLocaleDateString('fr-FR')}
                    formatter={(value, name) => [
                      name === 'tempMax' || name === 'tempMin' ? `${value}°C` : `${value}%`,
                      name === 'tempMax' ? 'Temp. Max' :
                      name === 'tempMin' ? 'Temp. Min' : 'Humidité'
                    ]}
                  />
                  <Line
                    yAxisId="temp"
                    type="monotone"
                    dataKey="tempMax"
                    stroke="#ff6b6b"
                    strokeWidth={2}
                    name="tempMax"
                  />
                  <Line
                    yAxisId="temp"
                    type="monotone"
                    dataKey="tempMin"
                    stroke="#4ecdc4"
                    strokeWidth={2}
                    name="tempMin"
                  />
                  <Line
                    yAxisId="humidity"
                    type="monotone"
                    dataKey="humidity"
                    stroke="#95a5a6"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="humidity"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Impact sur l'élevage */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Impact sur la Production
              </Typography>
              <Box sx={{ mt: 2 }}>
                {weatherData && (
                  <>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body2">Conditions de ponte:</Typography>
                      <Chip
                        label={
                          weatherData.temperature >= 18 && weatherData.temperature <= 24 && weatherData.humidity <= 70
                            ? 'Optimales'
                            : 'Moyennes'
                        }
                        color={
                          weatherData.temperature >= 18 && weatherData.temperature <= 24 && weatherData.humidity <= 70
                            ? 'success'
                            : 'warning'
                        }
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body2">Stress thermique:</Typography>
                      <Chip
                        label={
                          weatherData.temperature > 30 ? 'Élevé' :
                          weatherData.temperature > 25 ? 'Modéré' : 'Faible'
                        }
                        color={
                          weatherData.temperature > 30 ? 'error' :
                          weatherData.temperature > 25 ? 'warning' : 'success'
                        }
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body2">Risque maladie:</Typography>
                      <Chip
                        label={
                          weatherData.humidity > 80 ? 'Élevé' :
                          weatherData.humidity > 60 ? 'Modéré' : 'Faible'
                        }
                        color={
                          weatherData.humidity > 80 ? 'error' :
                          weatherData.humidity > 60 ? 'warning' : 'success'
                        }
                        size="small"
                      />
                    </Box>
                  </>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Actions recommandées */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions Recommandées
              </Typography>
              <List dense>
                {weatherData && weatherData.temperature > 30 && (
                  <ListItem>
                    <ListItemIcon>
                      <TempIcon color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Température élevée"
                      secondary="Augmenter la ventilation et fournir plus d'eau fraîche"
                    />
                  </ListItem>
                )}
                {weatherData && weatherData.humidity > 70 && (
                  <ListItem>
                    <ListItemIcon>
                      <HumidityIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Humidité élevée"
                      secondary="Améliorer la ventilation pour réduire l'humidité"
                    />
                  </ListItem>
                )}
                {weatherData && weatherData.windSpeed > 15 && (
                  <ListItem>
                    <ListItemIcon>
                      <WindIcon color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Vent fort"
                      secondary="Vérifier la solidité des structures et protéger les volailles"
                    />
                  </ListItem>
                )}
                {alerts.length === 0 && weatherData && weatherData.temperature <= 30 && weatherData.humidity <= 70 && (
                  <ListItem>
                    <ListItemIcon>
                      <SunnyIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Conditions favorables"
                      secondary="Aucune action particulière requise"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default WeatherDashboard;
