import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  CircularProgress,
  Divider,
  Alert,
  useTheme
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format, subDays, subMonths, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import useProductionData from '../../../hooks/useProductionData';

/**
 * Composant de suivi de production pour le dashboard éleveur
 * Affiche des graphiques et statistiques de production par lot
 */
const ProductionTracking = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('month');
  const [poultryType, setPoultryType] = useState('all');
  const [startDate, setStartDate] = useState(subMonths(new Date(), 1));
  const [endDate, setEndDate] = useState(new Date());
  const [lotFilter, setLotFilter] = useState('all');

  // Récupérer les données de production
  const { 
    data: productionData, 
    loading, 
    error, 
    stats, 
    fetchProductionData 
  } = useProductionData({
    startDate: format(startDate, 'yyyy-MM-dd'),
    endDate: format(endDate, 'yyyy-MM-dd'),
    type: poultryType
  });

  // Gérer le changement d'onglet
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Gérer le changement de plage de temps
  const handleTimeRangeChange = (event) => {
    const range = event.target.value;
    setTimeRange(range);
    
    switch (range) {
      case 'week':
        setStartDate(subDays(new Date(), 7));
        break;
      case 'month':
        setStartDate(subMonths(new Date(), 1));
        break;
      case 'quarter':
        setStartDate(subMonths(new Date(), 3));
        break;
      case 'year':
        setStartDate(subMonths(new Date(), 12));
        break;
      default:
        break;
    }
  };

  // Gérer le changement de type de volaille
  const handlePoultryTypeChange = (event) => {
    setPoultryType(event.target.value);
  };

  // Gérer le changement de lot
  const handleLotChange = (event) => {
    setLotFilter(event.target.value);
  };

  // Appliquer les filtres
  const handleApplyFilters = () => {
    fetchProductionData({
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      type: poultryType
    });
  };

  // Données de démonstration pour les graphiques
  const productionByLotData = [
    { lot: 'LOT001', oeufs: 12500, poulets: 0 },
    { lot: 'LOT002', oeufs: 9800, poulets: 0 },
    { lot: 'LOT003', oeufs: 11200, poulets: 0 },
    { lot: 'LOT004', oeufs: 10500, poulets: 0 },
    { lot: 'LOT005', oeufs: 13100, poulets: 0 }
  ];

  const dailyProductionData = [
    { date: '01/01', production: 850 },
    { date: '01/02', production: 870 },
    { date: '01/03', production: 890 },
    { date: '01/04', production: 860 },
    { date: '01/05', production: 880 },
    { date: '01/06', production: 900 },
    { date: '01/07', production: 920 },
    { date: '01/08', production: 910 },
    { date: '01/09', production: 930 },
    { date: '01/10', production: 950 },
    { date: '01/11', production: 940 },
    { date: '01/12', production: 960 },
    { date: '01/13', production: 980 },
    { date: '01/14', production: 970 }
  ];

  const qualityDistributionData = [
    { name: 'Qualité A', value: 70 },
    { name: 'Qualité B', value: 20 },
    { name: 'Qualité C', value: 10 }
  ];

  const COLORS = [theme.palette.primary.main, theme.palette.secondary.main, theme.palette.error.main];

  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Afficher un message d'erreur
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {t('errors.fetchFailed')}: {error.message}
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        {t('production.tracking.title')}
      </Typography>
      <Divider sx={{ mb: 2 }} />

      {/* Filtres */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>{t('production.timeRange')}</InputLabel>
            <Select
              value={timeRange}
              label={t('production.timeRange')}
              onChange={handleTimeRangeChange}
            >
              <MenuItem value="week">{t('production.timeRanges.week')}</MenuItem>
              <MenuItem value="month">{t('production.timeRanges.month')}</MenuItem>
              <MenuItem value="quarter">{t('production.timeRanges.quarter')}</MenuItem>
              <MenuItem value="year">{t('production.timeRanges.year')}</MenuItem>
              <MenuItem value="custom">{t('production.timeRanges.custom')}</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>{t('production.poultryType')}</InputLabel>
            <Select
              value={poultryType}
              label={t('production.poultryType')}
              onChange={handlePoultryTypeChange}
            >
              <MenuItem value="all">{t('production.poultryTypes.all')}</MenuItem>
              <MenuItem value="pondeuse">{t('production.poultryTypes.layers')}</MenuItem>
              <MenuItem value="chair">{t('production.poultryTypes.broilers')}</MenuItem>
              <MenuItem value="dinde">{t('production.poultryTypes.turkeys')}</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DatePicker
              label={t('production.startDate')}
              value={startDate}
              onChange={(newValue) => setStartDate(newValue)}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DatePicker
              label={t('production.endDate')}
              value={endDate}
              onChange={(newValue) => setEndDate(newValue)}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} md={2} sx={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            variant="contained" 
            color="primary" 
            fullWidth
            onClick={handleApplyFilters}
          >
            {t('common.apply')}
          </Button>
        </Grid>
      </Grid>

      {/* Onglets */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="production tracking tabs">
          <Tab label={t('production.tabs.byLot')} />
          <Tab label={t('production.tabs.daily')} />
          <Tab label={t('production.tabs.quality')} />
          <Tab label={t('production.tabs.comparison')} />
        </Tabs>
      </Box>

      {/* Contenu des onglets */}
      <Box sx={{ mt: 2 }}>
        {/* Production par lot */}
        {tabValue === 0 && (
          <Box>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>{t('production.lot')}</InputLabel>
                  <Select
                    value={lotFilter}
                    label={t('production.lot')}
                    onChange={handleLotChange}
                  >
                    <MenuItem value="all">{t('production.allLots')}</MenuItem>
                    <MenuItem value="LOT001">LOT001</MenuItem>
                    <MenuItem value="LOT002">LOT002</MenuItem>
                    <MenuItem value="LOT003">LOT003</MenuItem>
                    <MenuItem value="LOT004">LOT004</MenuItem>
                    <MenuItem value="LOT005">LOT005</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={productionByLotData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="lot" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="oeufs" name={t('production.eggs')} fill={theme.palette.primary.main} />
                <Bar dataKey="poulets" name={t('production.chickens')} fill={theme.palette.secondary.main} />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        )}

        {/* Production quotidienne */}
        {tabValue === 1 && (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={dailyProductionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="production" 
                name={t('production.dailyProduction')} 
                stroke={theme.palette.primary.main} 
                activeDot={{ r: 8 }} 
              />
            </LineChart>
          </ResponsiveContainer>
        )}

        {/* Distribution de qualité */}
        {tabValue === 2 && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={qualityDistributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={150}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {qualityDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                {t('production.qualityDistribution')}
              </Typography>
              <Typography variant="body1" paragraph>
                {t('production.qualityExplanation')}
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('production.qualityStandards')}:
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>{t('production.qualityA')}:</strong> {t('production.qualityADesc')}
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>{t('production.qualityB')}:</strong> {t('production.qualityBDesc')}
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>{t('production.qualityC')}:</strong> {t('production.qualityCDesc')}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        )}

        {/* Comparaison avec périodes précédentes */}
        {tabValue === 3 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              {t('production.comparisonTitle')}
            </Typography>
            <Typography variant="body2" paragraph>
              {t('production.comparisonDesc')}
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              {t('production.comparisonInfo')}
            </Alert>
            <Typography variant="body2">
              {t('production.comingSoon')}
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default ProductionTracking;

