import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  IconButton,
  Chip,
  Alert,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useVetConsultations } from '../../../hooks/useVetConsultations';

const VACCINATION_TYPES = {
  newcastle: 'Maladie de Newcastle',
  bronchite: 'Bronchite infectieuse',
  gumboro: 'Maladie de Gumboro',
  variole: 'Variole aviaire',
  coryza: '<PERSON>za infectieux',
  autre: 'Autre'
};

const VaccinationSchedule = () => {
  const { t } = useTranslation();
  const {
    consultations,
    loading,
    error,
    addVaccination,
    updateVaccination,
    deleteVaccination
  } = useVetConsultations();

  const [vaccinations, setVaccinations] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedVaccination, setSelectedVaccination] = useState(null);
  const [formData, setFormData] = useState({
    type: '',
    date: new Date(),
    lot_concerne: '',
    dose: '',
    rappel_necessaire: false,
    date_rappel: null,
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (consultations) {
      const allVaccinations = consultations
        .filter(consultation => consultation.vaccinations)
        .flatMap(consultation => consultation.vaccinations.map(vaccination => ({
          ...vaccination,
          consultationId: consultation.id
        })))
        .sort((a, b) => new Date(a.date) - new Date(b.date));
      setVaccinations(allVaccinations);
    }
  }, [consultations]);

  const handleOpenDialog = (vaccination = null) => {
    if (vaccination) {
      setSelectedVaccination(vaccination);
      setFormData({
        type: vaccination.type,
        date: new Date(vaccination.date),
        lot_concerne: vaccination.lot_concerne,
        dose: vaccination.dose,
        rappel_necessaire: vaccination.rappel_necessaire,
        date_rappel: vaccination.date_rappel ? new Date(vaccination.date_rappel) : null,
        notes: vaccination.notes || ''
      });
    } else {
      setSelectedVaccination(null);
      setFormData({
        type: '',
        date: new Date(),
        lot_concerne: '',
        dose: '',
        rappel_necessaire: false,
        date_rappel: null,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedVaccination(null);
    setFormData({
      type: '',
      date: new Date(),
      lot_concerne: '',
      dose: '',
      rappel_necessaire: false,
      date_rappel: null,
      notes: ''
    });
  };

  const handleChange = (event) => {
    const { name, value, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'rappel_necessaire' ? checked : value
    }));
  };

  const handleDateChange = (date, field = 'date') => {
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      const vaccinationData = {
        ...formData,
        date: formData.date.toISOString(),
        date_rappel: formData.date_rappel?.toISOString()
      };

      if (selectedVaccination) {
        await updateVaccination(selectedVaccination.consultationId, selectedVaccination.id, vaccinationData);
      } else {
        await addVaccination(vaccinationData);
      }

      setSuccess(true);
      setTimeout(() => {
        handleCloseDialog();
        setSuccess(false);
      }, 2000);
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement de la vaccination:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (vaccination) => {
    if (window.confirm(t('Êtes-vous sûr de vouloir supprimer cette vaccination ?'))) {
      try {
        await deleteVaccination(vaccination.consultationId, vaccination.id);
      } catch (err) {
        console.error('Erreur lors de la suppression de la vaccination:', err);
      }
    }
  };

  const getVaccinationStatus = (vaccination) => {
    const today = new Date();
    const vaccinationDate = new Date(vaccination.date);
    const rappelDate = vaccination.date_rappel ? new Date(vaccination.date_rappel) : null;

    if (today < vaccinationDate) {
      return { color: 'info', label: t('Planifié') };
    } else if (rappelDate && today > rappelDate) {
      return { color: 'error', label: t('Rappel dépassé') };
    } else if (rappelDate && today < rappelDate) {
      return { color: 'warning', label: t('Rappel à venir') };
    } else {
      return { color: 'success', label: t('Effectué') };
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h5">
                {t('Calendrier des Vaccinations')}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
              >
                {t('Nouvelle Vaccination')}
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('Type')}</TableCell>
                    <TableCell>{t('Date')}</TableCell>
                    <TableCell>{t('Lot concerné')}</TableCell>
                    <TableCell>{t('Dose')}</TableCell>
                    <TableCell>{t('Rappel')}</TableCell>
                    <TableCell>{t('Statut')}</TableCell>
                    <TableCell align="center">{t('Actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {vaccinations.map((vaccination) => {
                    const status = getVaccinationStatus(vaccination);
                    return (
                      <TableRow key={vaccination.id}>
                        <TableCell>{t(VACCINATION_TYPES[vaccination.type])}</TableCell>
                        <TableCell>
                          {new Date(vaccination.date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{vaccination.lot_concerne}</TableCell>
                        <TableCell>{vaccination.dose}</TableCell>
                        <TableCell>
                          {vaccination.rappel_necessaire ? (
                            vaccination.date_rappel ? (
                              new Date(vaccination.date_rappel).toLocaleDateString()
                            ) : t('À programmer')
                          ) : t('Non nécessaire')}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={status.label}
                            color={status.color}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(vaccination)}
                            color="primary"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(vaccination)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedVaccination
            ? t('Modifier la vaccination')
            : t('Nouvelle vaccination')}
        </DialogTitle>
        <DialogContent>
          {success ? (
            <Alert severity="success">
              {selectedVaccination
                ? t('Vaccination mise à jour avec succès')
                : t('Vaccination ajoutée avec succès')}
            </Alert>
          ) : (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('Type de vaccination')}
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                >
                  {Object.entries(VACCINATION_TYPES).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {t(value)}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} md={6}>
                <DatePicker
                  label={t('Date de vaccination')}
                  value={formData.date}
                  onChange={(date) => handleDateChange(date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label={t('Lot concerné')}
                  name="lot_concerne"
                  value={formData.lot_concerne}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Dose')}
                  name="dose"
                  value={formData.dose}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('Rappel nécessaire')}
                  name="rappel_necessaire"
                  value={formData.rappel_necessaire}
                  onChange={handleChange}
                >
                  <MenuItem value={true}>{t('Oui')}</MenuItem>
                  <MenuItem value={false}>{t('Non')}</MenuItem>
                </TextField>
              </Grid>

              {formData.rappel_necessaire && (
                <Grid item xs={12}>
                  <DatePicker
                    label={t('Date de rappel')}
                    value={formData.date_rappel}
                    onChange={(date) => handleDateChange(date, 'date_rappel')}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('Notes')}
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {t('Annuler')}
          </Button>
          <Button
            onClick={handleSubmit}
            color="primary"
            disabled={submitting || !formData.type || !formData.date || !formData.lot_concerne || !formData.dose}
          >
            {submitting ? <CircularProgress size={24} /> : t('Enregistrer')}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
};

export default VaccinationSchedule;
