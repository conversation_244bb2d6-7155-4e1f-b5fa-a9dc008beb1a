/**
 * Script de diagnostic et correction des problèmes d'authentification
 * Pour le tableau de bord administrateur de Poultray DZ
 */

// 1. Vérifier le token actuel
const currentToken = localStorage.getItem('token');
console.log('1. Vérification du token actuel:');
console.log('Token présent:', !!currentToken);

// Fonction pour décoder un token JWT sans vérification
function decodeJWT(token) {
  if (!token) return null;
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error('Erreur lors du décodage du token:', e);
    return null;
  }
}

// 2. Analyser le contenu du token
const decodedToken = decodeJWT(currentToken);
console.log('2. Analyse du contenu du token:');
console.log('Token décodé:', decodedToken);

// 3. Vérifier l'expiration du token
let tokenExpired = false;
if (decodedToken && decodedToken.exp) {
  const expirationDate = new Date(decodedToken.exp * 1000);
  const now = new Date();
  tokenExpired = expirationDate < now;
  console.log('3. Vérification de l\'expiration:');
  console.log('Date d\'expiration:', expirationDate.toLocaleString());
  console.log('Date actuelle:', now.toLocaleString());
  console.log('Token expiré:', tokenExpired);
}

// 4. Vérifier le rôle de l'utilisateur
let isAdmin = false;
if (decodedToken && decodedToken.user && decodedToken.user.role) {
  isAdmin = decodedToken.user.role === 'admin';
  console.log('4. Vérification du rôle:');
  console.log('Rôle de l\'utilisateur:', decodedToken.user.role);
  console.log('L\'utilisateur est admin:', isAdmin);
}

// 5. Vérifier les en-têtes des requêtes API
console.log('5. Vérification des en-têtes des requêtes API:');
// Créer une fonction pour intercepter et afficher les en-têtes des requêtes
const originalFetch = window.fetch;
window.fetch = function(url, options = {}) {
  console.log('Requête fetch interceptée:', url);
  console.log('En-têtes:', options.headers);
  return originalFetch(url, options);
};

const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
XMLHttpRequest.prototype.open = function(method, url) {
  this._url = url;
  return originalXHROpen.apply(this, arguments);
};
XMLHttpRequest.prototype.setRequestHeader = function(header, value) {
  if (header === 'x-auth-token') {
    console.log('Requête XHR interceptée:', this._url);
    console.log('En-tête x-auth-token:', value);
  }
  return originalXHRSetRequestHeader.apply(this, arguments);
};

// 6. Corriger les problèmes détectés
console.log('6. Correction des problèmes détectés:');

// Si le token est expiré ou invalide, rediriger vers la page de connexion
if (!currentToken || tokenExpired) {
  console.log('Token manquant ou expiré. Redirection vers la page de connexion...');
  // Décommenter pour activer la redirection
  // window.location.href = '/login';
}

// Si l'utilisateur n'est pas admin, afficher un message
if (!isAdmin) {
  console.log('L\'utilisateur n\'a pas les droits d\'administrateur.');
  // Décommenter pour activer la redirection
  // window.location.href = '/dashboard';
}

// 7. Tester une requête API admin
console.log('7. Test d\'une requête API admin:');
fetch('/api/admin/stats', {
  headers: {
    'Content-Type': 'application/json',
    'x-auth-token': currentToken
  }
})
.then(response => {
  console.log('Statut de la réponse:', response.status);
  if (!response.ok) {
    throw new Error(`Erreur HTTP: ${response.status}`);
  }
  return response.json();
})
.then(data => {
  console.log('Données reçues:', data);
})
.catch(error => {
  console.error('Erreur lors de la requête API:', error);
});

// 8. Instructions pour l'utilisateur
console.log('8. Instructions pour résoudre le problème:');
console.log('- Si le token est expiré: se reconnecter');
console.log('- Si l\'utilisateur n\'est pas admin: se connecter avec un compte administrateur');
console.log('- Si les en-têtes ne contiennent pas le token: vérifier l\'implémentation des intercepteurs Axios');
