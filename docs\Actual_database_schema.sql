-- Adminer 4.8.4 PostgreSQL 17.4 dump
\ connect "poultraydz";
< br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
< tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
    < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
    < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
        < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
        < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > create_sql(
            < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
            < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
            < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                    < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > create_sql(
                        < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                        < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                            < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                    < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                    < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                                        < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                            < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 2.4953 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1199904 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                                < span > $Q = < / span > < span > & #39;ApiConfigs&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                                < / table > < / font > DROP TABLE IF EXISTS "ApiConfigs";
DROP SEQUENCE IF EXISTS "ApiConfigs_id_seq";
CREATE SEQUENCE "ApiConfigs_id_seq" INCREMENT MINVALUE MAXVALUE CACHE;
CREATE TABLE "public"."ApiConfigs" (
    "id" integer DEFAULT nextval('"ApiConfigs_id_seq"') NOT NULL,
    "cle" character varying(255) NOT NULL,
    "valeur" text,
    "description" text,
    "actif" boolean DEFAULT true,
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    "isEnabled" boolean DEFAULT true,
    CONSTRAINT "ApiConfigs_cle_key" UNIQUE ("cle"),
    CONSTRAINT "ApiConfigs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
INSERT INTO "ApiConfigs" (
        "id",
        "cle",
        "valeur",
        "description",
        "actif",
        "createdAt",
        "updatedAt",
        "isEnabled"
    )
VALUES (
        1,
        'OPENAI_API_KEY',
        'your_openai_api_key',
        'Clé API pour OpenAI',
        't',
        '2025-07-07 10:35:08.702+01',
        '2025-07-07 10:35:08.724+01',
        't'
    ),
    (
        2,
        'SMTP_CONFIG',
        '{"host":"smtp.example.com","port":587,"secure":false,"auth":{"user":"<EMAIL>","pass":"default_password"}}',
        'Configuration SMTP pour l''envoi d''emails',
        't',
        '2025-07-07 10:35:08.745+01',
        '2025-07-07 10:35:08.751+01',
        't'
    );
< br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
< tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
    < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
    < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
        < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
        < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > create_sql(
            < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
            < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
            < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                    < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > create_sql(
                        < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                        < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                            < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                    < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                    < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                                        < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                            < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.4482 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1283512 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                                < span > $Q = < / span > < span > & #39;Notifications&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                                < / table > < / font > DROP TABLE IF EXISTS "Notifications";
DROP SEQUENCE IF EXISTS "Notifications_id_seq";
CREATE SEQUENCE "Notifications_id_seq" INCREMENT MINVALUE MAXVALUE CACHE;
CREATE TABLE "public"."Notifications" (
    "id" integer DEFAULT nextval('"Notifications_id_seq"') NOT NULL,
    "type" character varying(255) NOT NULL,
    "message" text NOT NULL,
    "destinataire_id" integer NOT NULL,
    "lu" boolean DEFAULT false,
    "date_creation" timestamptz,
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    CONSTRAINT "Notifications_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "SequelizeMeta";
CREATE TABLE "public"."SequelizeMeta" (
    "name" character varying(255) NOT NULL,
    CONSTRAINT "SequelizeMeta_pkey" PRIMARY KEY ("name")
) WITH (oids = false);
INSERT INTO "SequelizeMeta" ("name")
VALUES ('20231220_create_veterinaires.js'),
    ('20240426_create_general_config.js'),
    ('20240426_create_security_settings.js'),
    ('20240426_create_smtp_config.js'),
    ('20241220_create_feed_management_tables.js'),
    ('20240305_create_homepage_sections.js'),
    ('20240306_add_firebase_uid_to_users.js'),
    ('20240306_add_phone_and_address_to_users.js'),
    ('20240306_add_auth_fields_to_users.js');
< br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
< tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
    < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
    < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
        < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
        < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > create_sql(
            < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
            < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
            < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                    < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > create_sql(
                        < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                        < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                            < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                    < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                    < / table > < / font > < br / > < font size = '1' > < table class = 'xdebug-error xe-warning' dir = 'ltr' border = '1' cellspacing = '0' cellpadding = '1' > < tr > < th align = 'left' bgcolor = '#f57900' colspan = "5" > < span style = 'background-color: #cc0000; color: #fce94f; font-size: x-large;' >(!) < / span > Warning: Trying to access array offset on false in K: \ wamp64 \ apps \ adminer4.8.4 \ adminer -4.8.4.php on line < i > 624 < / i > < / th > < / tr > < tr > < th align = 'left' bgcolor = '#e9b96e' colspan = '5' > Call Stack < / th > < / tr > < tr > < th align = 'center' bgcolor = '#eeeeec' > #</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
                                    < tr > < td bgcolor = '#eeeeec' align = 'center' > 1 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0002 < / td > < td bgcolor = '#eeeeec' align = 'right' > 462712 < / td > < td bgcolor = '#eeeeec' > { main }() < / td > < td title = 'K:\wamp64\apps\adminer4.8.4\index.php' bgcolor = '#eeeeec' >...\ index.php < b >: < / b > 0 < / td > < / tr > < tr > < td bgcolor = '#eeeeec' align = 'center' > 2 < / td > < td bgcolor = '#eeeeec' align = 'center' > 0.0016 < / td > < td bgcolor = '#eeeeec' align = 'right' > 985336 < / td > < td bgcolor = '#eeeeec' > include(
                                        < font color = '#00bb00' > 'K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php</font> )</td><td title=' K: \ wamp64 \ apps \ adminer4.8.4 \ index.php ' bgcolor=' #eeeeec'>...\index.php<b>:</b>69</td></tr>
                                        < tr > < td bgcolor = '#eeeeec' align = 'center' > 3 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > Adminer->dumpTable(
                                            < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span>, <span>$ke = </span><span>0</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1610</td></tr>
                                            < tr > < td bgcolor = '#eeeeec' align = 'center' > 4 < / td > < td bgcolor = '#eeeeec' align = 'center' > 3.6571 < / td > < td bgcolor = '#eeeeec' align = 'right' > 1276184 < / td > < td bgcolor = '#eeeeec' > create_sql(
                                                < span > $Q = < / span > < span > & #39;Users&#39;</span>, <span>$Ma = </span><span>NULL</span>, <span>$Ph = </span><span>&#39;DROP+CREATE&#39;</span> )</td><td title='K:\wamp64\apps\adminer4.8.4\adminer-4.8.4.php' bgcolor='#eeeeec'>...\adminer-4.8.4.php<b>:</b>1259</td></tr>
                                                < / table > < / font > DROP TABLE IF EXISTS "Users";
DROP SEQUENCE IF EXISTS "Users_id_seq";
CREATE SEQUENCE "Users_id_seq" INCREMENT MINVALUE MAXVALUE CACHE;
CREATE TABLE "public"."Users" (
    "id" integer DEFAULT nextval('"Users_id_seq"') NOT NULL,
    "username" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "password" character varying(255) NOT NULL,
    "role" character varying(20) DEFAULT 'eleveur' NOT NULL,
    "first_name" character varying(100),
    "last_name" character varying(100),
    "profile_id" integer,
    "status" character varying(20) DEFAULT 'active',
    "preferences" jsonb DEFAULT '{}',
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    "role_id" integer,
    "subscription_plan_id" integer,
    "firebase_uid" character varying(255),
    "phone" character varying(50),
    "address" text,
    CONSTRAINT "Users_email_key" UNIQUE ("email"),
    CONSTRAINT "Users_email_key1" UNIQUE ("email"),
    CONSTRAINT "Users_email_key10" UNIQUE ("email"),
    CONSTRAINT "Users_email_key11" UNIQUE ("email"),
    CONSTRAINT "Users_email_key12" UNIQUE ("email"),
    CONSTRAINT "Users_email_key2" UNIQUE ("email"),
    CONSTRAINT "Users_email_key3" UNIQUE ("email"),
    CONSTRAINT "Users_email_key4" UNIQUE ("email"),
    CONSTRAINT "Users_email_key5" UNIQUE ("email"),
    CONSTRAINT "Users_email_key6" UNIQUE ("email"),
    CONSTRAINT "Users_email_key7" UNIQUE ("email"),
    CONSTRAINT "Users_email_key8" UNIQUE ("email"),
    CONSTRAINT "Users_email_key9" UNIQUE ("email"),
    CONSTRAINT "Users_firebase_uid_key" UNIQUE ("firebase_uid"),
    CONSTRAINT "Users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "Users_username_key" UNIQUE ("username"),
    CONSTRAINT "Users_username_key1" UNIQUE ("username"),
    CONSTRAINT "Users_username_key10" UNIQUE ("username"),
    CONSTRAINT "Users_username_key11" UNIQUE ("username"),
    CONSTRAINT "Users_username_key12" UNIQUE ("username"),
    CONSTRAINT "Users_username_key2" UNIQUE ("username"),
    CONSTRAINT "Users_username_key3" UNIQUE ("username"),
    CONSTRAINT "Users_username_key4" UNIQUE ("username"),
    CONSTRAINT "Users_username_key5" UNIQUE ("username"),
    CONSTRAINT "Users_username_key6" UNIQUE ("username"),
    CONSTRAINT "Users_username_key7" UNIQUE ("username"),
    CONSTRAINT "Users_username_key8" UNIQUE ("username"),
    CONSTRAINT "Users_username_key9" UNIQUE ("username")
) WITH (oids = false);
INSERT INTO "Users" (
        "id",
        "username",
        "email",
        "password",
        "role",
        "first_name",
        "last_name",
        "profile_id",
        "status",
        "preferences",
        "createdAt",
        "updatedAt",
        "role_id",
        "subscription_plan_id",
        "firebase_uid",
        "phone",
        "address"
    )
VALUES (
        1,
        'admin',
        '<EMAIL>',
        '$2a$10$LKKJaGLZWG5K2B7lIMedVuz6kTOEyrixCsGO.66EGjBzOsAzUfKru',
        'admin',
        NULL,
        NULL,
        NULL,
        'active',
        '{}',
        '2025-05-27 21:48:23.239+01',
        '2025-06-01 00:07:26.869+01',
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
    );
DROP VIEW IF EXISTS "active_permissions";
CREATE TABLE "active_permissions" (
    "id" integer,
    "user_id" integer,
    "resource_type" character varying(100),
    "resource_id" character varying(100),
    "permission_type" character varying(100),
    "granted_by" integer,
    "granted_at" timestamp,
    "expires_at" timestamp,
    "ip_restrictions" text,
    "time_restrictions" text,
    "is_active" boolean,
    "revoked_at" timestamp,
    "revoked_by" integer,
    "revocation_reason" text,
    "username" character varying(100),
    "email" character varying(255)
);
DROP TABLE IF EXISTS "active_sessions";
DROP SEQUENCE IF EXISTS active_sessions_id_seq;
CREATE SEQUENCE active_sessions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."active_sessions" (
    "id" integer DEFAULT nextval('active_sessions_id_seq') NOT NULL,
    "user_id" integer NOT NULL,
    "session_id" character varying(255) NOT NULL,
    "ip_address" inet NOT NULL,
    "user_agent" text,
    "login_time" timestamp DEFAULT CURRENT_TIMESTAMP,
    "last_activity" timestamp DEFAULT CURRENT_TIMESTAMP,
    "expires_at" timestamp NOT NULL,
    "is_active" boolean DEFAULT true,
    "is_suspicious" boolean DEFAULT false,
    "failed_attempts" integer DEFAULT '0',
    "country_code" character varying(2),
    "city" character varying(100),
    CONSTRAINT "active_sessions_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "active_sessions_session_id_key" UNIQUE ("session_id")
) WITH (oids = false);
CREATE INDEX "idx_session_active" ON "public"."active_sessions" USING btree ("is_active", "expires_at");
CREATE INDEX "idx_session_id" ON "public"."active_sessions" USING btree ("session_id");
CREATE INDEX "idx_session_user_id" ON "public"."active_sessions" USING btree ("user_id");
DROP TABLE IF EXISTS "alertes_stock";
DROP SEQUENCE IF EXISTS alertes_stock_id_seq;
CREATE SEQUENCE alertes_stock_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."alertes_stock" (
    "id" integer DEFAULT nextval('alertes_stock_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "type_alerte" enum_alertes_stock_type_alerte NOT NULL,
    "priorite" enum_alertes_stock_priorite DEFAULT 'normale',
    "statut" enum_alertes_stock_statut DEFAULT 'active',
    "titre" character varying(200) NOT NULL,
    "message" text NOT NULL,
    "message_ar" text,
    "donnees_contexte" jsonb DEFAULT '{}',
    "seuil_declenche" jsonb DEFAULT '{"unite": null, "operateur": null, "valeur_seuil": null, "valeur_actuelle": null}',
    "source_donnees" jsonb DEFAULT '{"table_source": null, "champ_surveille": null, "derniere_valeur": null, "id_enregistrement": null}',
    "actions_recommandees" jsonb DEFAULT '[]',
    "actions_entreprises" jsonb DEFAULT '[]',
    "date_declenchement" timestamptz NOT NULL,
    "date_vue" timestamptz,
    "date_traitee" timestamptz,
    "date_expiration" timestamptz,
    "frequence_rappel" integer,
    "nombre_rappels" integer DEFAULT '0',
    "dernier_rappel" timestamptz,
    "canaux_notification" jsonb DEFAULT '{"sms": false, "push": false, "email": false, "whatsapp": false, "dashboard": true}',
    "notifications_envoyees" jsonb DEFAULT '[]',
    "impact_estime" jsonb DEFAULT '{"financier": null, "production": null, "duree_estimee": null, "sante_animaux": null}',
    "cout_inaction" numeric(12, 2),
    "cout_resolution" numeric(12, 2),
    "automatique" boolean DEFAULT true,
    "recurrente" boolean DEFAULT false,
    "conditions_resolution" jsonb DEFAULT '{}',
    "liens_utiles" jsonb DEFAULT '[]',
    "contacts_urgence" jsonb DEFAULT '[]',
    "historique_similaires" jsonb DEFAULT '[]',
    "feedback_eleveur" jsonb DEFAULT '{"timing": null, "utilite": null, "precision": null, "suggestions": null}',
    "tags" jsonb DEFAULT '[]',
    "visible" boolean DEFAULT true,
    "archivee" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "alertes_stock_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "alertes_stock_archivee" ON "public"."alertes_stock" USING btree ("archivee");
CREATE INDEX "alertes_stock_automatique" ON "public"."alertes_stock" USING btree ("automatique");
CREATE INDEX "alertes_stock_date_declenchement" ON "public"."alertes_stock" USING btree ("date_declenchement");
CREATE INDEX "alertes_stock_eleveur_id" ON "public"."alertes_stock" USING btree ("eleveur_id");
CREATE INDEX "alertes_stock_eleveur_id_statut_visible" ON "public"."alertes_stock" USING btree ("eleveur_id", "statut", "visible");
CREATE INDEX "alertes_stock_priorite" ON "public"."alertes_stock" USING btree ("priorite");
CREATE INDEX "alertes_stock_statut" ON "public"."alertes_stock" USING btree ("statut");
CREATE INDEX "alertes_stock_type_alerte" ON "public"."alertes_stock" USING btree ("type_alerte");
CREATE INDEX "alertes_stock_visible" ON "public"."alertes_stock" USING btree ("visible");
COMMENT ON COLUMN "public"."alertes_stock"."eleveur_id" IS 'Référence vers l''éleveur';
COMMENT ON COLUMN "public"."alertes_stock"."type_alerte" IS 'Type d''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."priorite" IS 'Niveau de priorité';
COMMENT ON COLUMN "public"."alertes_stock"."statut" IS 'Statut de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."titre" IS 'Titre de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."message" IS 'Message détaillé de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."message_ar" IS 'Message en arabe';
COMMENT ON COLUMN "public"."alertes_stock"."donnees_contexte" IS 'Données contextuelles de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."seuil_declenche" IS 'Informations sur le seuil qui a déclenché l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."source_donnees" IS 'Source des données qui ont déclenché l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."actions_recommandees" IS 'Actions recommandées pour résoudre l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."actions_entreprises" IS 'Actions déjà entreprises par l''éleveur';
COMMENT ON COLUMN "public"."alertes_stock"."date_declenchement" IS 'Date de déclenchement de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."date_vue" IS 'Date à laquelle l''alerte a été vue';
COMMENT ON COLUMN "public"."alertes_stock"."date_traitee" IS 'Date de traitement de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."date_expiration" IS 'Date d''expiration de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."frequence_rappel" IS 'Fréquence de rappel en heures';
COMMENT ON COLUMN "public"."alertes_stock"."nombre_rappels" IS 'Nombre de rappels envoyés';
COMMENT ON COLUMN "public"."alertes_stock"."dernier_rappel" IS 'Date du dernier rappel';
COMMENT ON COLUMN "public"."alertes_stock"."canaux_notification" IS 'Canaux de notification activés';
COMMENT ON COLUMN "public"."alertes_stock"."notifications_envoyees" IS 'Historique des notifications envoyées';
COMMENT ON COLUMN "public"."alertes_stock"."impact_estime" IS 'Impact estimé de l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."cout_inaction" IS 'Coût estimé de l''inaction en DA';
COMMENT ON COLUMN "public"."alertes_stock"."cout_resolution" IS 'Coût estimé de la résolution en DA';
COMMENT ON COLUMN "public"."alertes_stock"."automatique" IS 'Alerte générée automatiquement';
COMMENT ON COLUMN "public"."alertes_stock"."recurrente" IS 'Alerte récurrente';
COMMENT ON COLUMN "public"."alertes_stock"."conditions_resolution" IS 'Conditions pour considérer l''alerte comme résolue';
COMMENT ON COLUMN "public"."alertes_stock"."liens_utiles" IS 'Liens vers des ressources utiles';
COMMENT ON COLUMN "public"."alertes_stock"."contacts_urgence" IS 'Contacts d''urgence pour ce type d''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."historique_similaires" IS 'Références vers des alertes similaires passées';
COMMENT ON COLUMN "public"."alertes_stock"."feedback_eleveur" IS 'Feedback de l''éleveur sur l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."tags" IS 'Tags pour catégoriser l''alerte';
COMMENT ON COLUMN "public"."alertes_stock"."visible" IS 'Alerte visible dans le dashboard';
COMMENT ON COLUMN "public"."alertes_stock"."archivee" IS 'Alerte archivée';
DROP TABLE IF EXISTS "annonces";
DROP SEQUENCE IF EXISTS annonces_id_seq;
CREATE SEQUENCE annonces_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."annonces" (
    "id" integer DEFAULT nextval('annonces_id_seq') NOT NULL,
    "titre" character varying(255) NOT NULL,
    "description" text,
    "categorie" character varying(100) NOT NULL,
    "prix" numeric(10, 2) NOT NULL,
    "localisation" character varying(255),
    "images" text [] DEFAULT 'ARRAY[]',
    "est_active" boolean DEFAULT true,
    "utilisateur_id" integer NOT NULL,
    "date_creation" timestamptz,
    "date_mise_a_jour" timestamptz,
    CONSTRAINT "annonces_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "apiconfigs";
DROP SEQUENCE IF EXISTS apiconfigs_id_seq;
CREATE SEQUENCE apiconfigs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."apiconfigs" (
    "id" integer DEFAULT nextval('apiconfigs_id_seq') NOT NULL,
    "cle" character varying(255) NOT NULL,
    "valeur" text,
    "description" text,
    "actif" boolean DEFAULT true,
    "createdat" timestamptz NOT NULL,
    "updatedat" timestamptz NOT NULL,
    CONSTRAINT "apiconfigs_cle_key" UNIQUE ("cle"),
    CONSTRAINT "apiconfigs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "audit_logs";
DROP SEQUENCE IF EXISTS audit_logs_id_seq;
CREATE SEQUENCE audit_logs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."audit_logs" (
    "id" integer DEFAULT nextval('audit_logs_id_seq') NOT NULL,
    "user_id" integer,
    "action_type" character varying(100) NOT NULL,
    "resource_type" character varying(100) NOT NULL,
    "resource_id" character varying(100),
    "encrypted_data" text NOT NULL,
    "iv" character varying(32) NOT NULL,
    "integrity_hash" character varying(64) NOT NULL,
    "ip_address" inet,
    "user_agent" text,
    "session_id" character varying(255),
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_audit_action_type" ON "public"."audit_logs" USING btree ("action_type");
CREATE INDEX "idx_audit_created_at" ON "public"."audit_logs" USING btree ("created_at");
CREATE INDEX "idx_audit_resource" ON "public"."audit_logs" USING btree ("resource_type", "resource_id");
CREATE INDEX "idx_audit_user_id" ON "public"."audit_logs" USING btree ("user_id");
DROP TABLE IF EXISTS "blog_posts";
DROP SEQUENCE IF EXISTS blog_posts_id_seq;
CREATE SEQUENCE blog_posts_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."blog_posts" (
    "id" integer DEFAULT nextval('blog_posts_id_seq') NOT NULL,
    "title" character varying(200) NOT NULL,
    "slug" character varying(200) NOT NULL,
    "content" text NOT NULL,
    "excerpt" text,
    "author_id" integer,
    "status" character varying(20) DEFAULT 'draft',
    "tags" jsonb DEFAULT '[]',
    "featured_image" character varying(255),
    "published_at" timestamptz,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "blog_posts_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "blog_posts_slug_key" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key1" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key10" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key11" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key12" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key2" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key3" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key4" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key5" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key6" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key7" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key8" UNIQUE ("slug"),
    CONSTRAINT "blog_posts_slug_key9" UNIQUE ("slug")
) WITH (oids = false);
INSERT INTO "blog_posts" (
        "id",
        "title",
        "slug",
        "content",
        "excerpt",
        "author_id",
        "status",
        "tags",
        "featured_image",
        "published_at",
        "created_at",
        "updated_at"
    )
VALUES (
        1,
        'اطلاق الموقع',
        'atlaq-almwqa',
        'fsgsdfgfsdgdfsgdfgdfdfgdfg',
        '',
        1,
        'draft',
        '"[]"',
        NULL,
        NULL,
        '2025-06-12 20:48:58.753+01',
        '2025-06-12 20:48:58.753+01'
    );
DROP TABLE IF EXISTS "clients";
DROP SEQUENCE IF EXISTS clients_id_seq;
CREATE SEQUENCE clients_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."clients" (
    "id" integer DEFAULT nextval('clients_id_seq') NOT NULL,
    "marchand_id" integer,
    "user_id" integer,
    "name" character varying(100) NOT NULL,
    "email" character varying(255),
    "phone" character varying(20),
    "address" text,
    "notes" text,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "clients_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_clients_marchand_id" ON "public"."clients" USING btree ("marchand_id");
COMMENT ON TABLE "public"."clients" IS 'Clients des marchands';
DELIMITER;
;
CREATE TRIGGER "update_clients_modtime" BEFORE
UPDATE ON "public"."clients" FOR EACH ROW EXECUTE FUNCTION update_modified_column();
;
DELIMITER;
DROP TABLE IF EXISTS "consultations";
DROP SEQUENCE IF EXISTS consultations_id_seq;
CREATE SEQUENCE consultations_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."consultations" (
    "id" integer DEFAULT nextval('consultations_id_seq') NOT NULL,
    "veterinaire_id" integer NOT NULL,
    "eleveur_id" integer NOT NULL,
    "diagnostic" text,
    "notes" text,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "volaille_id" integer NOT NULL,
    "date" timestamptz,
    "symptomes" text,
    "traitement" text,
    "statut" character varying(20) DEFAULT 'en_cours',
    "cout" numeric(10, 2),
    CONSTRAINT "consultations_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_consultations_eleveur_id" ON "public"."consultations" USING btree ("eleveur_id");
CREATE INDEX "idx_consultations_veterinaire_id" ON "public"."consultations" USING btree ("veterinaire_id");
COMMENT ON TABLE "public"."consultations" IS 'Consultations vÃ©tÃ©rinaires';
DROP TABLE IF EXISTS "eleveurs";
DROP SEQUENCE IF EXISTS eleveurs_id_seq;
CREATE SEQUENCE eleveurs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."eleveurs" (
    "id" integer DEFAULT nextval('eleveurs_id_seq') NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "telephone" character varying(20),
    "adresse" text,
    "date_inscription" timestamptz NOT NULL,
    "statut" character varying(20) DEFAULT 'actif',
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    "date_modification" timestamptz NOT NULL,
    "user_id" integer,
    "veterinaire_id" integer,
    "date_naissance" date,
    "experience_annees" integer,
    "specialite" character varying(255),
    "status" character varying(20) DEFAULT 'actif',
    CONSTRAINT "eleveurs_email" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key1" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key10" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key11" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key12" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key13" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key14" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key15" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key16" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key17" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key2" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key3" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key4" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key5" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key6" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key7" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key8" UNIQUE ("email"),
    CONSTRAINT "eleveurs_email_key9" UNIQUE ("email"),
    CONSTRAINT "eleveurs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
INSERT INTO "eleveurs" (
        "id",
        "nom",
        "prenom",
        "email",
        "telephone",
        "adresse",
        "date_inscription",
        "statut",
        "created_at",
        "updated_at",
        "date_modification",
        "user_id",
        "veterinaire_id",
        "date_naissance",
        "experience_annees",
        "specialite",
        "status"
    )
VALUES (
        1,
        'Benali',
        'Mohamed',
        '<EMAIL>',
        '0555123456',
        'Alger, Algérie',
        '2025-05-11 21:28:16.229887+01',
        'actif',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-27 20:44:07.91626+01',
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        'actif'
    ),
    (
        2,
        'Hadj',
        'Karim',
        '<EMAIL>',
        '0555789012',
        'Oran, Algérie',
        '2025-05-11 21:28:16.229887+01',
        'actif',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-27 20:44:07.91626+01',
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        'actif'
    ),
    (
        3,
        'Ziani',
        'Fatima',
        '<EMAIL>',
        '0555345678',
        'Constantine, Algérie',
        '2025-05-11 21:28:16.229887+01',
        'actif',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-25 23:51:40.490325+01',
        '2025-05-27 20:44:07.91626+01',
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        'actif'
    );
DROP TABLE IF EXISTS "favoris";
DROP SEQUENCE IF EXISTS favoris_id_seq;
CREATE SEQUENCE favoris_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."favoris" (
    "id" integer DEFAULT nextval('favoris_id_seq') NOT NULL,
    "utilisateur_id" integer NOT NULL,
    "annonce_id" integer NOT NULL,
    "date_ajout" timestamptz,
    CONSTRAINT "favoris_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "favoris_utilisateur_id_annonce_id" UNIQUE ("utilisateur_id", "annonce_id")
) WITH (oids = false);
DROP TABLE IF EXISTS "feed_alerts";
DROP SEQUENCE IF EXISTS feed_alerts_id_seq;
CREATE SEQUENCE feed_alerts_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_alerts" (
    "id" integer DEFAULT nextval('feed_alerts_id_seq') NOT NULL,
    "farm_id" integer NOT NULL,
    "feed_stock_id" integer,
    "alert_type" character varying(50) NOT NULL,
    "message" text NOT NULL,
    "severity" character varying(20) DEFAULT 'medium' NOT NULL,
    "is_read" boolean DEFAULT false NOT NULL,
    "is_resolved" boolean DEFAULT false NOT NULL,
    "resolved_at" timestamptz,
    "resolved_by" integer,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_alerts_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_alerts_alert_type_severity" ON "public"."feed_alerts" USING btree ("alert_type", "severity");
CREATE INDEX "feed_alerts_farm_id_is_resolved" ON "public"."feed_alerts" USING btree ("farm_id", "is_resolved");
COMMENT ON COLUMN "public"."feed_alerts"."alert_type" IS 'low_stock, expiry_warning, out_of_stock';
COMMENT ON COLUMN "public"."feed_alerts"."severity" IS 'low, medium, high, critical';
DROP TABLE IF EXISTS "feed_composition";
DROP SEQUENCE IF EXISTS feed_composition_id_seq;
CREATE SEQUENCE feed_composition_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_composition" (
    "id" integer DEFAULT nextval('feed_composition_id_seq') NOT NULL,
    "feed_plan_id" integer NOT NULL,
    "feed_item_id" integer NOT NULL,
    "age_week_start" integer NOT NULL,
    "age_week_end" integer NOT NULL,
    "daily_quantity_per_bird" numeric(8, 3) NOT NULL,
    "percentage_of_diet" numeric(5, 2),
    "notes" text,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_composition_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_composition_feed_plan_id_age_week_start" ON "public"."feed_composition" USING btree ("feed_plan_id", "age_week_start");
COMMENT ON COLUMN "public"."feed_composition"."age_week_start" IS 'starting week for this feed';
COMMENT ON COLUMN "public"."feed_composition"."age_week_end" IS 'ending week for this feed';
COMMENT ON COLUMN "public"."feed_composition"."daily_quantity_per_bird" IS 'daily quantity per bird in grams';
COMMENT ON COLUMN "public"."feed_composition"."percentage_of_diet" IS 'percentage of total diet (0-100)';
DROP TABLE IF EXISTS "feed_consumption_logs";
DROP SEQUENCE IF EXISTS feed_consumption_logs_id_seq;
CREATE SEQUENCE feed_consumption_logs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_consumption_logs" (
    "id" integer DEFAULT nextval('feed_consumption_logs_id_seq') NOT NULL,
    "farm_id" integer NOT NULL,
    "feed_stock_id" integer NOT NULL,
    "quantity_consumed" numeric(10, 3) NOT NULL,
    "consumption_date" date NOT NULL,
    "bird_count" integer,
    "consumption_per_bird" numeric(8, 3),
    "building_section" character varying(50),
    "notes" text,
    "recorded_by" integer NOT NULL,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_consumption_logs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_consumption_logs_farm_id_consumption_date" ON "public"."feed_consumption_logs" USING btree ("farm_id", "consumption_date");
CREATE INDEX "feed_consumption_logs_feed_stock_id_consumption_date" ON "public"."feed_consumption_logs" USING btree ("feed_stock_id", "consumption_date");
COMMENT ON COLUMN "public"."feed_consumption_logs"."quantity_consumed" IS 'quantity consumed in kg';
COMMENT ON COLUMN "public"."feed_consumption_logs"."bird_count" IS 'number of birds at time of consumption';
COMMENT ON COLUMN "public"."feed_consumption_logs"."consumption_per_bird" IS 'calculated consumption per bird in grams';
COMMENT ON COLUMN "public"."feed_consumption_logs"."building_section" IS 'specific building or section where feed was used';
DROP TABLE IF EXISTS "feed_items";
DROP SEQUENCE IF EXISTS feed_items_id_seq;
CREATE SEQUENCE feed_items_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_items" (
    "id" integer DEFAULT nextval('feed_items_id_seq') NOT NULL,
    "name" character varying(200) NOT NULL,
    "brand" character varying(100) NOT NULL,
    "category" character varying(50) NOT NULL,
    "description" text,
    "unit_of_measure" character varying(20) DEFAULT 'kg' NOT NULL,
    "nutritional_info" jsonb DEFAULT '{}',
    "recommended_age_min" integer,
    "recommended_age_max" integer,
    "poultry_types" jsonb DEFAULT '[]' NOT NULL,
    "daily_consumption_per_bird" numeric(8, 3),
    "storage_instructions" text,
    "shelf_life_days" integer DEFAULT '90',
    "status" character varying(20) DEFAULT 'active' NOT NULL,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_items_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_items_category_status" ON "public"."feed_items" USING btree ("category", "status");
CREATE INDEX "feed_items_poultry_types" ON "public"."feed_items" USING btree ("poultry_types");
COMMENT ON COLUMN "public"."feed_items"."category" IS 'starter, grower, finisher, layer, breeder';
COMMENT ON COLUMN "public"."feed_items"."unit_of_measure" IS 'kg, tonnes, sacs';
COMMENT ON COLUMN "public"."feed_items"."nutritional_info" IS 'protein, energy, fiber, etc.';
COMMENT ON COLUMN "public"."feed_items"."recommended_age_min" IS 'minimum age in days';
COMMENT ON COLUMN "public"."feed_items"."recommended_age_max" IS 'maximum age in days';
COMMENT ON COLUMN "public"."feed_items"."poultry_types" IS 'poulets, dindes, pondeuses, etc.';
COMMENT ON COLUMN "public"."feed_items"."daily_consumption_per_bird" IS 'average daily consumption per bird in grams';
INSERT INTO "feed_items" (
        "id",
        "name",
        "brand",
        "category",
        "description",
        "unit_of_measure",
        "nutritional_info",
        "recommended_age_min",
        "recommended_age_max",
        "poultry_types",
        "daily_consumption_per_bird",
        "storage_instructions",
        "shelf_life_days",
        "status",
        "created_at",
        "updated_at"
    )
VALUES (
        67,
        'Aliment Poussin Démarrage Premium',
        'ONAB',
        'starter',
        'Aliment complet pour poussins de 0 à 21 jours, riche en protéines et vitamines',
        'kg',
        '{"ash": 6.5, "fat": 4.5, "fiber": 3.5, "energy": 2950, "calcium": 1, "protein": 22, "phosphorus": 0.7}',
        0,
        21,
        '["poulet_chair", "pondeuse"]',
        25.000,
        '{"temperature":"15-25°C","humidity":"<65%","ventilation":"required","protection":"rodents, insects"}',
        90,
        'active',
        '2025-06-15 18:04:36.396+01',
        '2025-06-15 18:04:36.396+01'
    ),
    (
        68,
        'Aliment Croissance Chair',
        'ONAB',
        'grower',
        'Aliment de croissance pour poulets de chair de 22 à 35 jours',
        'kg',
        '{"ash": 6, "fat": 5, "fiber": 4, "energy": 3100, "calcium": 0.9, "protein": 20, "phosphorus": 0.65}',
        22,
        35,
        '["poulet_chair"]',
        80.000,
        '{"temperature":"15-25°C","humidity":"<65%","ventilation":"required","protection":"rodents, insects"}',
        90,
        'active',
        '2025-06-15 18:04:36.396+01',
        '2025-06-15 18:04:36.396+01'
    ),
    (
        69,
        'Aliment Finition Chair Premium',
        'ONAB',
        'finisher',
        'Aliment de finition pour poulets de chair de 36 jours à abattage',
        'kg',
        '{"ash": 5.5, "fat": 5.5, "fiber": 4.5, "energy": 3200, "calcium": 0.85, "protein": 18, "phosphorus": 0.6}',
        36,
        56,
        '["poulet_chair"]',
        120.000,
        '{"temperature":"15-25°C","humidity":"<65%","ventilation":"required","protection":"rodents, insects"}',
        90,
        'active',
        '2025-06-15 18:04:36.396+01',
        '2025-06-15 18:04:36.396+01'
    ),
    (
        70,
        'Aliment Pondeuse Production',
        'ONAB',
        'layer',
        'Aliment complet pour poules pondeuses en production',
        'kg',
        '{"ash": 12, "fat": 3.5, "fiber": 5, "energy": 2750, "calcium": 3.8, "protein": 16.5, "phosphorus": 0.65}',
        140,
        500,
        '["pondeuse"]',
        110.000,
        '{"temperature":"15-25°C","humidity":"<65%","ventilation":"required","protection":"rodents, insects"}',
        90,
        'active',
        '2025-06-15 18:04:36.397+01',
        '2025-06-15 18:04:36.397+01'
    ),
    (
        71,
        'Aliment Dinde Démarrage',
        'ONAB',
        'starter',
        'Aliment spécialisé pour dindonneaux de 0 à 28 jours',
        'kg',
        '{"ash": 7, "fat": 4, "fiber": 3, "energy": 2900, "calcium": 1.2, "protein": 28, "phosphorus": 0.8}',
        0,
        28,
        '["dinde"]',
        35.000,
        '{"temperature":"15-25°C","humidity":"<65%","ventilation":"required","protection":"rodents, insects"}',
        90,
        'active',
        '2025-06-15 18:04:36.397+01',
        '2025-06-15 18:04:36.397+01'
    ),
    (
        72,
        'Complément Vitaminé Premium',
        'VitaPoultry',
        'supplement',
        'Complément vitaminé et minéral pour toutes volailles',
        'kg',
        '{"niacin": 40, "calcium": 25, "vitamin_a": 12000, "vitamin_e": 30, "phosphorus": 12, "vitamin_b1": 2, "vitamin_b2": 6, "vitamin_b6": 4, "vitamin_d3": 2500, "vitamin_k3": 3, "vitamin_b12": 0.02}',
        0,
        365,
        '["poulet_chair", "pondeuse", "dinde", "canard"]',
        2.000,
        '{"temperature":"10-25°C","humidity":"<60%","light":"protected","ventilation":"required"}',
        90,
        'active',
        '2025-06-15 18:04:36.397+01',
        '2025-06-15 18:04:36.397+01'
    );
DROP TABLE IF EXISTS "feed_plans";
DROP SEQUENCE IF EXISTS feed_plans_id_seq;
CREATE SEQUENCE feed_plans_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_plans" (
    "id" integer DEFAULT nextval('feed_plans_id_seq') NOT NULL,
    "farm_id" integer NOT NULL,
    "name" character varying(200) NOT NULL,
    "description" text,
    "poultry_type" character varying(50) NOT NULL,
    "start_date" date NOT NULL,
    "end_date" date,
    "bird_count" integer NOT NULL,
    "feeding_schedule" jsonb DEFAULT '{}' NOT NULL,
    "total_estimated_cost" numeric(12, 2),
    "actual_cost" numeric(12, 2),
    "status" character varying(20) DEFAULT 'active' NOT NULL,
    "created_by" integer NOT NULL,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_plans_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_plans_farm_id_status" ON "public"."feed_plans" USING btree ("farm_id", "status");
CREATE INDEX "feed_plans_poultry_type_start_date" ON "public"."feed_plans" USING btree ("poultry_type", "start_date");
COMMENT ON COLUMN "public"."feed_plans"."poultry_type" IS 'poulets, dindes, pondeuses, etc.';
COMMENT ON COLUMN "public"."feed_plans"."feeding_schedule" IS 'detailed feeding schedule by age/week';
COMMENT ON COLUMN "public"."feed_plans"."status" IS 'active, completed, cancelled';
INSERT INTO "feed_plans" (
        "id",
        "farm_id",
        "name",
        "description",
        "poultry_type",
        "start_date",
        "end_date",
        "bird_count",
        "feeding_schedule",
        "total_estimated_cost",
        "actual_cost",
        "status",
        "created_by",
        "created_at",
        "updated_at"
    )
VALUES (
        19,
        11,
        'Plan Standard Poulet Chair 42 jours',
        'Plan standard optimisé pour poulets de chair',
        'poulet_chair',
        '2025-06-15',
        NULL,
        1000,
        '{"feeding_times": ["07:00", "13:00", "19:00"], "feed_composition": [{"age_end": 21, "age_start": 0, "percentage": 100, "feed_item_id": 1}, {"age_end": 35, "age_start": 22, "percentage": 100, "feed_item_id": 2}, {"age_end": 42, "age_start": 36, "percentage": 100, "feed_item_id": 3}], "nutritional_requirements": {"energy_min": 3000, "calcium_max": 1, "protein_min": 18, "phosphorus_min": 0.6}}',
        45500.00,
        NULL,
        'active',
        1,
        '2025-06-15 18:04:36.465+01',
        '2025-06-15 18:04:36.465+01'
    ),
    (
        20,
        11,
        'Plan Pondeuse Production Intensive',
        'Plan pour poules pondeuses en production',
        'pondeuse',
        '2025-06-15',
        NULL,
        500,
        '{"feeding_times": ["07:00", "17:00"], "feed_composition": [{"age_end": 500, "age_start": 140, "percentage": 95, "feed_item_id": 4}, {"age_end": 500, "age_start": 140, "percentage": 5, "feed_item_id": 6}], "nutritional_requirements": {"energy_min": 2700, "calcium_min": 3.5, "protein_min": 16, "phosphorus_min": 0.6}}',
        90000.00,
        NULL,
        'completed',
        1,
        '2025-06-15 18:04:36.465+01',
        '2025-06-15 18:04:36.465+01'
    ),
    (
        21,
        12,
        'Plan Dinde Croissance Rapide',
        'Plan personnalisé pour dindes de Noël',
        'dinde',
        '2025-06-15',
        NULL,
        200,
        '{"feeding_times": ["06:00", "11:00", "16:00", "20:00"], "feed_composition": [{"age_end": 28, "age_start": 0, "percentage": 100, "feed_item_id": 5}, {"age_end": 56, "age_start": 29, "percentage": 100, "feed_item_id": 2}, {"age_end": 84, "age_start": 57, "percentage": 100, "feed_item_id": 3}], "nutritional_requirements": {"energy_min": 2900, "calcium_max": 1.2, "protein_min": 20, "phosphorus_min": 0.7}}',
        25000.00,
        NULL,
        'draft',
        1,
        '2025-06-15 18:04:36.465+01',
        '2025-06-15 18:04:36.465+01'
    );
DROP TABLE IF EXISTS "feed_stock";
DROP SEQUENCE IF EXISTS feed_stock_id_seq;
CREATE SEQUENCE feed_stock_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_stock" (
    "id" integer DEFAULT nextval('feed_stock_id_seq') NOT NULL,
    "farm_id" integer NOT NULL,
    "feed_item_id" integer NOT NULL,
    "supplier_id" integer,
    "batch_number" character varying(50),
    "quantity_received" numeric(10, 3) NOT NULL,
    "quantity_current" numeric(10, 3) NOT NULL,
    "unit_cost" numeric(10, 2) NOT NULL,
    "total_cost" numeric(12, 2) NOT NULL,
    "purchase_date" date NOT NULL,
    "expiry_date" date,
    "storage_location" character varying(100),
    "minimum_stock_alert" numeric(10, 3),
    "notes" text,
    "status" character varying(20) DEFAULT 'active' NOT NULL,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_stock_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_stock_farm_id_status" ON "public"."feed_stock" USING btree ("farm_id", "status");
CREATE INDEX "feed_stock_feed_item_id_expiry_date" ON "public"."feed_stock" USING btree ("feed_item_id", "expiry_date");
CREATE INDEX "feed_stock_quantity_current_minimum_stock_alert" ON "public"."feed_stock" USING btree ("quantity_current", "minimum_stock_alert");
COMMENT ON COLUMN "public"."feed_stock"."quantity_received" IS 'initial quantity received';
COMMENT ON COLUMN "public"."feed_stock"."quantity_current" IS 'current available quantity';
COMMENT ON COLUMN "public"."feed_stock"."unit_cost" IS 'cost per unit';
COMMENT ON COLUMN "public"."feed_stock"."total_cost" IS 'total cost of the batch';
COMMENT ON COLUMN "public"."feed_stock"."minimum_stock_alert" IS 'minimum quantity before alert';
COMMENT ON COLUMN "public"."feed_stock"."status" IS 'active, depleted, expired';
DROP TABLE IF EXISTS "feed_suppliers";
DROP SEQUENCE IF EXISTS feed_suppliers_id_seq;
CREATE SEQUENCE feed_suppliers_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."feed_suppliers" (
    "id" integer DEFAULT nextval('feed_suppliers_id_seq') NOT NULL,
    "name" character varying(200) NOT NULL,
    "contact_person" character varying(100),
    "phone" character varying(20),
    "email" character varying(255),
    "address" text,
    "wilaya" character varying(50),
    "commune" character varying(50),
    "delivery_zones" jsonb DEFAULT '[]',
    "payment_terms" character varying(100),
    "credit_limit" numeric(12, 2),
    "rating" numeric(3, 2),
    "notes" text,
    "status" character varying(20) DEFAULT 'active' NOT NULL,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT "feed_suppliers_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "feed_suppliers_delivery_zones" ON "public"."feed_suppliers" USING btree ("delivery_zones");
CREATE INDEX "feed_suppliers_wilaya_status" ON "public"."feed_suppliers" USING btree ("wilaya", "status");
COMMENT ON COLUMN "public"."feed_suppliers"."rating" IS 'rating from 1.00 to 5.00';
INSERT INTO "feed_suppliers" (
        "id",
        "name",
        "contact_person",
        "phone",
        "email",
        "address",
        "wilaya",
        "commune",
        "delivery_zones",
        "payment_terms",
        "credit_limit",
        "rating",
        "notes",
        "status",
        "created_at",
        "updated_at"
    )
VALUES (
        51,
        'ONAB - Office National des Aliments du Bétail',
        'Ahmed Benali',
        '+213 21 45 67 89',
        '<EMAIL>',
        'Zone Industrielle, Rouiba, Alger',
        NULL,
        NULL,
        '["Alger", "Blida", "Boumerdes", "Tipaza", "Bouira"]',
        'credit_30',
        500000.00,
        4.50,
        'Fournisseur principal, livraisons régulières, bonne qualité',
        'active',
        '2025-06-15 18:04:36.415+01',
        '2025-06-15 18:04:36.415+01'
    ),
    (
        52,
        'Société Algérienne des Aliments Composés (SAAC)',
        'Fatima Khelifi',
        '+213 25 78 90 12',
        '<EMAIL>',
        'Route Nationale 5, Sétif',
        NULL,
        NULL,
        '["Sétif", "Batna", "Biskra", "Khenchela", "Oum El Bouaghi"]',
        'credit_15',
        300000.00,
        4.20,
        'Spécialisé dans les aliments biologiques, prix compétitifs',
        'active',
        '2025-06-15 18:04:36.415+01',
        '2025-06-15 18:04:36.415+01'
    ),
    (
        53,
        'Groupe Avicole de l''Ouest (GAO)',
        'Mohamed Benaissa',
        '+213 41 23 45 67',
        '<EMAIL>',
        'Zone Industrielle Es-Sénia, Oran',
        NULL,
        NULL,
        '["Oran", "Mostaganem", "Relizane", "Mascara", "Saida"]',
        'credit_30',
        400000.00,
        4.30,
        'Couverture excellente de l''ouest, service client réactif',
        'active',
        '2025-06-15 18:04:36.415+01',
        '2025-06-15 18:04:36.415+01'
    ),
    (
        54,
        'Nutrition Animale du Sud (NAS)',
        'Karim Ouali',
        '+213 29 87 65 43',
        '<EMAIL>',
        'Route de Hassi Messaoud, Ouargla',
        NULL,
        NULL,
        '["Ouargla", "Ghardaïa", "El Oued", "Illizi", "Tamanrasset"]',
        'credit_15',
        200000.00,
        4.00,
        'Seul fournisseur fiable du sud, délais de livraison parfois longs',
        'active',
        '2025-06-15 18:04:36.415+01',
        '2025-06-15 18:04:36.415+01'
    ),
    (
        55,
        'VitaPoultry Algeria',
        'Amina Cherif',
        '+213 21 98 76 54',
        '<EMAIL>',
        'Bab Ezzouar, Alger',
        NULL,
        NULL,
        '["Alger", "Blida", "Boumerdes", "Tipaza", "Médéa"]',
        'credit_7',
        150000.00,
        4.70,
        'Spécialisé en compléments et vitamines, qualité premium',
        'active',
        '2025-06-15 18:04:36.415+01',
        '2025-06-15 18:04:36.415+01'
    );
DROP TABLE IF EXISTS "fermes";
DROP SEQUENCE IF EXISTS fermes_id_seq;
CREATE SEQUENCE fermes_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."fermes" (
    "id" integer DEFAULT nextval('fermes_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "nom" character varying(200) NOT NULL,
    "adresse" text,
    "superficie" numeric(10, 2),
    "capacite_maximale" integer,
    "type_elevage" character varying(50),
    "coordonnees_gps" jsonb DEFAULT '{}',
    "equipements" jsonb DEFAULT '[]',
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamptz DEFAULT now() NOT NULL,
    "updated_at" timestamptz DEFAULT now() NOT NULL,
    CONSTRAINT "fermes_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_fermes_eleveur_id" ON "public"."fermes" USING btree ("eleveur_id");
COMMENT ON TABLE "public"."fermes" IS 'Table des fermes gÃ©rÃ©es par les Ã©leveurs';
INSERT INTO "fermes" (
        "id",
        "eleveur_id",
        "nom",
        "adresse",
        "superficie",
        "capacite_maximale",
        "type_elevage",
        "coordonnees_gps",
        "equipements",
        "status",
        "created_at",
        "updated_at"
    )
VALUES (
        11,
        1,
        'Ferme Avicole El Bahdja',
        'Route Nationale 1, Blida',
        5.50,
        10000,
        'mixte',
        '{"latitude": 36.4203, "longitude": 2.8277}',
        '["incubateurs", "ventilation", "chauffage"]',
        'active',
        '2025-06-15 18:04:36.452+01',
        '2025-06-15 18:04:36.452+01'
    ),
    (
        12,
        2,
        'Exploitation Avicole Moderne',
        'Zone Industrielle, Sétif',
        8.20,
        15000,
        'chair',
        '{"latitude": 36.1906, "longitude": 5.4137}',
        '["automatisation", "ventilation", "alimentation_automatique"]',
        'active',
        '2025-06-15 18:04:36.453+01',
        '2025-06-15 18:04:36.453+01'
    );
DROP TABLE IF EXISTS "fermes_ouvriers";
DROP SEQUENCE IF EXISTS fermes_ouvriers_id_seq;
CREATE SEQUENCE fermes_ouvriers_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."fermes_ouvriers" (
    "id" integer DEFAULT nextval('fermes_ouvriers_id_seq') NOT NULL,
    "ouvrier_id" integer NOT NULL,
    "ferme_id" integer NOT NULL,
    "date_assignation" timestamptz DEFAULT now(),
    "permissions" jsonb DEFAULT '{"lecture": true, "ecriture": true, "saisie_quotidienne": true}',
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamptz DEFAULT now() NOT NULL,
    "updated_at" timestamptz DEFAULT now() NOT NULL,
    CONSTRAINT "fermes_ouvriers_ouvrier_id_ferme_id_key" UNIQUE ("ouvrier_id", "ferme_id"),
    CONSTRAINT "fermes_ouvriers_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_fermes_ouvriers_ferme_id" ON "public"."fermes_ouvriers" USING btree ("ferme_id");
CREATE INDEX "idx_fermes_ouvriers_ouvrier_id" ON "public"."fermes_ouvriers" USING btree ("ouvrier_id");
COMMENT ON TABLE "public"."fermes_ouvriers" IS 'Association entre ouvriers et fermes';
DROP TABLE IF EXISTS "general_config";
DROP SEQUENCE IF EXISTS general_config_id_seq;
CREATE SEQUENCE general_config_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."general_config" (
    "id" integer DEFAULT nextval('general_config_id_seq') NOT NULL,
    "siteName" character varying(255) DEFAULT 'Poultray DZ' NOT NULL,
    "siteDescription" text,
    "contactEmail" character varying(255),
    "contactPhone" character varying(255),
    "address" text,
    "logo" character varying(255),
    "favicon" character varying(255),
    "primaryColor" character varying(255) DEFAULT '#2c5530',
    "secondaryColor" character varying(255) DEFAULT '#e7eae2',
    "defaultLanguage" character varying(255) DEFAULT 'fr' NOT NULL,
    "availableLanguages" text DEFAULT '["fr","ar","en"]' NOT NULL,
    "dateFormat" character varying(255) DEFAULT 'DD/MM/YYYY',
    "timeFormat" character varying(255) DEFAULT 'HH:mm',
    "timezone" character varying(255) DEFAULT 'Africa/Algiers',
    "maintenanceMode" boolean DEFAULT false NOT NULL,
    "maintenanceMessage" text,
    "googleAnalyticsId" character varying(255),
    "facebookPixelId" character varying(255),
    "maxUploadSize" integer DEFAULT '5',
    "allowUserRegistration" boolean DEFAULT true NOT NULL,
    "defaultUserRole" character varying(255) DEFAULT 'user',
    "footerText" text DEFAULT '© Poultray DZ',
    "socialLinks" text,
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    CONSTRAINT "general_config_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
INSERT INTO "general_config" (
        "id",
        "siteName",
        "siteDescription",
        "contactEmail",
        "contactPhone",
        "address",
        "logo",
        "favicon",
        "primaryColor",
        "secondaryColor",
        "defaultLanguage",
        "availableLanguages",
        "dateFormat",
        "timeFormat",
        "timezone",
        "maintenanceMode",
        "maintenanceMessage",
        "googleAnalyticsId",
        "facebookPixelId",
        "maxUploadSize",
        "allowUserRegistration",
        "defaultUserRole",
        "footerText",
        "socialLinks",
        "createdAt",
        "updatedAt"
    )
VALUES (
        1,
        'Poultray DZ',
        'Plateforme de l''industrie avicole en Algérie',
        '<EMAIL>',
        '+213 XXXXXXXXX',
        'Alger, Algérie',
        '/assets/logo.png',
        '/assets/favicon.ico',
        '#2c5530',
        '#e7eae2',
        'fr',
        '["fr","ar","en"]',
        'DD/MM/YYYY',
        'HH:mm',
        'Africa/Algiers',
        'f',
        'Le site est actuellement en maintenance. Merci de revenir plus tard.',
        NULL,
        NULL,
        5,
        't',
        'user',
        '© Poultray DZ - Tous droits réservés',
        '{"facebook":"https://facebook.com/poultraydz","twitter":"https://twitter.com/poultraydz","instagram":"https://instagram.com/poultraydz"}',
        '2025-06-13 12:29:21.605+01',
        '2025-06-13 12:29:21.605+01'
    );
DROP TABLE IF EXISTS "homepage_sections";
DROP SEQUENCE IF EXISTS homepage_sections_id_seq;
CREATE SEQUENCE homepage_sections_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."homepage_sections" (
    "id" integer DEFAULT nextval('homepage_sections_id_seq') NOT NULL,
    "title" character varying(255) NOT NULL,
    "content" text NOT NULL,
    "order" integer DEFAULT '0' NOT NULL,
    "status" enum_homepage_sections_status DEFAULT 'active',
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    CONSTRAINT "homepage_sections_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "homepage_sections_order" ON "public"."homepage_sections" USING btree ("order");
DROP TABLE IF EXISTS "marchands";
DROP SEQUENCE IF EXISTS marchands_id_seq;
CREATE SEQUENCE marchands_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."marchands" (
    "id" integer DEFAULT nextval('marchands_id_seq') NOT NULL,
    "nom" character varying(100) NOT NULL,
    "prenom" character varying(100) NOT NULL,
    "email" character varying(255),
    "telephone" character varying(20),
    "adresse" text,
    "type_commerce" character varying(100),
    "licence_commerce" character varying(100),
    "date_inscription" timestamp DEFAULT now(),
    "status" character varying(20) DEFAULT 'actif',
    "created_at" timestamp DEFAULT now(),
    "updated_at" timestamp DEFAULT now(),
    CONSTRAINT "marchands_email_key" UNIQUE ("email"),
    CONSTRAINT "marchands_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "messages";
DROP SEQUENCE IF EXISTS messages_id_seq;
CREATE SEQUENCE messages_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."messages" (
    "id" integer DEFAULT nextval('messages_id_seq') NOT NULL,
    "expediteur_id" integer,
    "destinataire_id" integer,
    "contenu" text NOT NULL,
    "lu" boolean DEFAULT false,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "messages_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "notes";
DROP SEQUENCE IF EXISTS notes_id_seq;
CREATE SEQUENCE notes_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."notes" (
    "id" integer DEFAULT nextval('notes_id_seq') NOT NULL,
    "evaluateur_id" integer,
    "evalue_id" integer,
    "note" integer,
    "commentaire" text,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "notes_evaluateur_id_evalue_id_key" UNIQUE ("evaluateur_id", "evalue_id"),
    CONSTRAINT "notes_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "notifications";
DROP SEQUENCE IF EXISTS notifications_id_seq;
CREATE SEQUENCE notifications_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."notifications" (
    "id" integer DEFAULT nextval('notifications_id_seq') NOT NULL,
    "user_id" integer NOT NULL,
    "title" character varying(255) NOT NULL,
    "message" text NOT NULL,
    "type" character varying(50) NOT NULL,
    "data" jsonb DEFAULT '{}',
    "is_read" boolean DEFAULT false,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "order_items";
DROP SEQUENCE IF EXISTS order_items_id_seq;
CREATE SEQUENCE order_items_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."order_items" (
    "id" integer DEFAULT nextval('order_items_id_seq') NOT NULL,
    "order_id" integer,
    "product_id" integer,
    "quantity" integer NOT NULL,
    "unit_price" numeric(10, 2) NOT NULL,
    "total_price" numeric(10, 2) NOT NULL,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "order_items_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_order_items_order_id" ON "public"."order_items" USING btree ("order_id");
CREATE INDEX "idx_order_items_product_id" ON "public"."order_items" USING btree ("product_id");
COMMENT ON TABLE "public"."order_items" IS 'Articles dans les commandes';
DELIMITER;
;
CREATE TRIGGER "trigger_calculate_order_item_total" BEFORE
INSERT
    OR
UPDATE ON "public"."order_items" FOR EACH ROW EXECUTE FUNCTION calculate_order_item_total();
;
DELIMITER;
DROP TABLE IF EXISTS "orders";
DROP SEQUENCE IF EXISTS orders_id_seq;
CREATE SEQUENCE orders_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."orders" (
    "id" integer DEFAULT nextval('orders_id_seq') NOT NULL,
    "marchand_id" integer,
    "client_id" integer,
    "order_number" character varying(50) NOT NULL,
    "total_amount" numeric(10, 2) NOT NULL,
    "status" character varying(20) DEFAULT 'pending',
    "payment_status" character varying(20) DEFAULT 'unpaid',
    "payment_method" character varying(50),
    "shipping_address" text,
    "notes" text,
    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "tracking_number" character varying(50),
    "shipping_method" character varying(50),
    "shipping_cost" numeric(10, 2) DEFAULT '0',
    "tax_amount" numeric(10, 2) DEFAULT '0',
    "estimated_delivery_date" timestamptz,
    "actual_delivery_date" timestamptz,
    CONSTRAINT "orders_order_number_key" UNIQUE ("order_number"),
    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_orders_created_at" ON "public"."orders" USING btree ("created_at");
CREATE INDEX "idx_orders_marchand_id" ON "public"."orders" USING btree ("marchand_id");
CREATE INDEX "idx_orders_status" ON "public"."orders" USING btree ("status");
CREATE INDEX "orders_client_id" ON "public"."orders" USING btree ("client_id");
CREATE INDEX "orders_marchand_id" ON "public"."orders" USING btree ("marchand_id");
COMMENT ON TABLE "public"."orders" IS 'Commandes passÃ©es aux marchands';
DELIMITER;
;
CREATE TRIGGER "trigger_generate_order_number" BEFORE
INSERT ON "public"."orders" FOR EACH ROW EXECUTE FUNCTION generate_order_number();
;
CREATE TRIGGER "update_orders_modtime" BEFORE
UPDATE ON "public"."orders" FOR EACH ROW EXECUTE FUNCTION update_modified_column();
;
DELIMITER;
DROP TABLE IF EXISTS "poussins";
DROP SEQUENCE IF EXISTS poussins_id_seq;
CREATE SEQUENCE poussins_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."poussins" (
    "id" integer DEFAULT nextval('poussins_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "lot_numero" character varying(50) NOT NULL,
    "race" character varying(50) NOT NULL,
    "souche" character varying(50),
    "quantite_initiale" integer NOT NULL,
    "quantite_actuelle" integer NOT NULL,
    "date_eclosion" timestamptz NOT NULL,
    "date_arrivee" timestamptz NOT NULL,
    "poids_moyen" numeric(6, 2),
    "poids_objectif" numeric(6, 2),
    "gain_quotidien_moyen" numeric(5, 2),
    "taux_mortalite" numeric(5, 2) DEFAULT '0',
    "mortalite_cumulative" integer DEFAULT '0',
    "statut" enum_poussins_statut DEFAULT 'actif',
    "type_elevage" enum_poussins_type_elevage NOT NULL,
    "batiment" character varying(50),
    "conditions_elevage" jsonb DEFAULT '{"densite": null, "humidite": null, "eclairage": null, "temperature": null, "ventilation": null}',
    "alimentation" jsonb DEFAULT '{"type_aliment": null, "indice_consommation": null, "consommation_quotidienne": null}',
    "vaccinations" jsonb DEFAULT '[]',
    "traitements" jsonb DEFAULT '[]',
    "fournisseur" character varying(100),
    "prix_achat_unitaire" numeric(8, 2),
    "cout_total_actuel" numeric(12, 2),
    "notes" text,
    "alerte_active" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "poussins_eleveur_id_lot_numero" UNIQUE ("eleveur_id", "lot_numero"),
    CONSTRAINT "poussins_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "poussins_date_eclosion" ON "public"."poussins" USING btree ("date_eclosion");
CREATE INDEX "poussins_eleveur_id" ON "public"."poussins" USING btree ("eleveur_id");
CREATE INDEX "poussins_lot_numero" ON "public"."poussins" USING btree ("lot_numero");
CREATE INDEX "poussins_statut" ON "public"."poussins" USING btree ("statut");
CREATE INDEX "poussins_type_elevage" ON "public"."poussins" USING btree ("type_elevage");
COMMENT ON COLUMN "public"."poussins"."eleveur_id" IS 'Référence vers l''éleveur propriétaire';
COMMENT ON COLUMN "public"."poussins"."lot_numero" IS 'Numéro unique du lot de poussins';
COMMENT ON COLUMN "public"."poussins"."race" IS 'Race des poussins (ex: Cobb 500, Ross 308, ISA Brown)';
COMMENT ON COLUMN "public"."poussins"."souche" IS 'Souche spécifique de la race';
COMMENT ON COLUMN "public"."poussins"."quantite_initiale" IS 'Nombre de poussins à l''arrivée';
COMMENT ON COLUMN "public"."poussins"."quantite_actuelle" IS 'Nombre de poussins vivants actuellement';
COMMENT ON COLUMN "public"."poussins"."date_eclosion" IS 'Date d''éclosion des poussins';
COMMENT ON COLUMN "public"."poussins"."date_arrivee" IS 'Date d''arrivée dans l''élevage';
COMMENT ON COLUMN "public"."poussins"."poids_moyen" IS 'Poids moyen du lot en grammes';
COMMENT ON COLUMN "public"."poussins"."poids_objectif" IS 'Poids objectif selon l''âge et la race';
COMMENT ON COLUMN "public"."poussins"."gain_quotidien_moyen" IS 'Gain de poids quotidien moyen en grammes';
COMMENT ON COLUMN "public"."poussins"."taux_mortalite" IS 'Taux de mortalité en pourcentage';
COMMENT ON COLUMN "public"."poussins"."mortalite_cumulative" IS 'Nombre total de morts depuis l''arrivée';
COMMENT ON COLUMN "public"."poussins"."statut" IS 'Statut actuel du lot';
COMMENT ON COLUMN "public"."poussins"."type_elevage" IS 'Destination finale des poussins';
COMMENT ON COLUMN "public"."poussins"."batiment" IS 'Bâtiment ou zone d''élevage';
COMMENT ON COLUMN "public"."poussins"."conditions_elevage" IS 'Conditions d''élevage actuelles';
COMMENT ON COLUMN "public"."poussins"."alimentation" IS 'Données d''alimentation';
COMMENT ON COLUMN "public"."poussins"."vaccinations" IS 'Historique des vaccinations';
COMMENT ON COLUMN "public"."poussins"."traitements" IS 'Historique des traitements médicaux';
COMMENT ON COLUMN "public"."poussins"."fournisseur" IS 'Fournisseur des poussins';
COMMENT ON COLUMN "public"."poussins"."prix_achat_unitaire" IS 'Prix d''achat par poussin en DA';
COMMENT ON COLUMN "public"."poussins"."cout_total_actuel" IS 'Coût total investi (achat + alimentation + soins)';
COMMENT ON COLUMN "public"."poussins"."notes" IS 'Notes et observations de l''éleveur';
COMMENT ON COLUMN "public"."poussins"."alerte_active" IS 'Indique si une alerte est active pour ce lot';
DROP TABLE IF EXISTS "prescriptions";
DROP SEQUENCE IF EXISTS prescriptions_id_seq;
CREATE SEQUENCE prescriptions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."prescriptions" (
    "id" integer DEFAULT nextval('prescriptions_id_seq') NOT NULL,
    "veterinaire_id" integer,
    "eleveur_id" integer,
    "volaille_id" integer,
    "medicament" character varying(255) NOT NULL,
    "dosage" character varying(100) NOT NULL,
    "duree_traitement" character varying(100),
    "instructions" text,
    "diagnostic" text,
    "status" character varying(20) DEFAULT 'active',
    "created_at" timestamp DEFAULT now(),
    "updated_at" timestamp DEFAULT now(),
    CONSTRAINT "prescriptions_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_prescriptions_eleveur_id" ON "public"."prescriptions" USING btree ("eleveur_id");
CREATE INDEX "idx_prescriptions_status" ON "public"."prescriptions" USING btree ("status");
CREATE INDEX "idx_prescriptions_veterinaire_id" ON "public"."prescriptions" USING btree ("veterinaire_id");
COMMENT ON TABLE "public"."prescriptions" IS 'Prescriptions mÃ©dicales pour les animaux';
DROP TABLE IF EXISTS "product_views";
DROP SEQUENCE IF EXISTS product_views_id_seq;
CREATE SEQUENCE product_views_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."product_views" (
    "id" integer DEFAULT nextval('product_views_id_seq') NOT NULL,
    "product_id" integer,
    "user_id" integer,
    "ip_address" character varying(50),
    "view_date" timestamp DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "product_views_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_product_views_product_id" ON "public"."product_views" USING btree ("product_id");
COMMENT ON TABLE "public"."product_views" IS 'Statistiques de consultation des produits';
DROP TABLE IF EXISTS "production_oeufs";
DROP SEQUENCE IF EXISTS production_oeufs_id_seq;
CREATE SEQUENCE production_oeufs_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."production_oeufs" (
    "id" integer DEFAULT nextval('production_oeufs_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "volaille_id" integer,
    "date_production" date NOT NULL,
    "nombre_poules" integer NOT NULL,
    "oeufs_collectes" integer DEFAULT '0' NOT NULL,
    "oeufs_vendables" integer DEFAULT '0' NOT NULL,
    "oeufs_casses" integer DEFAULT '0',
    "oeufs_deformes" integer DEFAULT '0',
    "oeufs_sales" integer DEFAULT '0',
    "oeufs_petits" integer DEFAULT '0',
    "poids_moyen_oeuf" numeric(5, 2),
    "calibre_repartition" jsonb DEFAULT '{"gros": 0, "moyen": 0, "petit": 0, "tres_gros": 0}',
    "couleur_coquille" enum_production_oeufs_couleur_coquille,
    "qualite_coquille" jsonb DEFAULT '{"texture": null, "resistance": null, "epaisseur_moyenne": null}',
    "conditions_collecte" jsonb DEFAULT '{"humidite": null, "heure_collecte": null, "frequence_collecte": null, "temperature_poulailler": null}',
    "alimentation_jour" jsonb DEFAULT '{"quantite_kg": null, "supplements": [], "type_aliment": null}',
    "prix_vente_unitaire" numeric(6, 2),
    "oeufs_vendus" integer DEFAULT '0',
    "stock_restant" integer DEFAULT '0',
    "pertes_diverses" integer DEFAULT '0',
    "observations" text,
    "meteo" jsonb DEFAULT '{"vent": null, "humidite": null, "precipitation": null, "temperature_max": null, "temperature_min": null}',
    "alerte_active" boolean DEFAULT false,
    "validee" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "production_oeufs_eleveur_id_date_production_volaille_id" UNIQUE ("eleveur_id", "date_production", "volaille_id"),
    CONSTRAINT "production_oeufs_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_production_oeufs_date" ON "public"."production_oeufs" USING btree ("date_production");
CREATE INDEX "idx_production_oeufs_eleveur_id" ON "public"."production_oeufs" USING btree ("eleveur_id");
CREATE INDEX "production_oeufs_date_production" ON "public"."production_oeufs" USING btree ("date_production");
CREATE INDEX "production_oeufs_eleveur_id" ON "public"."production_oeufs" USING btree ("eleveur_id");
CREATE INDEX "production_oeufs_validee" ON "public"."production_oeufs" USING btree ("validee");
CREATE INDEX "production_oeufs_volaille_id" ON "public"."production_oeufs" USING btree ("volaille_id");
COMMENT ON COLUMN "public"."production_oeufs"."eleveur_id" IS 'Référence vers l''éleveur propriétaire';
COMMENT ON COLUMN "public"."production_oeufs"."volaille_id" IS 'Référence vers le lot de pondeuses (optionnel)';
COMMENT ON COLUMN "public"."production_oeufs"."date_production" IS 'Date de la production';
COMMENT ON COLUMN "public"."production_oeufs"."nombre_poules" IS 'Nombre de poules pondeuses actives';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_collectes" IS 'Nombre total d''œufs collectés';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_vendables" IS 'Nombre d''œufs de qualité vendable';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_casses" IS 'Nombre d''œufs cassés';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_deformes" IS 'Nombre d''œufs déformés';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_sales" IS 'Nombre d''œufs sales';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_petits" IS 'Nombre d''œufs trop petits';
COMMENT ON COLUMN "public"."production_oeufs"."poids_moyen_oeuf" IS 'Poids moyen d''un œuf en grammes';
COMMENT ON COLUMN "public"."production_oeufs"."calibre_repartition" IS 'Répartition par calibre';
COMMENT ON COLUMN "public"."production_oeufs"."couleur_coquille" IS 'Couleur dominante des coquilles';
COMMENT ON COLUMN "public"."production_oeufs"."qualite_coquille" IS 'Données sur la qualité de la coquille';
COMMENT ON COLUMN "public"."production_oeufs"."conditions_collecte" IS 'Conditions lors de la collecte';
COMMENT ON COLUMN "public"."production_oeufs"."alimentation_jour" IS 'Alimentation du jour';
COMMENT ON COLUMN "public"."production_oeufs"."prix_vente_unitaire" IS 'Prix de vente par œuf en DA';
COMMENT ON COLUMN "public"."production_oeufs"."oeufs_vendus" IS 'Nombre d''œufs vendus ce jour';
COMMENT ON COLUMN "public"."production_oeufs"."stock_restant" IS 'Stock d''œufs restant en fin de journée';
COMMENT ON COLUMN "public"."production_oeufs"."pertes_diverses" IS 'Pertes diverses (vol, prédateurs, etc.)';
COMMENT ON COLUMN "public"."production_oeufs"."observations" IS 'Observations de l''éleveur';
COMMENT ON COLUMN "public"."production_oeufs"."meteo" IS 'Conditions météorologiques du jour';
COMMENT ON COLUMN "public"."production_oeufs"."alerte_active" IS 'Indique si une alerte est active';
COMMENT ON COLUMN "public"."production_oeufs"."validee" IS 'Indique si les données ont été validées';
DROP TABLE IF EXISTS "products";
DROP SEQUENCE IF EXISTS products_id_seq;
CREATE SEQUENCE products_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."products" (
    "id" integer DEFAULT nextval('products_id_seq') NOT NULL,
    "description" text,
    "created_at" timestamptz,
    "updated_at" timestamptz,
    "espece" character varying(100) NOT NULL,
    "titre" character varying(255) NOT NULL,
    "race" character varying(100),
    "quantite" integer NOT NULL,
    "prix_unitaire" numeric(10, 2) NOT NULL,
    "images" text [] DEFAULT 'ARRAY[]',
    "status" enum_products_status DEFAULT 'active',
    "vendeur_id" integer NOT NULL,
    "localisation" character varying(255),
    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_products_status" ON "public"."products" USING btree ("status");
CREATE INDEX "products_espece" ON "public"."products" USING btree ("espece");
CREATE INDEX "products_status" ON "public"."products" USING btree ("status");
CREATE INDEX "products_vendeur_id" ON "public"."products" USING btree ("vendeur_id");
COMMENT ON TABLE "public"."products" IS 'Produits vendus par les marchands';
DELIMITER;
;
CREATE TRIGGER "update_products_modtime" BEFORE
UPDATE ON "public"."products" FOR EACH ROW EXECUTE FUNCTION update_modified_column();
;
DELIMITER;
DROP TABLE IF EXISTS "roles";
DROP SEQUENCE IF EXISTS roles_id_seq;
CREATE SEQUENCE roles_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."roles" (
    "id" integer DEFAULT nextval('roles_id_seq') NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" text,
    "permissions" jsonb DEFAULT '[]',
    "is_active" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "roles_name_key" UNIQUE ("name"),
    CONSTRAINT "roles_name_key1" UNIQUE ("name"),
    CONSTRAINT "roles_name_key10" UNIQUE ("name"),
    CONSTRAINT "roles_name_key11" UNIQUE ("name"),
    CONSTRAINT "roles_name_key12" UNIQUE ("name"),
    CONSTRAINT "roles_name_key13" UNIQUE ("name"),
    CONSTRAINT "roles_name_key14" UNIQUE ("name"),
    CONSTRAINT "roles_name_key15" UNIQUE ("name"),
    CONSTRAINT "roles_name_key16" UNIQUE ("name"),
    CONSTRAINT "roles_name_key17" UNIQUE ("name"),
    CONSTRAINT "roles_name_key2" UNIQUE ("name"),
    CONSTRAINT "roles_name_key3" UNIQUE ("name"),
    CONSTRAINT "roles_name_key4" UNIQUE ("name"),
    CONSTRAINT "roles_name_key5" UNIQUE ("name"),
    CONSTRAINT "roles_name_key6" UNIQUE ("name"),
    CONSTRAINT "roles_name_key7" UNIQUE ("name"),
    CONSTRAINT "roles_name_key8" UNIQUE ("name"),
    CONSTRAINT "roles_name_key9" UNIQUE ("name"),
    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
INSERT INTO "roles" (
        "id",
        "name",
        "description",
        "permissions",
        "is_active",
        "created_at",
        "updated_at"
    )
VALUES (
        1,
        'admin',
        '',
        '["admin_access", "read_sales", "read_volailles", "write_volailles", "write_sales", "read_users", "read_stats", "write_users", "access_ai"]',
        't',
        '2025-05-25 11:10:24.777112+01',
        '2025-05-25 20:25:00.386288+01'
    ),
    (
        2,
        'Admin',
        '',
        '["read_volailles", "read_sales", "write_volailles", "write_sales", "read_stats", "read_users", "write_users", "access_ai", "admin_access"]',
        't',
        '2025-06-03 01:47:37.032+01',
        '2025-06-03 01:47:37.032+01'
    ),
    (
        15,
        'eleveur',
        'Utilisateur de type éleveur',
        '["view_own_data", "manage_poultry", "manage_sales"]',
        't',
        '2025-06-08 21:56:00.545885+01',
        '2025-06-08 21:56:00.545885+01'
    ),
    (
        16,
        'veterinaire',
        'Utilisateur de type vétérinaire',
        '["view_all_poultry_health", "manage_health_records"]',
        't',
        '2025-06-08 21:56:01.452205+01',
        '2025-06-08 21:56:01.452205+01'
    ),
    (
        17,
        'marchand',
        'Utilisateur de type marchand',
        '["manage_products", "manage_marketplace_orders"]',
        't',
        '2025-06-08 21:56:01.469562+01',
        '2025-06-08 21:56:01.469562+01'
    );
DROP TABLE IF EXISTS "saisies_quotidiennes";
DROP SEQUENCE IF EXISTS saisies_quotidiennes_id_seq;
CREATE SEQUENCE saisies_quotidiennes_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."saisies_quotidiennes" (
    "id" integer DEFAULT nextval('saisies_quotidiennes_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "ouvrier_id" integer NOT NULL,
    "ferme_id" integer,
    "volaille_id" integer,
    "date_saisie" date NOT NULL,
    "nombre_morts" integer DEFAULT '0',
    "nombre_malades" integer DEFAULT '0',
    "temperature_moyenne" numeric(5, 2),
    "humidite_moyenne" numeric(5, 2),
    "consommation_eau" numeric(10, 2),
    "consommation_aliment" numeric(10, 2),
    "incidents" text,
    "besoins_materiels" text,
    "observations" text,
    "donnees_supplementaires" jsonb DEFAULT '{}',
    "valide_par_eleveur" boolean DEFAULT false,
    "date_validation" timestamptz,
    "created_at" timestamptz DEFAULT now() NOT NULL,
    "updated_at" timestamptz DEFAULT now() NOT NULL,
    CONSTRAINT "saisies_quotidiennes_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_saisies_quotidiennes_date_saisie" ON "public"."saisies_quotidiennes" USING btree ("date_saisie");
CREATE INDEX "idx_saisies_quotidiennes_eleveur_id" ON "public"."saisies_quotidiennes" USING btree ("eleveur_id");
CREATE INDEX "idx_saisies_quotidiennes_ouvrier_id" ON "public"."saisies_quotidiennes" USING btree ("ouvrier_id");
COMMENT ON TABLE "public"."saisies_quotidiennes" IS 'Saisies quotidiennes effectuÃ©es par les ouvriers';
DROP TABLE IF EXISTS "security_incidents";
DROP SEQUENCE IF EXISTS security_incidents_id_seq;
CREATE SEQUENCE security_incidents_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."security_incidents" (
    "id" integer DEFAULT nextval('security_incidents_id_seq') NOT NULL,
    "user_id" integer,
    "ip_address" inet NOT NULL,
    "incident_type" character varying(50) NOT NULL,
    "description" text,
    "severity_level" integer DEFAULT '1',
    "encrypted_context" text,
    "context_iv" character varying(32),
    "status" character varying(20) DEFAULT 'open',
    "assigned_to" integer,
    "resolution_notes" text,
    "detected_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "resolved_at" timestamp,
    CONSTRAINT "security_incidents_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_incident_ip" ON "public"."security_incidents" USING btree ("ip_address");
CREATE INDEX "idx_incident_severity" ON "public"."security_incidents" USING btree ("severity_level");
CREATE INDEX "idx_incident_type" ON "public"."security_incidents" USING btree ("incident_type");
CREATE INDEX "idx_incident_user_id" ON "public"."security_incidents" USING btree ("user_id");
DROP TABLE IF EXISTS "security_settings";
DROP SEQUENCE IF EXISTS security_settings_id_seq;
CREATE SEQUENCE security_settings_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."security_settings" (
    "id" integer DEFAULT nextval('security_settings_id_seq') NOT NULL,
    "enable2FA" boolean DEFAULT false,
    "sessionTimeout" integer DEFAULT '30',
    "maxLoginAttempts" integer DEFAULT '5',
    "lockoutDuration" integer DEFAULT '15',
    "passwordComplexityRegex" character varying(255),
    "passwordHistoryCount" integer DEFAULT '3',
    "passwordExpiryDays" integer DEFAULT '90',
    "contentSecurityPolicy" text,
    "corsAllowedOrigins" text,
    "logLevel" "enum_security_settings_logLevel" DEFAULT 'info',
    "apiRateLimitingEnabled" boolean DEFAULT true,
    "apiRateLimitRequests" integer DEFAULT '100',
    "apiRateLimitWindowMs" integer DEFAULT '900000',
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    CONSTRAINT "security_settings_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
COMMENT ON COLUMN "public"."security_settings"."enable2FA" IS 'Enable Two-Factor Authentication for all users or admins.';
COMMENT ON COLUMN "public"."security_settings"."sessionTimeout" IS 'Session timeout duration in minutes.';
COMMENT ON COLUMN "public"."security_settings"."maxLoginAttempts" IS 'Maximum number of failed login attempts before lockout.';
COMMENT ON COLUMN "public"."security_settings"."lockoutDuration" IS 'Lockout duration in minutes after too many failed login attempts.';
COMMENT ON COLUMN "public"."security_settings"."passwordComplexityRegex" IS 'Regex for password complexity requirements.';
COMMENT ON COLUMN "public"."security_settings"."passwordHistoryCount" IS 'Number of previous passwords to remember to prevent reuse.';
COMMENT ON COLUMN "public"."security_settings"."passwordExpiryDays" IS 'Number of days after which passwords expire.';
COMMENT ON COLUMN "public"."security_settings"."contentSecurityPolicy" IS 'Content Security Policy (CSP) header value.';
COMMENT ON COLUMN "public"."security_settings"."corsAllowedOrigins" IS 'Allowed origins for CORS.';
COMMENT ON COLUMN "public"."security_settings"."logLevel" IS 'Application logging level.';
INSERT INTO "security_settings" (
        "id",
        "enable2FA",
        "sessionTimeout",
        "maxLoginAttempts",
        "lockoutDuration",
        "passwordComplexityRegex",
        "passwordHistoryCount",
        "passwordExpiryDays",
        "contentSecurityPolicy",
        "corsAllowedOrigins",
        "logLevel",
        "apiRateLimitingEnabled",
        "apiRateLimitRequests",
        "apiRateLimitWindowMs",
        "createdAt",
        "updatedAt"
    )
VALUES (
        1,
        'f',
        40,
        5,
        15,
        '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$',
        3,
        90,
        NULL,
        'http://localhost:3000,http://localhost:5173',
        'info',
        't',
        120,
        900000,
        '2025-06-13 12:29:22.488+01',
        '2025-06-13 12:29:22.488+01'
    );
DROP TABLE IF EXISTS "smtp_configurations";
DROP SEQUENCE IF EXISTS smtp_configurations_id_seq;
CREATE SEQUENCE smtp_configurations_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."smtp_configurations" (
    "id" integer DEFAULT nextval('smtp_configurations_id_seq') NOT NULL,
    "host" character varying(255) NOT NULL,
    "port" integer NOT NULL,
    "secure" boolean DEFAULT true,
    "user" character varying(255) NOT NULL,
    "pass" character varying(255) NOT NULL,
    "fromName" character varying(255),
    "fromEmail" character varying(255) NOT NULL,
    "replyTo" character varying(255),
    "testEmailRecipient" character varying(255),
    "isEnabled" boolean DEFAULT true,
    "createdAt" timestamptz NOT NULL,
    "updatedAt" timestamptz NOT NULL,
    CONSTRAINT "smtp_configurations_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
INSERT INTO "smtp_configurations" (
        "id",
        "host",
        "port",
        "secure",
        "user",
        "pass",
        "fromName",
        "fromEmail",
        "replyTo",
        "testEmailRecipient",
        "isEnabled",
        "createdAt",
        "updatedAt"
    )
VALUES (
        1,
        'smtp.example.com',
        587,
        'f',
        '<EMAIL>',
        'password',
        'Poultray DZ',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        't',
        '2025-06-13 12:29:23.506+01',
        '2025-06-13 12:29:23.506+01'
    );
DROP TABLE IF EXISTS "subscription_plans";
DROP SEQUENCE IF EXISTS subscription_plans_id_seq;
CREATE SEQUENCE subscription_plans_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."subscription_plans" (
    "id" integer DEFAULT nextval('subscription_plans_id_seq') NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" text,
    "price" numeric(10, 2) NOT NULL,
    "duration_days" integer NOT NULL,
    "features" jsonb DEFAULT '[]',
    "is_active" boolean DEFAULT true,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "subscription_plans_name_key" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key1" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key10" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key11" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key12" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key2" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key3" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key4" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key5" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key6" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key7" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key8" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_name_key9" UNIQUE ("name"),
    CONSTRAINT "subscription_plans_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
DROP TABLE IF EXISTS "suivi_veterinaire";
DROP SEQUENCE IF EXISTS suivi_veterinaire_id_seq;
CREATE SEQUENCE suivi_veterinaire_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."suivi_veterinaire" (
    "id" integer DEFAULT nextval('suivi_veterinaire_id_seq') NOT NULL,
    "eleveur_id" integer NOT NULL,
    "veterinaire_id" integer,
    "volaille_id" integer,
    "poussin_id" integer,
    "type_intervention" enum_suivi_veterinaire_type_intervention NOT NULL,
    "date_intervention" timestamptz NOT NULL,
    "date_planifiee" timestamptz,
    "statut" enum_suivi_veterinaire_statut DEFAULT 'planifie',
    "urgence" enum_suivi_veterinaire_urgence DEFAULT 'normale',
    "motif" text NOT NULL,
    "symptomes_observes" jsonb DEFAULT '[]',
    "diagnostic" text,
    "diagnostic_differentiel" jsonb DEFAULT '[]',
    "traitement_prescrit" jsonb DEFAULT '{"posologie": null, "medicaments": [], "precautions": [], "duree_traitement": null, "voie_administration": null}',
    "vaccin_administre" jsonb DEFAULT '{"dose": null, "fabricant": null, "nom_vaccin": null, "numero_lot": null, "site_injection": null, "date_expiration": null, "voie_administration": null}',
    "animaux_concernes" jsonb DEFAULT '{"sexe": null, "age_moyen": null, "poids_moyen": null, "nombre_total": 0, "nombre_traites": 0}',
    "examens_complementaires" jsonb DEFAULT '{"resultats": [], "laboratoire": null, "date_resultats": null, "analyses_demandees": []}',
    "cout_intervention" numeric(10, 2),
    "cout_medicaments" numeric(10, 2),
    "duree_intervention" integer,
    "conditions_intervention" jsonb DEFAULT '{"humidite": null, "materiel_utilise": [], "conditions_hygiene": null, "temperature_ambiante": null}',
    "suivi_post_intervention" jsonb DEFAULT '{"observations_j1": null, "observations_j3": null, "observations_j7": null, "effets_secondaires": [], "date_controle_prevu": null, "efficacite_traitement": null}',
    "recommandations" text,
    "mesures_preventives" jsonb DEFAULT '[]',
    "prochaine_intervention" jsonb DEFAULT '{"type": null, "motif": null, "date_prevue": null, "rappel_active": false}',
    "photos_avant" jsonb DEFAULT '[]',
    "photos_apres" jsonb DEFAULT '[]',
    "documents_joints" jsonb DEFAULT '[]',
    "notes_veterinaire" text,
    "notes_eleveur" text,
    "satisfaction_eleveur" integer,
    "alerte_active" boolean DEFAULT false,
    "confidentiel" boolean DEFAULT false,
    "date_modification" timestamptz NOT NULL,
    "created_at" timestamptz NOT NULL,
    "updated_at" timestamptz NOT NULL,
    CONSTRAINT "suivi_veterinaire_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "suivi_veterinaire_date_intervention" ON "public"."suivi_veterinaire" USING btree ("date_intervention");
CREATE INDEX "suivi_veterinaire_eleveur_id" ON "public"."suivi_veterinaire" USING btree ("eleveur_id");
CREATE INDEX "suivi_veterinaire_poussin_id" ON "public"."suivi_veterinaire" USING btree ("poussin_id");
CREATE INDEX "suivi_veterinaire_statut" ON "public"."suivi_veterinaire" USING btree ("statut");
CREATE INDEX "suivi_veterinaire_type_intervention" ON "public"."suivi_veterinaire" USING btree ("type_intervention");
CREATE INDEX "suivi_veterinaire_urgence" ON "public"."suivi_veterinaire" USING btree ("urgence");
CREATE INDEX "suivi_veterinaire_veterinaire_id" ON "public"."suivi_veterinaire" USING btree ("veterinaire_id");
CREATE INDEX "suivi_veterinaire_volaille_id" ON "public"."suivi_veterinaire" USING btree ("volaille_id");
COMMENT ON COLUMN "public"."suivi_veterinaire"."eleveur_id" IS 'Référence vers l''éleveur';
COMMENT ON COLUMN "public"."suivi_veterinaire"."veterinaire_id" IS 'Référence vers le vétérinaire (optionnel)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."volaille_id" IS 'Référence vers le lot de volailles concerné (optionnel)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."poussin_id" IS 'Référence vers le lot de poussins concerné (optionnel)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."type_intervention" IS 'Type d''intervention vétérinaire';
COMMENT ON COLUMN "public"."suivi_veterinaire"."date_intervention" IS 'Date et heure de l''intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."date_planifiee" IS 'Date planifiée pour l''intervention (si différente)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."statut" IS 'Statut de l''intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."urgence" IS 'Niveau d''urgence';
COMMENT ON COLUMN "public"."suivi_veterinaire"."motif" IS 'Motif de l''intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."symptomes_observes" IS 'Liste des symptômes observés';
COMMENT ON COLUMN "public"."suivi_veterinaire"."diagnostic" IS 'Diagnostic établi par le vétérinaire';
COMMENT ON COLUMN "public"."suivi_veterinaire"."diagnostic_differentiel" IS 'Diagnostics différentiels possibles';
COMMENT ON COLUMN "public"."suivi_veterinaire"."traitement_prescrit" IS 'Détails du traitement prescrit';
COMMENT ON COLUMN "public"."suivi_veterinaire"."vaccin_administre" IS 'Détails du vaccin administré';
COMMENT ON COLUMN "public"."suivi_veterinaire"."animaux_concernes" IS 'Informations sur les animaux concernés';
COMMENT ON COLUMN "public"."suivi_veterinaire"."examens_complementaires" IS 'Examens complémentaires et résultats';
COMMENT ON COLUMN "public"."suivi_veterinaire"."cout_intervention" IS 'Coût de l''intervention en DA';
COMMENT ON COLUMN "public"."suivi_veterinaire"."cout_medicaments" IS 'Coût des médicaments en DA';
COMMENT ON COLUMN "public"."suivi_veterinaire"."duree_intervention" IS 'Durée de l''intervention en minutes';
COMMENT ON COLUMN "public"."suivi_veterinaire"."conditions_intervention" IS 'Conditions lors de l''intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."suivi_post_intervention" IS 'Suivi post-intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."recommandations" IS 'Recommandations du vétérinaire';
COMMENT ON COLUMN "public"."suivi_veterinaire"."mesures_preventives" IS 'Mesures préventives recommandées';
COMMENT ON COLUMN "public"."suivi_veterinaire"."prochaine_intervention" IS 'Planification de la prochaine intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."photos_avant" IS 'Photos avant intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."photos_apres" IS 'Photos après intervention';
COMMENT ON COLUMN "public"."suivi_veterinaire"."documents_joints" IS 'Documents joints (ordonnances, analyses, etc.)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."notes_veterinaire" IS 'Notes privées du vétérinaire';
COMMENT ON COLUMN "public"."suivi_veterinaire"."notes_eleveur" IS 'Notes et observations de l''éleveur';
COMMENT ON COLUMN "public"."suivi_veterinaire"."satisfaction_eleveur" IS 'Note de satisfaction de l''éleveur (1-5)';
COMMENT ON COLUMN "public"."suivi_veterinaire"."alerte_active" IS 'Indique si une alerte est active';
COMMENT ON COLUMN "public"."suivi_veterinaire"."confidentiel" IS 'Intervention confidentielle';
DROP TABLE IF EXISTS "translations";
DROP SEQUENCE IF EXISTS translations_id_seq;
CREATE SEQUENCE translations_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."translations" (
    "id" integer DEFAULT nextval('translations_id_seq') NOT NULL,
    "key" character varying(255) NOT NULL,
    "value" text NOT NULL,
    "language" character varying(10) DEFAULT 'fr' NOT NULL,
    "category" character varying(100) DEFAULT 'general',
    "created_at" timestamp DEFAULT now(),
    "updated_at" timestamp DEFAULT now(),
    CONSTRAINT "translations_key_language_key" UNIQUE ("key", "language"),
    CONSTRAINT "translations_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_translations_category" ON "public"."translations" USING btree ("category");
CREATE INDEX "idx_translations_key" ON "public"."translations" USING btree ("key");
CREATE INDEX "idx_translations_language" ON "public"."translations" USING btree ("language");
INSERT INTO "translations" (
        "id",
        "key",
        "value",
        "language",
        "category",
        "created_at",
        "updated_at"
    )
VALUES (
        1,
        'app.title',
        'Poultray DZ',
        'fr',
        'general',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        2,
        'app.title',
        'Poultray DZ',
        'en',
        'general',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        3,
        'app.title',
        'دواجن الجزائر',
        'ar',
        'general',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        4,
        'nav.dashboard',
        'Tableau de bord',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        5,
        'nav.dashboard',
        'Dashboard',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        6,
        'nav.dashboard',
        'لوحة التحكم',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        7,
        'nav.volailles',
        'Volailles',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        8,
        'nav.volailles',
        'Poultry',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        9,
        'nav.volailles',
        'الدواجن',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        10,
        'nav.ventes',
        'Ventes',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        11,
        'nav.ventes',
        'Sales',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        12,
        'nav.ventes',
        'المبيعات',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        13,
        'nav.eleveurs',
        'Éleveurs',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        14,
        'nav.eleveurs',
        'Breeders',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        15,
        'nav.eleveurs',
        'المربون',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        16,
        'nav.veterinaires',
        'Vétérinaires',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        17,
        'nav.veterinaires',
        'Veterinarians',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        18,
        'nav.veterinaires',
        'الأطباء البيطريون',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        19,
        'nav.marchands',
        'Marchands',
        'fr',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        20,
        'nav.marchands',
        'Merchants',
        'en',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        21,
        'nav.marchands',
        'التجار',
        'ar',
        'navigation',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        22,
        'status.active',
        'Actif',
        'fr',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        23,
        'status.active',
        'Active',
        'en',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        24,
        'status.active',
        'نشط',
        'ar',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        25,
        'status.inactive',
        'Inactif',
        'fr',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        26,
        'status.inactive',
        'Inactive',
        'en',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        27,
        'status.inactive',
        'غير نشط',
        'ar',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        28,
        'status.pending',
        'En attente',
        'fr',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        29,
        'status.pending',
        'Pending',
        'en',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        30,
        'status.pending',
        'في الانتظار',
        'ar',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        31,
        'status.confirmed',
        'Confirmé',
        'fr',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        32,
        'status.confirmed',
        'Confirmed',
        'en',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    ),
    (
        33,
        'status.confirmed',
        'مؤكد',
        'ar',
        'status',
        '2025-05-25 23:03:52.731453',
        '2025-05-25 23:03:52.731453'
    );
DROP TABLE IF EXISTS "user_permissions";
DROP SEQUENCE IF EXISTS user_permissions_id_seq;
CREATE SEQUENCE user_permissions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."user_permissions" (
    "id" integer DEFAULT nextval('user_permissions_id_seq') NOT NULL,
    "user_id" integer NOT NULL,
    "resource_type" character varying(100) NOT NULL,
    "resource_id" character varying(100),
    "permission_type" character varying(100) NOT NULL,
    "granted_by" integer,
    "granted_at" timestamp DEFAULT CURRENT_TIMESTAMP,
    "expires_at" timestamp,
    "ip_restrictions" text,
    "time_restrictions" text,
    "is_active" boolean DEFAULT true,
    "revoked_at" timestamp,
    "revoked_by" integer,
    "revocation_reason" text,
    CONSTRAINT "unique_user_permission" UNIQUE (
        "user_id",
        "resource_type",
        "resource_id",
        "permission_type"
    ),
    CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id")
) WITH (oids = false);
CREATE INDEX "idx_perm_active" ON "public"."user_permissions" USING btree ("is_active", "expires_at");
CREATE INDEX "idx_perm_resource" ON "public"."user_permissions" USING btree ("resource_type", "resource_id");
CREATE INDEX "idx_perm_type" ON "public"."user_permissions" USING btree ("permission_type");
CREATE INDEX "idx_perm_user_id" ON "public"."user_permissions" USING btree ("user_id");
INSERT INTO "user_permissions" (
        "id",
        "user_id",
        "resource_type",
        "resource_id",
        "permission_type",
        "granted_by",
        "granted_at",
        "expires_at",
        "ip_restrictions",
        "time_restrictions",
        "is_active",
        "revoked_at",
        "revoked_by",
        "revocation_reason"
    )
VALUES (
        1,
        1,
        'veterinaires',
        NULL,
        'full_access',
        1,
        '2025-07-04 11:12:23.070306',
        NULL,
        NULL,
        NULL,
        't',
        NULL,
        NULL,
        NULL
    ),
    (
        2,
        6,
        'veterinaires',
        NULL,
        'read_only',
        1,
        '2025-07-04 11:12:23.070306',
        NULL,
        NULL,
        NULL,
        't',
        NULL,
        NULL,
        NULL
    ),
    (
        3,
        2,
        'veterinaires',
        NULL,
        'read_partners',
        1,
        '2025-07-04 11:12:23.070306',
        NULL,
        NULL,
        NULL,
        't',
        NULL,
        NULL,
        NULL
    ),
    (
        4,
        8,
        'veterinaires',
        NULL,
        'read_colleagues',
        1,
        '2025-07-04 11:12:23.070306',
        NULL,
        NULL,
        NULL,
        't',
        NULL,
        NULL,
        NULL
    );
DROP TABLE IF EXISTS "users";
DROP SEQUENCE IF EXISTS users_id_seq;
CREATE SEQUENCE users_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;
CREATE TABLE "public"."users" (
    "id" integer DEFAULT nextval('users_id_seq') NOT NULL,
    "username" character varying(100) NOT NULL,
    "email" character varying(255) NOT NULL,
    "password" character varying(255) NOT NULL,
    "role" character varying(20) DEFAULT 'eleveur' NOT NULL,
    "first_name" character varying(100),
    "last_name" character varying(100),
    "profile_id" integer,
    "created_at" timestamptz NOT NULL,
    "status" character varying(20) DEFAULT 'active',
    "preferences" jsonb DEFAULT '{}',
    "updated_at" timestamptz NOT NULL,
    "role_id" integer,
    "firebase_uid" character varying(255),
    "phone" character varying(50),
    "address" text,
    CONSTRAINT "users_email_key" UNIQUE ("email"),
    CONSTRAINT "users_email_key1" UNIQUE ("email"),
    CONSTRAINT "users_email_key2" UNIQUE ("email"),
    CONSTRAINT "users_email_key3" UNIQUE ("email"),
    CONSTRAINT "users_email_key4" UNIQUE ("email"),
    CONSTRAINT "users_email_key5" UNIQUE ("email"),
    CONSTRAINT "users_firebase_uid_key" UNIQUE ("firebase_uid"),
    CONSTRAINT "users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "users_username_key" UNIQUE ("username"),
    CONSTRAINT "users_username_key1" UNIQUE ("username"),
    CONSTRAINT "users_username_key2" UNIQUE ("username"),
    CONSTRAINT "users_username_key3" UNIQUE ("username"),
    CONSTRAINT "users_username_key4" UNIQUE ("username"),
    CONSTRAINT "users_username_key5" UNIQUE ("username")
) WITH (oids = false);
INSERT INTO "users" (
        "id",
        "username",
        "email",
        "password",
        "role",
        "first_name",
        "last_name",
        "profile_id",
        "created_at",
        "status",
        "preferences",
        "updated_at",
        "role_id",
        "firebase_uid",
        "phone",
        "address"
    )
VALUES (
        1,
        'admin',
        '<EMAIL>',
        '$2a$10$hVuz3sEoWGBLNJsIc3y6qOZGHLzsFJ0lXi01jfdwpqhcgYWQAaFfG',
        'admin',
        'Admin',
        'Poultray',
        NULL,
        '2025-05-11 21:17:51.627143+01',
        'active',
        '{}',
        '2025-06-16 23:35:46.379+01',
        1,
        '96CSpHFAoKaAvBD018vRtRe8qW93',
        NULL,
        NULL
    ),
    (
        6,
        'marchand',
        '<EMAIL>',
        '$2a$10$sbfl5VmS0SRUgBpOIlpgZeVA/QItQiU5oKxw38hi0xla9H.f0gW/K',
        'eleveur',
        '',
        '',
        NULL,
        '2025-06-16 23:52:20.797+01',
        'active',
        '{}',
        '2025-06-21 00:01:44.268+01',
        17,
        'xuWu3IFPx1eZPgUzSPGO3IGBNG32',
        '',
        ''
    ),
    (
        2,
        'eleveur',
        '<EMAIL>',
        '$2a$10$fcVrnlT6xUqW4QByKxPhHuCM1zt8rbME9Ic3stlW2b7RGhwRPGDEm',
        'eleveur',
        '',
        '',
        NULL,
        '2025-05-13 00:21:44.316383+01',
        'active',
        '{}',
        '2025-06-21 09:35:26.727+01',
        15,
        'Uf4xoz4o2Fctt9AFZt3kiomkiWy1',
        NULL,
        NULL
    ),
    (
        8,
        'vetirinaire',
        '<EMAIL>',
        '\\\.Mrq4H3zQ8JY0Xgq7/QH6z3r6Q7JQ1mW',
        'veterinaire',
        '',
        '',
        NULL,
        '2025-06-17 23:47:44.955+01',
        'active',
        '{}',
        '2025-06-20 17:54:12.836+01',
        16,
        'gePyjBdy0uUKiTiQmV6KxmreNqz2',
        '',
        ''
    );
