/**
 * Test script to check the eleveurs and veterinaires tables
 */
const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');

// Read config file
const configPath = path.join(__dirname, '../config/config.json');
const configFile = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const config = configFile.development;

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: 'postgres',
    logging: (sql) => console.log('SQL Query:', sql)
  }
);

async function testTables() {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');

    // Check eleveurs table
    console.log('\n🔍 Testing table: eleveurs');
    const eleveursExist = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'eleveurs'
      );`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (eleveursExist[0].exists) {
      console.log('✅ Table \'eleveurs\' exists.');

      // Get table columns
      const eleveursColumns = await sequelize.query(
        `SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'eleveurs'
        ORDER BY ordinal_position;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      console.log('✅ Table structure:');
      console.table(eleveursColumns);

      // Check if veterinaire_id column exists
      const hasVeterinaireId = eleveursColumns.some(col => col.column_name === 'veterinaire_id');
      console.log(`✅ Has veterinaire_id column: ${hasVeterinaireId}`);

      // Get sample data
      const eleveursSample = await sequelize.query(
        `SELECT * FROM eleveurs LIMIT 1;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (eleveursSample.length > 0) {
        console.log('✅ Sample data:');
        console.log(eleveursSample[0]);
      } else {
        console.log('⚠️ No data found in the table.');
      }
    } else {
      console.log('❌ Table \'eleveurs\' does not exist.');
    }

    // Check veterinaires table
    console.log('\n🔍 Testing table: veterinaires');
    const veterinairesExist = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'veterinaires'
      );`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (veterinairesExist[0].exists) {
      console.log('✅ Table \'veterinaires\' exists.');

      // Get table columns
      const veterinairesColumns = await sequelize.query(
        `SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'veterinaires'
        ORDER BY ordinal_position;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      console.log('✅ Table structure:');
      console.table(veterinairesColumns);

      // Get sample data
      const veterinairesSample = await sequelize.query(
        `SELECT * FROM veterinaires LIMIT 1;`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (veterinairesSample.length > 0) {
        console.log('✅ Sample data:');
        console.log(veterinairesSample[0]);
      } else {
        console.log('⚠️ No data found in the table.');
      }
    } else {
      console.log('❌ Table \'veterinaires\' does not exist.');
    }

  } catch (error) {
    console.error('❌ Error testing database tables:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the tests
testTables();
