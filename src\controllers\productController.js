const { Product, Order, OrderItem, User, Marchand } = require('../models');
const { Op } = require('sequelize');

// Créer un nouveau produit
const createProduct = async (req, res) => {
  try {
    const marchand_id = req.user.id;
    const { nom, description, prix, quantite, categorie, images } = req.body;

    const product = await Product.create({
      marchand_id,
      nom,
      description,
      prix,
      quantite,
      categorie,
      images,
      statut: 'disponible'
    });

    res.status(201).json({
      message: 'Produit créé avec succès',
      product
    });

  } catch (error) {
    console.error('Erreur lors de la création du produit:', error);
    res.status(500).json({
      error: 'Erreur lors de la création du produit'
    });
  }
};

// Mettre à jour un produit
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const marchand_id = req.user.id;
    const updates = req.body;

    const product = await Product.findOne({
      where: { id, marchand_id }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Produit non trouvé'
      });
    }

    await product.update(updates);

    res.json({
      message: 'Produit mis à jour avec succès',
      product
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du produit:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour du produit'
    });
  }
};

// Supprimer un produit
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const marchand_id = req.user.id;

    const product = await Product.findOne({
      where: { id, marchand_id }
    });

    if (!product) {
      return res.status(404).json({
        error: 'Produit non trouvé'
      });
    }

    await product.update({ statut: 'supprimé' });

    res.json({
      message: 'Produit supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression du produit:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du produit'
    });
  }
};

// Récupérer les commandes
const getOrders = async (req, res) => {
  try {
    const marchand_id = req.user.id;
    const { statut } = req.query;

    const whereClause = { marchand_id };
    if (statut) {
      whereClause.statut = statut;
    }

    const orders = await Order.findAll({
      where: whereClause,
      include: [
        {
          model: OrderItem,
          include: [{
            model: Product,
            attributes: ['id', 'nom', 'prix']
          }]
        },
        {
          model: User,
          as: 'client',
          attributes: ['id', 'nom', 'prenom', 'email', 'telephone']
        }
      ],
      order: [['date_commande', 'DESC']]
    });

    res.json(orders);

  } catch (error) {
    console.error('Erreur lors de la récupération des commandes:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des commandes'
    });
  }
};

// Mettre à jour le statut d'une commande
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { statut } = req.body;
    const marchand_id = req.user.id;

    const order = await Order.findOne({
      where: { id, marchand_id }
    });

    if (!order) {
      return res.status(404).json({
        error: 'Commande non trouvée'
      });
    }

    await order.update({
      statut,
      date_modification: new Date()
    });

    res.json({
      message: 'Statut de la commande mis à jour avec succès',
      order
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut de la commande:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour du statut de la commande'
    });
  }
};

// Récupérer les statistiques des ventes
const getSalesStats = async (req, res) => {
  try {
    const marchand_id = req.user.id;
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Ventes du mois en cours
    const monthlyStats = await Order.findAll({
      where: {
        marchand_id,
        date_commande: { [Op.gte]: startOfMonth },
        statut: 'terminée'
      },
      attributes: [
        [sequelize.fn('DATE', sequelize.col('date_commande')), 'date'],
        [sequelize.fn('SUM', sequelize.col('montant_total')), 'total']
      ],
      group: [sequelize.fn('DATE', sequelize.col('date_commande'))],
      order: [[sequelize.fn('DATE', sequelize.col('date_commande')), 'ASC']]
    });

    // Total des ventes
    const totalSales = await Order.sum('montant_total', {
      where: {
        marchand_id,
        statut: 'terminée'
      }
    });

    // Produits les plus vendus
    const topProducts = await OrderItem.findAll({
      include: [{
        model: Order,
        where: {
          marchand_id,
          statut: 'terminée'
        },
        attributes: []
      }, {
        model: Product,
        attributes: ['id', 'nom']
      }],
      attributes: [
        'product_id',
        [sequelize.fn('SUM', sequelize.col('quantite')), 'total_vendu']
      ],
      group: ['product_id'],
      order: [[sequelize.fn('SUM', sequelize.col('quantite')), 'DESC']],
      limit: 5
    });

    res.json({
      monthlyStats,
      totalSales,
      topProducts
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de vente:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques de vente'
    });
  }
};

module.exports = {
  createProduct,
  updateProduct,
  deleteProduct,
  getOrders,
  updateOrderStatus,
  getSalesStats
};