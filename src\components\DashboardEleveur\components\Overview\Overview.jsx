import React from 'react';
import { Typo<PERSON>, Grid, Box, Card, CardContent, Divider } from '@mui/material';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

const Overview = ({ data }) => {
  // Données fictives pour la démonstration
  const mockData = {
    stats: {
      totalVolailles: 5280,
      productionJournaliere: 1850,
      tauxMortalite: 1.2,
      revenuMensuel: 125000
    },
    tendances: {
      production: [
        { mois: 'Jan', quantite: 1200 },
        { mois: 'Fév', quantite: 1350 },
        { mois: 'Mar', quantite: 1400 },
        { mois: 'Avr', quantite: 1650 },
        { mois: 'Mai', quantite: 1500 },
        { mois: 'Juin', quantite: 1850 }
      ],
      sante: [
        { semaine: 'S1', taux: 0.8 },
        { semaine: 'S2', taux: 1.0 },
        { semaine: 'S3', taux: 1.5 },
        { semaine: 'S4', taux: 1.2 },
        { semaine: 'S5', taux: 0.9 },
        { semaine: 'S6', taux: 1.2 }
      ]
    }
  };

  // Utiliser les données réelles si disponibles, sinon utiliser les données fictives
  const statsData = data?.stats || mockData.stats;
  const tendancesData = data?.tendances || mockData.tendances;

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Vue d'ensemble</Typography>
      <Divider sx={{ mb: 2 }} />

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card className="stat-card">
            <CardContent>
              <Typography className="stat-card-title">Total Volailles</Typography>
              <Typography className="stat-card-value">{statsData.totalVolailles.toLocaleString()}</Typography>
              <Box className="stat-card-trend">
                <ArrowUpward fontSize="small" className="trend-up" />
                <Typography variant="body2" className="trend-up">+5% depuis le mois dernier</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="stat-card">
            <CardContent>
              <Typography className="stat-card-title">Production Journalière</Typography>
              <Typography className="stat-card-value">{statsData.productionJournaliere.toLocaleString()} œufs</Typography>
              <Box className="stat-card-trend">
                <ArrowUpward fontSize="small" className="trend-up" />
                <Typography variant="body2" className="trend-up">+12% depuis la semaine dernière</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="stat-card">
            <CardContent>
              <Typography className="stat-card-title">Taux de Mortalité</Typography>
              <Typography className="stat-card-value">{statsData.tauxMortalite}%</Typography>
              <Box className="stat-card-trend">
                <ArrowDownward fontSize="small" className="trend-down" />
                <Typography variant="body2" className="trend-down">+0.3% depuis la semaine dernière</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="stat-card">
            <CardContent>
              <Typography className="stat-card-title">Revenu Mensuel</Typography>
              <Typography className="stat-card-value">{statsData.revenuMensuel.toLocaleString()} DZD</Typography>
              <Box className="stat-card-trend">
                <ArrowUpward fontSize="small" className="trend-up" />
                <Typography variant="body2" className="trend-up">+8% depuis le mois dernier</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Graphiques */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>Tendance de Production</Typography>
          <div className="chart-container">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={tendancesData.production}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mois" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="quantite" fill="#3B82F6" name="Production (œufs)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>Taux de Mortalité</Typography>
          <div className="chart-container">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={tendancesData.sante}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="semaine" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="taux" stroke="#EF4444" name="Taux de mortalité (%)" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Overview;