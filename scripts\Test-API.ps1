# Test-API.ps1
# Script pour tester l'accessibilité de l'API

$baseUrls = @(
    "http://localhost:3003",
    "http://127.0.0.1:3003",
    "http://*************:3003"
)

$endpoints = @(
    "",
    "/api",
    "/api/health",
    "/api/auth",
    "/api/eleveur",
    "/api/veterinaire"
)

function Test-Endpoint {
    param (
        [string]$url
    )

    Write-Host "`nTesting: $url" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $url -Method Get -UseBasicParsing -TimeoutSec 5
        Write-Host "✅ Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
        Write-Host "Response: $($response.Content)"
    }
    catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ("=" * 50)
}

Write-Host "🔍 API Accessibility Test`n" -ForegroundColor Yellow

# Test port accessibility first
foreach ($baseUrl in $baseUrls) {
    $uri = [System.Uri]$baseUrl
    Write-Host "`nChecking port accessibility for $($uri.Host):$($uri.Port)..." -ForegroundColor Cyan
    try {
        $portTest = Test-NetConnection -ComputerName $uri.Host -Port $uri.Port -WarningAction SilentlyContinue
        if ($portTest.TcpTestSucceeded) {
            Write-Host "✅ Port $($uri.Port) is accessible" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Port $($uri.Port) is not accessible" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error testing port: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test each endpoint
foreach ($baseUrl in $baseUrls) {
    Write-Host "`nTesting endpoints for $baseUrl" -ForegroundColor Yellow
    foreach ($endpoint in $endpoints) {
        Test-Endpoint -url "$baseUrl$endpoint"
    }
}
