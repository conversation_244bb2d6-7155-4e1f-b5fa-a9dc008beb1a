'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create feed_items table
    await queryInterface.createTable('feed_items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      brand: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      category: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'starter, grower, finisher, layer, breeder'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      unit_of_measure: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'kg',
        comment: 'kg, tonnes, sacs'
      },
      nutritional_info: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {},
        comment: 'protein, energy, fiber, etc.'
      },
      recommended_age_min: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'minimum age in days'
      },
      recommended_age_max: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'maximum age in days'
      },
      poultry_types: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: [],
        comment: 'poulets, dindes, pondeuses, etc.'
      },
      daily_consumption_per_bird: {
        type: Sequelize.DECIMAL(8, 3),
        allowNull: true,
        comment: 'average daily consumption per bird in grams'
      },
      storage_instructions: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      shelf_life_days: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 90
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'active'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_suppliers table
    await queryInterface.createTable('feed_suppliers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      contact_person: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      wilaya: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      commune: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      delivery_zones: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      payment_terms: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      credit_limit: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      rating: {
        type: Sequelize.DECIMAL(3, 2),
        allowNull: true,
        comment: 'rating from 1.00 to 5.00'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'active'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_stock table
    await queryInterface.createTable('feed_stock', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      farm_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'fermes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feed_item_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'feed_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      supplier_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'feed_suppliers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      batch_number: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      quantity_received: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        comment: 'initial quantity received'
      },
      quantity_current: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        comment: 'current available quantity'
      },
      unit_cost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'cost per unit'
      },
      total_cost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
        comment: 'total cost of the batch'
      },
      purchase_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      expiry_date: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      storage_location: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      minimum_stock_alert: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true,
        comment: 'minimum quantity before alert'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'active',
        comment: 'active, depleted, expired'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_consumption_logs table
    await queryInterface.createTable('feed_consumption_logs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      farm_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'fermes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feed_stock_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'feed_stock',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity_consumed: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        comment: 'quantity consumed in kg'
      },
      consumption_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      bird_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'number of birds at time of consumption'
      },
      consumption_per_bird: {
        type: Sequelize.DECIMAL(8, 3),
        allowNull: true,
        comment: 'calculated consumption per bird in grams'
      },
      building_section: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'specific building or section where feed was used'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      recorded_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_plans table
    await queryInterface.createTable('feed_plans', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      farm_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'fermes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      poultry_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'poulets, dindes, pondeuses, etc.'
      },
      start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      end_date: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      bird_count: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      feeding_schedule: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'detailed feeding schedule by age/week'
      },
      total_estimated_cost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      actual_cost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'active',
        comment: 'active, completed, cancelled'
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_composition table (for feed plan details)
    await queryInterface.createTable('feed_composition', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      feed_plan_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'feed_plans',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feed_item_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'feed_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      age_week_start: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'starting week for this feed'
      },
      age_week_end: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'ending week for this feed'
      },
      daily_quantity_per_bird: {
        type: Sequelize.DECIMAL(8, 3),
        allowNull: false,
        comment: 'daily quantity per bird in grams'
      },
      percentage_of_diet: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true,
        comment: 'percentage of total diet (0-100)'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create feed_alerts table
    await queryInterface.createTable('feed_alerts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      farm_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'fermes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      feed_stock_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'feed_stock',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      alert_type: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'low_stock, expiry_warning, out_of_stock'
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      severity: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'medium',
        comment: 'low, medium, high, critical'
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      is_resolved: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      resolved_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      resolved_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('feed_items', ['category', 'status']);
    await queryInterface.addIndex('feed_items', ['poultry_types'], {
      using: 'gin'
    });
    
    await queryInterface.addIndex('feed_suppliers', ['wilaya', 'status']);
    await queryInterface.addIndex('feed_suppliers', ['delivery_zones'], {
      using: 'gin'
    });
    
    await queryInterface.addIndex('feed_stock', ['farm_id', 'status']);
    await queryInterface.addIndex('feed_stock', ['feed_item_id', 'expiry_date']);
    await queryInterface.addIndex('feed_stock', ['quantity_current', 'minimum_stock_alert']);
    
    await queryInterface.addIndex('feed_consumption_logs', ['farm_id', 'consumption_date']);
    await queryInterface.addIndex('feed_consumption_logs', ['feed_stock_id', 'consumption_date']);
    
    await queryInterface.addIndex('feed_plans', ['farm_id', 'status']);
    await queryInterface.addIndex('feed_plans', ['poultry_type', 'start_date']);
    
    await queryInterface.addIndex('feed_composition', ['feed_plan_id', 'age_week_start']);
    
    await queryInterface.addIndex('feed_alerts', ['farm_id', 'is_resolved']);
    await queryInterface.addIndex('feed_alerts', ['alert_type', 'severity']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order due to foreign key constraints
    await queryInterface.dropTable('feed_alerts');
    await queryInterface.dropTable('feed_composition');
    await queryInterface.dropTable('feed_plans');
    await queryInterface.dropTable('feed_consumption_logs');
    await queryInterface.dropTable('feed_stock');
    await queryInterface.dropTable('feed_suppliers');
    await queryInterface.dropTable('feed_items');
  }
};