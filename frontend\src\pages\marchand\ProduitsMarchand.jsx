import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Fab,
  Tooltip,
  Pagination,
  Switch,
  FormControlLabel,
  Avatar,
  Badge,
  CardMedia,
  CircularProgress,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Inventory as InventoryIcon,
  ShoppingCart as CartIcon,
  TrendingUp as TrendingIcon,
  Warning as WarningIcon,
  LocalOffer as OfferIcon,
  Star as StarIcon,
  Image as ImageIcon,
  Store as StoreIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  PhotoCamera as PhotoIcon
} from '@mui/icons-material';
import axios from 'axios';

const ProduitsMarchand = () => {
  const [produits, setProduits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduit, setSelectedProduit] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    categorie: 'aliments',
    sous_categorie: '',
    marque: '',
    prix_unitaire: '',
    prix_gros: '',
    quantite_min_gros: '',
    unite_mesure: 'kg',
    stock_disponible: '',
    stock_minimum: '',
    stock_maximum: '',
    statut: 'disponible',
    images: [],
    specifications: {},
    date_expiration: null,
    numero_lot: '',
    fournisseur: '',
    conditions_stockage: '',
    instructions_utilisation: '',
    poids: '',
    dimensions: { longueur: '', largeur: '', hauteur: '' },
    promotion_active: false,
    prix_promotion: '',
    date_debut_promotion: null,
    date_fin_promotion: null,
    tags: [],
    prix: '',
    stock_quantite: '',
    image_url: '',
    disponible: true,
    origine: '',
    date_production: '',
    certification: '',
    notes: ''
  });
  const [statistiques, setStatistiques] = useState({
    total_produits: 0,
    produits_disponibles: 0,
    produits_rupture: 0,
    valeur_stock: 0,
    produits_populaires: 0,
    note_moyenne: 0,
    nombre_avis: 0
  });
  const [stats, setStats] = useState({
    total_produits: 0,
    produits_disponibles: 0,
    stock_total: 0,
    valeur_stock: 0
  });

  // Récupérer l'ID du marchand depuis le contexte d'authentification
  const marchandId = localStorage.getItem('userId'); // À adapter selon votre système d'auth

  useEffect(() => {
    fetchProduits();
    fetchStats();
    fetchStatistiques();
  }, [page, searchTerm]);

  const fetchProduits = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });
      if (searchTerm) {
        params.append('search', searchTerm);
      }
      const response = await axios.get(`/api/produits/marchand/${marchandId}?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setProduits(response.data.data.produits);
      setTotalPages(response.data.data.pagination.totalPages);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des produits');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/marchand/produits/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Erreur lors de la récupération des statistiques:', err);
    }
  };

  const fetchStatistiques = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`/api/produits/marchand/${marchandId}/statistiques`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setStatistiques(response.data.data);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  const handleOpenDialog = (produit = null) => {
    if (produit) {
      setSelectedProduit(produit);
      setFormData({
        nom: produit.nom,
        description: produit.description,
        categorie: produit.categorie,
        prix: produit.prix,
        stock_quantite: produit.stock_quantite,
        unite_mesure: produit.unite_mesure,
        stock_minimum: produit.stock_minimum,
        image_url: produit.image_url || '',
        disponible: produit.disponible,
        origine: produit.origine || '',
        date_production: produit.date_production || '',
        date_expiration: produit.date_expiration || '',
        certification: produit.certification || '',
        notes: produit.notes || ''
      });
      setImagePreview(produit.image_url);
    } else {
      setSelectedProduit(null);
      setFormData({
        nom: '',
        description: '',
        categorie: '',
        prix: '',
        stock_quantite: '',
        unite_mesure: '',
        stock_minimum: '',
        image_url: '',
        disponible: true,
        origine: '',
        date_production: '',
        date_expiration: '',
        certification: '',
        notes: ''
      });
      setImagePreview(null);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduit(null);
    setImagePreview(null);
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        setFormData({ ...formData, image_url: reader.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem('token');
      const produitData = {
        ...formData,
        marchand_id: marchandId,
        prix_unitaire: parseFloat(formData.prix_unitaire),
        prix_gros: formData.prix_gros ? parseFloat(formData.prix_gros) : null,
        quantite_min_gros: formData.quantite_min_gros ? parseInt(formData.quantite_min_gros) : null,
        stock_disponible: parseInt(formData.stock_disponible),
        stock_minimum: parseInt(formData.stock_minimum),
        stock_maximum: formData.stock_maximum ? parseInt(formData.stock_maximum) : null,
        poids: formData.poids ? parseFloat(formData.poids) : null,
        prix_promotion: formData.prix_promotion ? parseFloat(formData.prix_promotion) : null
      };

      if (selectedProduit) {
        await axios.put(`/api/produits/${selectedProduit.id}`, produitData, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else {
        await axios.post('/api/produits', produitData, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }

      fetchProduits();
      fetchStatistiques();
      handleCloseDialog();
    } catch (err) {
      setError('Erreur lors de la sauvegarde du produit');
      console.error('Erreur:', err);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`/api/produits/${id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        fetchProduits();
        fetchStatistiques();
      } catch (err) {
        setError('Erreur lors de la suppression du produit');
        console.error('Erreur:', err);
      }
    }
  };

  const handleTogglePromotion = async (id, active) => {
    try {
      const token = localStorage.getItem('token');
      await axios.patch(`/api/produits/${id}/promotion`, { active }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchProduits();
      fetchStatistiques();
    } catch (err) {
      setError('Erreur lors de la mise à jour de la promotion');
      console.error('Erreur:', err);
    }
  };

  const handleUpdateStock = async (id, newStock) => {
    try {
      const token = localStorage.getItem('token');
      await axios.patch(`/api/produits/${id}/stock`, { stock_disponible: newStock }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchProduits();
      fetchStatistiques();
    } catch (err) {
      setError('Erreur lors de la mise à jour du stock');
      console.error('Erreur:', err);
    }
  };

  const getStockStatus = (produit) => {
    if (produit.stock_quantite === 0) {
      return { label: 'Rupture', color: 'error' };
    } else if (produit.stock_quantite <= produit.stock_minimum) {
      return { label: 'Stock faible', color: 'warning' };
    } else {
      return { label: 'En stock', color: 'success' };
    }
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'disponible': return 'success';
      case 'rupture': return 'error';
      case 'bientot_expire': return 'warning';
      case 'expire': return 'error';
      case 'suspendu': return 'default';
      default: return 'default';
    }
  };

  const getCategorieColor = (categorie) => {
    switch (categorie) {
      case 'aliments': return 'primary';
      case 'medicaments': return 'secondary';
      case 'equipements': return 'info';
      case 'supplements': return 'success';
      case 'accessoires': return 'warning';
      default: return 'default';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD'
    }).format(amount);
  };

  const categories = [
    'Volailles vivantes',
    'Œufs',
    'Viande de volaille',
    'Aliments pour volailles',
    'Médicaments vétérinaires',
    'Équipements d\'élevage',
    'Accessoires',
    'Autres'
  ];

  const unitesMesure = [
    'Pièce',
    'Kg',
    'Gramme',
    'Litre',
    'Sac',
    'Boîte',
    'Carton'
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Mes Produits
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Statistiques */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <StoreIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Produits
                  </Typography>
                  <Typography variant="h5">
                    {statistiques.total_produits}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Disponibles
                  </Typography>
                  <Typography variant="h5">
                    {statistiques.produits_disponibles}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WarningIcon color="error" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    En Rupture
                  </Typography>
                  <Typography variant="h5">
                    {statistiques.produits_rupture}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Populaires
                  </Typography>
                  <Typography variant="h5">
                    {statistiques.produits_populaires}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <StarIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Note Moyenne
                  </Typography>
                  <Typography variant="h5">
                    {statistiques.note_moyenne.toFixed(1)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <MoneyIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Valeur Stock
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(statistiques.valeur_stock)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Nouveau Produit
        </Button>
      </Box>

      {/* Grille des produits */}
      <Grid container spacing={3}>
        {produits.map((produit) => {
          const stockStatus = getStockStatus(produit);
          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={produit.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardMedia
                  component="div"
                  sx={{
                    height: 200,
                    backgroundColor: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {produit.image_url ? (
                    <img
                      src={produit.image_url}
                      alt={produit.nom}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  ) : (
                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'grey.300' }}>
                      <StoreIcon sx={{ fontSize: 40 }} />
                    </Avatar>
                  )}
                </CardMedia>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography gutterBottom variant="h6" component="div">
                    {produit.nom}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {produit.description}
                  </Typography>
                  <Chip
                    label={produit.categorie}
                    size="small"
                    sx={{ mb: 1 }}
                  />
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="h6" color="primary">
                      {formatCurrency(produit.prix)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      par {produit.unite_mesure}
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      Stock: {produit.stock_quantite} {produit.unite_mesure}
                    </Typography>
                    <Chip
                      label={stockStatus.label}
                      color={stockStatus.color}
                      size="small"
                    />
                  </Box>
                  {!produit.disponible && (
                    <Chip
                      label="Non disponible"
                      color="error"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                  )}
                </CardContent>
                <Box sx={{ p: 1 }}>
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog(produit)}
                    title="Modifier"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(produit.id)}
                    title="Supprimer"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Dialog pour ajouter/modifier un produit */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProduit ? 'Modifier le produit' : 'Nouveau produit'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nom du produit"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={formData.categorie}
                  onChange={(e) => setFormData({ ...formData, categorie: e.target.value })}
                >
                  {categories.map((cat) => (
                    <MenuItem key={cat} value={cat}>
                      {cat}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Prix"
                type="number"
                value={formData.prix}
                onChange={(e) => setFormData({ ...formData, prix: e.target.value })}
                InputProps={{
                  endAdornment: <InputAdornment position="end">DZD</InputAdornment>,
                }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Quantité en stock"
                type="number"
                value={formData.stock_quantite}
                onChange={(e) => setFormData({ ...formData, stock_quantite: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth required>
                <InputLabel>Unité de mesure</InputLabel>
                <Select
                  value={formData.unite_mesure}
                  onChange={(e) => setFormData({ ...formData, unite_mesure: e.target.value })}
                >
                  {unitesMesure.map((unite) => (
                    <MenuItem key={unite} value={unite}>
                      {unite}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Stock minimum"
                type="number"
                value={formData.stock_minimum}
                onChange={(e) => setFormData({ ...formData, stock_minimum: e.target.value })}
                helperText="Seuil d'alerte pour le stock"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Origine"
                value={formData.origine}
                onChange={(e) => setFormData({ ...formData, origine: e.target.value })}
                placeholder="ex: Ferme locale, Import"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date de production"
                type="date"
                value={formData.date_production}
                onChange={(e) => setFormData({ ...formData, date_production: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date d'expiration"
                type="date"
                value={formData.date_expiration}
                onChange={(e) => setFormData({ ...formData, date_expiration: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Certification"
                value={formData.certification}
                onChange={(e) => setFormData({ ...formData, certification: e.target.value })}
                placeholder="ex: Bio, Halal, etc."
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.disponible}
                    onChange={(e) => setFormData({ ...formData, disponible: e.target.checked })}
                  />
                }
                label="Produit disponible"
              />
            </Grid>
            <Grid item xs={12}>
              <Box>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="image-upload"
                  type="file"
                  onChange={handleImageChange}
                />
                <label htmlFor="image-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<PhotoIcon />}
                    sx={{ mb: 2 }}
                  >
                    Choisir une image
                  </Button>
                </label>
                {imagePreview && (
                  <Box sx={{ mt: 2 }}>
                    <img
                      src={imagePreview}
                      alt="Aperçu"
                      style={{
                        maxWidth: '200px',
                        maxHeight: '200px',
                        objectFit: 'cover',
                        borderRadius: '8px'
                      }}
                    />
                  </Box>
                )}
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Notes additionnelles sur le produit"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedProduit ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProduitsMarchand;