# Documentation API Poultry DZ - Exemples Postman

## Configuration de l'environnement

```json
{
  "BASE_URL": "http://localhost:3003/api",
  "TOKEN": "votre_jwt_token"
}
```

## Authentication

### Inscription (Register)

```http
POST {{BASE_URL}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "<PERSON><PERSON><PERSON>",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "role": "veterinaire"
}
```

### Connexion (Login)

```http
POST {{BASE_URL}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## Vétérinaires

### Liste des vétérinaires

```http
GET {{BASE_URL}}/veterinaires?page=1&limit=10
Authorization: Bearer {{TOKEN}}
```

### Détails d'un vétérinaire

```http
GET {{BASE_URL}}/veterinaires/1
Authorization: Bearer {{TOKEN}}
```

### Créer un vétérinaire

```http
POST {{BASE_URL}}/veterinaires
Authorization: Bearer {{TOKEN}}
Content-Type: application/json

{
  "numero_ordre": "VET123456",
  "specialites": ["Volailles", "Maladies infectieuses"],
  "disponibilites": {
    "lundi": ["09:00-12:00", "14:00-17:00"],
    "mardi": ["09:00-12:00", "14:00-17:00"],
    "mercredi": ["09:00-12:00"],
    "jeudi": ["09:00-12:00", "14:00-17:00"],
    "vendredi": ["09:00-12:00", "14:00-16:00"]
  },
  "telephone": "0555123456",
  "email_professionnel": "<EMAIL>",
  "adresse_cabinet": "123 Rue des Vétérinaires, Alger",
  "description": "Spécialiste en santé aviaire avec 10 ans d'expérience",
  "tarifs": {
    "consultation_standard": 2000,
    "visite_elevage": 5000,
    "vaccination": 1500
  }
}
```

### Modifier un vétérinaire

```http
PUT {{BASE_URL}}/veterinaires/1
Authorization: Bearer {{TOKEN}}
Content-Type: application/json

{
  "specialites": ["Volailles", "Maladies infectieuses", "Nutrition"],
  "disponibilites": {
    "lundi": ["08:00-12:00", "14:00-18:00"],
    "mardi": ["08:00-12:00", "14:00-18:00"],
    "mercredi": ["08:00-12:00"],
    "jeudi": ["08:00-12:00", "14:00-18:00"],
    "vendredi": ["08:00-12:00", "14:00-17:00"]
  },
  "telephone": "0555123457",
  "description": "Spécialiste en santé aviaire avec plus de 10 ans d'expérience",
  "tarifs": {
    "consultation_standard": 2500,
    "visite_elevage": 6000,
    "vaccination": 2000
  }
}
```

### Supprimer un vétérinaire

```http
DELETE {{BASE_URL}}/veterinaires/1
Authorization: Bearer {{TOKEN}}
```

## Réponses attendues

### Succès

```json
// 200 OK - Liste des vétérinaires
{
  "veterinaires": [
    {
      "id": 1,
      "numero_ordre": "VET123456",
      "specialites": ["Volailles", "Maladies infectieuses"],
      "note_moyenne": 4.5,
      "nombre_avis": 12
    }
  ],
  "totalPages": 1,
  "currentPage": 1,
  "totalItems": 1
}

// 201 Created - Création réussie
{
  "id": 1,
  "numero_ordre": "VET123456",
  "specialites": ["Volailles", "Maladies infectieuses"],
  "created_at": "2023-12-20T10:00:00.000Z"
}
```

### Erreurs

```json
// 400 Bad Request - Validation échouée
{
  "errors": [
    {
      "msg": "Le numéro d'ordre est requis",
      "param": "numero_ordre",
      "location": "body"
    }
  ]
}

// 401 Unauthorized - Token manquant ou invalide
{
  "message": "Token non fourni ou invalide"
}

// 403 Forbidden - Accès non autorisé
{
  "message": "Accès refusé. Rôle vétérinaire requis."
}

// 404 Not Found - Ressource non trouvée
{
  "message": "Vétérinaire non trouvé"
}

// 500 Internal Server Error - Erreur serveur
{
  "message": "Erreur serveur"
}
```
