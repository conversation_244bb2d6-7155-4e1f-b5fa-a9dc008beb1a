/**
 * Configuration des tests pour le backend
 */

const { Pool } = require('pg');
const app = require('../src/app');
const logger = require('../src/utils/logger');

// Configuration de la base de données de test
const testDbConfig = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: process.env.TEST_DB_PORT || 5432,
  database: process.env.TEST_DB_NAME || 'poultraydz_test',
  user: process.env.TEST_DB_USER || 'postgres',
  password: process.env.TEST_DB_PASSWORD || 'password',
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

let testPool;

/**
 * Configuration globale des tests
 */
beforeAll(async () => {
  // Désactiver les logs pendant les tests
  logger.level = 'error';
  
  // Créer la connexion à la base de test
  testPool = new Pool(testDbConfig);
  
  try {
    await testPool.connect();
    console.log('✅ Connexion à la base de données de test établie');
    
    // Nettoyer la base de données
    await cleanDatabase();
    
    // Créer les tables de test
    await setupTestTables();
    
  } catch (error) {
    console.error('❌ Erreur lors de la configuration des tests:', error);
    process.exit(1);
  }
});

/**
 * Nettoyage après tous les tests
 */
afterAll(async () => {
  if (testPool) {
    await cleanDatabase();
    await testPool.end();
    console.log('✅ Connexion à la base de données de test fermée');
  }
});

/**
 * Nettoyage avant chaque test
 */
beforeEach(async () => {
  await cleanTestData();
});

/**
 * Nettoyer complètement la base de données
 */
async function cleanDatabase() {
  const client = await testPool.connect();
  
  try {
    // Désactiver les contraintes de clés étrangères temporairement
    await client.query('SET session_replication_role = replica;');
    
    // Supprimer toutes les données des tables
    const tables = [
      'commande_items',
      'commandes',
      'consultations',
      'prescriptions',
      'production',
      'volailles',
      'user_roles',
      'users',
      'roles'
    ];
    
    for (const table of tables) {
      await client.query(`TRUNCATE TABLE ${table} RESTART IDENTITY CASCADE;`);
    }
    
    // Réactiver les contraintes
    await client.query('SET session_replication_role = DEFAULT;');
    
  } finally {
    client.release();
  }
}

/**
 * Nettoyer seulement les données de test
 */
async function cleanTestData() {
  const client = await testPool.connect();
  
  try {
    // Supprimer les données créées pendant les tests
    await client.query(`
      DELETE FROM commande_items WHERE commande_id IN (
        SELECT id FROM commandes WHERE eleveur_id IN (
          SELECT id FROM users WHERE email LIKE '%test%'
        )
      );
    `);
    
    await client.query(`
      DELETE FROM commandes WHERE eleveur_id IN (
        SELECT id FROM users WHERE email LIKE '%test%'
      );
    `);
    
    await client.query(`
      DELETE FROM consultations WHERE eleveur_id IN (
        SELECT id FROM users WHERE email LIKE '%test%'
      );
    `);
    
    await client.query(`
      DELETE FROM production WHERE volaille_id IN (
        SELECT id FROM volailles WHERE eleveur_id IN (
          SELECT id FROM users WHERE email LIKE '%test%'
        )
      );
    `);
    
    await client.query(`
      DELETE FROM volailles WHERE eleveur_id IN (
        SELECT id FROM users WHERE email LIKE '%test%'
      );
    `);
    
    await client.query(`DELETE FROM users WHERE email LIKE '%test%';`);
    
  } finally {
    client.release();
  }
}

/**
 * Créer les tables de test si elles n'existent pas
 */
async function setupTestTables() {
  const client = await testPool.connect();
  
  try {
    // Créer la table des rôles
    await client.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Insérer les rôles par défaut
    await client.query(`
      INSERT INTO roles (name, description) VALUES
      ('admin', 'Administrateur système'),
      ('eleveur', 'Éleveur de volailles'),
      ('veterinaire', 'Vétérinaire'),
      ('marchand', 'Marchand/Vendeur')
      ON CONFLICT (name) DO NOTHING;
    `);
    
    // Créer la table des utilisateurs
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        role_id INTEGER REFERENCES roles(id),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Créer la table des volailles
    await client.query(`
      CREATE TABLE IF NOT EXISTS volailles (
        id SERIAL PRIMARY KEY,
        nom VARCHAR(100) NOT NULL,
        type_volaille VARCHAR(50) NOT NULL,
        nombre_total INTEGER NOT NULL DEFAULT 0,
        nombre_actuel INTEGER NOT NULL DEFAULT 0,
        description TEXT,
        statut VARCHAR(20) DEFAULT 'actif',
        eleveur_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Créer la table des consultations
    await client.query(`
      CREATE TABLE IF NOT EXISTS consultations (
        id SERIAL PRIMARY KEY,
        eleveur_id INTEGER REFERENCES users(id),
        veterinaire_id INTEGER REFERENCES users(id),
        volaille_id INTEGER REFERENCES volailles(id),
        date_consultation TIMESTAMP NOT NULL,
        diagnostic TEXT,
        traitement TEXT,
        notes TEXT,
        urgence BOOLEAN DEFAULT false,
        statut VARCHAR(20) DEFAULT 'programmee',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Créer la table des commandes
    await client.query(`
      CREATE TABLE IF NOT EXISTS commandes (
        id SERIAL PRIMARY KEY,
        eleveur_id INTEGER REFERENCES users(id),
        marchand_id INTEGER REFERENCES users(id),
        date_commande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        montant_total DECIMAL(10,2) DEFAULT 0,
        statut VARCHAR(20) DEFAULT 'en_attente',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Créer la table des items de commande
    await client.query(`
      CREATE TABLE IF NOT EXISTS commande_items (
        id SERIAL PRIMARY KEY,
        commande_id INTEGER REFERENCES commandes(id) ON DELETE CASCADE,
        produit_nom VARCHAR(255) NOT NULL,
        quantite INTEGER NOT NULL,
        prix_unitaire DECIMAL(10,2) NOT NULL,
        sous_total DECIMAL(10,2) NOT NULL
      );
    `);
    
    // Créer la table de production
    await client.query(`
      CREATE TABLE IF NOT EXISTS production (
        id SERIAL PRIMARY KEY,
        volaille_id INTEGER REFERENCES volailles(id) ON DELETE CASCADE,
        type_production VARCHAR(50) NOT NULL,
        quantite_produite INTEGER NOT NULL,
        unite VARCHAR(20) NOT NULL,
        date_production DATE NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
  } finally {
    client.release();
  }
}

/**
 * Créer un utilisateur de test
 */
async function createTestUser(userData = {}) {
  const defaultUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password_hash: '$2b$10$test.hash.for.testing',
    first_name: 'Test',
    last_name: 'User',
    role_id: 2 // eleveur par défaut
  };
  
  const user = { ...defaultUser, ...userData };
  
  const client = await testPool.connect();
  try {
    const result = await client.query(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, role_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *;
    `, [user.username, user.email, user.password_hash, user.first_name, user.last_name, user.role_id]);
    
    return result.rows[0];
  } finally {
    client.release();
  }
}

/**
 * Créer une volaille de test
 */
async function createTestVolaille(eleveurId, volailleData = {}) {
  const defaultVolaille = {
    nom: 'Test Volaille',
    type_volaille: 'poule',
    nombre_total: 100,
    nombre_actuel: 95,
    description: 'Volaille de test'
  };
  
  const volaille = { ...defaultVolaille, ...volailleData };
  
  const client = await testPool.connect();
  try {
    const result = await client.query(`
      INSERT INTO volailles (nom, type_volaille, nombre_total, nombre_actuel, description, eleveur_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *;
    `, [volaille.nom, volaille.type_volaille, volaille.nombre_total, volaille.nombre_actuel, volaille.description, eleveurId]);
    
    return result.rows[0];
  } finally {
    client.release();
  }
}

/**
 * Utilitaires pour les tests
 */
const testUtils = {
  pool: testPool,
  createTestUser,
  createTestVolaille,
  cleanDatabase,
  cleanTestData
};

module.exports = {
  app,
  testUtils
};
