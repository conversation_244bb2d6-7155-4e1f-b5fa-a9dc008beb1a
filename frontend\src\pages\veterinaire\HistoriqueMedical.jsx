/**
 * Composant d'historique médical pour les vétérinaires
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  TextField,
  Autocomplete,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Paper,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  MedicalServices as MedicalIcon,
  LocalPharmacy as PharmacyIcon,
  Assignment as ConsultationIcon,
  Vaccines as VaccineIcon,
  Science as AnalysisIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { veterinaireAPI } from '../../services/veterinaireService';

const HistoriqueMedical = () => {
  const { user } = useAuth();
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [historique, setHistorique] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  useEffect(() => {
    loadPatients();
  }, []);

  useEffect(() => {
    if (selectedPatient) {
      loadPatientHistory();
    }
  }, [selectedPatient, startDate, endDate]);

  const loadPatients = async () => {
    try {
      const response = await veterinaireAPI.getPatients();
      setPatients(response.data || []);
    } catch (err) {
      setError('Erreur lors du chargement des patients');
      console.error('Erreur patients:', err);
    }
  };

  const loadPatientHistory = async () => {
    if (!selectedPatient) return;

    try {
      setLoading(true);
      const response = await veterinaireAPI.getPatientHistory(selectedPatient.id);
      setHistorique(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement de l\'historique');
      console.error('Erreur historique:', err);
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'consultation':
        return <ConsultationIcon />;
      case 'prescription':
        return <PharmacyIcon />;
      case 'vaccination':
        return <VaccineIcon />;
      case 'analyse':
        return <AnalysisIcon />;
      case 'urgence':
        return <WarningIcon />;
      default:
        return <MedicalIcon />;
    }
  };

  const getEventColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'consultation':
        return 'primary';
      case 'prescription':
        return 'success';
      case 'vaccination':
        return 'info';
      case 'analyse':
        return 'warning';
      case 'urgence':
        return 'error';
      default:
        return 'default';
    }
  };

  const getEventLabel = (type) => {
    switch (type?.toLowerCase()) {
      case 'consultation':
        return 'Consultation';
      case 'prescription':
        return 'Prescription';
      case 'vaccination':
        return 'Vaccination';
      case 'analyse':
        return 'Analyse';
      case 'urgence':
        return 'Urgence';
      default:
        return type;
    }
  };

  const handleViewDetails = (event) => {
    setSelectedEvent(event);
    setOpenDetailDialog(true);
  };

  const handleCloseDetailDialog = () => {
    setOpenDetailDialog(false);
    setSelectedEvent(null);
  };

  const handlePrintHistory = async () => {
    if (!selectedPatient) return;

    try {
      const response = await veterinaireAPI.printPatientHistory(selectedPatient.id, {
        start_date: startDate,
        end_date: endDate
      });
      
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (err) {
      setError('Erreur lors de l\'impression de l\'historique');
      console.error('Erreur impression:', err);
    }
  };

  const filteredHistorique = historique.filter(event => {
    const eventDate = new Date(event.date);
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;

    if (start && eventDate < start) return false;
    if (end && eventDate > end) return false;
    return true;
  });

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Historique Médical
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Sélection du patient et filtres */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <Autocomplete
                  options={patients}
                  getOptionLabel={(option) => `${option.nom} - ${option.eleveur_nom}`}
                  value={selectedPatient}
                  onChange={(event, newValue) => {
                    setSelectedPatient(newValue);
                    setHistorique([]);
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Sélectionner un patient" fullWidth />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <PetsIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {option.nom}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.eleveur_nom} - {option.espece}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Date de début"
                  value={startDate}
                  onChange={setStartDate}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Date de fin"
                  value={endDate}
                  onChange={setEndDate}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PrintIcon />}
                  onClick={handlePrintHistory}
                  disabled={!selectedPatient}
                >
                  Imprimer
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {selectedPatient && (
          <Grid container spacing={3}>
            {/* Informations du patient */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informations Patient
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <PetsIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Nom"
                        secondary={selectedPatient.nom}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <PersonIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Propriétaire"
                        secondary={selectedPatient.eleveur_nom}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Espèce"
                        secondary={selectedPatient.espece}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Race"
                        secondary={selectedPatient.race || 'Non spécifiée'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Âge"
                        secondary={selectedPatient.age || 'Non spécifié'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Poids"
                        secondary={selectedPatient.poids ? `${selectedPatient.poids} kg` : 'Non spécifié'}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>

              {/* Résumé des événements */}
              <Card sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Résumé
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['consultation', 'prescription', 'vaccination', 'analyse', 'urgence'].map(type => {
                      const count = filteredHistorique.filter(event => event.type === type).length;
                      return count > 0 ? (
                        <Chip
                          key={type}
                          label={`${getEventLabel(type)}: ${count}`}
                          color={getEventColor(type)}
                          size="small"
                        />
                      ) : null;
                    })}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Timeline de l'historique */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Historique Médical ({filteredHistorique.length} événements)
                  </Typography>
                  
                  {loading ? (
                    <Typography>Chargement de l'historique...</Typography>
                  ) : filteredHistorique.length === 0 ? (
                    <Alert severity="info">
                      Aucun événement médical trouvé pour ce patient dans la période sélectionnée.
                    </Alert>
                  ) : (
                    <Timeline>
                      {filteredHistorique.map((event, index) => (
                        <TimelineItem key={event.id || index}>
                          <TimelineSeparator>
                            <TimelineDot color={getEventColor(event.type)}>
                              {getEventIcon(event.type)}
                            </TimelineDot>
                            {index < filteredHistorique.length - 1 && <TimelineConnector />}
                          </TimelineSeparator>
                          <TimelineContent>
                            <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Box sx={{ flex: 1 }}>
                                  <Typography variant="h6" component="div">
                                    {getEventLabel(event.type)}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" gutterBottom>
                                    {new Date(event.date).toLocaleDateString('fr-FR', {
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </Typography>
                                  <Typography variant="body1" gutterBottom>
                                    {event.titre || event.motif}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {event.description || event.diagnostic}
                                  </Typography>
                                  {event.medicaments && event.medicaments.length > 0 && (
                                    <Box sx={{ mt: 1 }}>
                                      <Typography variant="caption" color="text.secondary">
                                        Médicaments: {event.medicaments.map(m => m.nom).join(', ')}
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewDetails(event)}
                                >
                                  <ViewIcon />
                                </IconButton>
                              </Box>
                            </Paper>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {!selectedPatient && (
          <Card>
            <CardContent>
              <Alert severity="info">
                Veuillez sélectionner un patient pour voir son historique médical.
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Dialog de détails d'un événement */}
        <Dialog open={openDetailDialog} onClose={handleCloseDetailDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            Détails - {selectedEvent && getEventLabel(selectedEvent.type)}
          </DialogTitle>
          <DialogContent>
            {selectedEvent && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    {selectedEvent.titre || selectedEvent.motif}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {new Date(selectedEvent.date).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </Typography>
                </Grid>
                
                {selectedEvent.diagnostic && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Diagnostic:</Typography>
                    <Typography variant="body2">{selectedEvent.diagnostic}</Typography>
                  </Grid>
                )}
                
                {selectedEvent.traitement && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Traitement:</Typography>
                    <Typography variant="body2">{selectedEvent.traitement}</Typography>
                  </Grid>
                )}
                
                {selectedEvent.medicaments && selectedEvent.medicaments.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Médicaments prescrits:</Typography>
                    <List dense>
                      {selectedEvent.medicaments.map((med, index) => (
                        <ListItem key={index}>
                          <ListItemText
                            primary={med.nom}
                            secondary={`${med.dosage} - ${med.frequence} - ${med.duree}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
                
                {selectedEvent.notes && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Notes:</Typography>
                    <Typography variant="body2">{selectedEvent.notes}</Typography>
                  </Grid>
                )}
                
                {selectedEvent.cout && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Coût:</Typography>
                    <Typography variant="body2">{selectedEvent.cout} DA</Typography>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDetailDialog}>Fermer</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default HistoriqueMedical;
